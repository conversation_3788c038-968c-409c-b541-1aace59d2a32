<!DOCTYPE html>
{% load humanize %}
<html>
<title>供花供物</title>
<head>
<style type="text/css">
@page {
  @top-right {
    content: counter(page) "/" counter(pages);
  }
}
body {
  font-family: "Noto Serif CJK JP", serif;
  font-size: 12px;
  color: #333;
  margin: 0;
}
.content {
  position: relative;
}
.content table {
  border-collapse: collapse;
  width: 100%;
}
.content td {
  padding: 7px;
  border: 1px solid rgba(0,0,0,0.4);
  vertical-align: top;
}
.content table tr:nth-child(1) {
  background-color:rgba(0,0,0,0.05);
  text-align: center;
}
.content table tr td:nth-child(1) {
  width: 50px;
  text-align: center;
  vertical-align: middle;
}
.content table tr td:nth-child(2) {
  vertical-align: middle;
}
.money::after {
  content: '円';
}
.money:empty {
  display: none;
}
</style>
</head>
<body>
<section class="content" style="clear: both">
  <div>
    <table>
      <tr>
        <td>No</td>
        <td>ご芳名</td>
        <td>名礼</td>
        <td>摘要</td>
      </tr>
      {% for entry in seko.entries.all %}
      {% for detail in entry.details.all %}
      <tr>
        <td>{{ counter }}</td>
        <td>
          {{ entry.entry_name }}<br>
          {{ entry.entry_name_kana }}
        </td>
        <td>
          {{ detail.kumotsu.okurinushi_company|default_if_none:"" }}<br>
          {{ detail.kumotsu.okurinushi_title|default_if_none:"" }}<br>
          {{ detail.kumotsu.okurinushi_name|default_if_none:"" }}<br>
          {{ detail.kumotsu.renmei1|default_if_none:"" }}<br>
          {{ detail.kumotsu.renmei2|default_if_none:"" }}
        </td>
        <td>
          <div>{{ detail.item_name }}</div>
          <div>
            <span>{{ detail.kumotsu.henrei_kumotsu.henreihin_name|default_if_none:"" }}</span>
            <span class="money" style="float: right">{{ detail.kumotsu.henrei_kumotsu.henreihin_price|intcomma:False }}</span>
          </div>
        </td>
      </tr>
      {% endfor %}
      {% endfor %}
      {% if total_henrei > 0 %}
      <tr>
        <td colspan="4" style="text-align: right">返礼品合計：{{ total_henrei|intcomma:False }}円</td>
      </tr>
      {% endif %}
    </table>
  </div>
</section>
</body>
</html>
