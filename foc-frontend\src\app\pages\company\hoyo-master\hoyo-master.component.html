
<div class="container">
  <div class="inner with_footer">
    <div class="contents">
      <div class="menu_title">
        <i class="monument icon big"></i>
        法要マスタ
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini" (click)="loadSample()">
          <i class="file import icon"></i>サンプル読込
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" [(selectedName)]="company_name" (selectedItemChange)="companyChange($event)"></com-dropdown>
        </div>
        <div class="line">
          <label>法要形式</label>
          <ng-container *ngFor="let hoyo_style of hoyo_style_list">
            <div class="ui toggle checkbox">
              <input type="checkbox" [(ngModel)]="hoyo_style.checked" (change)="filterHoyoMasterList()">
            <label>{{hoyo_style.name}}</label>
            </div>
          </ng-container>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="hoyo_master_list?.length">
          全{{hoyo_master_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?hoyo_master_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination, pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(pagination, page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination, pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!hoyo_master_list?.length">
              <th class="display_num">表示順</th>
              <th class="style">形式</th>
              <th class="name">名称</th>
              <th class="elapsed_time">経過時間</th>
              <th class="unit">時間単位</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let hoyo_master of hoyo_master_list; index as i">
              <tr (click)="showData($event, hoyo_master)" *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
                <td class="center aligned display_num" title="{{hoyo_master.display_num}}">{{hoyo_master.display_num}}</td>
                <td class="style" title="{{hoyo_master.style.name}}">{{hoyo_master.style.name}}</td>
                <td class="name" title="{{hoyo_master.name}}">{{hoyo_master.name}}</td>
                <td class="center aligned elapsed_time" title="{{hoyo_master.elapsed_time}}">{{hoyo_master.elapsed_time}}</td>
                <td class="center aligned unit" title="{{getUnitName(hoyo_master.unit)}}">{{getUnitName(hoyo_master.unit)}}</td>
                <td class="center aligned button operation">
                  <i class="large trash alternate icon" title="削除" (click)="deleteData(hoyo_master)"></i>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="table_fixed body" *ngIf="company_id">
        <table class="ui celled structured unstackable table">
          <tbody>
            <tr title="新規追加" (click)="showData($event)">
              <td colspan="3" class="center aligned"><i class="large add icon"></i></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal mini" id="hoyo-master-edit">
        <div class="header ui">
          <span>
            <i class="monument icon large"></i>法要マスタ{{edit_type===1?'登録':'編集'}} 
          </span>
          <span class="title" *ngIf="company_name">【葬儀社:<span class="data">{{company_name}}</span>】</span>
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="hoyo_master_edit">
            <div class="line">
              <label class="required large">表示順</label>
              <div class="ui input tiny">
                <input type="number" min="1" [(ngModel)]="hoyo_master_edit.display_num">
              </div>
            </div>
            <div class="line">
              <label class="required large">形式</label>
              <div class="ui input">
                <com-dropdown [settings]="styleombo" [(selectedValue)]="hoyo_master_edit.style.id" *ngIf="hoyo_master_edit.style"></com-dropdown>
              </div>
            </div>
            <div class="line">
              <label class="required large">法要名称</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="name" [(ngModel)]="hoyo_master_edit.name">
              </div>
            </div>
            <div class="line">
              <label class="required large">逝去日からの経過時間</label>
              <div class="ui input tiny">
                <input type="number" min="1" [(ngModel)]="hoyo_master_edit.elapsed_time">
              </div>
              <div class="ui input">
                <com-dropdown class="divided tiny" [settings]="unitCombo" [(selectedValue)]="hoyo_master_edit.unit"></com-dropdown>
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeHoyoMasterEdit()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
