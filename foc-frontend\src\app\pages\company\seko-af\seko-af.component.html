
<div class="container">
  <div class="inner">
    <div class="contents">
      <div class="menu_title">
        <i class="hand holding medical icon big"></i>
        <i class="comment outline icon combo"></i>
        AF一覧
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(sekoBaseComboEm, hallComboEm, afStaffComboEm, sekoDateFromEm, sekoDateToEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchSeko()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>施行拠点</label>
          <com-dropdown #sekoBaseComboEm [settings]="sekoBaseCombo" [(selectedValue)]="form_data.seko_department_id"></com-dropdown>
          <!--label>部門</label>
          <com-dropdown #departComboEm [settings]="departCombo" [(selectedValue)]="form_data.seko_department_id" (selectedItemChange)="departChange($event, hallComboEm)"></com-dropdown>
          -->
          <label>式場</label>
          <com-dropdown #hallComboEm [settings]="hallCombo" [(selectedValue)]="form_data.hall_id"></com-dropdown>
          <label>AF担当者</label>
          <com-dropdown #afStaffComboEm [settings]="afStaffCombo" [(selectedValue)]="form_data.af_staff_id"></com-dropdown>
          <div class="ui toggle checkbox">
            <input type="checkbox" name="unanswerd_af" [(ngModel)]="form_data.unanswerd_af">
            <label>AF希望未回答のみ</label>
          </div>
        </div>
        <div class="line">
          <label>葬家名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.soke_name">
          </div>
          <label>施行日</label>
          <com-calendar #sekoDateFromEm [settings]="seko_date_from_config" id="seko_date_from" [(value)]="form_data.seko_date_from"></com-calendar>
          <label class="plane">～</label>
          <com-calendar #sekoDateToEm [settings]="seko_date_to_config" id="seko_date_to" [(value)]="form_data.seko_date_to"></com-calendar>
          <div class="ui toggle checkbox">
            <input type="checkbox" name="unanswerd_inquiry" [(ngModel)]="form_data.unanswerd_inquiry">
            <label>フリー問合せ未回答のみ</label>
          </div>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="seko_list?.length">
          全{{seko_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?seko_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="id">施行ID</th>
              <th class="mourner_family">葬家名</th>
              <th class="mourner_name">喪主名</th>
              <th class="exec_date">施行日</th>
              <th class="af_employee">AF担当者</th>
              <th class="depart">施行拠点</th>
              <th class="place">式場</th>
              <th class="af_wish">AF希望</th>
              <th class="af_wish_answer">AF希望<br>未回答</th>
              <th class="af_inquiry">フリー問合せ<br>未回答</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let seko of seko_list; index as i">
            <tr (click)="showSekoData($event, seko)" *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
              <td class="center aligned id" title="{{seko.id}}">{{seko.id}}</td>
              <td class="mourner_family" title="{{seko.soke_name}}">{{seko.soke_name}}</td>
              <td class="mourner_name" title="{{seko.moshu.name}}">{{seko.moshu.name}}</td>
              <td class="exec_date center aligned" title="{{seko.seko_date | date: 'yyyy/MM/dd'}}">{{seko.seko_date | date: 'yyyy/MM/dd'}}</td>
              <td class="af_employee" title="{{seko.af_staff_name}}">{{seko.af_staff_name}}</td>
              <td class="depart" title="{{seko.seko_department_name}}">{{seko.seko_department_name}}</td>
              <td class="place" title="{{getPlace(seko)}}">{{getPlace(seko)}}</td>
              <td class="center aligned af_wish" title="{{getWishCount(seko.seko_af)}}">{{getWishCount(seko.seko_af)}}</td>
              <td class="center aligned af_wish_answer" title="{{getWishCountUnanswered(seko.seko_af)}}">{{getWishCountUnanswered(seko.seko_af)}}</td>
              <td class="center aligned af_inquiry" title="{{getInquiryCountUnanswered(seko.inquiries)}}">{{getInquiryCountUnanswered(seko.inquiries)}}</td>
              <td class="center aligned button operation">
                <i class="large hand holding medical icon" title="AF活動状況" (click)="goActivity(seko)"></i>
              </td>
            </tr>
            </ng-container>
            <tr *ngIf="!seko_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="11">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal seko big" id="seko">
        <div class="header ui">
          <span><i class="comment icon large"></i>AF問合せ登録</span>
          <span class="title" *ngIf="af_wish_edit">【相談項目:<span class="data">{{af_wish_edit.af_type.name}}</span>】</span>
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content" *ngIf="selected_seko">
          <div class="sub_title">AF希望項目</div>
          <ng-container *ngFor="let af_group of af_group_list">
            <div class="af_group">{{af_group.name}}</div>
            <div class="af-check ui segment">
              <ng-container *ngFor="let after_follow of af_group.after_follows">
                <div class="ui checkbox">
                  <input type="checkbox" [(ngModel)]="after_follow.selected" disabled>
                  <label>{{after_follow.name}}</label>
                </div>
              </ng-container>
            </div>
          </ng-container>

          <div class="input_area">
            <div class="line">
              <label class="large">コンタクト方法</label>
              <label class="data contact_way" *ngIf="selected_seko.seko_af && selected_seko.seko_af?.contact_way">{{selected_seko.seko_af.contact_way.contact_way}}</label>
            </div>
            <div class="line">
              <label class="large">備考</label>
              <label class="data note" *ngIf="selected_seko.seko_af">{{selected_seko.seko_af.note}}</label>
            </div>
          </div>
          <div class="sub_title">問合せ一覧</div>
          <div class="table-area">
            <div class="table_fixed head">
              <table class="ui celled structured unstackable table">
                <thead>
                  <tr class="center aligned" [class.no-data]="!selected_seko?.inquiries?.length">
                    <th class="updated_at">登録日時</th>
                    <th class="query">質問内容</th>
                    <th class="answer">回答内容</th>
                    <th class="staff_name">回答者</th>
                    <th class="operation"></th>
                  </tr>
                </thead>
              </table>
            </div>
            <div class="table_fixed body" *ngIf="selected_seko">
              <table class="ui celled structured unstackable table">
                <tbody>
                  <ng-container *ngFor="let inquiry of selected_seko?.inquiries">
                    <ng-container *ngIf="inquiry.answers?.length">
                      <ng-container *ngFor="let answer of inquiry.answers; index as i">
                        <tr>
                          <ng-container *ngIf="i===0">
                            <td class="center aligned updated_at" title="{{inquiry.updated_at | date: 'yyyy/MM/dd HH:mm'}}">{{inquiry.updated_at | date: 'yyyy/MM/dd HH:mm'}}</td>
                            <td class="query" title="{{inquiry.query}}">{{inquiry.query}}</td>
                          </ng-container>
                          <ng-container *ngIf="i>0">
                            <td class="center aligned updated_at group" title="{{inquiry.updated_at | date: 'yyyy/MM/dd HH:mm'}}"></td>
                            <td class="query group" title="{{inquiry.query}}"></td>
                          </ng-container>

                          <td class="answer" title="{{answer.answer}}">{{answer.answer}}</td>
                          <td class="staff_name" title="{{answer.staff.name}}">{{answer.staff.name}}</td>
                          <ng-container *ngIf="i===0">
                            <td class="center aligned button operation">
                              <i class="large comment alternate icon" title="回答記入" (click)="showAnswerInput(inquiry)"></i>
                            </td>
                          </ng-container>
                          <ng-container *ngIf="i>0">
                            <td class="center aligned button operation group">
                            </td>
                          </ng-container>
                        </tr>
                      </ng-container>
                    </ng-container>
                    <ng-container *ngIf="!inquiry.answers?.length">
                      <tr>
                        <td class="center aligned updated_at" title="{{inquiry.updated_at | date: 'yyyy/MM/dd HH:mm'}}">{{inquiry.updated_at | date: 'yyyy/MM/dd HH:mm'}}</td>
                        <td class="query" title="{{inquiry.query}}">{{inquiry.query}}</td>
                        <td class="answer"></td>
                        <td class="staff_name"></td>
                        <td class="center aligned button operation">
                          <i class="large comment alternate icon" title="回答記入" (click)="showAnswerInput(inquiry)"></i>
                        </td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini light cancel" (click)="closeSekoData()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal small answer" id="answer-input">
        <div class="header ui">
          <span><i class="comment alternate　icon large"></i>回答内容登録</span>
        </div>
        <div class="messageArea">
          <div class="ui message {{message_sub?.type}}" [class.visible]="!!message_sub" [class.hidden]="!message_sub" (click)="closeMessageSub()">
            <i class="close icon" (click)="closeMessageSub()"></i>
            <div class="ui center aligned header">
              <i class="{{message_sub?.type==='info'?'info circle':message_sub?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message_sub?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message_sub?.msg">
              {{message_sub?.msg}}
            </div>
          </div>
        </div>
        <div class="content" *ngIf="selected_inquiry">
          <div class="input_area">
            <div class="line row5">
              <label class="required">回答内容</label>
              <div class="ui icon input textarea">
                <textarea id="sentence" rows="4" cols="35" [(ngModel)]="answer_edit.answer"></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveSekoAnswerData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeAnswerInput()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

		</div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group">
    <button class="ui labeled icon button mini light" (click)="exportCsv()">
      <i class="download icon"></i>CSV出力
    </button>
  </div>
</div>