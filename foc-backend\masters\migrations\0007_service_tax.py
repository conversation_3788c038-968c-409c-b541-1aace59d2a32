# Generated by Django 3.1.2 on 2020-10-30 02:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('masters', '0006_chobundaishimaster'),
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('name', models.TextField(verbose_name='name')),
                ('single_item_flg', models.BooleanField(verbose_name='single item flg')),
                ('cart_flg', models.BooleanField(verbose_name='cart flg')),
                ('payment_flg', models.BooleanField(verbose_name='payment flg')),
            ],
            options={
                'db_table': 'mm_service',
            },
        ),
        migrations.CreateModel(
            name='Tax',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('tax_pct', models.IntegerField(verbose_name='tax pct')),
                ('keigen_flg', models.BooleanField(verbose_name='keigen flg')),
            ],
            options={
                'db_table': 'mm_tax',
            },
        ),
    ]
