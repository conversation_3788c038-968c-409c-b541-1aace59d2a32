from typing import Dict

from django.contrib.auth import authenticate
from drf_extra_fields.relations import PresentablePrimary<PERSON>eyRelatedField
from rest_framework import exceptions, serializers
from rest_framework.validators import UniqueTogetherValidator
from rest_framework_simplejwt.serializers import TokenObtainSerializer

from bases.models import Base
from bases.serializers import BaseSerializer, BaseUpwardSerializer
from staffs.models import Staff
from utils.authentication import ClassableRefreshToken
from utils.serializer_mixins import AddUserIdMixin


class StaffTokenObtainPairSerializer(TokenObtainSerializer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields['company_code'] = serializers.CharField()

    def validate(self, attrs) -> Dict:
        authenticate_kwargs = {
            'company_code': attrs['company_code'],
            self.username_field: attrs[self.username_field],
            'password': attrs['password'],
        }
        authenticate_kwargs['request'] = self.context.get('request')

        self.user = authenticate(**authenticate_kwargs)

        if self.user is None or not self.user.is_active:
            raise exceptions.AuthenticationFailed(
                self.error_messages['no_active_account'],
                'no_active_account',
            )

        refresh = self.get_token(self.user)
        return {'refresh': str(refresh), 'access': str(refresh.access_token)}

    @classmethod
    def get_token(cls, user) -> ClassableRefreshToken:
        return ClassableRefreshToken.for_user(user)


class StaffParentBasesSerializer(serializers.ModelSerializer):

    base = BaseUpwardSerializer()

    class Meta:
        model = Staff
        fields = [
            'id',
            'company_code',
            'login_id',
            'name',
            'base',
            'mail_address',
            'retired_flg',
            'created_at',
            'updated_at',
            'fdn_code',
        ]
        read_only_fields = ['id', 'login_id', 'base', 'created_at', 'updated_at']


class StaffSerializer(AddUserIdMixin, serializers.ModelSerializer):
    base = PresentablePrimaryKeyRelatedField(
        queryset=Base.objects.all(), presentation_serializer=BaseSerializer
    )
    base_name = serializers.SerializerMethodField()
    password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = Staff
        fields = [
            'id',
            'company_code',
            'login_id',
            'name',
            'password',
            'base',
            'base_name',
            'mail_address',
            'retired_flg',
            'create_user_id',
            'created_at',
            'update_user_id',
            'updated_at',
            'fdn_code',
        ]
        read_only_fields = [
            'id',
            'create_user_id',
            'created_at',
            'update_user_id',
            'updated_at',
        ]
        validators = [
            UniqueTogetherValidator(
                queryset=Staff.objects.filter(del_flg=False).all(),
                fields=['company_code', 'login_id'],
            ),
            UniqueTogetherValidator(
                queryset=Staff.objects.filter(del_flg=False, fdn_code__isnull=False).all(),
                fields=['company_code', 'fdn_code'],
            ),
        ]

    def get_base_name(self, obj: Staff):
        return obj.base.base_name

    def create(self, validated_data):
        _password: str = validated_data.pop('password')
        instance: Staff = super().create(validated_data)
        instance.set_password(_password)
        instance.save()
        return instance

    def update(self, instance, validated_data):
        update_password = 'password' in validated_data
        _password: str = validated_data.pop('password', None)
        instance = super().update(instance, validated_data)
        if not self.partial or update_password:
            instance.set_password(_password)
            instance.save()
        return instance
