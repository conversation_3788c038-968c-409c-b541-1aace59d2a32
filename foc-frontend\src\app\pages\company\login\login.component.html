<div class="container">
  <div class="inner">
    <div class="login_form shake" [class.shake-stop]="!shaking" [class.shake-start]="shaking" (keyup)="onKeyUp($event)" *ngIf="!is_IE">
      <div class="ui labeled input">
        <div class="ui label">
          <i class="building icon"></i>
          会社コード
        </div>
        <input type="text" #companyCodeEm name="companyCode" id="companyCode" [(ngModel)]="companyCode"/>
      </div>
      <div class="ui labeled input">
        <div class="ui label">
          <i class="portrait icon"></i>
          ログインＩＤ
        </div>
				<input type="text" #loginIdEm name="loginId" id="loginId" [(ngModel)]="loginId"/>
      </div>
      <div class="ui labeled input">
        <div class="ui label">
          <i class="lock icon"></i>
          パスワード
        </div>
				<input type="password" #passwordEm name="password" id="password" [(ngModel)]="password"/>
      </div>
      <div class="ui toggle checkbox">
        <input type="checkbox" [(ngModel)]="save_company_info">
        <label>ログイン情報を保存</label>
      </div>
      <button class="ui button mini" (click)="login()">
        <i class="sign in icon button"></i>
        ログイン
      </button>
    </div>
  </div>
</div>
