import factory
import factory.fuzzy as fuzzy
from django.utils import timezone
from factory.django import DjangoModelFactory

from bases.tests.factories import BaseFactory
from fuho_samples.models import FuhoSample

tz = timezone.get_current_timezone()


class FuhoSampleFactory(DjangoModelFactory):
    class Meta:
        model = FuhoSample

    id = factory.Sequence(lambda n: n)
    company = factory.SubFactory(BaseFactory)
    sentence = factory.Faker('sentence', locale='ja_JP')
    display_num = fuzzy.FuzzyInteger(0, 1000)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0
