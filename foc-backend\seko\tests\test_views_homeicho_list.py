from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from henrei.tests.factories import HenreihinKodenFactory, HenreihinKumotsuFactory
from orders.tests.factories import (
    EntryDetailChobunFactory,
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryDetailMsgFactory,
    EntryFactory,
)
from seko.tests.factories import SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class HomeichoListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        self.entry_1 = EntryFactory(seko=self.seko)
        entry_2 = EntryFactory(seko=self.seko)
        entry_3 = EntryFactory()

        entry_detail_21 = EntryDetailFactory(entry=entry_2)
        entry_detail_22 = EntryDetailFactory(entry=entry_2)
        self.entry_detail_11 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_12 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_13 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_31 = EntryDetailFactory(entry=entry_3)

        self.detail_kumotsu = EntryDetailKumotsuFactory(entry_detail=entry_detail_21)
        HenreihinKumotsuFactory(detail_kumotsu=self.detail_kumotsu)
        self.detail_koden = EntryDetailKodenFactory(entry_detail=entry_detail_22)
        HenreihinKodenFactory(detail_koden=self.detail_koden)
        self.detail_message = EntryDetailMsgFactory(entry_detail=self.entry_detail_11)
        EntryDetailChobunFactory(entry_detail=entry_detail_13)
        self.detail_chobun = EntryDetailChobunFactory(entry_detail=entry_detail_12)
        EntryDetailMsgFactory(entry_detail=entry_detail_31)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_homeicho_list_succeed(self) -> None:
        """芳名帳一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_list', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        record_entry_1 = records[0]
        self.assertEqual(record_entry_1['id'], self.entry_1.id)
        self.assertEqual(record_entry_1['entry_name'], self.entry_1.entry_name)
        self.assertEqual(record_entry_1['entry_prefecture'], self.entry_1.entry_prefecture)
        self.assertEqual(record_entry_1['entry_address_1'], self.entry_1.entry_address_1)
        self.assertEqual(record_entry_1['entry_address_2'], self.entry_1.entry_address_2)
        self.assertEqual(record_entry_1['entry_address_3'], self.entry_1.entry_address_3)
        self.assertEqual(record_entry_1['entry_tel'], self.entry_1.entry_tel)
        self.assertEqual(len(record_entry_1['details']), 3)

        self.assertEqual(
            record_entry_1['details'][0]['item']['name'], self.entry_detail_11.item.name
        )
        self.assertEqual(
            record_entry_1['details'][0]['item']['service']['name'],
            self.entry_detail_11.item.service.name,
        )

        # 追悼メッセージ
        record_detail_message = record_entry_1['details'][0]['message']
        self.assertEqual(record_detail_message['relation_ship'], self.detail_message.relation_ship)
        self.assertEqual(record_detail_message['honbun'], self.detail_message.honbun)

        # 弔文
        record_detail_chobun = record_entry_1['details'][1]['chobun']
        self.assertEqual(
            record_detail_chobun['okurinushi_name'], self.detail_chobun.okurinushi_name
        )

        record_entry_2 = records[1]
        # 供花供物
        self.assertEqual(
            record_entry_2['details'][0]['item_unit_price'],
            self.detail_kumotsu.entry_detail.item_unit_price,
        )
        self.assertEqual(
            record_entry_2['details'][0]['quantity'], self.detail_kumotsu.entry_detail.quantity
        )
        record_detail_kumotsu = record_entry_2['details'][0]['kumotsu']
        self.assertEqual(
            record_detail_kumotsu['okurinushi_name'], self.detail_kumotsu.okurinushi_name
        )
        record_henrei_kumotsu = record_detail_kumotsu['henrei_kumotsu']
        self.assertEqual(
            record_henrei_kumotsu['henreihin_name'],
            self.detail_kumotsu.henrei_kumotsu.henreihin_name,
        )
        self.assertEqual(
            record_henrei_kumotsu['henreihin_hinban'],
            self.detail_kumotsu.henrei_kumotsu.henreihin_hinban,
        )
        self.assertEqual(
            record_henrei_kumotsu['henreihin_price'],
            self.detail_kumotsu.henrei_kumotsu.henreihin_price,
        )

        # 香典
        self.assertEqual(
            record_entry_2['details'][1]['item_unit_price'],
            self.detail_koden.entry_detail.item_unit_price,
        )
        self.assertEqual(
            record_entry_2['details'][1]['quantity'], self.detail_koden.entry_detail.quantity
        )
        record_henrei_koden = record_entry_2['details'][1]['koden']['henrei_koden']
        self.assertEqual(
            record_henrei_koden['henreihin_name'], self.detail_koden.henrei_koden.henreihin_name
        )
        self.assertEqual(
            record_henrei_koden['henreihin_hinban'],
            self.detail_koden.henrei_koden.henreihin_hinban,
        )
        self.assertEqual(
            record_henrei_koden['henreihin_price'], self.detail_koden.henrei_koden.henreihin_price
        )

        # 申込IDの昇順になっているか
        entry_ids = [record['id'] for record in records]
        self.assertEqual(entry_ids, sorted(entry_ids))

    def test_homeicho_list_ignores_canceled_order(self) -> None:
        """キャンセルした申込は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.entry_1.cancel_ts = timezone.now() - timezone.timedelta(30)
        self.entry_1.save()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_list', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_homeicho_list_failed_without_auth(self) -> None:
        """芳名帳一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_list', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
