from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView

from seko import views

app_name = 'seko'
urlpatterns = [
    path('', views.SekoList.as_view(), name='seko_list'),
    path('<int:pk>/', views.SekoDetail.as_view(), name='seko_detail'),
    path('<int:pk>/fuho/', views.SekoFuhoDetail.as_view(), name='seko_fuho_detail'),
    path('<int:seko_id>/kojin/', views.SekoRelatedKojinList.as_view(), name='kojin_list'),
    path(
        '<int:seko_id>/kojin/<int:pk>/',
        views.SekoRelatedKojinDetail.as_view(),
        name='kojin_detail',
    ),
    path('<int:seko_id>/albums/', views.SekoRelatedAlbumList.as_view(), name='album_list'),
    path(
        '<int:seko_id>/albums/<int:pk>/',
        views.SekoRelatedAlbumDetail.as_view(),
        name='album_detail',
    ),
    path(
        '<int:seko_id>/share_images/',
        views.SekoRelatedShareImageList.as_view(),
        name='share_image_list',
    ),
    path(
        '<int:seko_id>/share_images/<int:pk>/',
        views.SekoRelatedShareImageDetail.as_view(),
        name='share_image_detail',
    ),
    path('<int:pk>/pdf/', views.FuhoshiPdf.as_view(), name='fuhoshi'),
    path('<int:pk>/moshu_mail/', views.GuideMailToMoshu.as_view(), name='moshu_mail'),
    path('<int:pk>/approve/', views.SekoApproval.as_view(), name='seko_approve'),
    path('<int:pk>/homeicho/', views.HomeichoList.as_view(), name='homeicho_list'),
    path(
        '<int:pk>/homeicho/chobun_pdf/',
        views.HomeichoChobunPDF.as_view(),
        name='homeicho_chobun_pdf',
    ),
    path(
        '<int:pk>/homeicho/kumotsu_pdf/',
        views.HomeichoKumotsuPDF.as_view(),
        name='homeicho_kumotsu_pdf',
    ),
    path(
        '<int:pk>/homeicho/koden_pdf/', views.HomeichoKodenPDF.as_view(), name='homeicho_koden_pdf'
    ),
    path('<int:pk>/homeicho/msg_pdf/', views.HomeichoMsgPDF.as_view(), name='homeicho_msg_pdf'),
    path('<int:pk>/msg_pdf/', views.MsgPDF.as_view(), name='msg_pdf'),
    path('login/', views.MoshuTokenObtainPair.as_view(), name='moshu_token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('moshu/', views.MoshuList.as_view(), name='moshu_list'),
    path('moshu/<int:pk>/', views.MoshuDetail.as_view(), name='moshu_detail'),
    path('soke/<int:pk>/approve/', views.MoshuApproval.as_view(), name='soke_approve'),
    path('<int:seko_id>/inquiries/', views.SekoInquiryList.as_view(), name='inquiry_list'),
    path(
        'inquiries/<int:inquiry_id>/answers/', views.SekoAnswerList.as_view(), name='answer_list'
    ),
    path('fdn/', views.FdnSekoCreater.as_view(), name='fdn_seko_create'),
    path('fdn/<int:pk>/', views.FdnSekoUpdate.as_view(), name='fdn_seko_update'),
    path(
        '<int:pk>/notice_attached_file/',
        views.NoticeAttachedFile.as_view(),
        name='notice_attached_file',
    ),
    path(
        '<int:pk>/notice_image_file/soke/',
        views.NoticeImageFileToSoke.as_view(),
        name='notice_image_file_to_soke',
    ),
    path(
        '<int:pk>/notice_image_file/company/',
        views.NoticeImageFileToCompany.as_view(),
        name='notice_image_file_to_company',
    ),
    path('<int:pk>/qrcode/', views.QRCodePdf.as_view(), name='qrcode'),
    path('list/', views.NowlSekoList.as_view(), name='nowl_seko_list'),
]
