# Generated by Django 3.1.2 on 2020-11-11 07:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0001_initial'),
        ('items', '0003_auto_20201104_1540'),
        ('suppliers', '0001_initial'),
        ('henrei', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='henreihinkoden',
            name='order',
        ),
        migrations.AddField(
            model_name='henreihinkoden',
            name='henrei_order',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='henrei_koden',
                to='henrei.orderhenreihin',
                verbose_name='henrei order',
            ),
        ),
        migrations.AlterField(
            model_name='henreihinkoden',
            name='detail_koden',
            field=models.OneToOneField(
                db_column='id',
                on_delete=django.db.models.deletion.CASCADE,
                primary_key=True,
                related_name='henrei_koden',
                serialize=False,
                to='orders.entrydetailkoden',
                verbose_name='entry detail koden',
            ),
        ),
        migrations.CreateModel(
            name='HenreihinKumotsu',
            fields=[
                (
                    'detail_kumotsu',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='henrei_kumotsu',
                        serialize=False,
                        to='orders.entrydetailkumotsu',
                        verbose_name='entry detail kumotsu',
                    ),
                ),
                ('henreihin_hinban', models.TextField(verbose_name='item hinban')),
                ('henreihin_name', models.TextField(verbose_name='item name')),
                ('henreihin_price', models.IntegerField(verbose_name='item price')),
                ('henreihin_tax', models.IntegerField(verbose_name='item tax')),
                ('henreihin_tax_pct', models.IntegerField(verbose_name='item tax percentage')),
                ('keigen_flg', models.BooleanField(verbose_name='tax reduced')),
                ('select_status', models.IntegerField(verbose_name='select status')),
                ('order_status', models.IntegerField(default=0, verbose_name='order status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'henrei_order',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='henrei_kumotsu',
                        to='henrei.orderhenreihin',
                        verbose_name='henrei order',
                    ),
                ),
                (
                    'henreihin',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='items.item',
                        verbose_name='henrei item',
                    ),
                ),
                (
                    'supplier',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='suppliers.supplier',
                        verbose_name='supplier',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_henreihin_kumotsu',
            },
        ),
    ]
