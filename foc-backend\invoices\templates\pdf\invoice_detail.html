{% load humanize %}
{% load invoice_filters %}
{% load fuhoshi_filters %}
<!DOCTYPE html>
<html>
<title>請求書</title>
<head>
<style type="text/css">
body {
  font-family: "Noto Serif CJK JP", serif;
  font-size: 12px;
  color: #333;
  margin: 0;
  line-height: 1.6;
}
@page {
  @top-right {
    font-size: 12px;
    content: counter(page) "/" counter(pages);
  }
}
@media print {
  .break-after {
    page-break-after: always;
  }
  .detail .page-header {
    position: fixed;
    right: 5px;
    top: 2px;
    font-size: 10px;
  }
}
@media screen {
  .detail .page-header {
    position: absolute;
    right: 5px;
    top: 2px;
    font-size: 10px;
  }
}
.page-header-space {
  height: 40px;
}
.detail {
  position: relative;
}
.detail .list {
  width: 98%;
  margin: 0px 5px 0;
  border-spacing: 0;
}
.detail .list tr td {
  padding: 2px 3px;
}
.detail .list tr.header td {
  border-top: solid 1px #000000;
  border-left: dotted 1px #000000;
  border-bottom: solid 1px #000000;
}
.detail .list tr.header td:first-child {
  border-left-style: solid;
}
.detail .list tr.header td:last-child {
  border-right: solid 1px #000000;
}
.detail .list tr.header {
  text-align: center;
  background-color: #eeeeee;
}
.detail .list tr.data td {
  border-left: dotted 1px #000000;
  border-bottom: dotted 1px #000000;
}
.detail .list tr.data td:first-child {
  border-left-style: solid;
}
.detail .list tr.data td:last-child {
  border-right: solid 1px #000000;
}
.detail .list tfoot tr td {
  border-left: solid 1px #000000;
  border-right: solid 1px #000000;
  border-bottom: solid 1px #000000;
  height: 20px;
}
.detail .list tr.data {
  text-align: left;
}
.detail .list .number {
  width: 10%;
}
.detail .list .quantity {
  width: 6%;
}
.detail .list .entry_name {
  width: 15%;
}
.detail .list .price {
  width: 10%;
}
.detail .list .amount {
  width: 10%;
}
.detail .list .mark {
  width: 15px;
}
.detail .list .center {
  text-align: center;
}
.detail .list .right {
  text-align: right;
}
.list .small-indent {
  padding-left: 15px !important;
}
.list .indent {
  padding-left: 20px !important;
}
</style>
</head>
<body>
<section class="content" style="clear: both">
  <div class="detail">
    <div class="page-header">
      <div>No. {{ sales.company_id }}-{{ sales.id }}</div>
      <div>発行日 {{ sales.invoice_date|date:"Y年n月j日" }}</div>
    </div>
    <table class="list">
      <thead>
        <tr>
          <td>
            <div class="page-header-space"></div>
          </td>
        </tr>
        <tr class="header">
          <td class="note">詳細</td>
          <td class="number">注文番号</td>
          <td class="entry_name">申込者</td>
          <td class="quantity">数量</td>
          <td class="price">販売単価<br>（税抜）</td>
          <td class="amount">販売金額<br>（税抜）</td>
          <td class="amount">販売金額<br>（税込）</td>
          <td class="mark"></td>
        </tr>
      </thead>
      <tbody>
        {% for department in sales.sales_seko_departments.all %}
        <tr class="data">
          <td colspan="8">【{{department.base_name}}】</td>
        </tr>
        {% for detail in department.sales_details.all %}
        {% if department.sales_details.all|is_first_of_seko:forloop.counter %}
        <tr class="data">
          <td colspan="8">施行日{{detail.seko_date|date:'n/j'}}【{{detail.soke_name}}】（故人）{{detail.kojin_name}}（喪主）{{detail.moshu_name}}　葬儀番号：{{detail.seko_id}}</td>
        </tr>
        {% endif %}
        {% if department.sales_details.all|is_first_of_service:forloop.counter %}
        <tr class="data">
          <td colspan="8">【{{detail.service_name}}】</td>
        </tr>
        {% endif %}
        <tr class="data">
        {% if detail.service_id == 30 and not detail.item_name %}
          <td class="small-indent">{{ detail.service_name|default_if_none:'' }}</td>
        {% else %}
          <td class="small-indent">{{ detail.item_name|default_if_none:'' }}</td>
        {% endif %}
          <td class="center">{{ detail.entry_id|default_if_none:'' }}</td>
          <td>{{ detail.entry_name|default_if_none:'' }}</td>
          <td class="center">{{ detail.quantity|intcomma }}</td>
          {% if detail.item_name != "香典システム利用料" %}
          <td class="right">{{ detail.item_price|divide:detail.quantity|intcomma }}</td>
          <td class="right">{{ detail.item_price|default_if_none:0|intcomma }}</td>
          {% else %}
          <td class="right"></td>
          <td class="right"></td>
          {% endif %}
          <td class="right">{{ detail.item_taxed_price|multiple:detail.quantity|intcomma }}</td>
          <td class="center">{% if detail.keigen_flg %}*{% endif %}</td>
        </tr>
        {% endfor %}
        <tr class="data">
          <td colspan="8">【{{department.base_name}} ホール合計】</td>
        </tr>
        {% if department.chobun_qty %}
        <tr class="data">
          <td class="indent">【弔文】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.chobun_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ department.chobun_order_price|intcomma }}</td>
          <td class="right">{{ department.chobun_order_price|add:department.chobun_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if department.kumotsu_qty %}
        <tr class="data">
          <td class="indent">【供花・供物】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.kumotsu_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ department.kumotsu_order_price|intcomma }}</td>
          <td class="right">{{ department.kumotsu_order_price|add:department.kumotsu_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if department.koden_qty %}
        <tr class="data">
          <td class="indent">【香典】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.koden_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ department.koden_order_price|intcomma }}</td>
          <td class="right">{{ department.koden_order_price|add:department.koden_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if department.koden_order_fee %}
        <tr class="data">
          <td class="indent">【香典システム利用料】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.koden_qty|intcomma }}</td>
          <td></td>
          <td class="right"></td>
          <td class="right">{{ department.koden_order_fee|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if department.henreihin_qty %}
        <tr class="data">
          <td class="indent">【返礼品】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.henreihin_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ department.henreihin_order_price|intcomma }}</td>
          <td class="right">{{ department.henreihin_order_price|add:department.henreihin_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% endfor %}

        <tr class="data">
          <td colspan="8">【販売総合計】</td>
        </tr>
        {% if sales.chobun_qty %}
        <tr class="data">
          <td class="indent">【弔文】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.chobun_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ sales.chobun_order_price|intcomma }}</td>
          <td class="right">{{ sales.chobun_order_price|add:sales.chobun_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if sales.kumotsu_qty %}
        <tr class="data">
          <td class="indent">【供花・供物】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.kumotsu_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ sales.kumotsu_order_price|intcomma }}</td>
          <td class="right">{{ sales.kumotsu_order_price|add:sales.kumotsu_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if sales.koden_qty %}
        <tr class="data">
          <td class="indent">【香典】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.koden_qty|intcomma }}</td>
          <td></td>
          <td class="right"></td>
          <td class="right">{{ sales.koden_order_price|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if sales.koden_order_fee %}
        <tr class="data">
          <td class="indent">【香典システム利用料】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.koden_qty|intcomma }}</td>
          <td></td>
          <td class="right"></td>
          <td class="right">{{ sales.koden_order_fee|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if sales.henreihin_qty %}
        <tr class="data">
          <td class="indent">【返礼品】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.henreihin_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ sales.henreihin_order_price|intcomma }}</td>
          <td class="right">{{ sales.henreihin_order_price|add:sales.henreihin_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
      </tbody>
      <tfoot>
        <tr>
          <td colspan="8">
            <div></div>
          </td>
        </tr>
      </tfoot>
    </table>
  </div>
</section>
</body>
</html>
