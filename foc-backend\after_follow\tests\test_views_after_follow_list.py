from typing import Dict, List

from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.models import AfterFollow
from after_follow.tests.factories import AfGroupFactory, AfterFollowFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class AfterFollowListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        af_group_1 = AfGroupFactory()
        self.after_follow_3 = AfterFollowFactory(af_group=AfGroupFactory(), display_num=2)
        self.after_follow_1 = AfterFollowFactory(af_group=af_group_1, display_num=3)
        self.after_follow_2 = AfterFollowFactory(af_group=af_group_1, display_num=1)
        self.after_follow_4 = AfterFollowFactory(af_group=af_group_1, display_num=1)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_after_follow_list_succeed(self) -> None:
        """AF項目一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:after_follow_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)
        # 順番確認 (2 -> 4 -> 1 -> 3)
        self.assertEqual(records[0]['id'], self.after_follow_2.pk)
        self.assertEqual(records[1]['id'], self.after_follow_4.pk)
        self.assertEqual(records[2]['id'], self.after_follow_1.pk)
        self.assertEqual(records[3]['id'], self.after_follow_3.pk)

    def test_after_follow_list_succeed_by_af_group(self) -> None:
        """AF項目グループで絞り込みしたAF項目一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'af_group': self.after_follow_1.af_group.pk,
        }
        response = self.api_client.get(
            reverse('after_follow:after_follow_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_after_follow_list_succeed_by_af_group_2(self) -> None:
        """AF項目グループで絞り込みしたAF項目一覧を返す(項目確認)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'af_group': self.after_follow_3.af_group.pk,
        }
        response = self.api_client.get(
            reverse('after_follow:after_follow_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)
        record = records[0]
        self.assertEqual(record['id'], self.after_follow_3.pk)
        self.assertEqual(record['af_group']['id'], self.after_follow_3.af_group.pk)
        self.assertEqual(record['af_group']['name'], self.after_follow_3.af_group.name)
        self.assertEqual(
            record['af_group']['display_num'], self.after_follow_3.af_group.display_num
        )
        self.assertEqual(record['name'], self.after_follow_3.name)
        self.assertEqual(record['display_num'], self.after_follow_3.display_num)
        self.assertEqual(record['del_flg'], self.after_follow_3.del_flg)
        self.assertEqual(
            record['created_at'], self.after_follow_3.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.after_follow_3.create_user_id)
        self.assertEqual(
            record['updated_at'], self.after_follow_3.updated_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['update_user_id'], self.after_follow_3.update_user_id)

    def test_after_follow_list_ignores_deleted(self) -> None:
        """AF項目一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.after_follow_1.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:after_follow_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_after_follow_list_failed_without_auth(self) -> None:
        """AF項目一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:after_follow_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class AfterFollowCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.after_follow_data: AfterFollow = AfterFollowFactory.build(af_group=AfGroupFactory())

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'af_group': self.after_follow_data.af_group.pk,
            'name': self.after_follow_data.name,
            'display_num': self.after_follow_data.display_num,
        }

    def test_after_follow_create_succeed(self) -> None:
        """AF項目を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('after_follow:after_follow_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['id'])
        self.assertEqual(record['af_group']['id'], self.after_follow_data.af_group.pk)
        self.assertEqual(record['af_group']['name'], self.after_follow_data.af_group.name)
        self.assertEqual(
            record['af_group']['display_num'], self.after_follow_data.af_group.display_num
        )
        self.assertEqual(record['name'], self.after_follow_data.name)
        self.assertEqual(record['display_num'], self.after_follow_data.display_num)
        self.assertEqual(record['del_flg'], self.after_follow_data.del_flg)
        self.assertIsNotNone(record['created_at'])
        self.assertEqual(record['create_user_id'], self.staff.pk)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_after_follow_create_failed_without_auth(self) -> None:
        """AF項目追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = model_to_dict(self.after_follow_data)
        response = self.api_client.post(
            reverse('after_follow:after_follow_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
