from rest_framework import serializers

from suppliers.models import Supplier
from utils.serializer_mixins import AddUserIdMixin


class SupplierSerializer(AddUserIdMixin, serializers.ModelSerializer):
    class Meta:
        model = Supplier
        fields = '__all__'
        read_only_fields = [
            'del_flg',
            'create_user_id',
            'created_at',
            'update_user_id',
            'updated_at',
        ]
