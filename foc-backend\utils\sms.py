from dataclasses import dataclass
from typing import Dict

import requests
from django.conf import settings
from django.db.models import Q

from bases.models import SmsAccount
from seko.models import Mo<PERSON>, Seko, SekoContact


@dataclass
class SmsRequestData:
    token: str
    client_id: str
    sms_code: str
    message: str
    phone_number: str

    def as_post_data(self) -> Dict:
        return {
            'token': self.token,
            'clientId': self.client_id,
            'smsCode': self.sms_code,
            'message': self.message,
            'phoneNumber': self.phone_number,
        }


@dataclass
class SmsResponseData:
    response_code: int
    response_message: str
    phone_number: str
    sms_message: str


def register_send_sms(token: str, client_id: str, sms_code: str, message: str, phone_number: str):
    post_data = SmsRequestData(
        token=token,
        client_id=client_id,
        sms_code=sms_code,
        message=message,
        phone_number=phone_number,
    )
    response = requests.post(settings.SMS_REQUEST_URL, post_data.as_post_data())
    record = response.json()
    return SmsResponseData(
        response_code=record['responseCode'],
        response_message=record['responseMessage'],
        phone_number=record['phoneNumber'],
        sms_message=record['smsMessage'],
    )


def compile_message(message_template: str, seko: Seko) -> str:
    moshu: Moshu = seko.moshu
    kojin_name_1: str = ''
    kojin_name_2: str = ''
    for kojin in seko.kojin.filter(Q(del_flg=False)):
        if kojin.kojin_num == 1:
            kojin_name_1 = kojin.name
        elif kojin.kojin_num == 2:
            kojin_name_2 = kojin.name
    if hasattr(seko, 'seko_contact') and seko.seko_contact.name:
        contact_name = seko.seko_contact.name
    else:
        contact_name = moshu.name
    return (
        message_template.replace('@m', contact_name)
        .replace('@k1', kojin_name_1)
        .replace('@k2', kojin_name_2)[:600]
    )


def send_seko_template_sms(seko: Seko, message_template: str) -> bool:
    sms_account: SmsAccount = seko.seko_company.sms_account
    seko_contact: SekoContact = seko.seko_contact

    result = register_send_sms(
        token=sms_account.token,
        client_id=sms_account.client_id,
        sms_code=sms_account.sms_code,
        message=compile_message(message_template=message_template, seko=seko),
        phone_number=seko_contact.mobile_num_digits(),
    )
    return result.response_code == 0
