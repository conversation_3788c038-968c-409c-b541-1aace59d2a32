
@import "src/assets/scss/company/setting";

.contents {
  min-width: 1380px!important;
  .ui.checkbox {
    padding: 8px 10px 0;
    label {
      cursor: pointer;
    }
  }
  tr {
    line-height: 1;
    td {
      cursor: default;
      >div {
        min-height: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:not(:last-child) {
          min-height: 19px;
          border-bottom: dotted 1px $background-light1;
          margin: 0 -10px 5px;
          padding-left: 10px;
          padding-bottom: 5px;
        }
      }
    }
    .entry_id {
      width: 6%;
    }
    .entry_name {
      width: 7%;
    }
    .okurinushi_name {
      width: 6%;
    }
    .renmei {
      width: 6%;
    }
    .seko_date {
      width: 10%;
    }
    .soke_name {
      width: 5%;
    }
    .kojin_name {
      width: 6%;
    }
    .item_name {
      width: 13%;
    }
    .item_price {
      width: 5%;
    }
    .entry_ts {
      width: 105px;
    }
    .printed_flg {
      width: 50px;
    }
    .is_cancel {
      width: 100px;
    }
    .operation {
      width: 80px;
      &.admin {
        width: 100px;
      }
      i {
        cursor: pointer;
      }
    }
  }
  #receipt-url.ui.modal {
    .header, .content {
      padding: 10px;
      .input_area .ui.input {
        max-width: 100%;
        >input {
          font-size: 1.2em;
        }
      }
    }
  }
  #chobun-edit.ui.modal .input_area {
    label {
      min-width: 120px;
    }
    textarea {
      width: 300px;
      padding-right: 10px;
    }
  }
}
