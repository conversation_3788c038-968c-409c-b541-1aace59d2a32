@import "src/assets/scss/company/setting";
.contents {
  min-width: 1305px!important;
  max-width: 1380px;
  .seko-contact {
    margin: 10px 0;
    padding: 5px;
    .title {
      margin-bottom: 5px;
      font-weight: bold;
      width: 205px;
      height: 33px;
    }
  }
  .ui.segment.schedule .ui.table {
    margin-top: 0;
    th, td {
      padding: 0;
      .ui.input {
        min-width: 0;
      }
      &.schedule_name {
        width: 6%;
      }
      &.display_name {
        width: 7%;
      }
      &.place {
        width: 15%;
      }
      &.phone {
        width: 9%;
      }
      &.zip_code {
        width: 8%;
      }
      &.date {
        width: 8%;
      }
      &.start_time {
        width: 5%;
      }
      &.end_time {
        width: 5%;
      }
      &.function {
        width: 60px;
      }
      &.button i.angle {
        cursor: pointer;
        &:hover {
          opacity: .8;
        }
      }
    }
    td.required_right {
      border-right: solid 2px $label-border-required;
    }
    td.required_left {
      border-left-color: $label-border-required;
    }
    td.disabled {
      background-color: #eeeeee;
      input, com-dropdown, com-calendar, com-time-input {
        background: inherit;
      }
    }
  }

  .ui.segment.invoice .input_area .line {
    position: relative;
    a {
      position: absolute;
      top: 6px;
      left: 50px;
      z-index: 99999;
    }
    .clear {
      position: absolute;
      top: 6px;
      right: 42px;
      cursor: pointer;
      opacity: .5;
      &:hover {
        opacity: 1;
      }
    }
    label.wide {
      min-width: 140px;
    }
  }
  .ui.segment.video .ui.table {
    margin-top: 0;
    tr {
      cursor: pointer;
      height: 40px;
    }
    tr.even_row {
      background: $table-row-dark;
    }
    .title {
      width: 15%;
    }
    .type {
      width: 10%;
    }
    .live_begin_ts {
      width: 12%;
    }
    .live_end_ts {
      width: 12%;
    }
    .delivery_end_ts {
      width: 12%;
    }
    .function {
      width: 50px;
    }
    td.button {
      i:hover {
        opacity: 0.5;
      }
    }
  }

  .ui.segment.album {
    .item-upload {
      width: 100px;
      height: 100px;
      background: $background-light1;
      margin: 5px;
      cursor: pointer;
      display: flex;
      >i {
        margin: auto;
        opacity: .5;
      }
      &:hover {
        opacity: .8;
      }
    }
    .desc {
      position: absolute;
      width: 100%;
      height: 100%;
      opacity: 0.5;
      display: flex;
      span {
        margin: auto;
      }
    }
    .message {
      width: 100%;
      height: 20px;
      opacity: 0.8;
      text-align: center;
    }

  }

  .ui.segment.share {
    .item-upload {
      width: 200px;
      height: 200px;
      background: $background-light1;
      margin: 5px;
      cursor: pointer;
      display: flex;
      >i {
        margin: auto;
        opacity: .5;
      }
      &:hover {
        opacity: .8;
      }
    }
    .message {
      margin-top: -10px;
    }
    .canvas{
      height: 50%;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      
      .image-box {
        width: 200px;
        height: 200px;
        background: #fff;
        border-radius: 5px;
        margin: 5px;
        display: flex;
        cursor: pointer;
        &:hover {
          box-shadow: 0 5px 20px rgba(0,0,0,.3);
          --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
          -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
        }
        img {
          height: auto;
          width: auto;
          max-height: 100%;
          max-width: 100%;
          margin: auto;;
          border-radius: 5px;
          min-width: 1px;
        }
        >.clear {
          position: absolute;
          padding: 2px;
          top: 5px;
          width: 30px;
          opacity: 0.5;
          right: 0;
          z-index: -1;
          >i {
            float: right;
            cursor: pointer;
            opacity: .5;
            margin-top: 3px;
            color: red;
            &:hover {
              opacity: 1;
            }
          }
        }
        &:hover >.clear {
          z-index: 1;
          -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
        }
      }
      .item-box {
        width: 200px;
        height: 200px;
        margin: 10px;
        text-align: center;
        cursor: pointer;
        &:hover {
          opacity: .8;
        }
        .image-box {
          width: 200px;
          height: 200px;
          background-color: #fff;
          display: flex;
          justify-content: center;
          img {
            height: auto;
            width: auto;
            max-height: 100%;
            max-width: 100%;
            margin: auto;
            min-width: 1px;
          }
        }
      }

    }
  }
  .ui.segment.kumotsu, .ui.segment.henreihin {
    .top_button_area {
      margin-top: 0px!important;
      float: left;
      .title {
        font-size: 1.1em;
        font-weight: bold;
        margin-top: 6px;
      }
    }
     .canvas{
      height: 100%;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .item-box {
        width: 200px;
        height: 300px;
        margin: 10px;
        text-align: center;
        cursor: pointer;
        &:hover {
          opacity: .8;
        }
        .image-box {
          width: 200px;
          height: 200px;
          background-color: #fff;
          display: flex;
          justify-content: center;
          img {
            height: auto;
            width: auto;
            max-height: 100%;
            max-width: 100%;
            margin: auto;
            min-width: 1px;
          }
          .no-image {
            margin-top: 30px;
            opacity: .5;
          }
        }
        >.checkbox {
          background-color: #fff;
          border-top: solid 1px $background-light1;
          &.kumotsu {
            display: flex;
            padding: 0px 25px;
          }
          >.ui.checkbox {
            margin-top: 7px;
          }
          .title {
            margin: 5px;
          }
        }
        .name {
          margin-top: 5px;
          font-size: 1.1em;
          font-weight: bold;
        }
        .hinban {
          font-size: 0.9em;

        }
      }

    }
  }
  .ui.segment.homeicho {
    .top_button_area {
      margin-top: 10px!important;
    }
    .search_area .line {
      border: none!important;
      padding-bottom: 10px;
    }
    .table_fixed.body {
      max-height: calc(100vh - 350px);
    }
    tr {
      td {
        cursor: default;
      }
      .entry_id {
        width: 10%;
      }
      .entry_name {
        width: 20%;
      }
      .entry_ts {
        width: 15%;
      }
      .okurinushi_name {
        width: 20%;
      }
      .service_name {
        width: 15%;
      }
    }
  }
  .ui.segment.hoyo {
    padding-left: 200px;
    padding-right: 200px;
    .table_fixed.body {
      max-height: calc(100vh - 320px);
    }
    tr {
      .hoyo_planned_date {
        width: 20%;
      }
      .hoyo_activity_date {
        width: 20%;
      }
      .operation {
        width: 100px;
      }
      td a {
        color: inherit;
      }
    }
  }
  .ui.segment.after-follow {
    .top_button_area {
      margin-top: 40px!important;
    }
    .table_fixed.body {
      max-height: calc(100vh - 320px);
    }
    tr {
      .af_name {
        width: 25%;
      }
      .proposal_status {
        width: 100px;
      }
      .order_status {
        width: 100px;
      }
      .order_chance {
        width: 100px;
      }
      .order_date {
        width: 120px;
      }
      .answered_flg {
        width: 100px;
        .ui.checkbox {
          width: 17px;
        }
      }
    }
  }
  .ui.segment.hoyo-schedule {
    margin-top: 0;
    width: 90%;
    border-radius: 0;
    box-shadow: none;
    .ui.table {
      margin-top: 0;
      th, td {
        padding: 0;
        .ui.input {
          min-width: 0;
        }
        &.place {
          width: 35%;
        }
        &.start_time {
          width: 15%;
        }
        &.end_time {
          width: 15%;
        }
        &.function {
          width: 60px;
        }
        &.button i {
          cursor: pointer;
          &:hover {
            opacity: .8;
          }
        }
      }
      td.disabled {
        background-color: #eeeeee;
        input, com-dropdown, com-calendar, com-time-input {
          background: inherit;
        }
      }
    }
  }
  .ui.toggle.checkbox {
    margin-top: 5px;
    margin-right: 20px;
  }
  .henreihin_select {
    display: none;
    &.show {
      display: block;
    }
  }
}

.album-item-box {
  width: 200px;
  height: 200px;
  background: #fff;
  border-radius: 5px;
  margin: 5px;
  display: flex;
  cursor: pointer;
  &:hover {
    box-shadow: 0 5px 20px rgba(0,0,0,.3);
    --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
  }
  img {
    height: auto;
    width: auto;
    max-height: 100%;
    max-width: 100%;
    margin: auto;;
    border-radius: 5px;
    min-width: 1px;
  }
  >.clear {
    position: absolute;
    padding: 2px;
    top: 5px;
    width: 30px;
    opacity: 0.5;
    right: 0;
    z-index: -1;
    >i {
      float: right;
      cursor: pointer;
      opacity: .5;
      margin-top: 3px;
      color: red;
      &:hover {
        opacity: 1;
      }
    }
  }
  &:hover >.clear {
    z-index: 1;
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
  }
}
.cdk-drop-list-dragging {
  cursor: move;
}
.drag-placeholder {
  border: dotted 2px red;
  height: 200px;
  width: 195px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
.ui.modal {
  &.kojin .content {
    min-height: 450px;
  }
  &.pattern .ui.segment {
    cursor: pointer;
    font-size: 1.2em;
    &:hover {
      box-shadow: 0 5px 20px rgba(0,0,0,.3);
      --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
      -webkit-transform: translateY(-2px);
      transform: translateY(-2px);
    }
    pre {
      white-space: pre-wrap;
    }
  }
  &.album .content {
    padding: 10px;
    .ui.segment {
      padding: 0;
      img {
        width: 100%;
        border-radius: 5px;
      }
    }
  }
  &.video {
    .ui.radio.checkbox {
      padding-top: 7px;
      padding-bottom: 7px;
    }
  }
  &.hoyo .content .line {
    .data.name {
      min-width: 150px;
      max-width: 400px;
    }
    com-time-input {
      min-width: 70px;
    }
    .plane.blank {
      padding: 0;
      border-right-width: 1px;
    }
    .checkbox.dine {
      margin-left: 10px;
    }

  }
  &.af-wish, &.af-activity {
    .content .table_fixed.body {
      max-height: calc(100vh - 35rem);
    }
    .content .input_area .line {
      .checkbox {
        padding: 8px 20px 8px 8px;
        background: white;
      }
      com-dropdown.tiny {
        width: 70px;
      }
      com-dropdown.mini {
        width: 90px;
        min-width: 90px;
      }
    }
    .title {
      font-size: smaller;
      margin-left: 20px;
      border: none;
      padding-bottom: 0;
      .data {
        margin-left: 10px;
        font-weight: normal;
      }
    }
    .sub_title {
      font-size: larger;
      font-weight: bold;
      margin-top: 20px;
    }
    .activity {
      white-space: pre-wrap;
    }
    .activity_ts {
      width: 150px;
    }
  }
}
:host ::ng-deep .ui.segment.service {
  display: flex;
  min-height: 60px;
  .ui.checkbox {
    margin: auto 20px;
  }
  .rate {
    margin: auto 5px;
  }
  com-dropdown {
    min-width: 50px;
  }

}
label.invoice-file {
  font: inherit;
  font-size: 1em;
  margin-top: 10px;
}
.ui.attached.segment {
  min-height: calc(100% - 100px);
  margin-bottom: 10px;
}
.ui.tabular.menu .item {
  padding: 0 25px;
}