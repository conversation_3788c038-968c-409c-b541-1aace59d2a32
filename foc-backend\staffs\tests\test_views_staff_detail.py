from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory
from staffs.models import Staff
from staffs.tests.factories import DEFAULT_PASSWORD, StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class StaffDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff: Staff = StaffFactory()

        self.api_client = APIClient()
        self.operator = StaffFactory()
        refresh = RefreshToken.for_user(self.operator)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_staff_detail_succeed(self) -> None:
        """担当者詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('company_code'), self.staff.company_code)
        self.assertEqual(record.get('login_id'), self.staff.login_id)
        self.assertEqual(record.get('name'), self.staff.name)
        base_rec: Dict = record.get('base', {})
        self.assertEqual(base_rec.get('id'), self.staff.base.pk)
        self.assertEqual(base_rec.get('base_type'), self.staff.base.base_type)
        self.assertEqual(base_rec.get('company_code'), self.staff.base.company_code)
        self.assertEqual(record.get('base_name'), self.staff.base.base_name)
        self.assertEqual(record.get('mail_address'), self.staff.mail_address)
        self.assertEqual(record.get('retired_flg'), self.staff.retired_flg)
        self.assertEqual(record.get('create_user_id'), self.staff.create_user_id)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.update_user_id)
        self.assertIsNotNone(record.get('updated_at'))

    def test_staff_detail_failed_by_notfound(self) -> None:
        """担当者詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_staff: Staff = StaffFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:staff_detail', kwargs={'pk': non_saved_staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_staff_detail_failed_with_notfound(self) -> None:
        """担当者詳細APIは無効になった担当者を返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.staff.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_staff_detail_succeed_with_retired_staff(self) -> None:
        """担当者詳細APIは退職した担当者は除外しない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.staff.retired_flg = True
        self.staff.save()

        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_staff_detail_failed_without_auth(self) -> None:
        """担当者詳細APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class StaffUpdateViewTest(TestCase):
    NEW_PASSWORD = 'the-new-password'

    def setUp(self):
        super().setUp()

        self.staff = StaffFactory()
        new_base: Base = BaseFactory()
        self.staff_data = StaffFactory.build(base=new_base)

        self.api_client = APIClient()
        self.operator = StaffFactory()
        refresh = RefreshToken.for_user(self.operator)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'base': self.staff_data.base.pk,
            'company_code': self.staff_data.company_code,
            'name': self.staff_data.name,
            'login_id': self.staff_data.login_id,
            'password': self.NEW_PASSWORD,
            'mail_address': self.staff_data.mail_address,
            'retired_flg': self.staff_data.retired_flg,
        }

    def test_staff_update_succeed(self) -> None:
        """担当者を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('company_code'), self.staff_data.base.company_code)
        self.assertEqual(record.get('login_id'), self.staff_data.login_id)
        self.assertEqual(record.get('name'), self.staff_data.name)
        base_rec: Dict = record.get('base', {})
        self.assertEqual(base_rec.get('id'), self.staff_data.base.pk)
        self.assertEqual(base_rec.get('base_type'), self.staff_data.base.base_type)
        self.assertEqual(base_rec.get('company_code'), self.staff_data.base.company_code)
        self.assertEqual(record.get('base_name'), self.staff_data.base.base_name)
        self.assertEqual(record.get('mail_address'), self.staff_data.mail_address)
        self.assertEqual(record.get('retired_flg'), self.staff_data.retired_flg)
        self.assertEqual(record.get('create_user_id'), self.staff.create_user_id)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('update_user_id'), self.operator.pk)
        self.assertIsNotNone(record.get('updated_at'))

        staff_rec: Staff = Staff.objects.get(pk=record['id'])
        self.assertTrue(staff_rec.check_password(self.NEW_PASSWORD))

    def test_staff_update_failed_without_auth(self) -> None:
        """担当者更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

        response = self.api_client.patch(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_staff_update_failed_by_notfound(self) -> None:
        """担当者更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_staff: Staff = StaffFactory.build()
        params: Dict = {}
        response = self.api_client.put(
            reverse('staffs:staff_detail', kwargs={'pk': non_saved_staff.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        response = self.api_client.patch(
            reverse('staffs:staff_detail', kwargs={'pk': non_saved_staff.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_staff_update_failed_by_already_deleted(self) -> None:
        """担当者更新APIが論理削除済みの担当者を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.staff.disable()

        params: Dict = {}
        response = self.api_client.put(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        response = self.api_client.patch(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_staff_update_success_with_retired_staff(self) -> None:
        """担当者更新APIは退職済みの担当者を更新できる"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.staff.retired_flg = True
        self.staff.save()

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response = self.api_client.patch(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_staff_update_success_without_password(self) -> None:
        """担当者更新APIはパスワードの項目がなくても更新できる"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        del params['password']

        # PATCHではpasswordは更新しない
        response = self.api_client.patch(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.staff.refresh_from_db()
        self.assertTrue(self.staff.check_password(DEFAULT_PASSWORD))

        # PUTではエラーにはならないがpasswordは更新される
        response = self.api_client.put(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.staff.refresh_from_db()
        self.assertFalse(self.staff.check_password(DEFAULT_PASSWORD))

    def test_staff_update_fail_by_duplicate(self) -> None:
        """担当者更新APIがcompany_codeとlogin_idの組み合わせ重複で失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 別の拠点に所属しているlogin_idが同じ担当者が存在する
        another_staff: Staff = StaffFactory(login_id=self.staff_data.login_id)
        params: Dict = self.basic_params()
        params['base'] = another_staff.base.pk
        params['company_code'] = another_staff.base.company_code
        response = self.api_client.put(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['non_field_errors'])


class StaffDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff = StaffFactory()

        self.api_client = APIClient()
        self.operator = StaffFactory()
        refresh = RefreshToken.for_user(self.operator)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_staff_delete_succeed(self) -> None:
        """担当者を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 担当者のdel_flgがTrueになる
        db_staff: Staff = Staff.objects.get(pk=self.staff.pk)
        self.assertTrue(db_staff.del_flg)
        self.assertEqual(db_staff.create_user_id, self.staff.create_user_id)
        self.assertEqual(db_staff.update_user_id, self.operator.id)

    def test_staff_delete_failed_by_notfound(self) -> None:
        """担当者削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_staff: Staff = StaffFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('staffs:staff_detail', kwargs={'pk': non_saved_staff.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_staff_delete_failed_by_already_deleted(self) -> None:
        """担当者削除APIが論理削除済みの担当者を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.staff.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_staff_delete_success_with_retired_staff(self) -> None:
        """担当者削除APIは退職済みの担当者を削除できる"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.staff.retired_flg = True
        self.staff.save()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_staff_delete_failed_without_auth(self) -> None:
        """担当者削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('staffs:staff_detail', kwargs={'pk': self.staff.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
