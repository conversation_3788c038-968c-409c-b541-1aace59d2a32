from decimal import Decimal

from django import template

from orders.models import EntryDetail

register = template.Library()


@register.simple_tag
def entry_detail_content(instance: EntryDetail) -> str:
    service_name: str = instance.item.service.name
    quantity: Decimal = Decimal(str(instance.quantity))
    item_name: str = instance.item_name
    if instance.keigen_flg:
        item_name = f'※{item_name}'
    amount_per_detail: Decimal = quantity * Decimal(str(instance.item_unit_price))
    str_amount_per_detail: str = f'\\{amount_per_detail:,}'

    lines = [
        f'    {service_name} | {item_name}',
        f'{" "*40}数量:{quantity:<10,} {str_amount_per_detail:>14}',
    ]

    if hasattr(instance, 'koden'):
        quantity_koden_commission: Decimal = Decimal('1')
        str_koden_commission: str = f'\\{instance.koden.koden_commission:,}'
        lines.extend(
            [
                '    ご香典システム利用料',
                f'{" "*40}数量:{quantity_koden_commission:<10,} {str_koden_commission:>14}',
            ]
        )

    return '\n'.join(lines)


@register.filter
def currency(value) -> str:
    return f'\\{value:,}'
