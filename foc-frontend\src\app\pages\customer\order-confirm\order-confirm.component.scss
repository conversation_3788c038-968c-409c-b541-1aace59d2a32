@import "src/assets/scss/customer/setting";
.container .inner .contents {
  padding: 15px;
  .data_area {
    max-width: 500px;
    width: 100%;
    margin: auto;
    border-bottom: solid 2px $border-grey;
    margin-bottom: 10px;
    padding: 0 15px 15px;
    .title {
      font-size: 1.1rem;
      font-weight: bold;
      position: relative;
      .button {
        position: absolute;
        top: 7px;
        left: 130px;
        font-size: 0.9rem;
        width: 60px;
        height: 25px;
        line-height: 21px;
        margin-right: 10px;
        border-radius: 30px;
        &.grey.light {
          background-color: $label-light;
          color: black;
        }
      }
    }
    table.normal {
      font-size: 1.1rem;
      line-height: 1.6;
      tr {
        .label {
          padding-left: 20px;
          min-width: 130px;
        }
      }
    }
    @media screen and (max-width: 560px) {
      padding: 10px 0;
      .title {
        .button {
          left: 100px;
        }
      }
      table {
        tr {
          .label {
            padding-left: 15px;
            min-width: 100px;
          }
        }
      }
    }
    &.detail {
      table {
        min-width: 400px;
        width: 100%;
        font-size: 1.1rem;
        border-collapse: collapse;
        line-height: 1.4;
        tr {
          &.divided {
            border-bottom: solid 1px $border-grey;
          }

        }
        td {
          vertical-align: top;
          text-align: left;
          padding-top: 6px;
          padding-bottom: 6px;
          &.name {
            min-width: 150px;
            padding-left: 15px;
            .item_name {
              font-size: 1rem;
              &.remark::after {
                margin-left: 2px;
                content: '*';
              }
            }
            .system_name {
              font-size: 1rem;
            }
          }
          &.quantity {
            min-width: 80px;
          }
          &.price {
            min-width: 100px;
            text-align: right;
            .system_price {
              font-size: 1rem;
            }
          }
          &.expired {
            padding-left: 15px;
            font-size: 0.7rem;
            color: $text-red;
          }
        }
        &.summary {
          tr{
            border-bottom: none;
            font-size: 0.95rem;
            td.summary_name {
              position: relative;
              min-width: 200px;
              padding-left: 100px;
              &.remark::before {
                position: absolute;
                left: 90px;
                content: '*';
              }
            }
            td.price {
              padding-right: 10px;
            }
            &.total {
              .summary_name {
                font-size: 1.2rem;
              }
              .price {
                font-size: 1.1rem;
              }
            }
          }
        }
        @media screen and (max-width: 560px) {
          min-width: 90%;
          td {
            &.name {
              min-width: 100px;
              padding-left: 15px;
              .item_name {
                font-size: 1rem;
              }
            }
            &.quantity {
              min-width: 60px;
            }
            &.price {
              min-width: 70px;
              text-align: right;
            }
            &.operate {
              width: 80px;
            }
          }
          &.summary {
            tr{
              border-bottom: none;
              font-size: 0.9rem;
              td.summary_name {
                padding-left: 50px;
                &.remark::before {
                  left: 42px;
                }
              }
              td.price {
                padding-right: 10px;
              }
              &.total {
                .summary_name {
                  font-size: 1.1rem;
                }
                .price {
                  font-size: 1.0rem;
                }
              }
            }
          }
        }

      }
    }
  }
}
