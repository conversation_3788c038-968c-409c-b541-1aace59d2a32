# Generated by Django 3.1.2 on 2020-10-22 03:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bases', '0004_focfee_tokusho'),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('name', models.TextField(verbose_name='name')),
                ('tel', models.CharField(max_length=15, verbose_name='tel no')),
                ('fax', models.CharField(max_length=15, verbose_name='fax no')),
                ('zip_code', models.CharField(max_length=7, verbose_name='zipcode')),
                ('address', models.TextField(verbose_name='address')),
                ('del_flg', models.BooleanField(default=False, verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('create_user_id', models.IntegerField(verbose_name='created by')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('update_user_id', models.IntegerField(verbose_name='updated by')),
                (
                    'company',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='suppliers',
                        to='bases.base',
                        verbose_name='company',
                    ),
                ),
            ],
            options={
                'db_table': 'm_supplier',
            },
        ),
    ]
