
<div class="container">
  <div class="inner with_footer">
    <div class="contents mini">
      <div class="menu_title">
        <i class="user tie icon big"></i>
        担当者
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" [(selectedName)]="company_name" (selectedItemChange)="companyChange($event, departComboEm, hallComboEm)"></com-dropdown>
          <label>部門</label>
          <com-dropdown #departComboEm [settings]="departCombo" [disabled]="is_admin_base" [(selectedValue)]="depart_id" (selectedItemChange)="departChange($event, hallComboEm)"></com-dropdown>
          <label>式場</label>
          <com-dropdown #hallComboEm [settings]="hallCombo" [disabled]="is_admin_base" [(selectedValue)]="hall_id" (selectedItemChange)="hallChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="item_list?.length">
          全{{item_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?item_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination, pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(pagination, page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination, pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!item_list?.length">
              <th class="base_name">所属拠点</th>
              <th class="name">氏名</th>
              <th class="login_id">ログインID</th>
              <th class="mail_address">メールアドレス</th>
              <th class="fdn_code">FDNコード</th>
              <th class="retired_flg">退職</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let item of item_list; index as i">
              <tr (click)="showData($event, item)" *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
                <td class="base_name" title="{{item.base_name}}">{{item.base_name}}</td>
                <td class="name" title="{{item.name}}">{{item.name}}</td>
                <td class="login_id" title="{{item.login_id}}">{{item.login_id}}</td>
                <td class="mail_address" title="{{item.mail_address}}">{{item.mail_address}}</td>
                <td class="fdn_code" title="{{item.fdn_code}}">{{item.fdn_code}}</td>
                <td class="center aligned retired_flg" >{{item.retired_flg?'退職':''}}</td>
                <td class="center aligned button operation">
                  <i class="large trash alternate icon" title="削除" (click)="deleteData(item)"></i>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <tr title="新規追加" (click)="showData($event)">
              <td colspan="6" class="center aligned"><i class="large add icon"></i></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal tiny" id="item-edit">
        <div class="header ui"><i class="user tie icon large"></i>
          担当者{{edit_type===1?'登録':'編集'}}
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="item_edit">
            <div class="line">
              <label class="required">所属拠点(葬儀社)</label>
              <label class="plane">{{company_name}}</label>
            </div>
            <div class="line">
              <label>所属拠点(部門)</label>
              <com-dropdown class="divided full" #inputDepartComboEm [settings]="inputDepartCombo" [disabled]="is_admin_base" [(selectedValue)]="item_edit.depart_id" (selectedItemChange)="inputDepartChange($event, inputHallComboEm)"></com-dropdown>
            </div>
            <div class="line">
              <label>所属拠点(式場)</label>
              <com-dropdown class="divided full" #inputHallComboEm [settings]="inputHallCombo" [disabled]="is_admin_base" [(selectedValue)]="item_edit.hall_id"></com-dropdown>
            </div>
            <div class="line">
              <label class="required">氏名</label>
              <div class="ui input full">
                <input [class.error]="errField.get('name')" type="text" autocomplete="off" id="name" maxlength="15" [(ngModel)]="item_edit.name">
              </div>
            </div>
            <div class="line">
              <label class="required">ログインID</label>
              <div class="ui input full">
                <input [class.error]="errField.get('login_id')" type="tel" autocomplete="off" id="login_id" maxlength="15" [(ngModel)]="item_edit.login_id">
              </div>
            </div>
            <div class="line">
              <label [class.required]="edit_type===1">パスワード</label>
              <div class="ui input full">
                <input [class.error]="errField.get('password')" type="tel" autocomplete="off" id="password" [(ngModel)]="item_edit.password">
              </div>
            </div>
            <div class="line">
              <label>メールアドレス</label>
              <div class="ui input full">
                <input [class.error]="errField.get('mail_address')" type="email" autocomplete="off" id="mail_address" [(ngModel)]="item_edit.mail_address">
              </div>
            </div>
            <div class="line">
              <label>FDNコード</label>
              <div class="ui input full">
                <input [class.error]="errField.get('fdn_code')" type="tel" autocomplete="off" id="fdn_code" [(ngModel)]="item_edit.fdn_code">
              </div>
            </div>
            <div class="line">
              <label>退職</label>
              <div class="ui toggle checkbox">
                <input type="checkbox" [(ngModel)]="item_edit.retired_flg">
                <label></label>
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeItemEdit()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
