import base64
import hashlib
import os


def generate_salt(length: int = 32) -> str:
    """Saltを生成します

    Args:
        length (int, optional): Defaults to 32. 長さの基準(生成途中のバイト列の長さなので、結果の文字列の長さとは異なります)

    Returns:
        str: Salt用にランダムで生成された文字列
    """
    return base64.b64encode(os.urandom(length)).decode('ascii')


def make_password(raw_password: str, salt: str) -> str:
    """パスワードをsaltとstretchingでハッシュ化します

    Args:
        raw_password (str): 平文パスワード
        salt (str): パスワードに付け足す文字列

    Returns:
        str: ハッシュ化されたパスワード
    """
    digest = b''
    salted = b''.join(
        [raw_password.encode('utf-8'), b'{', base64.b64decode(salt.encode('utf-8')), b'}']
    )
    for i in range(5000):
        digest = hashlib.sha512(digest + salted).digest()
    return base64.b64encode(digest).decode('ascii')
