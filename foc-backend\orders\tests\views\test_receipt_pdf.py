from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from items.tests.factories import ItemFactory
from masters.tests.factories import ServiceFactory
from orders.models import Entry
from orders.tests.factories import EntryDetailFactory, EntryDetailKodenFactory, EntryFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class ReceiptViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()

        self.entry = EntryFactory()
        service_10 = ServiceFactory(id=10)
        service_20 = ServiceFactory(id=20)
        service_30 = ServiceFactory(id=30)
        item = ItemFactory(service=ServiceFactory(id=40))
        item_10 = ItemFactory(service=service_10)
        item_20 = ItemFactory(service=service_20)
        item_30 = ItemFactory(service=service_30)

        entry_detail = EntryDetailFactory(entry=self.entry, item=item, item_tax_pct=8)
        entry_detail_10 = EntryDetailFactory(entry=self.entry, item=item_10, item_tax_pct=10)
        entry_detail_20 = EntryDetailFactory(entry=self.entry, item=item_20, item_tax_pct=8)
        entry_detail_30 = EntryDetailFactory(entry=self.entry, item=item_30, item_tax_pct=10)

        EntryDetailKodenFactory(entry_detail=entry_detail)
        EntryDetailKodenFactory(entry_detail=entry_detail_10)
        EntryDetailKodenFactory(entry_detail=entry_detail_20)
        EntryDetailKodenFactory(entry_detail=entry_detail_30)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_receipt_succeed(self) -> None:
        """領収書PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'atena': '宛名'}
        response = self.api_client.get(
            reverse('orders:receipt_pdf', kwargs={'pk': self.entry.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'領収書-{self.entry.pk}.pdf')

        saved_entry = Entry.objects.get(pk=self.entry.pk)
        self.assertEqual(self.entry.receipt_count + 1, saved_entry.receipt_count)

    def test_receipt_succeed_without_auth(self) -> None:
        """領収書PDF取得APIはAuthorizationヘッダがなくても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'atena': '宛名'}
        response = self.api_client.get(
            reverse('orders:receipt_pdf', kwargs={'pk': self.entry.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_receipt_failed_by_notfound(self) -> None:
        """存在しない申込IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'atena': '宛名'}
        not_exist_entry = EntryFactory.build()
        response = self.api_client.get(
            reverse('orders:receipt_pdf', kwargs={'pk': not_exist_entry.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
