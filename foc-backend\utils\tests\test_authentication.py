from django.test import TestCase
from rest_framework_simplejwt.exceptions import AuthenticationFailed, InvalidToken
from rest_framework_simplejwt.settings import api_settings

from seko.models import Moshu
from seko.tests.factories import MoshuFactory
from staffs.models import Staff
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableJWTAuthentication, ClassableRefreshToken


class ClassableRefreshTokenTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff: Staff = StaffFactory()
        self.moshu: Moshu = MoshuFactory()

    def test_for_user(self) -> None:
        """ユーザインスタンスに応じたuser_classクレームを持つRefreshTokenを生成する"""
        staff_refresh = ClassableRefreshToken.for_user(self.staff)
        self.assertEqual(staff_refresh['user_class'], 'Staff')

        moshu_refresh = ClassableRefreshToken.for_user(self.moshu)
        self.assertEqual(moshu_refresh['user_class'], 'Moshu')


class ClassableJWTAuthenticationTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff: Staff = StaffFactory()
        self.moshu: Moshu = MoshuFactory()
        self.auth = ClassableJWTAuthentication()

    def test_get_user(self) -> None:
        """Tokenからuser_classクレームに応じたユーザを返す"""
        staff_refresh = ClassableRefreshToken.for_user(self.staff)
        staff = self.auth.get_user(staff_refresh)
        self.assertEqual(staff, self.staff)

        moshu_refresh = ClassableRefreshToken.for_user(self.moshu)
        moshu = self.auth.get_user(moshu_refresh)
        self.assertEqual(moshu, self.moshu)

    def test_get_user_fail_by_less_claims(self) -> None:
        """トークンからユーザの特定が不正なトークンクレームで失敗する"""
        staff_refresh = ClassableRefreshToken.for_user(self.staff)
        del staff_refresh[api_settings.USER_ID_CLAIM]
        with self.assertRaises(InvalidToken):
            self.auth.get_user(staff_refresh)

        staff_refresh = ClassableRefreshToken.for_user(self.staff)
        del staff_refresh['user_class']
        with self.assertRaises(InvalidToken):
            self.auth.get_user(staff_refresh)

        another_staff = StaffFactory.build()
        staff_refresh = ClassableRefreshToken.for_user(self.staff)
        staff_refresh[api_settings.USER_ID_CLAIM] = another_staff.pk
        with self.assertRaises(AuthenticationFailed):
            self.auth.get_user(staff_refresh)

    def test_get_user_fail_by_staff_not_active(self) -> None:
        """トークンからユーザの特定がスタッフがログイン不可で失敗する"""
        staff_refresh = ClassableRefreshToken.for_user(self.staff)
        self.staff.retired_flg = True
        self.staff.save()
        with self.assertRaises(AuthenticationFailed):
            self.auth.get_user(staff_refresh)
