from typing import Dict, List

from django.core import mail
from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient

from bases.models import Base, Tokusho
from bases.tests.factories import BaseFactory, TokushoFactory

fake_provider = Faker('ja_JP')


class InquiryViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.tokusho: Tokusho = TokushoFactory()

        self.api_client = APIClient()

    def basic_params(self) -> Dict:
        return {
            'name': fake_provider.name(),
            'kana': fake_provider.kana_name(),
            'tel': fake_provider.phone_number().replace('-', ''),
            'mail_address': fake_provider.ascii_safe_email(),
            'content': fake_provider.paragraph(),
        }

    def test_inquiry_create_succeed(self) -> None:
        """顧客から問い合わせメールを送信する"""
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('bases:inquiry', kwargs={'pk': self.tokusho.company.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record, {})

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, 'FOCこころ　お問合せ')
        self.assertEqual(sent_mail.to, [self.tokusho.mail_address])

    def test_inquiry_create_failed_by_notfound(self) -> None:
        """顧客からの問い合わせAPIが存在しない拠点IDを指定して失敗する"""
        non_saved_base: Base = BaseFactory.build()
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('bases:inquiry', kwargs={'pk': non_saved_base.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_inquiry_create_failed_by_base_have_no_tokusho(self) -> None:
        """顧客からの問い合わせAPIが特商法レコードを持っていない拠点IDを指定して失敗する"""
        base = self.tokusho.company
        self.tokusho.delete()
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('bases:inquiry', kwargs={'pk': base.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_inquiry_create_failed_by_less_params(self) -> None:
        """顧客からの問い合わせAPIがパラメータ不足で失敗する"""
        required_fields: List[str] = ['name', 'kana', 'mail_address', 'content']
        for field_name in required_fields:
            params: Dict = self.basic_params()
            del params[field_name]
            response = self.api_client.post(
                reverse('bases:inquiry', kwargs={'pk': self.tokusho.company.pk}),
                data=params,
                format='json',
            )
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_inquiry_create_failed_by_null_param(self) -> None:
        """顧客からの問い合わせAPIがnullパラメータで失敗する"""
        not_null_fields: List[str] = ['name', 'kana', 'mail_address', 'content']
        for field_name in not_null_fields:
            params: Dict = self.basic_params()
            params[field_name] = None
            response = self.api_client.post(
                reverse('bases:inquiry', kwargs={'pk': self.tokusho.company.pk}),
                data=params,
                format='json',
            )
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # telはnullでもセーフ
        params: Dict = self.basic_params()
        params['tel'] = None
        response = self.api_client.post(
            reverse('bases:inquiry', kwargs={'pk': self.tokusho.company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
