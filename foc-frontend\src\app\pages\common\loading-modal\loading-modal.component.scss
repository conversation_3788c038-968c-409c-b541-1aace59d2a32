//loading component.css
.message {
  background-color: transparent;
  position: absolute;
  color: #ffffff;
}
.loading {
  position: absolute;
  z-index: 999;
  height: 100%;
  width: 100%;
  overflow: visible;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0,0,0,0.5);
}
/* Transparent Overlay */
.loading:before {
  content: '';
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
