
<div class="container">
  <div class="inner">
    <div class="contents tiny">
      <div class="menu_title">
        <i class="file invoice dollar icon big"></i>
        請求書出力
      </div>
      <div class="search_area">
        <div class="line">
          <label>年度</label>
          <com-dropdown class="mini" #yearComboEm [settings]="yearCombo" [(selectedValue)]="fiscal_year" (selectedValueChange)="yearChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="sales_yymm">対象年月</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let sales of sales_list_filter; index as i">
            <tr>
              <td class="center aligned sales_yymm" title="{{formatYYMM(sales.sales_index.sales_yymm)}}">{{formatYYMM(sales.sales_index.sales_yymm)}}</td>
              <td class="center aligned button operation">
                <i class="large file pdf icon" title="請求書PDF" (click)="downloadPDF(sales.id)"></i>
              </td>
            </tr>
            </ng-container>
            <tr *ngIf="!sales_list_filter?.length && searched" class="no_data">
              <td class="center aligned" colspan="13">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>

		</div>
  </div>
</div>

