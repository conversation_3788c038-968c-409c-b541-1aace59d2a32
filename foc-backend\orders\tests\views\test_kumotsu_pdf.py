from typing import Dict

from django.core import mail
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory, TokushoFactory
from masters.tests.factories import ScheduleFactory
from orders.tests.factories import EntryDetailFactory, EntryDetailKumotsuFactory, EntryFactory
from seko.tests.factories import KojinFactory, MoshuFactory, SekoFactory, SekoScheduleFactory
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class KumotsuPDFViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory()

        seko = SekoFactory(seko_company=base)
        KojinFactory(kojin_num=1, seko=seko)
        KojinFactory(kojin_num=2, seko=seko)
        MoshuFactory(seko=seko)
        SekoScheduleFactory(seko=seko, schedule=ScheduleFactory(id=1))
        SekoScheduleFactory(seko=seko, schedule=ScheduleFactory(id=2))
        entry = EntryFactory(seko=seko)
        entry_detail = EntryDetailFactory(entry=entry)
        self.detail_kumotsu = EntryDetailKumotsuFactory(entry_detail=entry_detail)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_kumotsu_pdf_succeed(self) -> None:
        """供花供物PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:kumotsu_pdf', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'供花・供物発注書-{self.detail_kumotsu.pk}.pdf')

    def test_kumotsu_pdf_succeed_without_auth(self) -> None:
        """供花供物PDF取得APIはAuthorizationヘッダがなくても成功する"""

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:kumotsu_pdf', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_kumotsu_pdf_failed_by_notfound(self) -> None:
        """供花供物PDF取得APIが存在しない明細_供花供物IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_kumotsu_detail = EntryDetailKumotsuFactory.build()
        response = self.api_client.get(
            reverse('orders:kumotsu_pdf', kwargs={'pk': not_exist_kumotsu_detail.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class KumotsuPDFFaxViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory()
        self.tokusho = TokushoFactory(company=base)
        self.supplier = SupplierFactory(company=base)

        seko = SekoFactory(seko_company=base)
        KojinFactory(kojin_num=1, seko=seko)
        KojinFactory(kojin_num=2, seko=seko)
        MoshuFactory(seko=seko)
        SekoScheduleFactory(seko=seko, schedule=ScheduleFactory(id=1))
        SekoScheduleFactory(seko=seko, schedule=ScheduleFactory(id=2))
        entry = EntryFactory(seko=seko)
        entry_detail = EntryDetailFactory(entry=entry)
        self.detail_kumotsu = EntryDetailKumotsuFactory(
            entry_detail=entry_detail,
            supplier=self.supplier,
            order_status=1,
            order_ts=None,
            order_staff=None,
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_kumotsu_pdf_fax_succeed(self) -> None:
        """供花供物PDFをFaxで送信する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('orders:kumotsu_fax', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.from_email, self.tokusho.from_name)
        self.assertEqual(sent_mail.to, [f'{self.supplier.fax_digits()}@cl1.faximo.jp'])
        self.assertEqual(sent_mail.subject, '===<<<')
        self.assertEqual(sent_mail.body, '')
        self.assertEqual(len(sent_mail.attachments), 1)

        self.detail_kumotsu.refresh_from_db()
        self.assertEqual(self.detail_kumotsu.order_status, 2)
        self.assertIsNotNone(self.detail_kumotsu.order_ts)
        self.assertEqual(self.detail_kumotsu.order_staff, self.staff)

    def test_kumotsu_pdf_fax_fail_with_auth(self) -> None:
        """供花供物PDF FAX送信APIはAuthorizationヘッダがなくて失敗する"""

        params: Dict = {}
        response = self.api_client.post(
            reverse('orders:kumotsu_fax', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_kumotsu_pdf_fax_failed_by_notfound(self) -> None:
        """供花供物PDF FAX送信APIが存在しない明細_供花供物IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_kumotsu_detail = EntryDetailKumotsuFactory.build()
        response = self.api_client.post(
            reverse('orders:kumotsu_fax', kwargs={'pk': not_exist_kumotsu_detail.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
