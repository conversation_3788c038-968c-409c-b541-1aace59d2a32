from django.urls import path

from orders import views

app_name = 'orders'
urlpatterns = [
    path('', views.EntryList.as_view(), name='entry_list'),
    path('<int:pk>/', views.EntryDetailView.as_view(), name='entry_detail'),
    path('<int:pk>/receipt/', views.ReceiptPDF.as_view(), name='receipt_pdf'),
    path('<int:pk>/cancel/', views.EntryCancelView.as_view(), name='entry_cancel'),
    path('detail/<int:pk>/chobun_pdf/', views.ChobunPDF.as_view(), name='chobun_pdf'),
    path('kumotsu/', views.KumotsuList.as_view(), name='kumotsu_list'),
    path('kumotsu/<int:pk>/', views.KumotsuDetail.as_view(), name='kumotsu_detail'),
    path(
        'kumotsu/order_status/', views.KumotsuUpdateStatus.as_view(), name='kumotsu_update_status'
    ),
    path('kumotsu/<int:pk>/pdf/', views.KumotsuPDF.as_view(), name='kumotsu_pdf'),
    path('kumotsu/<int:pk>/fax/', views.KumotsuPDFFax.as_view(), name='kumotsu_fax'),
    path('chobun/', views.ChobunList.as_view(), name='chobun_list'),
    path('chobun/<int:pk>/', views.ChobunDetail.as_view(), name='chobun_detail'),
    path('koden/', views.KodenList.as_view(), name='koden_list'),
    path('msg/', views.MsgList.as_view(), name='msg_list'),
    path('msg/<int:pk>/', views.MsgDetail.as_view(), name='msg_detail'),
    path('fdn/', views.FdnOrderList.as_view(), name='fdn_order_list'),
    path('epsilon/return/', views.EpsilonReturn.as_view(), name='epsilon_return'),
    path('paymentresult/', views.PaymentResultListView.as_view(), name='payment_result'),
]
