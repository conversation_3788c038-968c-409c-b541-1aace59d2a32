import base64
import re
from typing import Dict, List, Set

from django.conf import settings
from django.db.models import Q
from django.utils import timezone
from drf_extra_fields.relations import PresentablePrimaryKeyRelatedField
from rest_framework import exceptions, serializers

from henrei.models import HenreihinKoden
from henrei.serializers import (
    HenreiKodenRelatedSerializer,
    HenreiKumotsuRelatedSerializer,
    HenreiKumotsuSerializer,
)
from items.serializers import ItemWithServiceSerializer
from masters.models import ChobunDaishiMaster, PaymentType
from masters.serializers import ChobunDaishiMasterSerializer, PaymentTypeSerializer
from orders.epsilon import ContractType
from orders.models import (
    Entry,
    EntryDetail,
    EntryDetailChobun,
    EntryDetailKoden,
    EntryDetailKumotsu,
    EntryDetailMsg,
    PaymentResult,
)
from seko.serializers import SekoSerializer
from suppliers.models import Supplier
from suppliers.serializers import SupplierSerializer


class EntryDetailKodenSerializer(serializers.ModelSerializer):
    henrei_koden = HenreiKodenRelatedSerializer(required=False)

    class Meta:
        model = EntryDetailKoden
        exclude = ['entry_detail']
        read_only_fields = ['created_at', 'updated_at']


class EntryDetailKumotsuSerializer(serializers.ModelSerializer):
    supplier = SupplierSerializer(required=False)
    henrei_kumotsu = HenreiKumotsuRelatedSerializer(required=False)

    class Meta:
        model = EntryDetailKumotsu
        exclude = ['entry_detail']
        read_only_fields = [
            'supplier',
            'order_status',
            'order_ts',
            'delivery_ts',
            'order_staff',
            'created_at',
            'updated_at',
        ]


class EntryDetailChobunSerializer(serializers.ModelSerializer):
    daishi = PresentablePrimaryKeyRelatedField(
        queryset=ChobunDaishiMaster.objects.all(),
        presentation_serializer=ChobunDaishiMasterSerializer,
    )

    class Meta:
        model = EntryDetailChobun
        exclude = ['entry_detail']
        read_only_fields = ['printed_flg', 'created_at', 'updated_at']


class EntryDetailMsgSerializer(serializers.ModelSerializer):
    class Meta:
        model = EntryDetailMsg
        exclude = ['entry_detail']
        read_only_fields = ['release_status', 'created_at', 'updated_at']


class EntryDetailSerializer(serializers.ModelSerializer):
    chobun = EntryDetailChobunSerializer(required=False)
    koden = EntryDetailKodenSerializer(required=False)
    kumotsu = EntryDetailKumotsuSerializer(required=False)
    message = EntryDetailMsgSerializer(required=False)

    class Meta:
        model = EntryDetail
        exclude = ['entry']
        read_only_fields = ['created_at', 'updated_at']


class EntrySerializer(serializers.ModelSerializer):
    payment = PresentablePrimaryKeyRelatedField(
        queryset=PaymentType.objects.all(),
        presentation_serializer=PaymentTypeSerializer,
        required=False,
        allow_null=True,
    )
    details = EntryDetailSerializer(many=True, required=False)
    contract_type = serializers.ChoiceField(
        ContractType.choices, write_only=True, required=False, allow_null=True
    )
    epsilon_token = serializers.CharField(write_only=True, required=False, allow_null=True)
    billing_amount = serializers.IntegerField(write_only=True)
    con_count = serializers.IntegerField(write_only=True)
    order_number = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = Entry
        fields = '__all__'
        read_only_fields = ['entry_ts', 'cancel_ts', 'receipt_count', 'created_at', 'updated_at']

    def validate(self, attrs) -> Dict:
        validated_attrs: Dict = super().validate(attrs)
        if settings.ENABLE_EPSILON_PAYMENT and validated_attrs['billing_amount'] > 0:
            errors: Dict = {}
            if not validated_attrs.get('payment'):
                errors['payment'] = self.fields['payment'].error_messages['null']
            if not validated_attrs.get('contract_type'):
                errors['contract_type'] = self.fields['contract_type'].error_messages['null']
            if not validated_attrs.get('epsilon_token'):
                errors['epsilon_token'] = self.fields['epsilon_token'].error_messages['null']
            if errors:
                raise exceptions.ValidationError(errors)

        return validated_attrs

    def create(self, validated_data):
        details_data = validated_data.pop('details', [])

        validated_data['cancel_ts'] = None
        validated_data['receipt_count'] = 0
        instance: Entry = super().create(validated_data)

        for detail_dict in details_data:
            create_chobun: bool = 'chobun' in detail_dict
            create_koden: bool = 'koden' in detail_dict
            create_kumotsu: bool = 'kumotsu' in detail_dict
            create_message: bool = 'message' in detail_dict
            chobun_data: Dict = detail_dict.pop('chobun', {})
            koden_data: Dict = detail_dict.pop('koden', {})
            kumotsu_data: Dict = detail_dict.pop('kumotsu', {})
            message_data: Dict = detail_dict.pop('message', {})

            detail: EntryDetail = EntryDetail.objects.create(entry=instance, **detail_dict)

            if create_chobun:
                chobun_data['printed_flg'] = False
                EntryDetailChobun.objects.create(entry_detail=detail, **chobun_data)

            if create_koden:
                create_henrei_koden: bool = 'henrei_koden' in koden_data
                henrei_koden_data: Dict = koden_data.pop('henrei_koden', {})
                detail_koden = EntryDetailKoden.objects.create(entry_detail=detail, **koden_data)

                if create_henrei_koden:
                    henrei_koden_data['supplier'] = henrei_koden_data[
                        'henreihin'
                    ].default_supplier()
                    henrei_koden_data['order_status'] = 0
                    HenreihinKoden.objects.create(detail_koden=detail_koden, **henrei_koden_data)

            if create_kumotsu:
                kumotsu_data['supplier'] = detail.item.default_supplier()
                kumotsu_data['order_status'] = 1
                kumotsu_data['order_ts'] = None
                kumotsu_data['delivery_ts'] = None
                kumotsu_data['order_staff_id'] = None
                EntryDetailKumotsu.objects.create(entry_detail=detail, **kumotsu_data)

            if create_message:
                message_data['release_status'] = 1
                EntryDetailMsg.objects.create(entry_detail=detail, **message_data)

        return instance


class ReceiptSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    entry_name = serializers.CharField()
    amount = serializers.SerializerMethodField()
    date = serializers.SerializerMethodField()
    base_name = serializers.CharField(source='seko.seko_company.base_name')
    prefecture = serializers.CharField(source='seko.seko_company.prefecture')
    address_1 = serializers.CharField(source='seko.seko_company.address_1')
    address_2 = serializers.CharField(source='seko.seko_company.address_2')
    address_3 = serializers.CharField(source='seko.seko_company.address_3')
    tel = serializers.CharField(source='seko.seko_company.tel')
    logo = serializers.SerializerMethodField()
    staff_name = serializers.SerializerMethodField()
    stamp_size = serializers.SerializerMethodField()
    taxable_amount_8 = serializers.SerializerMethodField()
    tax_8 = serializers.SerializerMethodField()
    taxable_amount_10 = serializers.SerializerMethodField()
    tax_10 = serializers.SerializerMethodField()
    atena = serializers.SerializerMethodField()
    business_no = serializers.SerializerMethodField()

    class Meta:
        model = Entry
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super(ReceiptSerializer, self).__init__(*args, **kwargs)
        self.context['receipt_data'] = Entry.get_receipt_data(self.instance.pk)

    def get_amount(self, obj: Entry):
        receipt_data = self.context['receipt_data']
        return receipt_data['price_8'] + receipt_data['price_10'] + receipt_data['price_koden']

    def get_date(self, obj: Entry):
        return obj.entry_ts.strftime('%Y年%m月%d日')

    def get_logo(self, obj: Entry):
        if not obj.seko.seko_company.company_logo_file:
            return None
        with open(obj.seko.seko_company.company_logo_file.path, 'rb') as image_file:
            encoded_img = base64.b64encode(image_file.read()).decode('ascii')
        return f'data:image/png;base64,{encoded_img}'

    def get_taxable_amount_8(self, obj: Entry):
        receipt_data = self.context['receipt_data']
        return receipt_data['price_8']

    def get_tax_8(self, obj: Entry):
        receipt_data = self.context['receipt_data']
        return receipt_data['tax_8']

    def get_taxable_amount_10(self, obj: Entry):
        receipt_data = self.context['receipt_data']
        return receipt_data['price_10'] + receipt_data['price_koden']

    def get_tax_10(self, obj: Entry):
        receipt_data = self.context['receipt_data']
        return receipt_data['tax_10'] + receipt_data['tax_koden']

    def get_staff_name(self, obj: Entry):
        return re.split('[ 　]', obj.seko.seko_staff.name)[0]

    def get_stamp_size(self, obj: Entry):
        staff_name = re.split('[ 　]', obj.seko.seko_staff.name)[0]
        if len(staff_name) <= 1:
            return 40
        if len(staff_name) == 2:
            return 25
        if len(staff_name) == 3:
            return 16
        if len(staff_name) == 4:
            return 12
        if len(staff_name) >= 5:
            return 9

    def get_atena(self, obj: Entry):
        return self.context['request'].query_params.get('atena', None)

    def get_business_no(self, obj: Entry):
        return obj.seko.seko_company.tokusho.business_no


class SimpleEntrySerializer(serializers.ModelSerializer):
    seko = SekoSerializer()

    class Meta:
        model = Entry
        fields = '__all__'


class SimpleEntryDetailSerializer(serializers.ModelSerializer):
    entry = SimpleEntrySerializer()

    class Meta:
        model = EntryDetail
        fields = '__all__'


class SimpleEntryDetailKumotsuSerializer(serializers.ModelSerializer):
    entry_detail = SimpleEntryDetailSerializer(required=False)
    supplier = PresentablePrimaryKeyRelatedField(
        queryset=Supplier.objects.all(), presentation_serializer=SupplierSerializer, required=False
    )

    class Meta:
        model = EntryDetailKumotsu
        fields = '__all__'
        read_only_fields = ['entry_detail', 'created_at', 'updated_at']


class SimpleEntryDetailChobunSerializer(serializers.ModelSerializer):
    entry_detail = SimpleEntryDetailSerializer()

    class Meta:
        model = EntryDetailChobun
        fields = '__all__'
        read_only_fields = ['entry_detail', 'created_at', 'updated_at']


class SimpleEntryDetailKodenSerializer(serializers.ModelSerializer):
    entry_detail = SimpleEntryDetailSerializer()
    henrei_koden = HenreiKodenRelatedSerializer()

    class Meta:
        model = EntryDetailKoden
        fields = '__all__'


class SimpleEntryDetailMsgSerializer(serializers.ModelSerializer):
    entry_detail = SimpleEntryDetailSerializer(required=False)

    class Meta:
        model = EntryDetailMsg
        fields = '__all__'
        read_only_fields = ['entry_detail', 'created_at', 'updated_at']


class EntryDetailKumotsuUpdateStatusSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.IntegerField(), write_only=True)
    order_status = serializers.IntegerField(write_only=True)

    def validate_ids(self, value) -> List[int]:
        kumotsu_ids: Set[int] = set(
            EntryDetailKumotsu.objects.filter(Q(entry_detail__pk__in=value)).values_list(
                'entry_detail__pk', flat=True
            )
        )
        notfound_ids: Set[int] = set(value) - kumotsu_ids
        if notfound_ids:
            raise serializers.ValidationError(f'Entry detail kumotsu not found: ID={notfound_ids}')

        return value

    def save(self, **kwargs):
        self.instance: List[EntryDetailKumotsu] = []
        for kumotsu in EntryDetailKumotsu.objects.filter(
            Q(entry_detail__pk__in=self.validated_data['ids'])
        ).order_by('entry_detail__pk'):
            kumotsu.set_order_status(self.validated_data['order_status'], self.context['staff'])
            self.instance.append(kumotsu)

        return self.instance


class EntryDetailKumotsuDownwardSerializer(EntryDetailKumotsuSerializer):
    henrei_kumotsu = HenreiKumotsuSerializer(required=False)


class EntryDetailDownwardSerializer(EntryDetailSerializer):
    item = ItemWithServiceSerializer(required=False)
    kumotsu = EntryDetailKumotsuDownwardSerializer(required=False)


class EntryDownwardSerializer(serializers.ModelSerializer):
    details = EntryDetailDownwardSerializer(many=True, required=False)

    class Meta:
        model = Entry
        fields = '__all__'


class EntryCancelSerializer(serializers.Serializer):
    def update(self, instance: Entry, validated_data: Dict) -> Entry:
        instance.cancel_ts = timezone.localtime()
        instance.save()

        return instance


class FdnOrderListSerializer(serializers.Serializer):
    seko_id = serializers.IntegerField()
    fdn_seko_code = serializers.CharField()
    soke_name = serializers.CharField()
    tsuya_hall_name = serializers.CharField()
    tsuya_date = serializers.DateField()
    tsuya_begin_time = serializers.TimeField()
    sogi_hall_name = serializers.CharField()
    sogi_date = serializers.DateField()
    sogi_begin_time = serializers.TimeField()
    kojin_name = serializers.CharField()
    kojin_birth_date = serializers.DateField()
    kojin_death_date = serializers.DateField()
    moshu_name = serializers.CharField()
    moshu_tel = serializers.CharField()
    moshu_mobile = serializers.CharField()
    entry_id = serializers.IntegerField()
    entry_name = serializers.CharField()
    entry_name_kana = serializers.CharField()
    entry_zip_code = serializers.CharField()
    entry_prefecture = serializers.CharField()
    entry_address_1 = serializers.CharField()
    entry_address_2 = serializers.CharField()
    entry_address_3 = serializers.CharField()
    entry_tel = serializers.CharField()
    entry_mail_address = serializers.CharField()
    entry_ts = serializers.DateTimeField()
    cancel_ts = serializers.DateTimeField()
    service_id = serializers.IntegerField()
    service_name = serializers.CharField()
    entry_detail_id = serializers.IntegerField()
    item_id = serializers.IntegerField()
    fdn_item_code = serializers.CharField()
    item_hinban = serializers.CharField()
    item_name = serializers.CharField()
    item_unit_price = serializers.IntegerField()
    quantity = serializers.IntegerField()
    item_price = serializers.SerializerMethodField()
    item_tax = serializers.SerializerMethodField()
    item_tax_pct = serializers.IntegerField()
    keigen_flg = serializers.BooleanField()
    okurinushi_company = serializers.CharField()
    okurinushi_company_kana = serializers.CharField()
    okurinushi_title = serializers.CharField()
    okurinushi_name = serializers.CharField()
    okurinushi_zip_code = serializers.CharField()
    okurinushi_prefecture = serializers.CharField()
    okurinushi_address_1 = serializers.CharField()
    okurinushi_address_2 = serializers.CharField()
    okurinushi_address_3 = serializers.CharField()
    okurinushi_tel = serializers.CharField()
    renmei1 = serializers.CharField()
    renmei2 = serializers.CharField()
    update_ts = serializers.DateTimeField()

    def get_item_price(self, obj: Entry):
        return obj['item_unit_price'] * obj['quantity']

    def get_item_tax(self, obj: Entry):
        return obj['item_tax'] + obj['item_tax_adjust']


class PaymentResultSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentResult
        fields = '__all__'
