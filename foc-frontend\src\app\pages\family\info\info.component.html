
<div class="container">
  <div class="inner">
    <div class="contents">
      <h2>登録内容変更</h2>
      <div class="input-area">
        <div class="description">
          ※変更希望箇所を入力し、保存を押してください。
        </div>
        <div class="line">
          <div class="label">喪主氏名</div>
          <div class="input moshu_name" #moshuNameEm [class.error]="isErrorField(moshuNameEm)">
            <input type="text" disabled [(ngModel)]="seko_info.moshu.name">
          </div>
        </div>
        <div class="line">
          <div class="label">パスワード</div>
          <div class="input moshu_password">
            <input type="password" disabled value="password">
          </div>
        </div>
        <div class="description">
          ※パスワードの変更は別ページにて行えます。
        </div>
        <div class="line">
          <div class="label required">郵便番号</div>
          <div class="input moshu_zip_code" #moshuZipCodeEm [class.error]="isErrorField(moshuZipCodeEm)">
            <input type="tel" placeholder="1234567" autocomplete="off" maxlength="7" [(ngModel)]="seko_info.moshu.zip_code" (input)="zipcodeChange()">
          </div>
        </div>
        <div class="line">
          <div class="label required">都道府県</div>
          <div class="input moshu_prefecture" #moshuPrefectureEm [class.error]="isErrorField(moshuPrefectureEm)">
            <input type="text" [(ngModel)]="seko_info.moshu.prefecture">
          </div>
        </div>
        <div class="line">
          <div class="label required">市区町村</div>
          <div class="input moshu_address_1" #moshuAddress1Em [class.error]="isErrorField(moshuAddress1Em)">
            <input type="text" [(ngModel)]="seko_info.moshu.address_1">
          </div>
        </div>
        <div class="line">
          <div class="label required">所番地</div>
          <div class="input moshu_address_2" #moshuAddress2Em [class.error]="isErrorField(moshuAddress2Em)">
            <input type="text" [(ngModel)]="seko_info.moshu.address_2">
          </div>
        </div>
        <div class="line">
          <div class="label">建屋名</div>
          <div class="input">
            <input type="text" [(ngModel)]="seko_info.moshu.address_3">
          </div>
        </div>
        <div class="line">
          <div class="label">電話番号</div>
          <div class="input moshu_tel" #moshuTelEm [class.error]="isErrorField(moshuTelEm)">
            <input type="tel" autocomplete="off" maxlength="15" [(ngModel)]="seko_info.moshu.tel">
          </div>
        </div>
        <div class="line">
          <div class="label">携帯電話番号</div>
          <div class="input moshu_mobile_num" #moshuMobileNumEm [class.error]="isErrorField(moshuMobileNumEm)">
            <input type="tel" autocomplete="off" maxlength="15" [(ngModel)]="seko_info.moshu.mobile_num">
          </div>
        </div>
        <div class="line">
          <div class="label">メールアドレス</div>
          <div class="input moshu_mail_address" #moshuMailAddressEm [class.error]="isErrorField(moshuMailAddressEm)">
            <input type="email" [(ngModel)]="seko_info.moshu.mail_address">
          </div>
        </div>
        <div class="line">
          <div class="label required">連絡先氏名</div>
          <div class="input seko_contact_name" #sekoContactNameEm [class.error]="isErrorField(sekoContactNameEm)">
            <input type="text" [(ngModel)]="seko_info.seko_contact.name">
          </div>
        </div>
        <div class="line">
          <div class="label required">連絡先郵便番号</div>
          <div class="input seko_contact_zip_code" #sekoContactZipCodeEm [class.error]="isErrorField(sekoContactZipCodeEm)">
            <input type="tel" placeholder="1234567" autocomplete="off" maxlength="7" [(ngModel)]="seko_info.seko_contact.zip_code" (input)="contactZipcodeChange()">
          </div>
        </div>
        <div class="line">
          <div class="label required">連絡先都道府県</div>
          <div class="input seko_contact_prefecture" #sekoContactPrefectureEm [class.error]="isErrorField(sekoContactPrefectureEm)">
            <input type="text" [(ngModel)]="seko_info.seko_contact.prefecture">
          </div>
        </div>
        <div class="line">
          <div class="label required">連絡先市区町村</div>
          <div class="input seko_contact_address_1" #sekoContactAddress1Em [class.error]="isErrorField(sekoContactAddress1Em)">
            <input type="text" [(ngModel)]="seko_info.seko_contact.address_1">
          </div>
        </div>
        <div class="line">
          <div class="label required">連絡先所番地</div>
          <div class="input seko_contact_address_2" #sekoContactAddress2Em [class.error]="isErrorField(sekoContactAddress2Em)">
            <input type="text" [(ngModel)]="seko_info.seko_contact.address_2">
          </div>
        </div>
        <div class="line">
          <div class="label">連絡先建屋名</div>
          <div class="input">
            <input type="text" [(ngModel)]="seko_info.seko_contact.address_3">
          </div>
        </div>
        <div class="line">
          <div class="label">連絡先電話番号</div>
          <div class="input seko_contact_tel" #sekoContactTelEm [class.error]="isErrorField(sekoContactTelEm)">
            <input type="tel" autocomplete="off" maxlength="15" [(ngModel)]="seko_info.seko_contact.tel">
          </div>
        </div>
        <div class="line">
          <div class="label large">連絡先携帯電話番号</div>
          <div class="input seko_contact_mobile_num" #sekoContactMobileNumEm [class.error]="isErrorField(sekoContactMobileNumEm)">
            <input type="tel" autocomplete="off" maxlength="15" [(ngModel)]="seko_info.seko_contact.mobile_num">
          </div>
        </div>
        <div class="line">
          <div class="label required huge">連絡先メールアドレス</div>
          <div class="input seko_contact_mail_address" #sekoContactMailAddressEm [class.error]="isErrorField(sekoContactMailAddressEm)">
            <input type="email" [(ngModel)]="seko_info.seko_contact.mail_address">
          </div>
        </div>
        <div class="line">
          <div class="label required huge">訃報ページ掲載終了日</div>
          <div class="input fuho_site_end_date" #fuhoSiteEndDateEm [class.error]="isErrorField(fuhoSiteEndDateEm)">
            <input type="date" [ngModel]="seko_info.fuho_site_end_date | date: 'y-MM-dd'" (ngModelChange)="dateChange($event)">
          </div>
        </div>
        <div class="line">
          <div class="ui checkbox">
            <input type="checkbox" [ngModel]="!seko_info.moshu.mail_flg" (ngModelChange)="mailFlgChange($event)">
            <label>案内メール不要連絡</label>
          </div>
        </div>
        <div class="line">
          <div class="ui checkbox">
            <input type="checkbox" [(ngModel)]="seko_info.moshu.soke_site_del_request_flg">
            <label>葬家専用ページ削除依頼連絡</label>
          </div>
        </div>
        <div class="description">
          ※連絡を送りたい場合はチェックをして保存してください。
        </div>
      </div>
    </div>
    <div class="button-area">
      <a class="button red" (click)="saveData()">保存</a>
    </div>
    <div class="ui modal confirm" id="message-popup">
      <div class="content red">
        {{message}}
      </div>
      <div class="button-area">
        <a class="button grey" (click)="closePopup()">閉じる</a>
      </div>
    </div>
  </div>
</div>
