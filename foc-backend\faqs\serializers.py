from rest_framework import serializers

from faqs.models import Faq
from utils.serializer_mixins import AddUserIdMixin


class FaqSerializer(AddUserIdMixin, serializers.ModelSerializer):
    class Meta:
        model = Faq
        fields = '__all__'
        read_only_fields = [
            'del_flg',
            'create_user_id',
            'created_at',
            'update_user_id',
            'updated_at',
        ]


class FaqUpdateSerializer(FaqSerializer):
    class Meta(FaqSerializer.Meta):
        read_only_fields = FaqSerializer.Meta.read_only_fields + ['company']
        extra_kwargs = {'company': {'required': False}}
