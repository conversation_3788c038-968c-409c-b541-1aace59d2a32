@import "src/assets/scss/customer/setting";
.container .inner .contents {
  padding: 15px;
  .data_area {
    width: 100%;
    margin: auto;
    margin-bottom: 10px;
    padding: 0 5px 15px;
    table {
      font-size: 1rem;
      .bigger {
        font-size: 1.2rem;
      }
      line-height: 1.6;
      margin: auto;
      border-collapse: collapse;
      tbody {
        &:not(:first-child) {
          border-top: 15px solid white;
        }
        tr {
          td {
            display: flex;
            .label {
              padding-left: 0px;
              min-width: 130px;
              font-weight: bold;
            }
            .data a {
              text-decoration: underline;
            }
          } 
        }
      }
    }
  }
  &.top {
    .data_area {
      padding-bottom: 0;
      table {
        min-width: 300px;
      }
    }
    @media screen and (max-width: 560px) {
        margin-top: -10px;
        .data_area table {
          min-width: 0;
        }
    }
  }
  @media screen and (max-width: 560px) {
    &.body .data_area table tbody tr td {
      display: block;
      .label {
        min-width: 100%;
      }
      .data {
        padding-left: 20px;
      }
    }
  }
  .userpolicy {      
    text-align: center;
    text-decoration: underline;
    font-size: 1.1rem;
  }
}
