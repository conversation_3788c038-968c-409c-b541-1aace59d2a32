from django.test import TestCase

from suppliers.models import Supplier
from suppliers.tests.factories import SupplierFactory


class SupplierModelTest(TestCase):
    def setUp(self):
        super().setUp()
        self.supplier: Supplier = SupplierFactory()

    def test_supplier_disable(self) -> None:
        """発注先を無効化(論理削除)する"""
        self.supplier.disable()
        self.assertTrue(self.supplier.del_flg)

    def test_fax_digits(self) -> None:
        """FAX番号から数字以外を除去した文字列を返す"""
        self.supplier.fax = ' 0/1２34-5　6 '
        self.assertEqual(self.supplier.fax_digits(), '013456')
