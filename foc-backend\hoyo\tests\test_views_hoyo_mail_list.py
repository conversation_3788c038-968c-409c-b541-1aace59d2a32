import json
from datetime import date
from typing import Dict, List
from unittest import mock

from dateutil.relativedelta import relativedelta
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import SmsAccountFactory
from hoyo.models import HoyoMail, HoyoMailTarget
from hoyo.tests.factories import HoyoFactory, HoyoMailFactory, HoyoMailTargetFactory
from masters.tests.factories import ScheduleFactory
from seko.tests.factories import (
    KojinFactory,
    MoshuFactory,
    SekoContactFactory,
    SekoFactory,
    SekoScheduleFactory,
)
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class HoyoMailListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.hoyo_1 = HoyoFactory(display_num=2)
        self.hoyo_mail_1 = HoyoMailFactory(hoyo=self.hoyo_1)
        self.hoyo_mail_2 = HoyoMailFactory(
            hoyo=self.hoyo_1,
            select_ts=self.hoyo_mail_1.select_ts + relativedelta(days=-1),
            send_ts=self.hoyo_mail_1.send_ts + relativedelta(days=-1),
        )
        self.hoyo_2 = HoyoFactory(display_num=1, company=self.hoyo_1.company)
        self.hoyo_mail_3 = HoyoMailFactory(
            hoyo=self.hoyo_2, send_ts=self.hoyo_mail_1.send_ts + relativedelta(days=1)
        )
        self.hoyo_mail_4 = HoyoMailFactory(
            hoyo=None,
            select_ts=self.hoyo_mail_3.select_ts + relativedelta(days=1),
            send_ts=self.hoyo_mail_1.send_ts + relativedelta(days=2),
        )
        self.hoyo_3 = HoyoFactory(display_num=1)
        self.hoyo_mail_5 = HoyoMailFactory(
            hoyo=self.hoyo_3, send_ts=self.hoyo_mail_1.send_ts + relativedelta(days=3)
        )

        self.mail_target_1 = HoyoMailTargetFactory(hoyo_mail=self.hoyo_mail_1)
        MoshuFactory(seko=self.mail_target_1.seko)
        KojinFactory(seko=self.mail_target_1.seko, kojin_num=1)
        KojinFactory(seko=self.mail_target_1.seko, kojin_num=2)
        self.mail_target_2 = HoyoMailTargetFactory(hoyo_mail=self.hoyo_mail_1)
        MoshuFactory(seko=self.mail_target_2.seko)
        KojinFactory(seko=self.mail_target_2.seko, kojin_num=1)
        KojinFactory(seko=self.mail_target_2.seko, kojin_num=2)
        self.mail_target_3 = HoyoMailTargetFactory(hoyo_mail=self.hoyo_mail_5)
        MoshuFactory(seko=self.mail_target_3.seko)
        self.kojin_2 = KojinFactory(seko=self.mail_target_3.seko, kojin_num=2)
        self.kojin_1 = KojinFactory(seko=self.mail_target_3.seko, kojin_num=1)
        SekoScheduleFactory(seko=self.mail_target_3.seko, schedule=ScheduleFactory(id=1))
        SekoScheduleFactory(seko=self.mail_target_3.seko, schedule=ScheduleFactory(id=2))
        SekoScheduleFactory(seko=self.mail_target_3.seko, schedule=ScheduleFactory(id=3))

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_mail_list_succeed(self) -> None:
        """法要メール一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)
        # 順番確認 (4 > 3 > 2 > 1 > 5)
        self.assertEqual(records[0]['id'], self.hoyo_mail_4.pk)
        self.assertEqual(records[1]['id'], self.hoyo_mail_3.pk)
        self.assertEqual(records[2]['id'], self.hoyo_mail_2.pk)
        self.assertEqual(records[3]['id'], self.hoyo_mail_1.pk)
        self.assertEqual(records[4]['id'], self.hoyo_mail_5.pk)

        # hoyo_mail_1には対象者が2件
        self.assertEqual(len(records[3]['targets']), 2)

    def test_hoyo_mail_list_filter_by_hoyo(self) -> None:
        """法要メール一覧APIで法要の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'hoyo': self.hoyo_mail_1.hoyo.pk}
        response = self.api_client.get(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)
        self.assertEqual(
            set([rec['id'] for rec in records]), set([self.hoyo_mail_1.pk, self.hoyo_mail_2.pk])
        )

    def test_hoyo_mail_list_succeed_by_company(self) -> None:
        """法要メール一覧APIで拠点の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company': self.mail_target_2.seko.seko_company.pk,
        }
        response = self.api_client.get(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 指定したseko_company_idと一致する案内対象を持つ案内メールが返る
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)
        self.assertEqual(records[0]['id'], self.hoyo_mail_1.pk)
        # 案内メールに紐付く案内対象はseko_company_idが一致しないものも含めて返る
        self.assertEqual(len(records[0]['targets']), 2)
        self.assertEqual(
            set([rec['id'] for rec in records[0]['targets']]),
            set([self.mail_target_1.pk, self.mail_target_2.pk]),
        )

    def test_hoyo_mail_list_succeed_by_company_2(self) -> None:
        """法要メール一覧APIで拠点の絞り込みを行う(項目確認)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company': self.mail_target_3.seko.seko_company.pk,
        }
        response = self.api_client.get(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)
        record = records[0]
        self.assertEqual(record['id'], self.hoyo_mail_5.pk)
        self.assertEqual(record['hoyo']['id'], self.hoyo_mail_5.hoyo.pk)
        self.assertEqual(record['hoyo']['name'], self.hoyo_mail_5.hoyo.name)
        self.assertEqual(record['hoyo']['display_num'], self.hoyo_mail_5.hoyo.display_num)
        self.assertEqual(record['send_ts'], self.hoyo_mail_5.send_ts.astimezone(tz).isoformat())
        self.assertEqual(record['content'], self.hoyo_mail_5.content)
        self.assertEqual(record['staff']['id'], self.hoyo_mail_5.staff.pk)
        self.assertEqual(record['staff']['name'], self.hoyo_mail_5.staff.name)
        self.assertEqual(record['note'], self.hoyo_mail_5.note)
        self.assertEqual(
            record['created_at'], self.hoyo_mail_5.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(
            record['updated_at'], self.hoyo_mail_5.updated_at.astimezone(tz).isoformat()
        )

        self.assertEqual(len(record['targets']), 1)
        rec_target: Dict = record['targets'][0]
        self.assertEqual(rec_target['id'], self.mail_target_3.pk)
        self.assertEqual(rec_target['seko']['id'], self.mail_target_3.seko.pk)
        self.assertEqual(rec_target['seko']['soke_name'], self.mail_target_3.seko.soke_name)
        self.assertEqual(rec_target['seko']['kojin'][0]['name'], self.kojin_1.name)
        self.assertEqual(rec_target['seko']['kojin'][1]['name'], self.kojin_2.name)
        self.assertEqual(rec_target['seko']['moshu']['name'], self.mail_target_3.seko.moshu.name)
        self.assertEqual(
            rec_target['seko']['death_date'], self.mail_target_3.seko.death_date.isoformat()
        )
        self.assertEqual(
            rec_target['seko']['seko_date'], self.mail_target_3.seko.seko_date.isoformat()
        )
        self.assertEqual(len(rec_target['seko']['schedules']), 3)

    def test_hoyo_mail_list_succeed_filter_with_send_date(self) -> None:
        """法要メール一覧APIで送信日の絞り込み(範囲指定、片側でも可能)を行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        hoyo_mail_1_sent_on: date = timezone.localdate(self.hoyo_mail_1.send_ts)

        params: Dict = {
            'send_date_after': hoyo_mail_1_sent_on,
            'send_date_before': hoyo_mail_1_sent_on + relativedelta(days=2),
        }
        response = self.api_client.get(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        params: Dict = {'send_date_after': hoyo_mail_1_sent_on}
        response = self.api_client.get(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

        params: Dict = {'send_date_before': hoyo_mail_1_sent_on + relativedelta(days=2)}
        response = self.api_client.get(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

    def test_hoyo_mail_list_failed_without_auth(self) -> None:
        """法要メール一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_mail_list_deny_from_moshu(self) -> None:
        """法要メール一覧取得APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


FORCE_ERROR_NUMBER = '11122223333'


def mocked_post(*args, **kwargs):
    class MockResponse:
        def __init__(self, status_code, content):
            self.status_code = status_code
            self.text = content

        def json(self):
            return json.loads(self.text)

    return_text = (
        '{'
        '"responseCode":0,'
        '"responseMessage":"Success.",'
        '"phoneNumber":"+810000000000",'
        '"smsMessage":"lorem ipsum"'
        '}'
    )
    if args[1]['phoneNumber'] == FORCE_ERROR_NUMBER:
        return_text = (
            '{'
            '"responseCode":120,'
            '"responseMessage":"message too long.",'
            '"phoneNumber":"+810000000000",'
            '"smsMessage":"lorem ipsum"'
            '}'
        )
    return MockResponse(200, return_text)


class HoyoMailCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.hoyo_mail_data: HoyoMail = HoyoMailFactory.build(hoyo=HoyoFactory())
        self.hoyo_mail_target_data_1 = HoyoMailTargetFactory.build(seko=SekoFactory())
        self.hoyo_mail_target_data_2 = HoyoMailTargetFactory.build(seko=SekoFactory())
        self.hoyo_mail_target_data_3 = HoyoMailTargetFactory.build(seko=SekoFactory())

        for hoyo_mail_target_data in [
            self.hoyo_mail_target_data_1,
            self.hoyo_mail_target_data_2,
            self.hoyo_mail_target_data_3,
        ]:
            seko = hoyo_mail_target_data.seko
            MoshuFactory(seko=seko)
            KojinFactory(seko=seko, kojin_num=1)
            KojinFactory(seko=seko, kojin_num=2)
            SmsAccountFactory(company=seko.seko_company)
            SekoContactFactory(seko=seko)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        seko_ids: List[Dict] = [
            self.hoyo_mail_target_data_1.seko.pk,
            self.hoyo_mail_target_data_2.seko.pk,
            self.hoyo_mail_target_data_3.seko.pk,
        ]

        return {
            'hoyo': self.hoyo_mail_data.hoyo.pk,
            'select_ts': self.hoyo_mail_data.select_ts.isoformat(),
            'send_ts': self.hoyo_mail_data.send_ts.isoformat(),
            'content': self.hoyo_mail_data.content,
            'note': self.hoyo_mail_data.note,
            'target_seko_list': seko_ids,
        }

    @mock.patch('utils.sms.requests.post', side_effect=mocked_post)
    def test_hoyo_mail_create_succeed(self, mock_post) -> None:
        """法要施行案内メールを追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['id'])
        self.assertEqual(record['hoyo']['id'], self.hoyo_mail_data.hoyo.pk)
        self.assertEqual(record['select_ts'], self.hoyo_mail_data.select_ts.isoformat())
        self.assertEqual(record['send_ts'], self.hoyo_mail_data.send_ts.isoformat())
        self.assertEqual(record['content'], self.hoyo_mail_data.content)
        self.assertEqual(record['staff']['id'], self.staff.pk)
        self.assertEqual(record['note'], self.hoyo_mail_data.note)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

        correct_targets: List[HoyoMailTarget] = [
            self.hoyo_mail_target_data_1,
            self.hoyo_mail_target_data_2,
            self.hoyo_mail_target_data_3,
        ]
        self.assertEqual(mock_post.call_count, 3)
        for rec_target, correct_target in zip(record['targets'], correct_targets):
            self.assertEqual(rec_target['seko']['id'], correct_target.seko.pk)
            self.assertIsNotNone(rec_target['created_at'])
            self.assertIsNotNone(rec_target['updated_at'])

    @mock.patch('utils.sms.requests.post', side_effect=mocked_post)
    def test_hoyo_mail_create_sms_content(self, mock_post) -> None:
        """法要施行案内メール追加APIのSMS送信本文確認"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['target_seko_list'] = params['target_seko_list'][0:1]
        params['content'] = 'moshu=@m,kojin1=@k1,kojin2=@k2'
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        seko = self.hoyo_mail_target_data_1.seko
        kojin_names = [kojin.name for kojin in seko.kojin.order_by('kojin_num')]
        self.assertEqual(mock_post.call_count, 1)
        self.assertEqual(
            mock_post.call_args.args[1]['message'],
            f'moshu={seko.seko_contact.name},kojin1={kojin_names[0]},kojin2={kojin_names[1]}',
        )

    @mock.patch('utils.sms.requests.post', side_effect=mocked_post)
    def test_hoyo_mail_create_ignore_sms_failed_target(self, mock_post) -> None:
        """法要施行案内メール追加APIはSMS送信が失敗した対象者のレコードを作らず続行する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_mail_target_data_2.seko.seko_contact.mobile_num = FORCE_ERROR_NUMBER
        self.hoyo_mail_target_data_2.seko.seko_contact.save()

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertEqual(mock_post.call_count, 3)
        record: Dict = response.json()
        self.assertEqual(len(record['targets']), 2)

    @mock.patch('utils.sms.requests.post', side_effect=mocked_post)
    def test_hoyo_mail_create_success_without_hoyo(self, mock_post) -> None:
        """法要施行案内メール追加APIは法要がNoneでも成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['hoyo'] = None
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNone(record['hoyo'])

    @mock.patch('utils.sms.requests.post', side_effect=mocked_post)
    def test_hoyo_mail_create_success_without_select_ts(self, mock_post) -> None:
        """法要施行案内メール追加APIは抽出日時と送信日時が未入力でも成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params.pop('select_ts')
        params.pop('send_ts')
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['select_ts'])
        self.assertIsNotNone(record['send_ts'])

    @mock.patch('utils.sms.requests.post', side_effect=mocked_post)
    def test_hoyo_mail_create_success_when_select_ts_is_none(self, mock_post) -> None:
        """法要施行案内メール追加APIは抽出日時と送信日時がNoneを指定した場合は現在日時を入れる"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['select_ts'] = None
        params['send_ts'] = None
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['select_ts'])
        self.assertIsNotNone(record['send_ts'])

    @mock.patch('utils.sms.requests.post', side_effect=mocked_post)
    def test_hoyo_mail_create_ignore_missing_kojin(self, mock_post) -> None:
        """法要施行案内メール追加APIは施行の故人情報がなくても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_mail_target_data_2.seko.kojin.all().delete()

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertEqual(mock_post.call_count, 3)

    def test_hoyo_mail_create_fail_by_missing_sms_account(self) -> None:
        """法要施行案内メール追加APIは施行拠点のSMSアカウント情報が存在せず失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_mail_target_data_1.seko.seko_company.sms_account.delete()

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['target_seko_list'])

    def test_hoyo_mail_create_fail_by_missing_moshu(self) -> None:
        """法要施行案内メール追加APIは喪主情報が存在せず失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_mail_target_data_2.seko.moshu.delete()

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['target_seko_list'])

    # def test_hoyo_mail_create_fail_by_mobile_num_on_moshu(self) -> None:
    #     """法要施行案内メール追加APIは喪主情報に携帯番号が存在せず失敗する"""
    #     self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

    #     # 喪主は電話番号か携帯番号のどちらかはNoneになり得る
    #     self.hoyo_mail_target_data_3.seko.moshu.tel = '00000000000'
    #     self.hoyo_mail_target_data_3.seko.moshu.mobile_num = None
    #     self.hoyo_mail_target_data_3.seko.moshu.save()

    #     params: Dict = self.basic_params()
    #     response = self.api_client.post(
    #       reverse('hoyo:hoyo_mail_list'), data=params, format='json')
    #     self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    #     record: Dict = response.json()
    #     self.assertIsNotNone(record['target_seko_list'])

    def test_hoyo_mail_create_fail_with_non_existent_hoyo(self) -> None:
        """法要施行案内メール追加APIは存在しない法要IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        non_existent_hoyo = HoyoFactory.build()
        params['hoyo'] = non_existent_hoyo.pk
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['hoyo'])

    def test_hoyo_mail_create_fail_with_disabled_hoyo(self) -> None:
        """法要施行案内メール追加APIは無効化された法要IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_mail_data.hoyo.disable()

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['hoyo'])

    def test_hoyo_mail_create_fail_with_non_existent_seko(self) -> None:
        """法要施行案内メール追加APIは存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        non_existent_seko = SekoFactory.build()
        params['target_seko_list'][1] = non_existent_seko.pk
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['target_seko_list'])

    def test_hoyo_mail_create_fail_with_disabled_seko(self) -> None:
        """法要施行案内メール追加APIは無効化された施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_mail_target_data_2.seko.disable()

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['target_seko_list'])

    def test_hoyo_mail_create_failed_without_auth(self) -> None:
        """法要施行案内メール追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_mail_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
