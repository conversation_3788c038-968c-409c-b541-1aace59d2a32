# Generated by Django 3.1.14 on 2025-01-27 09:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0005_auto_20201130_1142'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentResult',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('contract_code', models.CharField(max_length=8, verbose_name='contract code')),
                ('order_number', models.IntegerField(verbose_name='ordernumber')),
                ('result_code', models.Char<PERSON>ield(max_length=1, verbose_name='result code')),
                ('err_code', models.TextField(blank=True, null=True, verbose_name='error code')),
                (
                    'err_detail',
                    models.TextField(blank=True, null=True, verbose_name='error detail'),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'db_table': 'tr_payment_result',
            },
        ),
    ]
