from typing import Dict
from unittest.mock import patch

import faker
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory
from invoices.models import SalesCompany
from invoices.tests.factories import SalesCompanyFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

fake_provider = faker.Faker('ja_JP')
jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class GatherSumSalesViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.sales_yymm = timezone.localdate().strftime('%Y%m')

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    @patch('invoices.views.connection')
    def test_gather_sales_succeed(self, mock_connection) -> None:
        """売上を集計するストアドを呼び出す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'sales_yymm': self.sales_yymm}
        response = self.api_client.post(
            reverse('invoices:gather_sales'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record, {})

    def test_gather_sales_fail_by_less_params(self) -> None:
        """売上集計がパラメータ不足で失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('invoices:gather_sales'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['sales_yymm'])

    def test_gather_sales_fail_by_illegal_params(self) -> None:
        """売上集計が不正なパラメータで失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 数値以外
        params: Dict = {'sales_yymm': 'abcdef'}
        response = self.api_client.post(
            reverse('invoices:gather_sales'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['sales_yymm'])

        # 存在しない月
        params: Dict = {'sales_yymm': '000099'}
        response = self.api_client.post(
            reverse('invoices:gather_sales'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['sales_yymm'])

        # 文字数不足
        params: Dict = {'sales_yymm': '20211'}
        response = self.api_client.post(
            reverse('invoices:gather_sales'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['sales_yymm'])

        # 文字数過多
        params: Dict = {'sales_yymm': '2021001'}
        response = self.api_client.post(
            reverse('invoices:gather_sales'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['sales_yymm'])


class FixSalesCompanyViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.sales_company: SalesCompany = SalesCompanyFactory(
            company_id=self.company.id,
            confirm_ts=None,
            confirm_staff_id=None,
            confirm_staff_name=None,
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_fix_sales_company_succeed(self) -> None:
        """売上集計を確定する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('invoices:sales_fix', kwargs={'pk': self.sales_company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertIsNotNone(record['confirm_ts'])
        self.assertEqual(record['confirm_staff_id'], self.staff.pk)
        self.assertEqual(record['confirm_staff_name'], self.staff.name)

    def test_fix_sales_company_fail_with_already_fixed(self) -> None:
        """売上集計確定APIが既に確定済みで失敗する"""
        self.sales_company.fix_invoice(self.staff)

        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('invoices:sales_fix', kwargs={'pk': self.sales_company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_fix_sales_company_fail_by_notfound(self) -> None:
        """売上集計確定APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_sales_company: SalesCompany = SalesCompanyFactory.build()
        params: Dict = {}
        response = self.api_client.post(
            reverse('invoices:sales_fix', kwargs={'pk': non_saved_sales_company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_fix_sales_company_deny_from_moshu(self) -> None:
        """売上集計確定APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('invoices:sales_fix', kwargs={'pk': self.sales_company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_fix_sales_company_failed_without_auth(self) -> None:
        """売上集計確定APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('invoices:sales_fix', kwargs={'pk': self.sales_company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
