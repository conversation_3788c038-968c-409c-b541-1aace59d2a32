from django.db import models
from django.utils.translation import gettext_lazy as _

from bases.models import Base
from utils.strings import extract_digits


class Supplier(models.Model):
    company = models.ForeignKey(
        Base, models.PROTECT, verbose_name=_('company'), related_name='suppliers'
    )
    name = models.TextField(_('name'))
    tel = models.CharField(_('tel no'), max_length=15)
    fax = models.CharField(_('fax no'), max_length=15)
    zip_code = models.CharField(_('zipcode'), max_length=7)
    address = models.TextField(_('address'))
    mail_address = models.TextField(_('mail address'), blank=True, null=True)
    order_mail_send_flg = models.BooleanField(_('order mail send flag'), default=False)
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_supplier'

    def disable(self) -> None:
        """発注先を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()

    def fax_digits(self) -> str:
        """FAX番号の文字列から数値以外を除去した文字列を返します"""
        return extract_digits(self.fax)
