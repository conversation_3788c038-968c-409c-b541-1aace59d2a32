from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class SupplierListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company1 = BaseFactory(base_type=Base.OrgType.COMPANY)

        self.supplier_11 = SupplierFactory(company=self.company1)
        self.supplier_12 = SupplierFactory(company=self.company1)
        self.supplier_13 = SupplierFactory(company=self.company1)

        company2 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.supplier_21 = SupplierFactory(company=company2)
        self.supplier_22 = SupplierFactory(company=company2)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_supplier_list_succeed(self) -> None:
        """発注先一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('suppliers:supplier_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

    def test_supplier_list_succeed_by_company(self) -> None:
        """拠点で絞り込みした発注先一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company': self.company1.pk,
        }
        response = self.api_client.get(
            reverse('suppliers:supplier_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        for record in records:
            self.assertEqual(record['company'], self.company1.pk)

    def test_supplier_list_ignores_deleted(self) -> None:
        """発注先一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.supplier_11.disable()
        self.supplier_12.disable()
        self.supplier_21.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('suppliers:supplier_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)
        supplier_dict13: Dict = records[0]
        supplier_dict22: Dict = records[1]
        self.assertEqual(supplier_dict13.get('name'), self.supplier_13.name)
        self.assertEqual(supplier_dict22.get('name'), self.supplier_22.name)

    def test_supplier_list_failed_without_auth(self) -> None:
        """発注先一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('suppliers:supplier_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SupplierCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        company = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.supplier_data = SupplierFactory.build(company=company)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.supplier_data.company.id,
            'name': self.supplier_data.name,
            'tel': self.supplier_data.tel,
            'fax': self.supplier_data.fax,
            'zip_code': self.supplier_data.zip_code,
            'address': self.supplier_data.address,
            'del_flg': self.supplier_data.del_flg,
        }

    def test_supplier_create_succeed(self) -> None:
        """発注先を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('suppliers:supplier_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()

        for attr, value in params.items():
            self.assertEqual(value, record[attr])
        self.assertEqual(record.get('create_user_id'), self.staff.id)
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_supplier_create_failed_without_auth(self) -> None:
        """発注先追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}

        response = self.api_client.post(
            reverse('suppliers:supplier_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
