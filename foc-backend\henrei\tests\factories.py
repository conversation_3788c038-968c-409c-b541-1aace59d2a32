from typing import Optional

import factory
import factory.fuzzy as fuzzy
import faker
from django.utils import timezone
from factory.django import DjangoModelFactory

from henrei.models import HenreihinKoden, HenreihinKumotsu, OrderHenreihin
from items.tests.factories import ItemFactory
from orders.tests.factories import EntryDetailKodenFactory, EntryDetailKumotsuFactory
from seko.tests.factories import SekoFactory
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory

fake_provider = faker.Faker('ja_JP')
tz = timezone.get_current_timezone()


class OrderHenreihinFactory(DjangoModelFactory):
    class Meta:
        model = OrderHenreihin

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    supplier = factory.SubFactory(SupplierFactory)
    order_ts: Optional[timezone.datetime] = None
    order_staff = factory.SubFactory(StaffFactory)
    order_status = 0
    order_note = factory.Faker('sentence', locale='ja_JP')
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class HenreihinKodenFactory(DjangoModelFactory):
    class Meta:
        model = HenreihinKoden

    detail_koden = factory.SubFactory(EntryDetailKodenFactory)
    henreihin = factory.SubFactory(ItemFactory)
    henreihin_hinban = factory.Faker('ean', locale='ja_JP', length=8)
    henreihin_name = factory.Faker('word', locale='ja_JP')
    henreihin_price = factory.Faker('pyint', min_value=0, max_value=9999999)
    henreihin_tax = factory.Faker('pyint', min_value=0, max_value=9999999)
    henreihin_tax_pct = factory.Faker('pyint', min_value=0, max_value=99)
    keigen_flg = fuzzy.FuzzyChoice([True, False])
    customer_self_select_flg = fuzzy.FuzzyChoice([True, False])
    select_status = fuzzy.FuzzyChoice([2, 0])
    henrei_order = None
    supplier = factory.SubFactory(SupplierFactory)
    order_status = 0
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class HenreihinKumotsuFactory(DjangoModelFactory):
    class Meta:
        model = HenreihinKumotsu

    detail_kumotsu = factory.SubFactory(EntryDetailKumotsuFactory)
    henreihin = factory.SubFactory(ItemFactory)
    henreihin_hinban = factory.Faker('ean', locale='ja_JP', length=8)
    henreihin_name = factory.Faker('word', locale='ja_JP')
    henreihin_price = factory.Faker('pyint', min_value=0, max_value=9999999)
    henreihin_tax = factory.Faker('pyint', min_value=0, max_value=9999999)
    henreihin_tax_pct = factory.Faker('pyint', min_value=0, max_value=99)
    keigen_flg = fuzzy.FuzzyChoice([True, False])
    select_status = fuzzy.FuzzyChoice([2, 0])
    henrei_order = None
    supplier = factory.SubFactory(SupplierFactory)
    order_status = 0
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
