server {
  listen 80;
  client_max_body_size 3g;
  keepalive_timeout 0;
  add_header X-Frame-Options SAMEORIGIN;
  add_header X-Content-Type-Options nosniff;
  add_header X-XSS-Protection "1; mode=block";
  add_header Cache-Control "no-store";

  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri $uri/ /index.html =404;
  }

  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/share/nginx/html;
  }

  location ~ ^/(${URLS})/ {
    proxy_pass http://${DAPHNE_HOST}:${DAPHNE_PORT};
    proxy_http_version 1.1;
    proxy_read_timeout ${TIMEOUT};
  }

}
