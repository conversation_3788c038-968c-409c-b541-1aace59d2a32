# Generated by Django 3.1.7 on 2021-08-26 07:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('masters', '0015_sokemenu'),
        ('bases', '0018_focfee_gmo_test_flg'),
    ]

    operations = [
        migrations.CreateModel(
            name='BaseSokeMenu',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'base',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='bases.base',
                        verbose_name='base',
                    ),
                ),
                (
                    'soke_menu',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='masters.sokemenu',
                        verbose_name='soke_menu',
                    ),
                ),
            ],
            options={
                'db_table': 'm_base_soke_menu',
            },
        ),
        migrations.AddField(
            model_name='base',
            name='soke_menus',
            field=models.ManyToManyField(
                related_name='bases',
                through='bases.BaseSokeMenu',
                to='masters.SokeMenu',
                verbose_name='soke menu',
            ),
        ),
    ]
