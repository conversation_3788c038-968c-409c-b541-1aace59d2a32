import base64
from datetime import date
from decimal import Decimal

import faker
from django.test import TestCase
from django.utils import timezone

from items.models import Item
from items.tests.factories import ItemFactory
from masters.models import Service, Wareki
from masters.tests.factories import ChobunDaishiMasterFactory, ServiceFactory, WarekiFactory
from orders.models import EntryDetail
from orders.templatetags import entry_complete_extras, orders_filters
from orders.tests.factories import EntryDetailFactory, EntryDetailKodenFactory

fake_provider = faker.Faker('ja_JP')


class FormatHonbunTest(TestCase):
    def test_format_honbun(self) -> None:
        """段落から文節一覧を返す"""

        text = fake_provider.text(max_nb_chars=1000)
        result = orders_filters.format_honbun(text)

        self.assertLessEqual(len(result), 10)
        for txt in result:
            self.assertLessEqual(len(result), 21)

    def test_format_honbun_with_blank(self) -> None:
        """空の段落から文節一覧を返す"""

        result = orders_filters.format_honbun('')
        self.assertEqual(result, [])


class VerticalTextTest(TestCase):
    def test_vertical_text(self) -> None:
        """文節から縦書き文節を返す"""

        text = 'サッカー－-、。'
        result = orders_filters.vertical_text(text)

        self.assertEqual(result, 'サ ッ カ ｜ ｜ ｜ <div> `</div> <div>゜</div>')

    def test_vertical_text_with_None(self) -> None:
        """文節から縦書き文節を返す"""

        result = orders_filters.vertical_text(None)
        self.assertEqual(result, '')

    def test_format_honbun_with_blank(self) -> None:
        """空の文節から縦書き文節を返す"""

        result = orders_filters.vertical_text('')
        self.assertEqual(result, '')


class WarekiDayTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

        self.wareki = WarekiFactory(
            begin_date=timezone.now() - timezone.timedelta(days=365),
            end_date=timezone.now() + timezone.timedelta(days=365),
        )

    def test_wareki_day(self) -> None:
        """日付から和暦表示の年を返す"""

        the_date: date = timezone.now()
        year_label: str = Wareki.int2kanji(the_date.year - self.wareki.begin_date.year + 1)
        month = Wareki.int2kanji(the_date.month)
        day = Wareki.int2kanji(the_date.day)
        self.assertEqual(
            orders_filters.wareki_day(the_date),
            f'{self.wareki.name}{year_label}年{month}月{day}日',
        )

    def test_wareki_day_failed_by_value(self) -> None:
        """最小の元号より前はValueError"""

        the_date: date = timezone.now()
        with self.assertRaises(ValueError):
            orders_filters.wareki_day(the_date - timezone.timedelta(days=366))


class Base64EncodeTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

        self.chobun_daishi = ChobunDaishiMasterFactory()

    def test_base64_encode(self) -> None:
        """base64エンコードされた画像を返す"""

        with open(self.chobun_daishi.file_name.path, 'rb') as image_file:
            encoded_img = base64.b64encode(image_file.read()).decode('ascii')

        self.assertEqual(
            orders_filters.base64_encode(self.chobun_daishi.file_name.path), encoded_img
        )

    def test_base64_encode_success_none_path(self) -> None:
        """Noneでbase64エンコードして成功する"""

        self.assertEqual(orders_filters.base64_encode(None), '')


class EntryDetailContentTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

        service: Service = ServiceFactory(name='弔文')
        item: Item = ItemFactory(service=service)
        self.entry_detail: EntryDetail = EntryDetailFactory(
            quantity=3, item=item, item_name='弔文+線香セット', item_unit_price=1500, keigen_flg=False
        )

    def test_entry_detail_content_simple(self) -> None:
        """申込完了メール申込内容シンプルパターン"""

        result: str = entry_complete_extras.entry_detail_content(self.entry_detail)
        correct: str = """\
    弔文 | 弔文+線香セット
                                        数量:3                  \\4,500"""
        self.assertEqual(result, correct)

    def test_entry_detail_content_keigen_enabled(self) -> None:
        """申込完了メール申込内容軽減税率あり"""

        self.entry_detail.keigen_flg = True
        self.entry_detail.save()

        result: str = entry_complete_extras.entry_detail_content(self.entry_detail)
        correct: str = """\
    弔文 | ※弔文+線香セット
                                        数量:3                  \\4,500"""
        self.assertEqual(result, correct)

    def test_entry_detail_content_with_koden(self) -> None:
        """申込完了メール申込内容香典あり"""

        EntryDetailKodenFactory(
            entry_detail=self.entry_detail,
            koden_commission=500,
            koden_commission_tax=45,
            koden_commission_tax_pct=10,
            tax_adjust=1,
        )

        result: str = entry_complete_extras.entry_detail_content(self.entry_detail)
        correct: str = """\
    弔文 | 弔文+線香セット
                                        数量:3                  \\4,500
    ご香典システム利用料
                                        数量:1                    \\500"""
        self.assertEqual(result, correct)


class CurrencyFormatTest(TestCase):
    def test_currency(self) -> None:
        """数値を円マークと3桁区切り文字列に変換する"""
        self.assertEqual(entry_complete_extras.currency(1234567), '\\1,234,567')
        self.assertEqual(entry_complete_extras.currency(0), '\\0')
        self.assertEqual(entry_complete_extras.currency(Decimal('1234567')), '\\1,234,567')
        self.assertEqual(entry_complete_extras.currency(Decimal('0')), '\\0')
