
<div class="ui selection dropdown fluid" [class.disabled]="disabled" [class.search]="settings?.allowAdditions || !settings?.disableSearch">
  <input type="hidden">
  <i class="dropdown icon"></i>
  <div class="text" [class.disabled]="disabled"></div>
  <input class="search" autocomplete="off" tabindex="0" *ngIf="settings?.allowAdditions" (keyup)="onKeyup($event)" (change)="onInputChange($event)">
</div>

<!--
<div class="ui selection dropdown"> <input type="hidden" [(ngModel)]="inputText">
  <input type="hidden" value="selectedData?.value">
  <i class="dropdown icon"></i>
  <div class="text">{{selectedData?.text}}</div>
  <div class="menu">
    <div class="item" data-value="item.value" *ngFor="let item of data">{{item.text}}</div>
  </div>
</div>

<div class="ui normal selection dropdown clearable">
  <input type="hidden" [(ngModel)]="orderEmp">
  <i class="dropdown icon"></i>
  <div class="text"></div>
  <div class="menu">
    <div class="item" data-value="1">担当者1</div>
    <div class="item" data-value="2">担当者2</div>
  </div>
</div>

<div class="ui search selection dropdown unclearable">
  <input type="hidden">
  <i class="dropdown icon"></i>
  <div class="text"></div>
  <div class="menu">
    <div class="item" data-value="1">2</div>
    <div class="item" data-value="2">3</div>
  </div>
</div>

<div class="ui search selection dropdown clearable tiny">
  <input type="hidden" name="employee">
  <i class="dropdown icon"></i>
  <div class="text"></div>
  <div class="menu">
    <div class="item" data-value="1">長男</div>
    <div class="item" data-value="2">次男</div>
  </div>
</div>
-->
