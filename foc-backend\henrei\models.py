from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Q, Value
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from items.models import Item
from orders.models import EntryDetailKoden, EntryDetailKumotsu
from seko.models import Seko
from staffs.models import Staff
from suppliers.models import Supplier


class OrderHenreihinListManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .select_related('seko', 'seko__moshu', 'supplier')
            .prefetch_related('seko__kojin', 'seko__schedules', 'henrei_koden', 'henrei_kumotsu')
            .filter(
                Q(seko__del_flg=False)
                & Q(seko__kojin__kojin_num=1)
                & Q(seko__schedules__schedule_id=2)
            )
            .annotate(hall_name=F('seko__schedules__hall_name'), kojin_name=F('seko__kojin__name'))
        )


class OrderHenreihin(models.Model):
    seko = models.ForeignKey(
        Seko, models.PROTECT, verbose_name=_('seko'), related_name='henrei_orders'
    )
    supplier = models.ForeignKey(Supplier, models.PROTECT, blank=True, null=True)
    order_ts = models.DateTimeField(_('ordered at'), blank=True, null=True)
    order_staff = models.ForeignKey(
        Staff, models.PROTECT, verbose_name=_('order staff'), blank=True, null=True
    )
    order_status = models.IntegerField(_('order status'))
    order_note = models.TextField(_('order note'), blank=True, null=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    objects = models.Manager()
    order_list = OrderHenreihinListManager()

    class Meta:
        db_table = 'tr_order_henreihin'

    def set_order_status(self, new_state: int, staff: Staff) -> None:
        self.order_status = new_state
        if new_state == 2 and self.order_ts is None:
            self.order_ts = timezone.localtime()
            self.order_staff = staff
        self.save()
        for koden in self.henrei_koden.all():
            koden.order_status = new_state
            koden.save()
        for kumotsu in self.henrei_kumotsu.all():
            kumotsu.order_status = new_state
            kumotsu.save()


class HenreiKodenListManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .select_related(
                'detail_koden',
                'detail_koden__entry_detail',
                'detail_koden__entry_detail__entry',
                'detail_koden__entry_detail__entry__seko',
                'detail_koden__entry_detail__entry__seko__moshu',
                'detail_koden__entry_detail__entry__seko__seko_department',
                'supplier',
            )
            .filter(
                Q(select_status=2)
                & Q(detail_koden__entry_detail__entry__seko__del_flg=False)
                & Q(detail_koden__entry_detail__entry__seko__kojin__kojin_num=1)
                & Q(detail_koden__entry_detail__entry__seko__schedules__schedule_id=2)
            )
            .annotate(
                henreihin_type=Value(1, IntegerField()),
                entry_id=F('detail_koden__entry_detail__entry'),
                detail_id=F('pk'),
                seko_id=F('detail_koden__entry_detail__entry__seko_id'),
                seko_date=F('detail_koden__entry_detail__entry__seko__seko_date'),
                seko_department_id=F(
                    'detail_koden__entry_detail__entry__seko__seko_department_id'
                ),
                seko_department_name=F(
                    'detail_koden__entry_detail__entry__seko__seko_department__base_name'
                ),
                hall_name=Max('detail_koden__entry_detail__entry__seko__schedules__hall_name'),
                soke_name=F('detail_koden__entry_detail__entry__seko__soke_name'),
                kojin_name=Max('detail_koden__entry_detail__entry__seko__kojin__name'),
                moshu_name=F('detail_koden__entry_detail__entry__seko__moshu__name'),
                supplier_name=F('supplier__name'),
                cancel_ts=F('detail_koden__entry_detail__entry__cancel_ts'),
                company_id=F('detail_koden__entry_detail__entry__seko__seko_company_id'),
                hall_id=Max('detail_koden__entry_detail__entry__seko__schedules__hall_id'),
            )
        )


class HenreiKodenItemsManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .select_related(
                'detail_koden__entry_detail',
                'detail_koden__entry_detail__entry',
                'henrei_order',
            )
            .annotate(
                entry_zip_code=F('detail_koden__entry_detail__entry__entry_zip_code'),
                entry_prefecture=F('detail_koden__entry_detail__entry__entry_prefecture'),
                entry_address_1=F('detail_koden__entry_detail__entry__entry_address_1'),
                entry_address_2=F('detail_koden__entry_detail__entry__entry_address_2'),
                entry_address_3=F('detail_koden__entry_detail__entry__entry_address_3'),
                entry_name=F('detail_koden__entry_detail__entry__entry_name'),
                entry_tel=F('detail_koden__entry_detail__entry__entry_tel'),
                entry_id=F('detail_koden__entry_detail__entry_id'),
            )
        )


class FdnHenreiKodenListManager(models.Manager):
    def get_queryset(self, company_id):
        return (
            super()
            .get_queryset()
            .select_related(
                'detail_koden',
                'detail_koden__henreihin',
                'detail_koden__entry_detail',
                'detail_koden__entry_detail__entry',
                'detail_koden__entry_detail__entry__seko',
                'detail_koden__entry_detail__entry__seko__moshu',
                'detail_koden__entry_detail__entry__seko__kojin',
            )
            .filter(
                Q(select_status=2)
                & Q(detail_koden__entry_detail__entry__seko__seko_company=company_id)
                & Q(detail_koden__entry_detail__entry__seko__del_flg=False)
                & Q(detail_koden__entry_detail__entry__seko__kojin__kojin_num=1)
            )
            .annotate(
                seko_id=F('detail_koden__entry_detail__entry__seko_id'),
                term=F('updated_at'),
                fdn_seko_code=F('detail_koden__entry_detail__entry__seko__fdn_code'),
                soke_name=F('detail_koden__entry_detail__entry__seko__soke_name'),
                kojin_name=F('detail_koden__entry_detail__entry__seko__kojin__name'),
                kojin_birth_date=F('detail_koden__entry_detail__entry__seko__kojin__birth_date'),
                kojin_death_date=F('detail_koden__entry_detail__entry__seko__kojin__death_date'),
                moshu_name=F('detail_koden__entry_detail__entry__seko__moshu__name'),
                moshu_tel=F('detail_koden__entry_detail__entry__seko__moshu__tel'),
                moshu_mobile=F('detail_koden__entry_detail__entry__seko__moshu__mobile_num'),
                entry_id=F('detail_koden__entry_detail__entry'),
                entry_name=F('detail_koden__entry_detail__entry__entry_name'),
                entry_name_kana=F('detail_koden__entry_detail__entry__entry_name_kana'),
                entry_zip_code=F('detail_koden__entry_detail__entry__entry_zip_code'),
                entry_prefecture=F('detail_koden__entry_detail__entry__entry_prefecture'),
                entry_address_1=F('detail_koden__entry_detail__entry__entry_address_1'),
                entry_address_2=F('detail_koden__entry_detail__entry__entry_address_2'),
                entry_address_3=F('detail_koden__entry_detail__entry__entry_address_3'),
                entry_tel=F('detail_koden__entry_detail__entry__entry_tel'),
                entry_mail_address=F('detail_koden__entry_detail__entry__entry_mail_address'),
                entry_ts=F('detail_koden__entry_detail__entry__entry_ts'),
                cancel_ts=F('detail_koden__entry_detail__entry__cancel_ts'),
                service_id=Value(42, IntegerField()),
                service_name=Value('返礼品（香典）', CharField()),
                entry_detail_id=F('detail_koden__entry_detail'),
                item_id=F('henreihin'),
                fdn_item_code=F('henreihin__fdn_code'),
                item_hinban=F('henreihin_hinban'),
                item_name=F('henreihin_name'),
                item_unit_price=F('henreihin_price'),
                quantity=Value(1, IntegerField()),
                item_tax=F('henreihin_tax'),
                item_tax_adjust=Value(0, IntegerField()),
                item_tax_pct=F('henreihin_tax_pct'),
                okurinushi_company=Value(None, CharField()),
                okurinushi_company_kana=Value(None, CharField()),
                okurinushi_title=Value(None, CharField()),
                okurinushi_name=Value(None, CharField()),
                okurinushi_zip_code=Value(None, CharField()),
                okurinushi_prefecture=Value(None, CharField()),
                okurinushi_address_1=Value(None, CharField()),
                okurinushi_address_2=Value(None, CharField()),
                okurinushi_address_3=Value(None, CharField()),
                okurinushi_tel=Value(None, CharField()),
                renmei1=Value(None, CharField()),
                renmei2=Value(None, CharField()),
                update_ts=F('updated_at'),
            )
        )


class HenreihinKoden(models.Model):
    detail_koden = models.OneToOneField(
        EntryDetailKoden,
        models.CASCADE,
        verbose_name=_('entry detail koden'),
        related_name='henrei_koden',
        db_column='id',
        primary_key=True,
    )
    henreihin = models.ForeignKey(Item, models.PROTECT, verbose_name=_('henrei item'))
    henreihin_hinban = models.TextField(_('item hinban'))
    henreihin_name = models.TextField(_('item name'))
    henreihin_price = models.IntegerField(_('item price'))
    henreihin_tax = models.IntegerField(_('item tax'))
    henreihin_tax_pct = models.IntegerField(_('item tax percentage'))
    keigen_flg = models.BooleanField(_('tax reduced'))
    customer_self_select_flg = models.BooleanField(_('selected by customer self'))
    select_status = models.IntegerField(_('select status'))
    henrei_order = models.ForeignKey(
        OrderHenreihin,
        models.PROTECT,
        verbose_name=_('henrei order'),
        related_name='henrei_koden',
        blank=True,
        null=True,
    )
    supplier = models.ForeignKey(
        Supplier, models.PROTECT, verbose_name=_('supplier'), blank=True, null=True
    )
    order_status = models.IntegerField(_('order status'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    objects = models.Manager()
    henrei_list = HenreiKodenListManager()
    henrei_items = HenreiKodenItemsManager()
    fdn_henrei_list = FdnHenreiKodenListManager()

    class Meta:
        db_table = 'tr_henreihin_koden'


class HenreiKumotsuListManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .select_related(
                'detail_kumotsu',
                'detail_kumotsu__entry_detail',
                'detail_kumotsu__entry_detail__entry',
                'detail_kumotsu__entry_detail__entry__seko',
                'detail_kumotsu__entry_detail__entry__seko__moshu',
                'detail_kumotsu__entry_detail__entry__seko__seko_department',
                'supplier',
            )
            .filter(
                Q(select_status=2)
                & Q(detail_kumotsu__entry_detail__entry__seko__del_flg=False)
                & Q(detail_kumotsu__entry_detail__entry__seko__kojin__kojin_num=1)
                & Q(detail_kumotsu__entry_detail__entry__seko__schedules__schedule_id=2)
            )
            .annotate(
                henreihin_type=Value(2, IntegerField()),
                entry_id=F('detail_kumotsu__entry_detail__entry'),
                detail_id=F('pk'),
                seko_id=F('detail_kumotsu__entry_detail__entry__seko_id'),
                seko_date=F('detail_kumotsu__entry_detail__entry__seko__seko_date'),
                seko_department_id=F(
                    'detail_kumotsu__entry_detail__entry__seko__seko_department_id'
                ),
                seko_department_name=F(
                    'detail_kumotsu__entry_detail__entry__seko__seko_department__base_name'
                ),
                hall_name=Max('detail_kumotsu__entry_detail__entry__seko__schedules__hall_name'),
                soke_name=F('detail_kumotsu__entry_detail__entry__seko__soke_name'),
                kojin_name=Max('detail_kumotsu__entry_detail__entry__seko__kojin__name'),
                moshu_name=F('detail_kumotsu__entry_detail__entry__seko__moshu__name'),
                supplier_name=F('supplier__name'),
                cancel_ts=F('detail_kumotsu__entry_detail__entry__cancel_ts'),
                company_id=F('detail_kumotsu__entry_detail__entry__seko__seko_company_id'),
                hall_id=Max('detail_kumotsu__entry_detail__entry__seko__schedules__hall_id'),
            )
        )


class HenreiKumotsuItemsManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .select_related(
                'detail_kumotsu__entry_detail',
                'detail_kumotsu__entry_detail__entry',
                'henrei_order',
            )
            .annotate(
                entry_zip_code=F('detail_kumotsu__entry_detail__entry__entry_zip_code'),
                entry_prefecture=F('detail_kumotsu__entry_detail__entry__entry_prefecture'),
                entry_address_1=F('detail_kumotsu__entry_detail__entry__entry_address_1'),
                entry_address_2=F('detail_kumotsu__entry_detail__entry__entry_address_2'),
                entry_address_3=F('detail_kumotsu__entry_detail__entry__entry_address_3'),
                entry_name=F('detail_kumotsu__entry_detail__entry__entry_name'),
                entry_tel=F('detail_kumotsu__entry_detail__entry__entry_tel'),
                entry_id=F('detail_kumotsu__entry_detail__entry_id'),
            )
        )


class FdnHenreiKumotsuListManager(models.Manager):
    def get_queryset(self, company_id):
        return (
            super()
            .get_queryset()
            .select_related(
                'detail_kumotsu',
                'detail_kumotsu__henreihin',
                'detail_kumotsu__entry_detail',
                'detail_kumotsu__entry_detail__entry',
                'detail_kumotsu__entry_detail__entry__seko',
                'detail_kumotsu__entry_detail__entry__seko__moshu',
                'detail_kumotsu__entry_detail__entry__seko__kojin',
            )
            .filter(
                Q(select_status=2)
                & Q(detail_kumotsu__entry_detail__entry__seko__seko_company=company_id)
                & Q(detail_kumotsu__entry_detail__entry__seko__del_flg=False)
                & Q(detail_kumotsu__entry_detail__entry__seko__kojin__kojin_num=1)
            )
            .annotate(
                seko_id=F('detail_kumotsu__entry_detail__entry__seko_id'),
                term=F('updated_at'),
                fdn_seko_code=F('detail_kumotsu__entry_detail__entry__seko__fdn_code'),
                soke_name=F('detail_kumotsu__entry_detail__entry__seko__soke_name'),
                kojin_name=F('detail_kumotsu__entry_detail__entry__seko__kojin__name'),
                kojin_birth_date=F('detail_kumotsu__entry_detail__entry__seko__kojin__birth_date'),
                kojin_death_date=F('detail_kumotsu__entry_detail__entry__seko__kojin__death_date'),
                moshu_name=F('detail_kumotsu__entry_detail__entry__seko__moshu__name'),
                moshu_tel=F('detail_kumotsu__entry_detail__entry__seko__moshu__tel'),
                moshu_mobile=F('detail_kumotsu__entry_detail__entry__seko__moshu__mobile_num'),
                entry_id=F('detail_kumotsu__entry_detail__entry'),
                entry_name=F('detail_kumotsu__entry_detail__entry__entry_name'),
                entry_name_kana=F('detail_kumotsu__entry_detail__entry__entry_name_kana'),
                entry_zip_code=F('detail_kumotsu__entry_detail__entry__entry_zip_code'),
                entry_prefecture=F('detail_kumotsu__entry_detail__entry__entry_prefecture'),
                entry_address_1=F('detail_kumotsu__entry_detail__entry__entry_address_1'),
                entry_address_2=F('detail_kumotsu__entry_detail__entry__entry_address_2'),
                entry_address_3=F('detail_kumotsu__entry_detail__entry__entry_address_3'),
                entry_tel=F('detail_kumotsu__entry_detail__entry__entry_tel'),
                entry_mail_address=F('detail_kumotsu__entry_detail__entry__entry_mail_address'),
                entry_ts=F('detail_kumotsu__entry_detail__entry__entry_ts'),
                cancel_ts=F('detail_kumotsu__entry_detail__entry__cancel_ts'),
                service_id=Value(41, IntegerField()),
                service_name=Value('返礼品（供花供物）', CharField()),
                entry_detail_id=F('detail_kumotsu__entry_detail'),
                item_id=F('henreihin'),
                fdn_item_code=F('henreihin__fdn_code'),
                item_hinban=F('henreihin_hinban'),
                item_name=F('henreihin_name'),
                item_unit_price=F('henreihin_price'),
                quantity=Value(1, IntegerField()),
                item_tax=F('henreihin_tax'),
                item_tax_adjust=Value(0, IntegerField()),
                item_tax_pct=F('henreihin_tax_pct'),
                okurinushi_company=F('detail_kumotsu__okurinushi_company'),
                okurinushi_company_kana=F('detail_kumotsu__okurinushi_company_kana'),
                okurinushi_title=F('detail_kumotsu__okurinushi_title'),
                okurinushi_name=F('detail_kumotsu__okurinushi_name'),
                okurinushi_zip_code=F('detail_kumotsu__okurinushi_zip_code'),
                okurinushi_prefecture=F('detail_kumotsu__okurinushi_prefecture'),
                okurinushi_address_1=F('detail_kumotsu__okurinushi_address_1'),
                okurinushi_address_2=F('detail_kumotsu__okurinushi_address_2'),
                okurinushi_address_3=F('detail_kumotsu__okurinushi_address_3'),
                okurinushi_tel=F('detail_kumotsu__okurinushi_tel'),
                renmei1=F('detail_kumotsu__renmei1'),
                renmei2=F('detail_kumotsu__renmei2'),
                update_ts=F('updated_at'),
            )
        )


class HenreihinKumotsu(models.Model):
    detail_kumotsu = models.OneToOneField(
        EntryDetailKumotsu,
        models.CASCADE,
        verbose_name=_('entry detail kumotsu'),
        related_name='henrei_kumotsu',
        db_column='id',
        primary_key=True,
    )
    henreihin = models.ForeignKey(Item, models.PROTECT, verbose_name=_('henrei item'))
    henreihin_hinban = models.TextField(_('item hinban'))
    henreihin_name = models.TextField(_('item name'))
    henreihin_price = models.IntegerField(_('item price'))
    henreihin_tax = models.IntegerField(_('item tax'))
    henreihin_tax_pct = models.IntegerField(_('item tax percentage'))
    keigen_flg = models.BooleanField(_('tax reduced'))
    select_status = models.IntegerField(_('select status'))
    henrei_order = models.ForeignKey(
        OrderHenreihin,
        models.PROTECT,
        verbose_name=_('henrei order'),
        related_name='henrei_kumotsu',
        blank=True,
        null=True,
    )
    supplier = models.ForeignKey(
        Supplier, models.PROTECT, verbose_name=_('supplier'), blank=True, null=True
    )
    order_status = models.IntegerField(_('order status'), default=0)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    objects = models.Manager()
    henrei_list = HenreiKumotsuListManager()
    henrei_items = HenreiKumotsuItemsManager()
    fdn_henrei_list = FdnHenreiKumotsuListManager()

    class Meta:
        db_table = 'tr_henreihin_kumotsu'
