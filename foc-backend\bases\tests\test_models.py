from django.test import TestCase

from bases.models import Base
from bases.tests.factories import BaseFactory
from seko.tests.factories import SekoFactory


class BaseModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base: Base = BaseFactory(del_flg=False)

    def test_base_disable(self) -> None:
        """拠点を無効化(論理削除)する"""
        self.base.disable()
        self.assertTrue(self.base.del_flg)

    def test_seko_inquiries(self) -> None:
        """拠点が担当する施行問合せを集計する"""
        # seko_1 = SekoFactory(seko_company=self.base)
        SekoFactory(seko_company=self.base)
        self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 0, 'unanswerd_af': 0})
        # SekoInquiryFactory(seko=seko_1)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 0, 'unanswerd_af': 0})
        # inquiry_1 = SekoInquiryFactory(seko=seko_1)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 2, 'unanswerd_af': 0})
        # SekoAnswerFactory(inquiry=inquiry_1)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 1, 'unanswerd_af': 0})
        # SekoAnswerFactory(inquiry=inquiry_1)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 1, 'unanswerd_af': 0})

        # seko_2 = SekoFactory(seko_company=self.base)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 1, 'unanswerd_af': 0})
        # inquiry_2 = SekoInquiryFactory(seko=seko_2)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 2, 'unanswerd_af': 0})
        # SekoAnswerFactory(inquiry=inquiry_2)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 1, 'unanswerd_af': 0})
        # SekoAnswerFactory(inquiry=inquiry_2)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 1, 'unanswerd_af': 0})
        # inquiry_3 = SekoInquiryFactory(seko=seko_2)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 2, 'unanswerd_af': 0})
        # SekoAnswerFactory(inquiry=inquiry_3)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 1, 'unanswerd_af': 0})
        # SekoInquiryFactory(seko=seko_2)
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 2, 'unanswerd_af': 0})

        # seko_1.disable()
        # self.assertEqual(self.base.seko_inquiries(), {'unanswerd_inquiry': 1, 'unanswerd_af': 0})
