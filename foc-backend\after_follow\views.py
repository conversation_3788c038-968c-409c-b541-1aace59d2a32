from django.db.models import Prefetch, Q, QuerySet
from django_filters import rest_framework as filters
from rest_framework import exceptions, generics
from rest_framework.permissions import IsAuthenticated

from after_follow.models import (
    AfActivityDetail,
    AfContactWay,
    AfGroup,
    AfterFollow,
    AfWish,
    SekoAf,
)
from after_follow.serializers import (
    AfActivityDetailSerializer,
    AfContactWaySerializer,
    AfGroupSerializer,
    AfterFollowSerializer,
    AfWishSerializer,
    SekoAfSerializer,
)
from bases.models import Base
from utils.permissions import IsStaff, ReadNeedAuth
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class AfGroupList(AddContextMixin, generics.ListCreateAPIView):

    queryset = (
        AfGroup.objects.prefetch_related(
            Prefetch(
                'after_follows',
                queryset=AfterFollow.objects.filter(Q(del_flg=False)).order_by('display_num'),
            )
        )
        .filter(Q(del_flg=False))
        .order_by('department', 'display_num', 'pk')
        .all()
    )
    serializer_class = AfGroupSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['department']
    permission_classes = [IsStaff | ReadNeedAuth]


class AfGroupListDown(AddContextMixin, generics.ListCreateAPIView):

    serializer_class = AfGroupSerializer
    permission_classes = [IsStaff | ReadNeedAuth]

    def get_queryset(self):
        base_id = self.request.query_params.get('department', None)
        if not base_id:
            raise exceptions.ParseError()
        specified_base: Base = Base.objects.get(Q(pk=base_id) & Q(del_flg=False))
        base_ids: QuerySet = (
            specified_base.get_descendants(include_self=True)
            .filter(Q(del_flg=False))
            .values_list('id', flat=True)
        )
        return (
            AfGroup.objects.select_related('department')
            .prefetch_related(
                Prefetch(
                    'after_follows',
                    queryset=AfterFollow.objects.filter(Q(del_flg=False)).order_by('display_num'),
                )
            )
            .filter(Q(department_id__in=base_ids) & Q(del_flg=False))
            .order_by('department', 'display_num', 'pk')
            .all()
        )


class AfGroupDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):

    queryset = (
        AfGroup.objects.prefetch_related(
            Prefetch(
                'after_follows',
                queryset=AfterFollow.objects.filter(Q(del_flg=False)).order_by('display_num'),
            )
        )
        .filter(Q(del_flg=False))
        .all()
    )
    serializer_class = AfGroupSerializer
    permission_classes = [IsStaff]


class AfterFollowList(AddContextMixin, generics.ListCreateAPIView):

    queryset = (
        AfterFollow.objects.select_related('af_group')
        .filter(Q(del_flg=False))
        .order_by('af_group', 'display_num', 'pk')
        .all()
    )
    serializer_class = AfterFollowSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['af_group']
    permission_classes = [IsStaff]


class AfterFollowDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):

    queryset = AfterFollow.objects.select_related('af_group').filter(Q(del_flg=False)).all()
    serializer_class = AfterFollowSerializer
    permission_classes = [IsStaff]


class AfContactWayList(AddContextMixin, generics.ListCreateAPIView):

    queryset = (
        AfContactWay.objects.filter(Q(del_flg=False))
        .order_by('company', 'display_num', 'pk')
        .all()
    )
    serializer_class = AfContactWaySerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['company']
    permission_classes = [IsStaff | ReadNeedAuth]


class AfContactWayDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):

    queryset = AfContactWay.objects.filter(Q(del_flg=False)).all()
    serializer_class = AfContactWaySerializer
    permission_classes = [IsStaff]


class SekoAfList(generics.CreateAPIView):

    queryset = SekoAf.objects.all()
    serializer_class = SekoAfSerializer
    permission_classes = [IsAuthenticated]


class SekoAfDetail(generics.UpdateAPIView):

    queryset = SekoAf.objects.all()
    serializer_class = SekoAfSerializer
    permission_classes = [IsAuthenticated]


class AfWishDetail(generics.UpdateAPIView):

    queryset = AfWish.objects.all()
    serializer_class = AfWishSerializer
    permission_classes = [IsStaff]


class AfActivityList(generics.CreateAPIView):

    queryset = AfActivityDetail.objects.all()
    serializer_class = AfActivityDetailSerializer
    permission_classes = [IsStaff]


class AfActivityDetail(generics.UpdateAPIView):

    queryset = AfActivityDetail.objects.all()
    serializer_class = AfActivityDetailSerializer
    permission_classes = [IsStaff]
