# Generated by Django 3.1.5 on 2021-01-06 04:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='SalesCompany',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('company_id', models.IntegerField(blank=True, null=True)),
                ('company_name', models.TextField(blank=True, null=True)),
                ('zip_code', models.CharField(blank=True, max_length=7, null=True)),
                ('address_1', models.TextField(blank=True, null=True)),
                ('address_2', models.TextField(blank=True, null=True)),
                ('tel', models.CharField(blank=True, max_length=15, null=True)),
                ('invoice_date', models.DateField(blank=True, null=True)),
                ('pay_date', models.DateField(blank=True, null=True)),
                ('monthly_fee', models.IntegerField(blank=True, null=True)),
                ('chobun_fee', models.IntegerField(blank=True, null=True)),
                ('chobun_fee_unit', models.TextField(blank=True, null=True)),
                ('chobun_qty', models.IntegerField(blank=True, null=True)),
                ('chobun_order_price', models.IntegerField(blank=True, null=True)),
                ('chobun_tax', models.IntegerField(blank=True, null=True)),
                ('chobun_order_fee', models.IntegerField(blank=True, null=True)),
                ('kumotsu_fee', models.IntegerField(blank=True, null=True)),
                ('kumotsu_fee_unit', models.TextField(blank=True, null=True)),
                ('kumotsu_qty', models.IntegerField(blank=True, null=True)),
                ('kumotsu_order_price', models.IntegerField(blank=True, null=True)),
                ('kumotsu_tax', models.IntegerField(blank=True, null=True)),
                ('kumotsu_order_fee', models.IntegerField(blank=True, null=True)),
                ('henreihin_fee', models.IntegerField(blank=True, null=True)),
                ('henreihin_fee_unit', models.TextField(blank=True, null=True)),
                ('henreihin_qty', models.IntegerField(blank=True, null=True)),
                ('henreihin_order_price', models.IntegerField(blank=True, null=True)),
                ('henreihin_tax', models.IntegerField(blank=True, null=True)),
                ('henreihin_order_fee', models.IntegerField(blank=True, null=True)),
                ('koden_qty', models.IntegerField(blank=True, null=True)),
                ('koden_order_price', models.IntegerField(blank=True, null=True)),
                ('koden_order_fee', models.IntegerField(blank=True, null=True)),
                ('koden_fee_commission', models.IntegerField(blank=True, null=True)),
                ('koden_fee_commission_tax', models.IntegerField(blank=True, null=True)),
                (
                    'koden_commission_pct',
                    models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True),
                ),
                ('sales_price', models.IntegerField(blank=True, null=True)),
                ('invoice_tax_pct', models.IntegerField(blank=True, null=True)),
                ('invoice_tax', models.IntegerField(blank=True, null=True)),
                ('confirm_ts', models.DateTimeField(blank=True, null=True)),
                ('confirm_staff_id', models.IntegerField(blank=True, null=True)),
                ('confirm_staff_name', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'sum_sales_company',
            },
        ),
        migrations.CreateModel(
            name='SalesIndex',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('sales_yymm', models.CharField(max_length=6, unique=True)),
                ('fiscal_year', models.IntegerField()),
                ('sum_ts', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'sum_sales_admin',
            },
        ),
        migrations.CreateModel(
            name='SalesSekoDepartment',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('seko_department_id', models.IntegerField(blank=True, null=True)),
                ('base_name', models.TextField(blank=True, null=True)),
                ('chobun_qty', models.IntegerField(blank=True, null=True)),
                ('chobun_order_price', models.IntegerField(blank=True, null=True)),
                ('chobun_tax', models.IntegerField(blank=True, null=True)),
                ('kumotsu_qty', models.IntegerField(blank=True, null=True)),
                ('kumotsu_order_price', models.IntegerField(blank=True, null=True)),
                ('kumotsu_tax', models.IntegerField(blank=True, null=True)),
                ('henreihin_qty', models.IntegerField(blank=True, null=True)),
                ('henreihin_order_price', models.IntegerField(blank=True, null=True)),
                ('henreihin_tax', models.IntegerField(blank=True, null=True)),
                ('koden_qty', models.IntegerField(blank=True, null=True)),
                ('koden_order_price', models.IntegerField(blank=True, null=True)),
                ('koden_tax', models.IntegerField(blank=True, null=True)),
                ('koden_order_fee', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                (
                    'sales_company',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='invoices.salescompany'
                    ),
                ),
            ],
            options={
                'db_table': 'sum_sales_seko_department',
            },
        ),
        migrations.CreateModel(
            name='SalesDetail',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('seko_id', models.IntegerField(blank=True, null=True)),
                ('seko_date', models.DateField(blank=True, null=True)),
                ('soke_name', models.TextField(blank=True, null=True)),
                ('kojin_name', models.TextField(blank=True, null=True)),
                ('moshu_name', models.TextField(blank=True, null=True)),
                ('entry_id', models.IntegerField(blank=True, null=True)),
                ('entry_name', models.TextField(blank=True, null=True)),
                ('entry_ts', models.DateTimeField(blank=True, null=True)),
                ('entry_detail_id', models.IntegerField(blank=True, null=True)),
                ('service_id', models.IntegerField(blank=True, null=True)),
                ('service_name', models.TextField(blank=True, null=True)),
                ('item_id', models.IntegerField(blank=True, null=True)),
                ('item_name', models.TextField(blank=True, null=True)),
                ('quantity', models.IntegerField(blank=True, null=True)),
                ('item_price', models.IntegerField(blank=True, null=True)),
                ('item_tax', models.IntegerField(blank=True, null=True)),
                ('item_tax_pct', models.IntegerField(blank=True, null=True)),
                ('keigen_flg', models.BooleanField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                (
                    'sales_seko_department',
                    models.ForeignKey(
                        blank=True,
                        db_column='sales_seko_dept_id',
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='invoices.salessekodepartment',
                    ),
                ),
            ],
            options={
                'db_table': 'sum_sales_detail',
            },
        ),
        migrations.AddField(
            model_name='salescompany',
            name='sales_index',
            field=models.ForeignKey(
                db_column='sales_admin_id',
                on_delete=django.db.models.deletion.CASCADE,
                to='invoices.salesindex',
            ),
        ),
    ]
