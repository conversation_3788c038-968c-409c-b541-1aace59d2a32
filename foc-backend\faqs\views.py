from django.db.models import Q
from django_filters import rest_framework as filters
from rest_framework import generics

from faqs.models import Faq
from faqs.serializers import FaqSerializer, FaqUpdateSerializer
from utils.permissions import Is<PERSON>taff, ReadNeedAuth
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class FaqList(AddContextMixin, generics.ListCreateAPIView):
    queryset = Faq.objects.filter(Q(del_flg=False)).order_by('company', 'display_num')
    serializer_class = FaqSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['company']
    permission_classes = [IsStaff | ReadNeedAuth]


class FaqDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    queryset = Faq.objects.filter(Q(del_flg=False))
    serializer_class = FaqUpdateSerializer
    permission_classes = [IsStaff | ReadNeedAuth]
