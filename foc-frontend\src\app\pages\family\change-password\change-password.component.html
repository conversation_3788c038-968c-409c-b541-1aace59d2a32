
<div class="container">
  <div class="inner">
    <div class="contents">
      <h2>パスワード変更</h2>
      <div class="input-area">
        <div class="line">
          <div class="label required huge">変更後のパスワード</div>
          <div class="input password" #passwordEm [class.error]="isErrorField(passwordEm)">
            <input [type]="input_type_password" [(ngModel)]="password">
            <i class="eye icon" [class.slash]="input_type_password==='password'" (click)="chanageType(1)"></i>
          </div>
        </div>
        <div class="line">
          <div class="label required huge">変更後のパスワード(確認用)</div>
          <div class="input password_confirm" #passwordConfirmEm [class.error]="isErrorField(passwordConfirmEm)">
            <input [type]="input_type_password_confirm" [(ngModel)]="password_confirm">
            <i class="eye icon" [class.slash]="input_type_password_confirm==='password'" (click)="chanageType(2)"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="button-area">
      <a class="button red" (click)="saveData()">保存</a>
    </div>
    <div class="ui modal confirm" id="message-popup">
      <div class="content red">
        {{message}}
      </div>
      <div class="button-area">
        <a class="button grey" (click)="closePopup()">閉じる</a>
      </div>
    </div>
  </div>
</div>
