from base64 import b64encode
from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from seko.models import Kojin, Seko, SekoAlbum
from seko.tests.factories import KojinFactory, SekoAlbumFactory, SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


class SekoRelatedKojinListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.seko = SekoFactory()
        self.kojin_1 = KojinFactory(seko=self.seko)
        self.kojin_2 = KojinFactory(seko=self.seko)

        self.another_seko = SekoFactory()
        KojinFactory(seko=self.another_seko)
        KojinFactory(seko=self.another_seko)

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_kojin_list_succeed(self) -> None:
        """故人一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        kojin: Dict[int, Kojin] = dict([(rec.id, rec) for rec in [self.kojin_1, self.kojin_2]])

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:kojin_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)
        for rec in records:
            a_kojin = kojin[rec['id']]
            self.assertEqual(rec.get('kojin_num'), a_kojin.kojin_num)
            self.assertEqual(rec.get('name'), a_kojin.name)
            self.assertEqual(rec.get('kana'), a_kojin.kana)
            self.assertEqual(rec.get('kaimyo'), a_kojin.kaimyo)
            self.assertIsNotNone(rec.get('iei_file_name'))
            self.assertEqual(rec.get('moshu_kojin_relationship'), a_kojin.moshu_kojin_relationship)
            self.assertEqual(rec.get('birth_date'), a_kojin.birth_date.strftime('%Y-%m-%d'))
            self.assertEqual(rec.get('death_date'), a_kojin.death_date.strftime('%Y-%m-%d'))
            self.assertEqual(rec.get('age_kbn'), a_kojin.age_kbn)
            self.assertEqual(rec.get('age'), a_kojin.age)
            self.assertEqual(rec.get('note'), a_kojin.note)
            self.assertEqual(rec.get('del_flg'), a_kojin.del_flg)

    def test_kojin_list_failed_by_notfound(self) -> None:
        """故人一覧取得APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:kojin_list', kwargs={'seko_id': non_saved_seko.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_kojin_list_failed_without_auth(self) -> None:
        """故人一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:kojin_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoRelatedKojinDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.kojin = KojinFactory()

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_kojin_detail_succeed(self) -> None:
        """故人詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': self.kojin.seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('id'), self.kojin.id)
        self.assertEqual(record.get('kojin_num'), self.kojin.kojin_num)
        self.assertEqual(record.get('name'), self.kojin.name)
        self.assertEqual(record.get('kana'), self.kojin.kana)
        self.assertEqual(record.get('kaimyo'), self.kojin.kaimyo)
        self.assertIsNotNone(record.get('iei_file_name'))
        self.assertEqual(
            record.get('moshu_kojin_relationship'), self.kojin.moshu_kojin_relationship
        )
        self.assertEqual(record.get('birth_date'), self.kojin.birth_date.strftime('%Y-%m-%d'))
        self.assertEqual(record.get('death_date'), self.kojin.death_date.strftime('%Y-%m-%d'))
        self.assertEqual(record.get('age_kbn'), self.kojin.age_kbn)
        self.assertEqual(record.get('age'), self.kojin.age)
        self.assertEqual(record.get('note'), self.kojin.note)
        self.assertEqual(record.get('del_flg'), self.kojin.del_flg)

    def test_kojin_detail_failed_by_notfound(self) -> None:
        """故人詳細取得APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_album: SekoAlbum = SekoAlbumFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:kojin_detail',
                kwargs={'seko_id': self.kojin.seko.id, 'pk': non_saved_album.pk},
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_kojin_detail_failed_by_notfound_seko(self) -> None:
        """故人詳細取得APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': non_saved_seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_kojin_detail_failed_by_illegal_seko(self) -> None:
        """故人詳細取得APIがアルバムが紐付いていない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_parent_seko: Seko = SekoFactory()
        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': non_parent_seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_kojin_detail_failed_without_auth(self) -> None:
        """故人詳細取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': self.kojin.seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoRelatedKojinCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.seko = SekoFactory()

        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_kojin_create_succeed(self) -> None:
        """故人を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        new_kojin_data: Kojin = KojinFactory.build()
        params: Dict = {
            'kojin_num': new_kojin_data.kojin_num,
            'name': new_kojin_data.name,
            'kana': new_kojin_data.kana,
            'kaimyo': new_kojin_data.kaimyo,
            'iei_file_name': b64encode(new_kojin_data.iei_file_name.read()),
            'moshu_kojin_relationship': new_kojin_data.moshu_kojin_relationship,
            'birth_date': new_kojin_data.birth_date,
            'death_date': new_kojin_data.death_date,
            'age_kbn': new_kojin_data.age_kbn,
            'age': new_kojin_data.age,
            'note': new_kojin_data.note,
        }
        response = self.api_client.post(
            reverse('seko:kojin_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record.get('kojin_num'), new_kojin_data.kojin_num)
        self.assertEqual(record.get('name'), new_kojin_data.name)
        self.assertEqual(record.get('kana'), new_kojin_data.kana)
        self.assertEqual(record.get('kaimyo'), new_kojin_data.kaimyo)
        self.assertIsNotNone(record.get('iei_file_name'))
        self.assertEqual(
            record.get('moshu_kojin_relationship'), new_kojin_data.moshu_kojin_relationship
        )
        self.assertEqual(record.get('birth_date'), new_kojin_data.birth_date.strftime('%Y-%m-%d'))
        self.assertEqual(record.get('death_date'), new_kojin_data.death_date.strftime('%Y-%m-%d'))
        self.assertEqual(record.get('age_kbn'), new_kojin_data.age_kbn)
        self.assertEqual(record.get('age'), new_kojin_data.age)
        self.assertEqual(record.get('note'), new_kojin_data.note)
        self.assertFalse(record.get('del_flg'))

        new_kojin = Kojin.objects.get(pk=record['id'])
        self.assertEqual(new_kojin.seko, self.seko)

    def test_kojin_create_ignore_del_flg(self) -> None:
        """故人追加APIはdel_flgの項目を無視する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        new_kojin_data: Kojin = KojinFactory.build()
        params: Dict = {
            'kojin_num': new_kojin_data.kojin_num,
            'name': new_kojin_data.name,
            'kana': new_kojin_data.kana,
            'kaimyo': new_kojin_data.kaimyo,
            'iei_file_name': b64encode(new_kojin_data.iei_file_name.read()),
            'moshu_kojin_relationship': new_kojin_data.moshu_kojin_relationship,
            'birth_date': new_kojin_data.birth_date,
            'death_date': new_kojin_data.death_date,
            'age_kbn': new_kojin_data.age_kbn,
            'age': new_kojin_data.age,
            'note': new_kojin_data.note,
            'del_flg': True,
        }
        response = self.api_client.post(
            reverse('seko:kojin_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record.get('kojin_num'), new_kojin_data.kojin_num)
        self.assertEqual(record.get('name'), new_kojin_data.name)
        self.assertEqual(record.get('kana'), new_kojin_data.kana)
        self.assertEqual(record.get('kaimyo'), new_kojin_data.kaimyo)
        self.assertIsNotNone(record.get('iei_file_name'))
        self.assertEqual(
            record.get('moshu_kojin_relationship'), new_kojin_data.moshu_kojin_relationship
        )
        self.assertEqual(record.get('birth_date'), new_kojin_data.birth_date.strftime('%Y-%m-%d'))
        self.assertEqual(record.get('death_date'), new_kojin_data.death_date.strftime('%Y-%m-%d'))
        self.assertEqual(record.get('age_kbn'), new_kojin_data.age_kbn)
        self.assertEqual(record.get('age'), new_kojin_data.age)
        self.assertEqual(record.get('note'), new_kojin_data.note)
        self.assertFalse(record.get('del_flg'))

        new_kojin = Kojin.objects.get(pk=record['id'])
        self.assertEqual(new_kojin.seko, self.seko)

    def test_kojin_create_failed_by_notfound(self) -> None:
        """故人追加APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:kojin_list', kwargs={'seko_id': non_saved_seko.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_kojin_create_failed_without_auth(self) -> None:
        """故人追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:kojin_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoRelatedKojinUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.kojin = KojinFactory()

        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_kojin_update_succeed(self) -> None:
        """故人を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        new_kojin_data: Kojin = KojinFactory.build(seko=self.kojin.seko)

        params: Dict = {
            'kojin_num': new_kojin_data.kojin_num,
            'name': new_kojin_data.name,
            'kana': new_kojin_data.kana,
            'kaimyo': new_kojin_data.kaimyo,
            'iei_file_name': b64encode(new_kojin_data.iei_file_name.read()),
            'moshu_kojin_relationship': new_kojin_data.moshu_kojin_relationship,
            'birth_date': new_kojin_data.birth_date,
            'death_date': new_kojin_data.death_date,
            'age_kbn': new_kojin_data.age_kbn,
            'age': new_kojin_data.age,
            'note': new_kojin_data.note,
        }
        response = self.api_client.put(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': self.kojin.seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('kojin_num'), new_kojin_data.kojin_num)
        self.assertEqual(record.get('name'), new_kojin_data.name)
        self.assertEqual(record.get('kana'), new_kojin_data.kana)
        self.assertEqual(record.get('kaimyo'), new_kojin_data.kaimyo)
        self.assertIsNotNone(record.get('iei_file_name'))
        self.assertEqual(
            record.get('moshu_kojin_relationship'), new_kojin_data.moshu_kojin_relationship
        )
        self.assertEqual(record.get('birth_date'), new_kojin_data.birth_date.strftime('%Y-%m-%d'))
        self.assertEqual(record.get('death_date'), new_kojin_data.death_date.strftime('%Y-%m-%d'))
        self.assertEqual(record.get('age_kbn'), new_kojin_data.age_kbn)
        self.assertEqual(record.get('age'), new_kojin_data.age)
        self.assertEqual(record.get('note'), new_kojin_data.note)
        self.assertFalse(record.get('del_flg'))

    def test_kojin_update_failed_by_notfound(self) -> None:
        """故人更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_kojin: Kojin = KojinFactory.build()
        params: Dict = {}
        response = self.api_client.put(
            reverse(
                'seko:kojin_detail',
                kwargs={'seko_id': self.kojin.seko.id, 'pk': non_saved_kojin.pk},
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_kojin_update_failed_by_notfound_seko(self) -> None:
        """故人更新APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.put(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': non_saved_seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_kojin_update_failed_by_illegal_seko(self) -> None:
        """故人更新APIがアルバムが紐付いていない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_parent_seko: Seko = SekoFactory()
        params: Dict = {}
        response = self.api_client.put(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': non_parent_seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_kojin_update_failed_without_auth(self) -> None:
        """故人更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': self.kojin.seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.kojin = KojinFactory()

        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_album_delete_succeed(self) -> None:
        """故人を削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        kojin_id: int = self.kojin.pk
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': self.kojin.seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # アルバムが物理削除されている
        self.assertIsNone(Kojin.objects.filter(pk=kojin_id).first())

    def test_seko_album_delete_failed_by_notfound(self) -> None:
        """故人削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:kojin_detail',
                kwargs={'seko_id': self.kojin.seko.id, 'pk': non_saved_seko.id},
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_delete_failed_by_notfound_seko(self) -> None:
        """故人削除APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': non_saved_seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_delete_failed_by_illegal_seko(self) -> None:
        """故人削除APIがアルバムが紐付いていない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_parent_seko: Seko = SekoFactory()
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': non_parent_seko.id, 'pk': self.kojin.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_delete_failed_without_auth(self) -> None:
        """故人削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:kojin_detail', kwargs={'seko_id': self.kojin.seko.id, 'pk': self.kojin.id}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
