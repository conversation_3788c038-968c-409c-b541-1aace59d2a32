@import "src/assets/scss/customer/setting";
  .advertise {
    position: absolute;
    margin-left: calc(10% + 200px);
    z-index: 100;
    max-width: 700px;
    width: calc(90% - 300px);
    max-height: 70px;
    margin-top: 20px;
    @media screen and (min-width: 1124px) {
      margin-left: calc(50% - 250px);
    }
    .flexslider {
      margin: auto;
      max-width: 700px;
      margin-bottom: 5px;
      border: 0;
      background-color: #ffffff;
      line-height: 1.25;
      ul.slides {
        display: flex;
        >li {
          margin: auto 0;
        }
      }
      img {
        border-radius: 3px;
        max-width: 100%;
        // min-width: 100%;
        margin: auto;
        width: auto;
        height: auto;
        max-height: 70px;
      }
    }
    @media screen and (max-width: 800px) {
      position: relative;
      margin: auto;
      max-width: 90%;
      width: 90%;
      padding-top: 10px;
      margin-bottom: -5px;
      max-height: 50px;
      .flexslider {
        max-width: 100%;
        img {
          max-height: 50px;
        }
      }
    }
  }
.main-header {
  position: relative;
  width: 90%;
  max-width: 1000px;
  height: 100px;
  top: 0;
  z-index: 99;
  margin: 0 auto;
  @media screen and (max-width: 560px) {
    height: 80px;
  }
  background-color: $background-light;
  // box-shadow: 0px 5px 5px -5px rgba(0,0,0,0.5);
  .logo {
    position: relative;
    width: 250px;
    height: 100px;
    margin: 0;
    background-image: url(../../../assets/img/customer/logo_cocoro.png);
    background-repeat: no-repeat;
    background-size: 240px auto;
    cursor: pointer;
    &:focus {
      outline: 0;
    }
    @media screen and (max-width: 560px) {
      height: 80px;
      background-size: 200px auto;
    }
  }

  .menu-button {
    position: absolute;
    right: 0px;
    top: 50px;
    cursor: pointer;
    outline: none;
    color: $text-dark;
    &:hover {
      opacity: .8;
    }
    i {
      margin: 0;
    }
    .menu-name {
      font-size: 0.6rem;
      line-height: 0.5;
      text-align: center;
    }
    @media screen and (max-width: 560px) {
      top: 35px;
    }
  }
  .menu-area {
    position: absolute;
    right: 0px;
    top: 90px;
    color: $text-light;
    background-color: $border-dark;
    padding: 30px 50px 30px 30px;
    font-size: 0.9rem;
    display: none;
    &.show {
      display: block;
    }
    .close {
      position: absolute;
      right: 7px;
      top: 7px;
      cursor: pointer;
      &:hover {
        opacity: .8;
      }
    }
    .item {
      padding: 5px 40px 5px 20px;
      cursor: pointer;
      border-bottom: solid $border-grey 1px;
      &:hover {
        opacity: .8;
      }
    }
    @media screen and (max-width: 560px) {
      top: 70px;
    }
  }
}
.contents.header {
  background-color: #ffffff;
  width: 90%;
  max-width: 1000px;
  margin: 0px auto 20px;
  text-align: center;
  border-radius: 10px;
  padding: 15px;
  .label {
    margin: auto 0;
    font-size: 1rem;
    font-weight: 700;
  }
  .name {
    font-size: 1.4rem;
    font-weight: 700;
  }
}
.pagetop {
  display: none;
  position: fixed;
  bottom: 0;
  right: -30px;
  color: rgba(0, 0, 0, 0);
  a {
    font-size: 26px;
  }
  img {
    width: 50px;
    @media screen and (max-width: 560px) {
      width: 30px;
    }
  }
}
.routerArea {
  min-height: calc(100% - 270px);
  @media screen and (max-width: 560px) {
  }
  @media screen and (max-width: 380px) {
  }
  >.contents {
    height: 100%;
  }

}
footer {
  background-color: #413f36;
  color: #ffffff;
  padding: 10px 0;
  font-size: 18px;
  line-height: 1;
  @media screen and (max-width: 560px) {
    font-size: 14px;
  }
  .contents {
    width: 90%;
    max-width: 1000px;
    margin: 0 auto;
    padding: 0;
    display: flex;
    .logo {
      height: 70px;
      img {
        height: auto;
        width: auto;
        max-height: 100%;
      }
    }
    .info {
      padding-left: 10px;
      line-height: 1.5;
      .name {
        font-size: 1.3rem;
        padding-bottom: 3px;
      }
      .address, .tel {
        font-size: 0.9rem;
      }
    }
  }
}

:host ::ng-deep .flex-control-nav {
  top: 60px;
  @media screen and (max-width: 800px) {
    top: 40px;
  }
}
:host ::ng-deep .flex-control-paging li a {
  width: 7px;
  height: 7px;
}
