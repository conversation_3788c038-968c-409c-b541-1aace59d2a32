@import "src/assets/scss/company/setting";
.container {
  .inner {
    .contents {

      position: relative;
      max-width: 930px;
      margin: 0 auto;
      padding: 20px 0;
      height: 100%;
      .title {
        // background-color: $background-light1;
        // color: $lable-text-dark;
        // box-shadow: 0 5px 20px rgba(0,0,0,.3);
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        padding: 5px 15px;
        // font-size: 18px;
        font-weight: 700;
        // margin: 10px 10px;
        // border-left: solid 5px $label-border-dark;
        // border-top-width: 0px;
        // border-bottom-width: 0px;
        // border-top-left-radius: 10px;
      }
      .content {
        display: flex;
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        padding: 0 20px 20px;
        .input_area {
          margin: -27px 0 -5px 20px;
          com-dropdown {
            width: 100%;
            max-width: 250px;
          }
        }
        &.main {
          padding-top: 20px;
          display: block;
          .menu {
            width: calc(100% - 40px);
          }
          >.reload {
            position: absolute;
            right: 35px;
            top: 20px;
            // left: 35px;
          }
        }
        .menu {
          display: flex;
          width: 240px;
          padding: 10px 0;
          margin: 10px 20px;
          background-color: $label-background-light;
          >i {
            margin: auto 10px;
            margin-right: 0;
            &.combo {
              margin: 0;
              margin-left: -5px;
            }
          }
          .subtitle {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            margin: auto 10px;
            font-size: 1.077em;
            font-weight: 700;
            color: $lable-text-dark;
            z-index: 1;
            min-width: 130px;
            &.mini {
              min-width: 90px;
            }
          }
          >div.info {
            >div {
              width: 100%;
              min-width: 130px;
              >.ui.pointing.label {
                min-width: 270px;
              }
              >.ui.button {
                min-width: 130px;
              }
            }
            min-width: 130px;
            padding: 10px;
            font-size: 1em;
            color: $lable-text-dark;
            .important {
              color: $important;
              font-weight: 700;
            }
          }
          &.selectable {
            cursor: pointer;
            &:hover {
              box-shadow: 0 5px 20px rgba(0,0,0,.3);
              --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
              -webkit-transform: translateY(-2px);
              transform: translateY(-2px);
            }
            &:focus {
              outline: none !important;
              border: 0;
            }
          }
          .ui.labeled.button {
            >.ui.label {
              cursor: default;
            }
            &:hover {
              box-shadow: none;
              -webkit-box-shadow: none;
              -webkit-transform: translateY(-0);
              transform: translateY(0);
            }
           }
        }
      }
      .main.content .menu {
        >i {
          &.combo {
            margin-top: 15px;
          }
        }
      }

      .ui.attached.segment {
        min-height: calc(100% - 70px);
        margin-bottom: 20px;
      }
      .ui.loading.button::before {
        z-index: 2;
      }
      .ui.loading.button:after {
        color: $lable-text-dark;
      }
    }
  }
}
