# FOCプロジェクト 新規機能開発フローガイド

## 概要

このドキュメントでは、FOCプロジェクトにおいて新規機能（例：ユーザープロフィール編集機能）を追加する際の一般的な開発フローを説明します。

## 技術スタック

### フロントエンド
- **フレームワーク**: Angular 10
- **言語**: TypeScript
- **UIライブラリ**: Fomantic UI (Semantic UI)
- **HTTP通信**: Angular HttpClient
- **状態管理**: SessionService（セッションストレージベース）
- **認証**: JWT Token + Route Guards

### バックエンド
- **フレームワーク**: Django 3.1 + Django REST Framework
- **言語**: Python 3.8
- **認証**: JWT (djangorestframework-simplejwt)
- **データベース**: PostgreSQL/SQLite
- **ORM**: Django ORM
- **パッケージ管理**: Poetry

## 1. フロントエンド側の実装フロー

### 1.1 新しい画面コンポーネントの追加

#### ステップ1: コンポーネント生成
```bash
cd foc-frontend
ng generate component pages/company/user-profile-edit
```

#### ステップ2: ルーティング設定
`src/app/app-routing.module.ts` にルートを追加：

```typescript
const routes: Routes = [
  // 既存のルート...
  { 
    path: 'foc/user-profile/edit', 
    component: CompanyFrameComponent, 
    canActivate: [AuthFGuard] 
  },
  // ...
];
```

#### ステップ3: フレームコンポーネントでの画面切り替え
`src/app/pages/company/frame.component.ts` で新しいコンポーネントを表示する条件を追加：

```typescript
export class CompanyFrameComponent {
  // 現在のURLに基づいて表示するコンポーネントを決定
  get isUserProfileEdit(): boolean {
    return this.router.url.includes('/foc/user-profile/edit');
  }
}
```

### 1.2 フォーム入力と状態管理

#### ステップ1: フォームモデル定義
```typescript
// src/app/model/user-profile.model.ts
export interface UserProfileEditRequest {
  name: string;
  email: string;
  phone: string;
  // その他のフィールド
}

export interface UserProfileGetResponse {
  id: number;
  name: string;
  email: string;
  phone: string;
  // その他のフィールド
}
```

#### ステップ2: コンポーネントでの状態管理
```typescript
// user-profile-edit.component.ts
export class UserProfileEditComponent implements OnInit {
  userProfile: UserProfileEditRequest = {
    name: '',
    email: '',
    phone: ''
  };

  constructor(
    private httpClientService: HttpClientService,
    private sessionSvc: SessionService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadUserProfile();
  }

  async loadUserProfile() {
    try {
      const response = await this.httpClientService.getUserProfile();
      this.userProfile = response;
    } catch (error) {
      // エラーハンドリング
    }
  }

  async saveUserProfile() {
    try {
      await this.httpClientService.updateUserProfile(this.userProfile);
      // 成功時の処理
      this.router.navigate(['/foc/top']);
    } catch (error) {
      // エラーハンドリング
    }
  }
}
```

### 1.3 APIとの通信部分

#### HttpClientServiceへのメソッド追加
`src/app/service/http-client.service.ts` に新しいAPIメソッドを追加：

```typescript
// ユーザープロフィール取得
public getUserProfile(): Promise<UserProfileGetResponse> {
  const url = this.host + '/staffs/profile/';
  const params = new HttpParams();
  Utils.log(url);
  return this.getWithToken<UserProfileGetResponse>('/staffs/profile/', params);
}

// ユーザープロフィール更新
public updateUserProfile(data: UserProfileEditRequest): Promise<UserProfileGetResponse> {
  Utils.log(this.host + '/staffs/profile/');
  return this.putWithToken<UserProfileEditRequest, UserProfileGetResponse>('/staffs/profile/', data);
}
```

## 2. バックエンド側の実装フロー

### 2.1 ルーティングの追加

#### ステップ1: アプリのURLパターン追加
`foc-backend/staffs/urls.py` に新しいエンドポイントを追加：

```python
from django.urls import path
from . import views

urlpatterns = [
    # 既存のURL...
    path('profile/', views.StaffProfileDetail.as_view(), name='staff_profile_detail'),
    # ...
]
```

### 2.2 ビジネスロジックの処理

#### ステップ1: シリアライザーの作成
`foc-backend/staffs/serializers.py` に新しいシリアライザーを追加：

```python
class StaffProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Staff
        fields = ['id', 'name', 'mail_address', 'phone', 'updated_at']
        read_only_fields = ['id', 'updated_at']

    def validate_mail_address(self, value):
        # メールアドレスのバリデーション
        if Staff.objects.filter(mail_address=value).exclude(id=self.instance.id if self.instance else None).exists():
            raise serializers.ValidationError("このメールアドレスは既に使用されています。")
        return value
```

#### ステップ2: ビューの実装
`foc-backend/staffs/views.py` に新しいビューを追加：

```python
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from .models import Staff
from .serializers import StaffProfileSerializer

class StaffProfileDetail(generics.RetrieveUpdateAPIView):
    """
    get: スタッフプロフィールを取得します
    put: スタッフプロフィールを更新します
    patch: スタッフプロフィールを部分更新します
    """
    serializer_class = StaffProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user
```

### 2.3 DBモデル・マイグレーションの変更

#### ステップ1: モデルの変更（必要に応じて）
`foc-backend/staffs/models.py` でモデルを変更：

```python
class Staff(models.Model):
    # 既存のフィールド...
    phone = models.CharField(max_length=20, blank=True, null=True)  # 新しいフィールド
    # ...
```

#### ステップ2: マイグレーションの作成と適用
```bash
cd foc-backend
poetry run python manage.py makemigrations staffs
poetry run python manage.py migrate
```

## 3. フロント↔バックエンド連携の仕様

### 3.1 APIエンドポイント設計方針

#### RESTful API設計
- **GET** `/staffs/profile/` - プロフィール取得
- **PUT** `/staffs/profile/` - プロフィール全体更新
- **PATCH** `/staffs/profile/` - プロフィール部分更新

#### レスポンス形式
```json
{
  "id": 1,
  "name": "田中太郎",
  "mail_address": "<EMAIL>",
  "phone": "090-1234-5678",
  "updated_at": "2023-12-01T10:30:00Z"
}
```

### 3.2 バリデーション・エラーハンドリング

#### バックエンド側のバリデーション
```python
# serializers.py
def validate(self, data):
    if 'name' in data and len(data['name']) < 2:
        raise serializers.ValidationError({
            'name': '名前は2文字以上で入力してください。'
        })
    return data
```

#### フロントエンド側のエラーハンドリング
```typescript
// user-profile-edit.component.ts
async saveUserProfile() {
  try {
    await this.httpClientService.updateUserProfile(this.userProfile);
    this.showSuccessMessage('プロフィールを更新しました。');
  } catch (error) {
    if (error.status === 400) {
      this.handleValidationErrors(error.error);
    } else {
      this.showErrorMessage('更新に失敗しました。');
    }
  }
}

private handleValidationErrors(errors: any) {
  // フィールドごとのエラーメッセージを表示
  Object.keys(errors).forEach(field => {
    this.showFieldError(field, errors[field][0]);
  });
}
```

## 4. 認証認可の影響

### 4.1 API保護の実装

#### バックエンド側の認証設定
```python
# settings.py
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': ('utils.authentication.ClassableJWTAuthentication',),
}

# views.py
class StaffProfileDetail(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated]  # 認証必須
    
    def get_object(self):
        return self.request.user  # 自分のプロフィールのみアクセス可能
```

#### フロントエンド側の認証ガード
```

## 5. テスト・レビュー・デプロイの流れ

### 5.1 テストケースの追加方法

#### フロントエンド側のテスト（Jasmine + Karma）

##### コンポーネントのユニットテスト
```typescript
// user-profile-edit.component.spec.ts
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { UserProfileEditComponent } from './user-profile-edit.component';
import { HttpClientService } from '../../service/http-client.service';
import { SessionService } from '../../service/session.service';

describe('UserProfileEditComponent', () => {
  let component: UserProfileEditComponent;
  let fixture: ComponentFixture<UserProfileEditComponent>;
  let httpClientService: jasmine.SpyObj<HttpClientService>;

  beforeEach(async () => {
    const httpClientSpy = jasmine.createSpyObj('HttpClientService', ['getUserProfile', 'updateUserProfile']);

    await TestBed.configureTestingModule({
      declarations: [UserProfileEditComponent],
      imports: [HttpClientTestingModule, RouterTestingModule],
      providers: [
        { provide: HttpClientService, useValue: httpClientSpy },
        SessionService
      ]
    }).compileComponents();

    httpClientService = TestBed.inject(HttpClientService) as jasmine.SpyObj<HttpClientService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UserProfileEditComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load user profile on init', async () => {
    const mockProfile = { id: 1, name: 'Test User', email: '<EMAIL>', phone: '090-1234-5678' };
    httpClientService.getUserProfile.and.returnValue(Promise.resolve(mockProfile));

    await component.ngOnInit();

    expect(httpClientService.getUserProfile).toHaveBeenCalled();
    expect(component.userProfile).toEqual(mockProfile);
  });

  it('should save user profile', async () => {
    const mockProfile = { id: 1, name: 'Updated User', email: '<EMAIL>', phone: '090-9876-5432' };
    httpClientService.updateUserProfile.and.returnValue(Promise.resolve(mockProfile));
    component.userProfile = mockProfile;

    await component.saveUserProfile();

    expect(httpClientService.updateUserProfile).toHaveBeenCalledWith(mockProfile);
  });
});
```

##### サービスのユニットテスト
```typescript
// http-client.service.spec.ts
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';

import { HttpClientService } from './http-client.service';

describe('HttpClientService', () => {
  let service: HttpClientService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [HttpClientService]
    });
    service = TestBed.inject(HttpClientService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should get user profile', () => {
    const mockProfile = { id: 1, name: 'Test User', email: '<EMAIL>' };

    service.getUserProfile().then(profile => {
      expect(profile).toEqual(mockProfile);
    });

    const req = httpMock.expectOne('/api/staffs/profile/');
    expect(req.request.method).toBe('GET');
    req.flush(mockProfile);
  });
});
```

#### バックエンド側のテスト（Django Test Framework）

##### モデルのテスト
```python
# foc-backend/staffs/tests/test_models.py
from django.test import TestCase
from django.core.exceptions import ValidationError
from staffs.models import Staff
from bases.tests.factories import BaseFactory

class StaffModelTest(TestCase):
    def setUp(self):
        self.base = BaseFactory()

    def test_staff_creation(self):
        staff = Staff.objects.create(
            company_code='TEST001',
            login_id='test_user',
            name='テストユーザー',
            mail_address='<EMAIL>',
            base=self.base
        )
        self.assertEqual(staff.name, 'テストユーザー')
        self.assertEqual(staff.mail_address, '<EMAIL>')

    def test_email_validation(self):
        # 重複メールアドレスのテスト
        Staff.objects.create(
            company_code='TEST001',
            login_id='test_user1',
            name='ユーザー1',
            mail_address='<EMAIL>',
            base=self.base
        )

        with self.assertRaises(ValidationError):
            staff2 = Staff(
                company_code='TEST002',
                login_id='test_user2',
                name='ユーザー2',
                mail_address='<EMAIL>',
                base=self.base
            )
            staff2.full_clean()
```

##### APIビューのテスト
```python
# foc-backend/staffs/tests/test_views.py
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from staffs.models import Staff
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]

class StaffProfileDetailTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.staff = StaffFactory()
        self.url = reverse('staff_profile_detail')

    def _authenticate(self, user):
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {refresh.access_token}')

    def test_get_profile_authenticated(self):
        self._authenticate(self.staff)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], self.staff.id)
        self.assertEqual(response.data['name'], self.staff.name)

    def test_get_profile_unauthenticated(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_update_profile(self):
        self._authenticate(self.staff)
        data = {
            'name': '更新されたユーザー',
            'mail_address': '<EMAIL>',
            'phone': '090-9999-9999'
        }

        response = self.client.put(self.url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.staff.refresh_from_db()
        self.assertEqual(self.staff.name, '更新されたユーザー')
        self.assertEqual(self.staff.mail_address, '<EMAIL>')

    def test_update_profile_validation_error(self):
        self._authenticate(self.staff)
        data = {
            'name': '',  # 空の名前
            'mail_address': 'invalid-email'  # 無効なメールアドレス
        }

        response = self.client.put(self.url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('name', response.data)
        self.assertIn('mail_address', response.data)
```

##### ファクトリーの作成
```python
# foc-backend/staffs/tests/factories.py
import factory
from factory.django import DjangoModelFactory

from staffs.models import Staff
from bases.tests.factories import BaseFactory

class StaffFactory(DjangoModelFactory):
    class Meta:
        model = Staff

    company_code = factory.Sequence(lambda n: f'COMP{n:03d}')
    login_id = factory.Sequence(lambda n: f'user{n}')
    name = factory.Faker('name', locale='ja_JP')
    mail_address = factory.Faker('email')
    base = factory.SubFactory(BaseFactory)
    retired_flg = False
```

### 5.2 テスト実行方法

#### フロントエンドテストの実行
```bash
cd foc-frontend

# 単体テスト実行
npm run test

# 特定のコンポーネントのテスト実行
ng test --include='**/user-profile-edit.component.spec.ts'

# カバレッジ付きテスト実行
ng test --code-coverage

# E2Eテスト実行
npm run e2e
```

#### バックエンドテストの実行
```bash
cd foc-backend

# 全テスト実行
poetry run python manage.py test

# 特定のアプリのテスト実行
poetry run python manage.py test staffs

# 特定のテストクラス実行
poetry run python manage.py test staffs.tests.test_views.StaffProfileDetailTest

# カバレッジ付きテスト実行
poetry run coverage run --source='.' manage.py test
poetry run coverage report
poetry run coverage html
```

### 5.3 CIのトリガーと自動テストの実行フロー

#### GitHub Actionsの設定
現在の設定（`.github/workflows/continuous_integration.yml`）：

```yaml
name: Test application then build a docker image

on: push  # 全てのプッシュでトリガー

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      SECRET_KEY: dummy-secret-key
    steps:
      - uses: actions/checkout@v2

      - name: Set up Python 3.8
        uses: actions/setup-python@v2
        with:
          python-version: 3.8

      - name: Use pip and poetry cache
        uses: actions/cache@v2
        with:
          path: |
            ~/.cache/pip
            ~/.cache/pypoetry
          key: ${{ runner.os }}-pip-${{ hashFiles('poetry.lock') }}

      - name: Install dependencies
        run: |
          python -m pip install poetry
          poetry install

      - name: Run lint
        run: |
          poetry run inv lint.check-all

      # テスト実行（現在はコメントアウト）
      # - name: Run test
      #   run: |
      #     poetry run inv cover.prun
      #     poetry run inv cover.report
      #     poetry run inv cover.clean
```

#### 推奨されるCI設定の改善
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  frontend-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./foc-frontend

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '12'
          cache: 'npm'
          cache-dependency-path: foc-frontend/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint

      - name: Run tests
        run: npm run test -- --watch=false --browsers=ChromeHeadless --code-coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          directory: ./foc-frontend/coverage

  backend-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./foc-backend

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_foc
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3

      - name: Set up Python 3.8
        uses: actions/setup-python@v3
        with:
          python-version: 3.8

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.1.4

      - name: Install dependencies
        run: poetry install

      - name: Run lint
        run: poetry run inv lint.check-all

      - name: Run tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_foc
          SECRET_KEY: test-secret-key
        run: |
          poetry run python manage.py test
          poetry run coverage run --source='.' manage.py test
          poetry run coverage xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          directory: ./foc-backend

  build:
    needs: [frontend-test, backend-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v3

      - name: Build Docker images
        run: |
          docker build -t foc-frontend ./foc-frontend
          docker build -t foc-backend ./foc-backend
```

### 5.4 本番デプロイまでのステップ

#### 開発フロー
1. **機能ブランチ作成**
   ```bash
   git checkout -b feature/user-profile-edit
   ```

2. **開発・テスト**
   - フロントエンド・バックエンドの実装
   - ユニットテスト作成・実行
   - 統合テスト実行

3. **プルリクエスト作成**
   - GitHub上でPR作成
   - CI/CDパイプラインが自動実行
   - コードレビュー実施

4. **マージ・デプロイ**
   ```bash
   # developブランチにマージ
   git checkout develop
   git merge feature/user-profile-edit

   # ステージング環境デプロイ（自動）
   # テスト・検証

   # mainブランチにマージ
   git checkout main
   git merge develop

   # 本番環境デプロイ（自動）
   ```

#### デプロイ設定例
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - uses: actions/checkout@v3

      - name: Deploy to production
        run: |
          # Docker imageのビルド・プッシュ
          docker build -t ${{ secrets.REGISTRY_URL }}/foc-frontend:${{ github.sha }} ./foc-frontend
          docker build -t ${{ secrets.REGISTRY_URL }}/foc-backend:${{ github.sha }} ./foc-backend

          # コンテナレジストリにプッシュ
          docker push ${{ secrets.REGISTRY_URL }}/foc-frontend:${{ github.sha }}
          docker push ${{ secrets.REGISTRY_URL }}/foc-backend:${{ github.sha }}

          # 本番環境にデプロイ
          # kubectl apply -f k8s/
```

## 6. 開発時の注意点とベストプラクティス

### 6.1 コード品質の維持
- **Linting**: TSLint（フロントエンド）、flake8/black（バックエンド）
- **型安全性**: TypeScriptの型定義を活用
- **コードレビュー**: 必須のPRレビュープロセス

### 6.2 セキュリティ考慮事項
- **認証**: JWTトークンの適切な管理
- **認可**: 適切な権限チェック
- **バリデーション**: フロント・バック両方でのデータ検証
- **CORS**: 適切なCORS設定

### 6.3 パフォーマンス最適化
- **フロントエンド**: Lazy loading、OnPush変更検知戦略
- **バックエンド**: データベースクエリ最適化、キャッシュ活用
- **API**: ページネーション、フィルタリング機能

### 6.4 保守性の向上
- **モジュール化**: 機能ごとの適切な分割
- **ドキュメント**: APIドキュメント、コメント
- **テスト**: 高いテストカバレッジの維持

このガイドに従って開発を進めることで、品質の高い機能を効率的に追加できます。typescript
// app-routing.module.ts
{ 
  path: 'foc/user-profile/edit', 
  component: CompanyFrameComponent, 
  canActivate: [AuthFGuard]  // 認証ガード
}

// authF.guard.ts
canActivate(): boolean {
  const login_info = this.sessionSvc.get('staff_login_info');
  if (!login_info || !login_info.staff) {
    this.router.navigate(['foc/login/']);
    return false;
  }
  return true;
}
```

### 4.2 JWTトークンの自動更新
```typescript
// http-client.service.ts
private async getWithToken<T>(method: string, params: HttpParams): Promise<T> {
  // トークンの有効期限チェックと自動更新
  await this.refreshTokenIfNeeded();
  
  const login_info = this.sessionSvc.get('staff_login_info');
  this.headers = new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${login_info.token.access}`
  });
  
  return this.http.get(this.host + method, { headers: this.headers, params: params })
    .toPromise();
}
```
