
<div class="container">
  <div class="inner with_footer">
    <div class="contents">
      <div class="menu_title">
        <i class="spa icon big"></i>
        供花・供物受付一覧
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(companyComboEm, sekoBaseComboEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchKumotsu()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="form_data.company_id" (selectedItemChange)="companyChange($event, sekoBaseComboEm)"></com-dropdown>
          <label>施行拠点</label>
          <com-dropdown #sekoBaseComboEm [settings]="sekoBaseCombo" [(selectedValue)]="form_data.seko_department"></com-dropdown>
          <label>申込番号</label>
          <div class="ui input small">
            <input type="tel" [(ngModel)]="form_data.entry_id">
          </div>
          <label>申込者名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.entry_name">
          </div>
          <label class="large">申込者電話番号</label>
          <div class="ui input small">
            <input type="tel" [(ngModel)]="form_data.entry_tel">
          </div>
        </div>
        <div class="line">
          <label>組織名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.okurinushi_company">
          </div>
          <label>役職名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.okurinushi_title">
          </div>
          <label>送り主名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.okurinushi_name">
          </div>
          <label>連名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.renmei">
          </div>
          <label class="large">発注状況</label>
          <div class="ui checkbox" (click)="checkClick('unorder', form_data.ordered, form_data.confirmed)">
            <input type="checkbox" name="unorder" [(ngModel)]="form_data.unorder" [checked]="form_data.unorder">
            <label>未発注</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('ordered', form_data.unorder, form_data.confirmed)">
            <input type="checkbox" name="ordered" [(ngModel)]="form_data.ordered" [checked]="form_data.ordered">
            <label>発注済</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('confirmed', form_data.unorder, form_data.ordered)">
            <input type="checkbox" name="confirmed" [(ngModel)]="form_data.confirmed" [checked]="form_data.confirmed">
            <label>確認済</label>
          </div>
        </div>
        <div class="line">
          <label>発注先</label>
          <com-dropdown #supplierComboEm [settings]="supplierCombo" [(selectedValue)]="form_data.supplier_id"></com-dropdown>
          <label>葬家名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.soke_name">
          </div>
          <label>故人名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.kojin_name">
          </div>
          <label>喪主名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.moshu_name">
          </div>
          <label class="large">キャンセル状況</label>
          <div class="ui checkbox" (click)="checkClick('not_canceled', form_data.canceled)">
            <input type="checkbox" name="not_canceled" [(ngModel)]="form_data.not_canceled">
            <label>未</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('canceled', form_data.not_canceled)">
            <input type="checkbox" name="canceled" [(ngModel)]="form_data.canceled">
            <label>済</label>
          </div>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="kumotsu_list?.length">
          全{{kumotsu_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?kumotsu_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="check">
                <div class="ui checkbox all_check" [class.disabled]="!kumotsu_list?.length">
                  <input type="checkbox" [(ngModel)]="all_check" (change)="checkAllItem()">
                  <label></label>
                </div>
              </th>
              <th class="entry_id">申込番号</th>
              <th class="entry_name"><p>申込者名</p>電話番号</th>
              <th class="okurinushi_name">送り主名</th>
              <th class="okurinushi_company"><p>組織名</p>役職名</th>
              <th class="renmei"><p>連名１</p>連名２</th>
              <th class="seko_date"><p>施行日</p>施行拠点</th>
              <th class="soke_name">葬家名</th>
              <th class="kojin_name"><p>故人名</p>喪主名</th>
              <th class="item_name"><p>商品名</p>価格</th>
              <th class="entry_ts">申込日時</th>
              <th class="supplier"><p>発注先</p>発注状況</th>
              <th class="order_ts"><p>発注日時</p>納入日時</th>
              <th class="is_cancel">キャンセル</th>
              <th class="operation" [class.admin]="login_company.base_type === Const.BASE_TYPE_ADMIN"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let kumotsu of kumotsu_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" (click)="checkItem($event, kumotsu)">
              <td class="center aligned check">
                <div class="ui checkbox">
                  <input type="checkbox" [(ngModel)]="kumotsu.selected">
                  <label></label>
                </div>
              </td>
              <td class="center aligned entry_id" title="{{kumotsu.entry_detail.entry.id}}">{{kumotsu.entry_detail.entry.id}}</td>
              <td class="entry_name">
                <div title="{{kumotsu.entry_detail.entry.entry_name}}">{{kumotsu.entry_detail.entry.entry_name}}</div>
                <div title="{{kumotsu.entry_detail.entry.entry_tel}}">{{kumotsu.entry_detail.entry.entry_tel}}</div>
              </td>
              <td class="okurinushi_name" title="{{kumotsu.okurinushi_name}}">{{kumotsu.okurinushi_name}}</td>
              <td class="okurinushi_company">
                <div title="{{kumotsu.okurinushi_company}}">{{kumotsu.okurinushi_company}}</div>
                <div title="{{kumotsu.okurinushi_title}}">{{kumotsu.okurinushi_title}}</div>
              </td>
              <td class="renmei">
                <div title="{{kumotsu.renmei1}}">{{kumotsu.renmei1}}</div>
                <div title="{{kumotsu.renmei2}}">{{kumotsu.renmei2}}</div>
              </td>
              <td class="center aligned seko_date">
                <div title="{{kumotsu.entry_detail.entry.seko.seko_date | date: 'yyyy/MM/dd'}}">{{kumotsu.entry_detail.entry.seko.seko_date | date: 'yyyy/MM/dd'}}</div>
                <div title="{{kumotsu.entry_detail.entry.seko.seko_department_name}}">{{kumotsu.entry_detail.entry.seko.seko_department_name}}</div>
              </td>
              <td class="soke_name" title="{{kumotsu.entry_detail.entry.seko.soke_name}}">{{kumotsu.entry_detail.entry.seko.soke_name}}</td>
              <td class="kojin_name">
                <div title="{{kumotsu.entry_detail.entry.seko.kojin[0].name}}">{{kumotsu.entry_detail.entry.seko.kojin[0].name}}</div>
                <div title="{{kumotsu.entry_detail.entry.seko.moshu.name}}">{{kumotsu.entry_detail.entry.seko.moshu.name}}</div>
              </td>
              <td class="item_name">
                <div title="{{kumotsu.entry_detail.item_name}}">{{kumotsu.entry_detail.item_name}}</div>
                <div class="right" title="{{(kumotsu.entry_detail.item_unit_price * kumotsu.entry_detail.quantity) | number}}">
                  {{(kumotsu.entry_detail.item_unit_price * kumotsu.entry_detail.quantity) | number}}
                </div>
              </td>
              <td class="center aligned entry_ts" title="{{kumotsu.entry_detail.entry.entry_ts | date: 'MM/dd H:mm'}}">
                {{kumotsu.entry_detail.entry.entry_ts | date: 'MM/dd H:mm'}}</td>
              <td class="supplier">
                <div class="center" title="{{kumotsu.supplier?kumotsu.supplier.name:''}}">{{kumotsu.supplier?kumotsu.supplier.name:''}}</div>
                <div class="center" title="{{Utils.getOrderStatusName(kumotsu.order_status)}}">{{Utils.getOrderStatusName(kumotsu.order_status)}}</div>

              </td>
              <td class="center aligned order_ts">
                <div title="{{kumotsu.order_ts | date: 'MM/dd H:mm'}}">{{kumotsu.order_ts | date: 'MM/dd H:mm'}}</div>
                <div title="{{kumotsu.delivery_ts | date: 'MM/dd H:mm'}}">{{kumotsu.delivery_ts | date: 'MM/dd H:mm'}}</div>
              </td>
              <td class="center aligned is_cancel" title="{{kumotsu.entry_detail.entry.cancel_ts?'キャンセル済':''}}">
                {{kumotsu.entry_detail.entry.cancel_ts?'キャンセル済':''}}
              </td>

              <td class="center aligned button operation" [class.admin]="login_company.base_type === Const.BASE_TYPE_ADMIN">
                <i class="large file pdf icon" title="発注PDF" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN" (click)="downloadPDF(kumotsu.entry_detail.id)"></i>
<!--
                <i class="large hands helping icon" title="発注先" *ngIf="canChangeSupplier(kumotsu)" (click)="showSupplier(kumotsu)"></i>
                <i class="large truck icon" title="納入日時" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN" (click)="showDelivery(kumotsu)"></i>
-->
                <i class="large tag icon" title="発注補足情報" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN" (click)="showItemEdit(kumotsu)"></i>
                <i class="large fax icon" title="発注書送信" *ngIf="canOrder(kumotsu)" (click)="showSendFax(kumotsu)"></i>
                <i class="large ban icon" title="キャンセル" *ngIf="(login_company.base_type === Const.BASE_TYPE_COMPANY|| login_company.base_type === Const.BASE_TYPE_ADMIN) && !kumotsu.entry_detail.entry.cancel_ts" (click)="showOrderCancel(kumotsu.entry_detail.entry)"></i>
                <i class="large linkify icon" title="領収書URL" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN" (click)="showUrl(kumotsu.entry_detail.entry.id)"></i>
                <i class="large edit icon" title="申込情報編集" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN" (click)="showKumotsuData(kumotsu)"></i>
              </td>
            </tr>
            </ng-container>
            <tr *ngIf="!kumotsu_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="11">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>
<!--
      <div class="ui modal supplier" id="supplier">
        <div class="header ui"><i class="hands helping icon large"></i>発注先</div>
        <div class="content">
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!supplier_list?.length">
                  <th class="select">選択</th>
                  <th class="supplier_name">発注先名</th>
                  <th class="supplier_address">住所</th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngFor="let supplier of supplier_list">
                  <tr (click)="selectSupplier(supplier)">
                    <td class="center aligned select">
                      <div class="ui radio checkbox">
                        <input type="radio" name="supplier_default" [checked]="supplier.selected">
                        <label></label>
                      </div>
                    </td>
                    <td class="supplier_name" title="{{supplier.name}}">{{supplier.name}}</td>
                    <td class="supplier_address" title="{{supplier.address}}">{{supplier.address}}</td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveKumotsuSupplier()" [disabled]="!supplierCanSave()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeDialog()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal small delivery" id="delivery">
        <div class="header ui"><i class="truck icon large"></i>納入日時</div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="selected_kumotsu">
            <div class="line">
              <label class="required">納入日時</label>
              <com-calendar [settings]="calendarOptionDateTime" [(value)]="selected_kumotsu.delivery_ts"></com-calendar>
            </div>
            <div class="line row4">
              <label>発注備考</label>
              <div class="ui input textarea">
                  <textarea rows="4" [(ngModel)]="selected_kumotsu.order_note"></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveDelivery()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeDialog()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
-->
      <div class="ui modal item-edit" id="item-edit">
        <div class="header ui"><i class="tag icon large"></i>発注補足情報</div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="sub_title">
            発注先
          </div>
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!supplier_list?.length">
                  <th class="select">選択</th>
                  <th class="supplier_name">発注先名</th>
                  <th class="supplier_address">住所</th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table" [class.disabled]="!canChangeSupplier(selected_kumotsu)">
              <tbody>
                <ng-container *ngFor="let supplier of supplier_list">
                  <tr (click)="selectSupplier(supplier)">
                    <td class="center aligned select">
                      <div class="ui radio checkbox">
                        <input type="radio" name="supplier_default" [checked]="supplier.selected" [disabled]="!canChangeSupplier(selected_kumotsu)">
                        <label></label>
                      </div>
                    </td>
                    <td class="supplier_name" title="{{supplier.name}}">{{supplier.name}}</td>
                    <td class="supplier_address" title="{{supplier.address}}">{{supplier.address}}</td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
          <div class="sub_title">
          </div>
          <div class="input_area" *ngIf="selected_kumotsu">
            <div class="line">
              <label>納入日時</label>
              <com-calendar [settings]="calendarOptionDateTime" [(value)]="selected_kumotsu.delivery_ts"></com-calendar>
            </div>
            <div class="line row2">
              <label>発注備考</label>
              <div class="ui input textarea full">
                  <textarea rows="2" [(ngModel)]="selected_kumotsu.order_note"></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveItem()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeDialog()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal mini url" id="receipt-url">
        <div class="header ui"><i class="linkify icon large"></i>
          領収書URL
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <div class="ui input fluid">
                <input type="text" autocomplete="off" value="{{receipt_url}}" readonly (focus)="onFocus($event)">
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="ui modal big" id="order-cancel">
        <div class="header ui"><i class="ban icon large"></i>
          申込キャンセル
        </div>
        <div class="content" *ngIf="selected_entry">
          <app-com-order-cancel [entry_id]="selected_entry.id"></app-com-order-cancel>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="cancelOrder()">
            <i class="ban icon"></i>キャンセル実行
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeCancelOrder()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal fax-pdf" id="send-fax">
        <div class="header ui"><i class="fax icon large"></i>
          発注書送信
        </div>
        <div class="content ui segment" [class.loading]="fax_pdf_loading">
          <ng2-pdfjs-viewer #pdfViewerEm [fullScreen]="false" [openFile]="false" [print]="true" [find]="false" [viewBookmark]="false" downloadFileName="供花・供物発注書" zoom="page-height">
          </ng2-pdfjs-viewer>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="faxPDF()">
            <i class="fax icon"></i>発注書送信
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeDialog()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
      <div class="ui modal small kumotsu-edit" id="kumotsu-edit">
        <div class="header ui"><i class="edit icon large"></i>
          申込情報編集
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="kumotsu_edit">
            <div class="line">
              <label>組織名</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_company" maxlength="25" [(ngModel)]="kumotsu_edit.okurinushi_company">
              </div>
            </div>
            <div class="line">
              <label>役職名</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_title" maxlength="15" [(ngModel)]="kumotsu_edit.okurinushi_title">
              </div>
            </div>
            <div class="line">
              <label>組織・役職カナ</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_company_kana" maxlength="40" [(ngModel)]="kumotsu_edit.okurinushi_company_kana">
              </div>
            </div>
            <div class="line">
              <label class="required">氏名</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="okurinushi_name" maxlength="15" [(ngModel)]="kumotsu_edit.okurinushi_name">
              </div>
              <label class="required">氏名カナ</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_kana" [(ngModel)]="kumotsu_edit.okurinushi_kana">
              </div>
            </div>
            <div class="line">
              <label class="required">郵便番号</label>
              <div class="ui input">
                <input type="tel" autocomplete="off" id="okurinushi_zip_code" maxlength="7" [(ngModel)]="kumotsu_edit.okurinushi_zip_code" (input)="zipcodeChange()">
              </div>
              <label class="required">電話番号</label>
              <div class="ui input full">
                <input type="tel" autocomplete="off" id="okurinushi_tel" maxlength="15" [(ngModel)]="kumotsu_edit.okurinushi_tel">
              </div>
            </div>
            <div class="line">
              <label class="required">都道府県</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="okurinushi_prefecture" [(ngModel)]="kumotsu_edit.okurinushi_prefecture">
              </div>
              <label class="required">市区町村</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_address_1" [(ngModel)]="kumotsu_edit.okurinushi_address_1">
              </div>
            </div>
            <div class="line">
              <label class="required">所番地</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="okurinushi_address_2" [(ngModel)]="kumotsu_edit.okurinushi_address_2">
              </div>
              <label>建屋名</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_address_3" maxlength="26" [(ngModel)]="kumotsu_edit.okurinushi_address_3">
              </div>
            </div>
            <div class="line">
              <label>連名１</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="renmei1" maxlength="15" [(ngModel)]="kumotsu_edit.renmei1">
              </div>
              <label>連名１カナ</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="renmei_kana1" [(ngModel)]="kumotsu_edit.renmei_kana1">
              </div>
            </div>
            <div class="line">
              <label>連名２</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="renmei2" maxlength="15" [(ngModel)]="kumotsu_edit.renmei2">
              </div>
              <label>連名２カナ</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="renmei_kana2" [(ngModel)]="kumotsu_edit.renmei_kana2">
              </div>
            </div>
            <div class="line">
              <label>備考</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="note" [(ngModel)]="kumotsu_edit.note">
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveKumotsuData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeKumotsuEdit()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

		</div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group">
    <button class="ui labeled icon button mini light" [class.disabled]="buttonDisabled(Const.ORDER_STATUS_ID_UNORDER)" (click)="saveOrderStatus(Const.ORDER_STATUS_ID_ORDERED)">
      <i class="check icon"></i>発注済
    </button>
    <button class="ui labeled icon button mini light" [class.disabled]="buttonDisabled(Const.ORDER_STATUS_ID_ORDERED)" (click)="saveOrderStatus(Const.ORDER_STATUS_ID_CONFIRMED)">
      <i class="double check icon"></i>発注確認
    </button>
    <button class="ui labeled icon button mini light" [class.disabled]="buttonDisabled(Const.ORDER_STATUS_ID_CONFIRMED)" (click)="saveOrderStatus(Const.ORDER_STATUS_ID_ORDERED)">
      <i class="times icon"></i>確認取消
    </button>
    <button class="ui labeled icon button mini light" (click)="exportCsv()">
      <i class="download icon"></i>CSV出力
    </button>
  </div>
</div>
