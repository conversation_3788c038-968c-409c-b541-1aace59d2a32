from datetime import date

from dateutil.relativedelta import relativedelta
from django.test import TestCase
from django.utils import timezone

from advertises.models import Advertise
from advertises.tests.factories import AdvertiseFactory


class AdvertiseModelTest(TestCase):
    def test_faq_disable(self) -> None:
        """広告を無効化(論理削除)する"""
        advertise: Advertise = AdvertiseFactory()
        self.assertFalse(advertise.del_flg)
        advertise.disable()
        self.assertTrue(advertise.del_flg)

    def test_filter_active(self) -> None:
        """広告のうち指定した日に有効なものを取得する"""
        today: date = timezone.localdate()
        advertise_1: Advertise = AdvertiseFactory(
            begin_date=today + relativedelta(days=-2), end_date=today + relativedelta(days=-1)
        )
        advertise_2: Advertise = AdvertiseFactory(
            begin_date=today + relativedelta(days=-1), end_date=today + relativedelta(days=1)
        )
        advertise_3: Advertise = AdvertiseFactory(
            begin_date=today, end_date=today + relativedelta(days=2)
        )
        advertise_4: Advertise = AdvertiseFactory(begin_date=today, end_date=today)
        advertise_5: Advertise = AdvertiseFactory(
            begin_date=today + relativedelta(days=1), end_date=today + relativedelta(days=2)
        )

        filtered_1 = Advertise.objects.filter_active()
        self.assertEqual(
            set([rec.pk for rec in filtered_1]),
            set([advertise_2.pk, advertise_3.pk, advertise_4.pk]),
        )
        filtered_2 = Advertise.objects.filter_active(today + relativedelta(days=-1))
        self.assertEqual(
            set([rec.pk for rec in filtered_2]),
            set([advertise_1.pk, advertise_2.pk]),
        )
        filtered_3 = Advertise.objects.filter_active(today + relativedelta(days=1))
        self.assertEqual(
            set([rec.pk for rec in filtered_3]),
            set([advertise_2.pk, advertise_3.pk, advertise_5.pk]),
        )
