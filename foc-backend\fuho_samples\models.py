from django.db import models
from django.utils.translation import gettext_lazy as _

from bases.models import Base


class FuhoSample(models.Model):
    company = models.ForeignKey(
        Base,
        models.DO_NOTHING,
        verbose_name=_('company'),
        related_name='fuho_samples',
    )
    sentence = models.TextField(_('sentence'))
    display_num = models.IntegerField(_('display order'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_fuho_sample'

    def disable(self) -> None:
        """訃報を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()
