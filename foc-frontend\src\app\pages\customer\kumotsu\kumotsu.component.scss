@import "src/assets/scss/customer/setting";
.container .inner .contents {
  &.with-background-image {
    background-image: url(../../../../assets/img/customer/hana.jpg);
  }
  &.description >div {
    max-width: 750px;
  }
  .input .button.grey {
    width: 180px;
  }
  .remark {
    font-size: 1rem;
    line-height: 1.5;
    .important {
      color: $text-red;
    }
  }
  .notice {
    font-size: 1rem;
    line-height: 1.5;
    .important {
      color: $text-red;
    }
    margin-top: -10px;
  }
  .seko_company {
    font-size: 1rem;
    line-height: 1.5;
  }
  .input {
    .item-box {
      cursor: pointer;
      padding: 10px;
      width: 100%;
      height: 170px;
      margin: 2px;
      display: flex;
      justify-content: space-between;
      border: solid 1px $border-grey;
      &:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,.3);
        --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
      }
      .image-box {
        width: 150px;
        height: 150px;
        background-color: #fff;
        display: flex;
        justify-content: center;
        &.no-image {
          background-color: #e9e9e9;
        }
        img {
          height: auto;
          width: auto;
          max-height: 100%;
          max-width: 100%;
          margin: auto;
          min-width: 1px;
        }
        .no-image {
          margin-top: 10px;
          opacity: .5;
          font-size: 1.2rem;
        }
      }
      .title-box {
        line-height: 1.6;
        .selecting {
          width: 100px;
          height: 30px;
          text-align: center;
          font-size: 0.9rem;
          border: solid 1px $border-dark;
          border-radius: 20px;
          background-color: #cfcfcf;
          line-height: 1.8;
          &.no-selected {
            color: $text-red;
          }
        }
        .name {
          margin-top: 5px;
          font-size: 1.1rem;
          font-weight: bold;
          line-height: 1.2;
        }
        .price {
          font-size: 0.9rem;
        }
      }
      @media screen and (max-width: 560px) {
        height: 150px;
        .image-box {
          width: 130px;
          height: 130px;
        }
        .title-box {
          .selecting {
            width: 80px;
            height: 25px;
          }
        }
      }
      @media screen and (max-width: 380px) {
        height: 130px;
        .image-box {
          width: 110px;
          height: 110px;
        }
        .title-box {
          .selecting {
            width: 60px;
            height: 20px;
          }
        }
      }
    }
    &.error .item-box {
      border: double 2px $border-red;
    }
  }
  @media screen and (max-width: 560px) {
    .input .button-area, .input .item-box {
      margin-left: 10px;

    }
  }
  .button.center {
    margin-left: auto;
    margin-right: auto;
  }
}
.container .inner {
  >.button-area .button {
    @media screen and (max-width: 560px) {
      width: 130px;
    }
    @media screen and (max-width: 380px) {
      width: 110px;
    }
  }
  >.button-area .button.grey {
    width: 200px;
    @media screen and (max-width: 560px) {
      width: 180px;
    }
    @media screen and (max-width: 380px) {
      width: 150px;
    }
  }
}
.ui.modal.item {
  position: fixed !important;
  width: 90% !important;
  max-width: 810px;
  max-height: 60vh;
  min-height: 200px;
  top: calc((100vh - 564px) / 2 - 10px) !important;
  >.content {
    padding: 15px !important;
    font-size: 1.4rem;
    height: 100%;
    width: 100%;
    max-height: 60vh;
    display: flex;
    flex-wrap: wrap;
    .item-box {
      padding: 10px;
      width: 380px;
      height: 170px;
      margin: 2px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      border-bottom: solid 2px $border-grey;;
      &:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,.3);
        --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
        -webkit-transform: translateY(-1px);
        transform: translateY(-1px);
      }
      .image-box {
        width: 150px;
        height: 150px;
        background-color: #fff;
        display: flex;
        justify-content: center;
        &.no-image {
          background-color: #e9e9e9;
        }
        img {
          height: auto;
          width: auto;
          max-height: 100%;
          max-width: 100%;
          margin: auto;
          min-width: 1px;
        }
        .no-image {
          margin-top: 10px;
          opacity: .5;
          font-size: 1.2rem;
        }
      }
      .title-box {
        line-height: 1.8;
        .name {
          margin-top: 5px;
          font-size: 1.1rem;
          font-weight: bold;
        }
        .price {
          font-size: 0.9rem;
        }
      }
    }
  }
  @media screen and (max-width: 560px) {
    max-width: 405px;
    top: 50px !important;
    max-height: 70vh;
    >.content {
      max-height: 70vh;
      .item-box {
        height: 150px;
        .image-box {
          width: 130px;
          height: 130px;
        }
      }
    }
  }
  @media screen and (max-width: 380px) {
    >.content {
      padding: 10px !important;
      .item-box {
        height: 130px;
        .image-box {
          width: 110px;
          height: 110px;
        }
      }
    }
  }
}

.ui.modal.fuda {
  position: fixed !important;;
  width: 400px !important;
  min-width: 200px;
  height: 564px;
  top: calc((100vh - 564px) / 2 - 10px) !important;
  >.scrolling.content {
    max-height: none;
  }
  >.content {
    padding: 0 !important;
    font-size: 1.4rem;
    >.tatefuda {
      width: 200px;
      height: 450px;
      font-size: 0.95rem;
      margin: 40px auto 20px;
      border: 1px solid $border-dark;
      >.contents {
        writing-mode: vertical-rl;
        position: absolute;
        width: 50px;
        border-radius: 0;
        padding-bottom: 0;
        margin-bottom: 0;
      }
      >.okurinushi_company{
        top: 13%;
        left: 55%;
      }
      >.okurinushi_title{
        top: 16%;
        left: 48%;
      }
      >.okurinushi_name{
        font-size: 1.3rem;
        top: 30%;
        left: 42%;
      }
      >.renmei1{
        font-size: 1.3rem;
        top: 30%;
        left: 34%;
      }
      >.renmei2{
        font-size: 1.3rem;
        top: 30%;
        left: 26%;
      }
    }
    >.description {
      font-size: 0.95rem;
      padding-left: 30px !important;
      line-height: 1.3;
    }
  }
  @media screen and (max-width: 560px) {
    width: 300px !important;
    height: 502px;
    top: 20px !important;
    >.content {
      >.tatefuda {
        width: 175px;
        height: 400px;
        font-size: 0.9rem;
        margin-bottom: 0;
        >.okurinushi_company{
          top: 15%;
        }
        >.okurinushi_title{
          top: 18%;
        }
        >.okurinushi_name{
          font-size: 1.2rem;
          top: 40%;
        }
        >.renmei1{
          font-size: 1.2rem;
          top: 40%;
        }
        >.renmei2{
          font-size: 1.2rem;
          top: 40%;
        }
      }
    }
  }
  @media screen and (max-width: 380px) {
    height: 472px;
    >.content {
      >.tatefuda {
        width: 150px;
        height: 370px;
        font-size: 1.05rem;
        >.okurinushi_company{
          top: 16%;
          left: 53%;
        }
        >.okurinushi_title{
          top: 19%;
        }
        >.okurinushi_name{
          font-size: 1.3rem;
          top: 39%;
        }
        >.renmei1{
          font-size: 1.3rem;
          top: 39%;
        }
        >.renmei2{
          font-size: 1.3rem;
          top: 39%;
        }
      }
    }
  }
}
