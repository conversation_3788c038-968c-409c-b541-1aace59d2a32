from base64 import b64encode
from datetime import date
from io import BytesIO

from django import template

from masters.models import Wareki

register = template.Library()


@register.filter
def b64encode_gif(img) -> str:
    gray_image = img.convert('L')
    buffer = BytesIO()
    gray_image.save(buffer, 'gif')
    b64encoded_image = b64encode(buffer.getvalue())
    return f'data:image/gif;base64,{b64encoded_image.decode("ascii")}'


@register.filter
def wareki_nen(the_date: date) -> str:
    return Wareki.japanese_era(the_date)
