# FOCプロジェクト 実装例集

## 概要

このドキュメントでは、FOCプロジェクトにおける具体的な実装例を示します。ユーザープロフィール編集機能を例に、実際のコードを交えて説明します。

## 1. フロントエンド実装例

### 1.1 コンポーネント実装

#### user-profile-edit.component.ts
```typescript
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClientService } from '../../service/http-client.service';
import { SessionService } from '../../service/session.service';
import { LoaderService } from '../../service/loader.service';

export interface UserProfileEditRequest {
  name: string;
  mail_address: string;
  phone: string;
}

export interface UserProfileGetResponse {
  id: number;
  name: string;
  mail_address: string;
  phone: string;
  updated_at: string;
}

@Component({
  selector: 'app-user-profile-edit',
  templateUrl: './user-profile-edit.component.html',
  styleUrls: ['./user-profile-edit.component.scss']
})
export class UserProfileEditComponent implements OnInit {
  userProfile: UserProfileEditRequest = {
    name: '',
    mail_address: '',
    phone: ''
  };

  errors: { [key: string]: string } = {};
  isLoading = false;

  constructor(
    private httpClientService: HttpClientService,
    private sessionSvc: SessionService,
    private loaderSvc: LoaderService,
    private router: Router
  ) {}

  async ngOnInit() {
    await this.loadUserProfile();
  }

  async loadUserProfile() {
    try {
      this.isLoading = true;
      this.loaderSvc.present();
      
      const response = await this.httpClientService.getUserProfile();
      this.userProfile = {
        name: response.name,
        mail_address: response.mail_address,
        phone: response.phone || ''
      };
    } catch (error) {
      console.error('プロフィール取得エラー:', error);
      this.showErrorMessage('プロフィールの取得に失敗しました。');
    } finally {
      this.isLoading = false;
      this.loaderSvc.dismiss();
    }
  }

  async saveUserProfile() {
    if (!this.validateForm()) {
      return;
    }

    try {
      this.isLoading = true;
      this.loaderSvc.present();
      
      await this.httpClientService.updateUserProfile(this.userProfile);
      
      this.showSuccessMessage('プロフィールを更新しました。');
      this.router.navigate(['/foc/top']);
    } catch (error) {
      console.error('プロフィール更新エラー:', error);
      
      if (error.status === 400 && error.error) {
        this.handleValidationErrors(error.error);
      } else {
        this.showErrorMessage('プロフィールの更新に失敗しました。');
      }
    } finally {
      this.isLoading = false;
      this.loaderSvc.dismiss();
    }
  }

  private validateForm(): boolean {
    this.errors = {};

    if (!this.userProfile.name || this.userProfile.name.trim().length < 2) {
      this.errors.name = '名前は2文字以上で入力してください。';
    }

    if (!this.userProfile.mail_address) {
      this.errors.mail_address = 'メールアドレスは必須です。';
    } else if (!this.isValidEmail(this.userProfile.mail_address)) {
      this.errors.mail_address = '有効なメールアドレスを入力してください。';
    }

    if (this.userProfile.phone && !this.isValidPhone(this.userProfile.phone)) {
      this.errors.phone = '有効な電話番号を入力してください。';
    }

    return Object.keys(this.errors).length === 0;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\d\-\(\)\+\s]+$/;
    return phoneRegex.test(phone);
  }

  private handleValidationErrors(errors: any) {
    this.errors = {};
    Object.keys(errors).forEach(field => {
      if (Array.isArray(errors[field])) {
        this.errors[field] = errors[field][0];
      } else {
        this.errors[field] = errors[field];
      }
    });
  }

  private showSuccessMessage(message: string) {
    const messageData = {
      type: 'success',
      title: message
    };
    this.sessionSvc.save('message', messageData);
  }

  private showErrorMessage(message: string) {
    const messageData = {
      type: 'error',
      title: message
    };
    this.sessionSvc.save('message', messageData);
  }

  onCancel() {
    this.router.navigate(['/foc/top']);
  }
}
```

#### user-profile-edit.component.html
```html
<div class="ui container">
  <div class="ui segment">
    <h2 class="ui header">
      <i class="user icon"></i>
      <div class="content">
        プロフィール編集
        <div class="sub header">ユーザー情報を編集できます</div>
      </div>
    </h2>

    <form class="ui form" [class.loading]="isLoading" (ngSubmit)="saveUserProfile()">
      <!-- 名前 -->
      <div class="field" [class.error]="errors.name">
        <label>名前 <span class="required">*</span></label>
        <input 
          type="text" 
          [(ngModel)]="userProfile.name" 
          name="name"
          placeholder="名前を入力してください"
          [disabled]="isLoading">
        <div class="ui pointing red basic label" *ngIf="errors.name">
          {{ errors.name }}
        </div>
      </div>

      <!-- メールアドレス -->
      <div class="field" [class.error]="errors.mail_address">
        <label>メールアドレス <span class="required">*</span></label>
        <input 
          type="email" 
          [(ngModel)]="userProfile.mail_address" 
          name="mail_address"
          placeholder="メールアドレスを入力してください"
          [disabled]="isLoading">
        <div class="ui pointing red basic label" *ngIf="errors.mail_address">
          {{ errors.mail_address }}
        </div>
      </div>

      <!-- 電話番号 -->
      <div class="field" [class.error]="errors.phone">
        <label>電話番号</label>
        <input 
          type="tel" 
          [(ngModel)]="userProfile.phone" 
          name="phone"
          placeholder="電話番号を入力してください（任意）"
          [disabled]="isLoading">
        <div class="ui pointing red basic label" *ngIf="errors.phone">
          {{ errors.phone }}
        </div>
      </div>

      <!-- ボタン -->
      <div class="ui buttons">
        <button 
          type="button" 
          class="ui button" 
          (click)="onCancel()"
          [disabled]="isLoading">
          キャンセル
        </button>
        <div class="or" data-text="または"></div>
        <button 
          type="submit" 
          class="ui positive button"
          [disabled]="isLoading">
          <i class="save icon"></i>
          保存
        </button>
      </div>
    </form>
  </div>
</div>
```

#### user-profile-edit.component.scss
```scss
.required {
  color: #db2828;
}

.ui.form .field.error .ui.pointing.label {
  margin-top: 0.5em;
}

.ui.buttons {
  margin-top: 1em;
}

.ui.container {
  margin-top: 2em;
}

.ui.segment {
  max-width: 600px;
  margin: 0 auto;
}
```

### 1.2 サービス拡張

#### http-client.service.ts への追加
```typescript
// ユーザープロフィール取得
public getUserProfile(): Promise<UserProfileGetResponse> {
  const url = this.host + '/staffs/profile/';
  const params = new HttpParams();
  Utils.log(url);
  return this.getWithToken<UserProfileGetResponse>('/staffs/profile/', params);
}

// ユーザープロフィール更新
public updateUserProfile(data: UserProfileEditRequest): Promise<UserProfileGetResponse> {
  Utils.log(this.host + '/staffs/profile/');
  return this.putWithToken<UserProfileEditRequest, UserProfileGetResponse>('/staffs/profile/', data);
}
```

### 1.3 ルーティング設定

#### app-routing.module.ts への追加
```typescript
const routes: Routes = [
  // 既存のルート...
  { 
    path: 'foc/user-profile/edit', 
    component: CompanyFrameComponent, 
    canActivate: [AuthFGuard] 
  },
  // ...
];
```

#### frame.component.ts での条件追加
```typescript
export class CompanyFrameComponent implements OnInit {
  // 既存のプロパティ...

  get isUserProfileEdit(): boolean {
    return this.router.url.includes('/foc/user-profile/edit');
  }

  // 既存のメソッド...
}
```

#### frame.component.html での表示条件追加
```html
<!-- 既存のコンポーネント表示条件... -->

<app-user-profile-edit *ngIf="isUserProfileEdit"></app-user-profile-edit>

<!-- ... -->
```

## 2. バックエンド実装例

### 2.1 シリアライザー実装

#### staffs/serializers.py への追加
```python
from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import Staff

class StaffProfileSerializer(serializers.ModelSerializer):
    """スタッフプロフィール用シリアライザー"""
    
    class Meta:
        model = Staff
        fields = ['id', 'name', 'mail_address', 'phone', 'updated_at']
        read_only_fields = ['id', 'updated_at']

    def validate_name(self, value):
        """名前のバリデーション"""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("名前は2文字以上で入力してください。")
        return value.strip()

    def validate_mail_address(self, value):
        """メールアドレスのバリデーション"""
        if not value:
            raise serializers.ValidationError("メールアドレスは必須です。")
        
        # 重複チェック（自分以外）
        queryset = Staff.objects.filter(mail_address=value)
        if self.instance:
            queryset = queryset.exclude(id=self.instance.id)
        
        if queryset.exists():
            raise serializers.ValidationError("このメールアドレスは既に使用されています。")
        
        return value

    def validate_phone(self, value):
        """電話番号のバリデーション"""
        if value:
            # 基本的な電話番号形式チェック
            import re
            phone_pattern = r'^[\d\-\(\)\+\s]+$'
            if not re.match(phone_pattern, value):
                raise serializers.ValidationError("有効な電話番号を入力してください。")
        return value

    def validate(self, data):
        """全体的なバリデーション"""
        # 追加の業務ロジックバリデーションがあればここに記述
        return data

    def update(self, instance, validated_data):
        """更新処理"""
        # 更新者情報を設定（リクエストユーザーから取得）
        if hasattr(self.context.get('request'), 'user'):
            validated_data['update_user_id'] = self.context['request'].user.id
        
        return super().update(instance, validated_data)


### 2.2 ビュー実装

#### staffs/views.py への追加
```python
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from .models import Staff
from .serializers import StaffProfileSerializer

class StaffProfileDetail(generics.RetrieveUpdateAPIView):
    """
    スタッフプロフィール詳細API

    get: 認証済みスタッフのプロフィールを取得します
    put: 認証済みスタッフのプロフィールを更新します
    patch: 認証済みスタッフのプロフィールを部分更新します
    """
    serializer_class = StaffProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        """認証済みユーザー自身のプロフィールを返す"""
        return self.request.user

    def get(self, request, *args, **kwargs):
        """プロフィール取得"""
        try:
            return super().get(request, *args, **kwargs)
        except Exception as e:
            return Response(
                {'detail': 'プロフィールの取得に失敗しました。'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def put(self, request, *args, **kwargs):
        """プロフィール全体更新"""
        try:
            return super().put(request, *args, **kwargs)
        except Exception as e:
            return Response(
                {'detail': 'プロフィールの更新に失敗しました。'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request, *args, **kwargs):
        """プロフィール部分更新"""
        try:
            return super().patch(request, *args, **kwargs)
        except Exception as e:
            return Response(
                {'detail': 'プロフィールの更新に失敗しました。'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
```

### 2.3 URL設定

#### staffs/urls.py への追加
```python
from django.urls import path
from . import views

urlpatterns = [
    # 既存のURL...
    path('profile/', views.StaffProfileDetail.as_view(), name='staff_profile_detail'),
    # ...
]
```

### 2.4 モデル拡張（必要に応じて）

#### staffs/models.py への追加
```python
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import RegexValidator

class Staff(AbstractUser):
    # 既存のフィールド...

    # 電話番号フィールドを追加する場合
    phone_regex = RegexValidator(
        regex=r'^[\d\-\(\)\+\s]+$',
        message="有効な電話番号を入力してください。"
    )
    phone = models.CharField(
        validators=[phone_regex],
        max_length=20,
        blank=True,
        null=True,
        verbose_name='電話番号'
    )

    class Meta:
        db_table = 'staffs_staff'
        verbose_name = 'スタッフ'
        verbose_name_plural = 'スタッフ'

    def __str__(self):
        return f"{self.name} ({self.login_id})"
```

### 2.5 マイグレーション作成
```bash
cd foc-backend
poetry run python manage.py makemigrations staffs --name add_phone_field
poetry run python manage.py migrate
```

## 3. テスト実装例

### 3.1 フロントエンドテスト

#### user-profile-edit.component.spec.ts
```typescript
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { UserProfileEditComponent } from './user-profile-edit.component';
import { HttpClientService } from '../../service/http-client.service';
import { SessionService } from '../../service/session.service';
import { LoaderService } from '../../service/loader.service';

describe('UserProfileEditComponent', () => {
  let component: UserProfileEditComponent;
  let fixture: ComponentFixture<UserProfileEditComponent>;
  let httpClientService: jasmine.SpyObj<HttpClientService>;
  let sessionService: jasmine.SpyObj<SessionService>;
  let loaderService: jasmine.SpyObj<LoaderService>;
  let router: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const httpClientSpy = jasmine.createSpyObj('HttpClientService', ['getUserProfile', 'updateUserProfile']);
    const sessionSpy = jasmine.createSpyObj('SessionService', ['save']);
    const loaderSpy = jasmine.createSpyObj('LoaderService', ['present', 'dismiss']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [UserProfileEditComponent],
      imports: [FormsModule, HttpClientTestingModule, RouterTestingModule],
      providers: [
        { provide: HttpClientService, useValue: httpClientSpy },
        { provide: SessionService, useValue: sessionSpy },
        { provide: LoaderService, useValue: loaderSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    httpClientService = TestBed.inject(HttpClientService) as jasmine.SpyObj<HttpClientService>;
    sessionService = TestBed.inject(SessionService) as jasmine.SpyObj<SessionService>;
    loaderService = TestBed.inject(LoaderService) as jasmine.SpyObj<LoaderService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UserProfileEditComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load user profile on init', async () => {
    const mockProfile = {
      id: 1,
      name: 'テストユーザー',
      mail_address: '<EMAIL>',
      phone: '090-1234-5678',
      updated_at: '2023-12-01T10:30:00Z'
    };
    httpClientService.getUserProfile.and.returnValue(Promise.resolve(mockProfile));

    await component.ngOnInit();

    expect(httpClientService.getUserProfile).toHaveBeenCalled();
    expect(component.userProfile.name).toBe('テストユーザー');
    expect(component.userProfile.mail_address).toBe('<EMAIL>');
    expect(component.userProfile.phone).toBe('090-1234-5678');
  });

  it('should validate form correctly', () => {
    // 有効なデータ
    component.userProfile = {
      name: 'テストユーザー',
      mail_address: '<EMAIL>',
      phone: '090-1234-5678'
    };
    expect(component['validateForm']()).toBe(true);
    expect(Object.keys(component.errors).length).toBe(0);

    // 無効なデータ
    component.userProfile = {
      name: '',
      mail_address: 'invalid-email',
      phone: 'invalid-phone'
    };
    expect(component['validateForm']()).toBe(false);
    expect(component.errors.name).toBeTruthy();
    expect(component.errors.mail_address).toBeTruthy();
    expect(component.errors.phone).toBeTruthy();
  });

  it('should save user profile successfully', async () => {
    const mockProfile = {
      id: 1,
      name: '更新されたユーザー',
      mail_address: '<EMAIL>',
      phone: '090-9876-5432',
      updated_at: '2023-12-01T11:00:00Z'
    };

    component.userProfile = {
      name: '更新されたユーザー',
      mail_address: '<EMAIL>',
      phone: '090-9876-5432'
    };

    httpClientService.updateUserProfile.and.returnValue(Promise.resolve(mockProfile));

    await component.saveUserProfile();

    expect(httpClientService.updateUserProfile).toHaveBeenCalledWith(component.userProfile);
    expect(sessionService.save).toHaveBeenCalledWith('message', jasmine.objectContaining({
      type: 'success',
      title: 'プロフィールを更新しました。'
    }));
    expect(router.navigate).toHaveBeenCalledWith(['/foc/top']);
  });

  it('should handle validation errors', async () => {
    const validationError = {
      status: 400,
      error: {
        name: ['名前は2文字以上で入力してください。'],
        mail_address: ['このメールアドレスは既に使用されています。']
      }
    };

    component.userProfile = {
      name: 'a',
      mail_address: '<EMAIL>',
      phone: ''
    };

    httpClientService.updateUserProfile.and.returnValue(Promise.reject(validationError));

    await component.saveUserProfile();

    expect(component.errors.name).toBe('名前は2文字以上で入力してください。');
    expect(component.errors.mail_address).toBe('このメールアドレスは既に使用されています。');
  });
});
```

### 3.2 バックエンドテスト

#### staffs/tests/test_views_profile.py
```python
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from staffs.models import Staff
from staffs.tests.factories import StaffFactory
from bases.tests.factories import BaseFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]

class StaffProfileDetailTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.base = BaseFactory()
        self.staff = StaffFactory(base=self.base)
        self.url = reverse('staff_profile_detail')

    def _authenticate(self, user):
        """認証ヘッダーを設定"""
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {refresh.access_token}')

    def test_get_profile_authenticated(self):
        """認証済みユーザーのプロフィール取得"""
        self._authenticate(self.staff)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], self.staff.id)
        self.assertEqual(response.data['name'], self.staff.name)
        self.assertEqual(response.data['mail_address'], self.staff.mail_address)

    def test_get_profile_unauthenticated(self):
        """未認証ユーザーのアクセス拒否"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_update_profile_success(self):
        """プロフィール更新成功"""
        self._authenticate(self.staff)
        data = {
            'name': '更新されたユーザー',
            'mail_address': '<EMAIL>',
            'phone': '090-9999-9999'
        }

        response = self.client.put(self.url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.staff.refresh_from_db()
        self.assertEqual(self.staff.name, '更新されたユーザー')
        self.assertEqual(self.staff.mail_address, '<EMAIL>')
        self.assertEqual(self.staff.phone, '090-9999-9999')

    def test_update_profile_validation_error(self):
        """バリデーションエラー"""
        self._authenticate(self.staff)
        data = {
            'name': '',  # 空の名前
            'mail_address': 'invalid-email'  # 無効なメールアドレス
        }

        response = self.client.put(self.url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('name', response.data)
        self.assertIn('mail_address', response.data)

    def test_update_profile_duplicate_email(self):
        """重複メールアドレスエラー"""
        # 別のスタッフを作成
        other_staff = StaffFactory(base=self.base, mail_address='<EMAIL>')

        self._authenticate(self.staff)
        data = {
            'name': 'テストユーザー',
            'mail_address': '<EMAIL>'  # 既存のメールアドレス
        }

        response = self.client.put(self.url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('mail_address', response.data)
        self.assertIn('既に使用されています', str(response.data['mail_address']))

    def test_partial_update_profile(self):
        """部分更新"""
        self._authenticate(self.staff)
        original_email = self.staff.mail_address

        data = {
            'name': '部分更新されたユーザー'
        }

        response = self.client.patch(self.url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.staff.refresh_from_db()
        self.assertEqual(self.staff.name, '部分更新されたユーザー')
        self.assertEqual(self.staff.mail_address, original_email)  # 変更されていない
```

## 4. 統合テスト例

### 4.1 E2Eテスト（Protractor）

#### e2e/user-profile-edit.e2e-spec.ts
```typescript
import { browser, by, element } from 'protractor';

describe('User Profile Edit Page', () => {
  beforeEach(() => {
    // ログイン処理
    browser.get('/foc/login');
    element(by.name('login_id')).sendKeys('test_user');
    element(by.name('password')).sendKeys('test_password');
    element(by.css('button[type="submit"]')).click();

    // プロフィール編集ページに移動
    browser.get('/foc/user-profile/edit');
  });

  it('should display profile edit form', () => {
    expect(element(by.css('h2')).getText()).toContain('プロフィール編集');
    expect(element(by.name('name')).isPresent()).toBe(true);
    expect(element(by.name('mail_address')).isPresent()).toBe(true);
    expect(element(by.name('phone')).isPresent()).toBe(true);
  });

  it('should update profile successfully', () => {
    const nameField = element(by.name('name'));
    const emailField = element(by.name('mail_address'));
    const phoneField = element(by.name('phone'));
    const saveButton = element(by.css('button[type="submit"]'));

    nameField.clear();
    nameField.sendKeys('更新されたユーザー');

    emailField.clear();
    emailField.sendKeys('<EMAIL>');

    phoneField.clear();
    phoneField.sendKeys('090-9999-9999');

    saveButton.click();

    // 成功メッセージの確認
    browser.wait(() => {
      return browser.getCurrentUrl().then(url => url.includes('/foc/top'));
    }, 5000);
  });

  it('should show validation errors', () => {
    const nameField = element(by.name('name'));
    const saveButton = element(by.css('button[type="submit"]'));

    nameField.clear();
    saveButton.click();

    expect(element(by.css('.ui.pointing.red.basic.label')).isDisplayed()).toBe(true);
  });
});
```

このように、FOCプロジェクトでは Angular + Django の組み合わせで、型安全性とテスタビリティを重視した実装を行います。各層での適切なバリデーション、エラーハンドリング、テストケースの実装により、保守性の高いコードベースを維持できます。
```
