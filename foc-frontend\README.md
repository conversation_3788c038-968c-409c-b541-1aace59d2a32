# Funeral Online system Connect (FOC)

## ＜インストール方法＞
### 1.「Node.js」をインストールする。(本プロジェクトではv12.18.3を利用しています)
https://nodejs.org/download/release/v12.18.3/
```bat
こちらから自分の環境に合わせたインストーラーをダウンロードして、インストールをしてください。
Windows(32bit)→node-v12.18.3-x86.msi
Windows(64bit)→node-v12.18.3-x64.msi
macOS→node-v12.18.3.pkg
```
### 2.「Node.js」のインストールが出来ているか確認する。
```bat
コマンドプロンプト(ターミナル)に以下のコマンドを入力する。
node --version

これでNode.jsのバージョン(v12.18.3)が表示されればOK。
```

### 3.「npm」がインストール出来ているか確認する。
```bat
(Node.jsがインストール出来ていれば一緒にインストールされているはず)
コマンドプロンプト(ターミナル)に以下のコマンドを入力する。
npm --version

これでnpmのバージョン(v6.14.6)が表示されればOK。
```

### 4.「npm」を利用して、プロジェクトの起動に必要なライブラリをインストールする。
```bat
gitからローカルにクローンしたディレクトリに移動して、以下のコマンドを入力する。
npm install
```
### 5.「Angular CLI」がインストール出来ているか確認する。
```bat
コマンドプロンプト(ターミナル)に以下のコマンドを入力する。
ng --version

これでAngular CLIのバージョン(v10.0.5)が表示されればOK。
```

## ＜プロジェクトの起動方法＞
### 1.gitからローカルにクローンしたディレクトリに移動して、以下のコマンドを入力する。
```bat
ng serve --open

ビルドが成功すると、既定のブラウザに設定しているブラウザでプロジェクトが起動する。
```
