from django.db import models
from django.utils.translation import gettext_lazy as _

from bases.models import Base


class Faq(models.Model):
    company = models.ForeignKey(
        Base, models.PROTECT, verbose_name=_('company'), related_name='faqs'
    )
    question = models.TextField(_('question'))
    answer = models.TextField(_('answer'))
    display_num = models.IntegerField(_('display order'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_faq'

    def disable(self) -> None:
        """FAQを即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()
