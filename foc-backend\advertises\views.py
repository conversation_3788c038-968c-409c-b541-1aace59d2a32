from django.db.models import Q, QuerySet
from rest_framework import exceptions, generics

from advertises.models import Advertise
from advertises.serializers import AdvertiseSerializer
from bases.models import Base
from utils.permissions import IsStaff, ReadNeedAuth
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class AdvertiseList(AddContextMixin, generics.ListCreateAPIView):
    serializer_class = AdvertiseSerializer
    permission_classes = [IsStaff | ReadNeedAuth]

    def get_queryset(self):
        company_id = self.request.query_params.get('company', None)
        if not company_id:
            raise exceptions.ParseError()
        specified_base: Base = Base.objects.get(Q(pk=company_id) & Q(del_flg=False))
        base_ids: QuerySet = (
            specified_base.get_descendants(include_self=True)
            .filter(Q(del_flg=False))
            .values_list('id', flat=True)
        )
        return (
            Advertise.objects.select_related('company')
            .filter(Q(company_id__in=base_ids) & Q(del_flg=False))
            .all()
            .order_by('company', 'pk')
        )


class AdvertiseDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    queryset = Advertise.objects.filter(Q(del_flg=False)).all()
    serializer_class = AdvertiseSerializer
    permission_classes = [IsStaff | ReadNeedAuth]
