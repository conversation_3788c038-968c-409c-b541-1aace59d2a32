from django.db import models
from django.utils.translation import gettext_lazy as _

from bases.models import Base
from masters.models import ChobunDaishiMaster


class <PERSON>bun<PERSON><PERSON><PERSON>(models.Model):
    company = models.ForeignKey(Base, models.CASCADE, verbose_name=_('company'))
    daishi = models.ForeignKey(ChobunDaishiMaster, models.PROTECT, verbose_name=_('chobun daishi'))

    class Meta:
        db_table = 'm_chobun_daishi'
