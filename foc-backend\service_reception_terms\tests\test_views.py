from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from service_reception_terms.models import Base
from service_reception_terms.tests.factories import ServiceReceptionTermFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class ServiceReceptionTermListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.base_1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.service_reception_term_1 = ServiceReceptionTermFactory(department=self.base_1)
        self.service_reception_term_2 = ServiceReceptionTermFactory(department=self.base_1)
        self.service_reception_term_3 = ServiceReceptionTermFactory(
            department=BaseFactory(base_type=Base.OrgType.COMPANY)
        )

        self.staff = StaffFactory(base=self.base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_service_reception_term_list_succeed(self) -> None:
        """サービス締切一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('service_reception_terms:service_reception_term_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        param_service_reception_term_ids = [
            self.service_reception_term_1.id,
            self.service_reception_term_2.id,
            self.service_reception_term_3.id,
        ]
        record_service_reception_term_ids = [record['id'] for record in records]
        self.assertSetEqual(
            set(param_service_reception_term_ids), set(record_service_reception_term_ids)
        )

    def test_service_reception_term_list_succeed_without_auth(self) -> None:
        """サービス締切一覧取得APIがAuthorizationヘッダがなくても成功する"""

        params: Dict = {}
        response = self.api_client.get(
            reverse('service_reception_terms:service_reception_term_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        param_service_reception_term_ids = [
            self.service_reception_term_1.id,
            self.service_reception_term_2.id,
            self.service_reception_term_3.id,
        ]
        record_service_reception_term_ids = [record['id'] for record in records]
        self.assertSetEqual(
            set(param_service_reception_term_ids), set(record_service_reception_term_ids)
        )

    def test_supplier_list_succeed_by_department(self) -> None:
        """拠点で絞り込みしたサービス締切一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'department': self.base_1.pk,
        }
        response = self.api_client.get(
            reverse('service_reception_terms:service_reception_term_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        for record in records:
            self.assertEqual(record['department'], self.base_1.pk)

        param_service_reception_term_ids = [
            self.service_reception_term_1.id,
            self.service_reception_term_2.id,
        ]
        record_service_reception_term_ids = [record['id'] for record in records]
        self.assertSetEqual(
            set(param_service_reception_term_ids), set(record_service_reception_term_ids)
        )
