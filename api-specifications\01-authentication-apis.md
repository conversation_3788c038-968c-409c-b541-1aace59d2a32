# 認証API仕様書

## 概要

FOCシステムの認証関連APIの詳細仕様です。スタッフ認証と葬家認証の2つの認証方式をサポートしています。

## 1. スタッフ認証API

### 1.1 スタッフログイン

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /staffs/login/` |
| **機能** | スタッフユーザーのログイン認証とJWTトークン取得 |
| **認証** | 不要 |
| **実装クラス** | `StaffTokenObtainPair` |
| **シリアライザー** | `StaffTokenObtainPairSerializer` |

#### リクエスト仕様

**Content-Type:** `application/json`

**リクエストボディ:**
```json
{
  "company_code": "string",
  "login_id": "string", 
  "password": "string"
}
```

**パラメータ詳細:**

| フィールド | 型 | 必須 | 桁数制限 | 説明 | バリデーション |
|---|---|---|---|---|---|
| `company_code` | string | ✓ | 最大50文字 | 会社コード | 存在する会社コードである必要がある |
| `login_id` | string | ✓ | 最大150文字 | ログインID | 英数字、記号使用可能 |
| `password` | string | ✓ | 最大128文字 | パスワード | ハッシュ化されて検証される |

#### バリデーション処理

1. **会社コード + ログインID + パスワードの組み合わせ認証**
   ```python
   authenticate_kwargs = {
       'company_code': attrs['company_code'],
       'login_id': attrs['login_id'],
       'password': attrs['password'],
   }
   user = authenticate(**authenticate_kwargs)
   ```

2. **ユーザー状態チェック**
   - ユーザーが存在すること
   - `is_active`がTrueであること
   - `del_flg`がFalseであること（削除されていない）
   - `retired_flg`がFalseであること（退職していない）

3. **認証バックエンド処理**
   - `CombinationModelBackend`による認証
   - パスワードハッシュの検証
   - 最終ログイン日時の更新

#### レスポンス仕様

**成功時（200 OK）:**
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
}
```

**エラー時（401 Unauthorized）:**
```json
{
  "detail": "No active account found with the given credentials"
}
```

**エラー時（400 Bad Request）:**
```json
{
  "company_code": ["This field is required."],
  "login_id": ["This field is required."],
  "password": ["This field is required."]
}
```

#### 使用例

**リクエスト例:**
```bash
curl -X POST http://localhost:8000/staffs/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "company_code": "COMPANY001",
    "login_id": "staff001",
    "password": "password123"
  }'
```

**レスポンス例:**
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************...",
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************..."
}
```

### 1.2 アクセストークン更新

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /staffs/token/refresh/` |
| **機能** | リフレッシュトークンを使用してアクセストークンを更新 |
| **認証** | リフレッシュトークン必須 |
| **実装クラス** | `TokenRefreshView`（django-rest-framework-simplejwt） |

#### リクエスト仕様

**Content-Type:** `application/json`

**リクエストボディ:**
```json
{
  "refresh": "string"
}
```

**パラメータ詳細:**

| フィールド | 型 | 必須 | 説明 | バリデーション |
|---|---|---|---|---|
| `refresh` | string | ✓ | リフレッシュトークン | 有効なJWTリフレッシュトークンである必要がある |

#### レスポンス仕様

**成功時（200 OK）:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
}
```

**エラー時（401 Unauthorized）:**
```json
{
  "detail": "Token is invalid or expired",
  "code": "token_not_valid"
}
```

### 1.3 トークン検証

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /staffs/token/verify/` |
| **機能** | JWTトークンの有効性を検証 |
| **認証** | 不要 |
| **実装クラス** | `TokenVerifyView`（django-rest-framework-simplejwt） |

#### リクエスト仕様

**Content-Type:** `application/json`

**リクエストボディ:**
```json
{
  "token": "string"
}
```

**パラメータ詳細:**

| フィールド | 型 | 必須 | 説明 | バリデーション |
|---|---|---|---|---|
| `token` | string | ✓ | 検証対象のJWTトークン | 有効なJWTトークンである必要がある |

#### レスポンス仕様

**成功時（200 OK）:**
```json
{}
```

**エラー時（401 Unauthorized）:**
```json
{
  "detail": "Token is invalid or expired",
  "code": "token_not_valid"
}
```

## 2. 葬家認証API

### 2.1 葬家ログイン

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /seko/login/` |
| **機能** | 葬家（喪主）ユーザーのログイン認証とJWTトークン取得 |
| **認証** | 不要 |
| **実装クラス** | `MoshuTokenObtainPair` |
| **シリアライザー** | `MoshuTokenObtainSerializer` |

#### リクエスト仕様

**Content-Type:** `application/json`

**リクエストボディ:**
```json
{
  "seko": "integer",
  "password": "string"
}
```

**パラメータ詳細:**

| フィールド | 型 | 必須 | 説明 | バリデーション |
|---|---|---|---|---|
| `seko` | integer | ✓ | 施行ID | 存在する施行IDである必要がある |
| `password` | string | ✓ | パスワード | ハッシュ化されて検証される |

#### バリデーション処理

1. **施行ID存在チェック**
   ```python
   seko = Seko.objects.filter(Q(pk=value) & Q(del_flg=False)).first()
   if not seko:
       raise ValidationError(f'Seko not found: ID={value}')
   ```

2. **認証処理**
   ```python
   authenticate_kwargs = {
       'seko_id': attrs['seko'].pk,
       'password': attrs['password'],
   }
   user = authenticate(**authenticate_kwargs)
   ```

#### レスポンス仕様

**成功時（200 OK）:**
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
}
```

**エラー時（401 Unauthorized）:**
```json
{
  "detail": "No active account found with the given credentials"
}
```

**エラー時（400 Bad Request）:**
```json
{
  "seko": ["Seko not found: ID=999"],
  "password": ["This field is required."]
}
```

#### 使用例

**リクエスト例:**
```bash
curl -X POST http://localhost:8000/seko/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "seko": 123,
    "password": "moshu_password"
  }'
```

### 2.2 アクセストークン更新（葬家）

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /seko/token/refresh/` |
| **機能** | リフレッシュトークンを使用してアクセストークンを更新 |
| **認証** | リフレッシュトークン必須 |
| **実装クラス** | `TokenRefreshView`（django-rest-framework-simplejwt） |

仕様は「1.2 アクセストークン更新」と同様です。

### 2.3 トークン検証（葬家）

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /seko/token/verify/` |
| **機能** | JWTトークンの有効性を検証 |
| **認証** | 不要 |
| **実装クラス** | `TokenVerifyView`（django-rest-framework-simplejwt） |

仕様は「1.3 トークン検証」と同様です。

## 3. JWT設定

### 3.1 トークン設定

| 設定項目 | 値 | 説明 |
|---|---|---|
| **アルゴリズム** | RS256 | RSA署名アルゴリズム |
| **アクセストークン有効期限** | 5分 | 短い有効期限でセキュリティを強化 |
| **リフレッシュトークン有効期限** | 1日 | 長期間の認証状態維持 |
| **認証ヘッダー** | `Authorization: Bearer {token}` | 標準的なBearer認証 |

### 3.2 トークンペイロード

**スタッフトークン例:**
```json
{
  "token_type": "access",
  "exp": **********,
  "iat": **********,
  "jti": "def456",
  "user_id": 1,
  "user_class": "Staff"
}
```

**葬家トークン例:**
```json
{
  "token_type": "access", 
  "exp": **********,
  "iat": **********,
  "jti": "ghi789",
  "user_id": 123,
  "user_class": "Moshu"
}
```

## 4. エラーコード一覧

| ステータスコード | エラーコード | メッセージ | 発生条件 |
|---|---|---|---|
| 400 | validation_error | フィールドエラー | 必須フィールド未入力、形式エラー |
| 401 | no_active_account | No active account found | 認証情報が無効 |
| 401 | token_not_valid | Token is invalid or expired | トークンが無効または期限切れ |

## 5. セキュリティ考慮事項

### 5.1 認証強化
- **短いアクセストークン有効期限**（5分）でセキュリティリスクを最小化
- **RSA署名**による改ざん防止
- **パスワードハッシュ化**による平文パスワード保存の回避

### 5.2 アクセス制御
- **会社コードベース**のデータ分離（スタッフ認証）
- **施行IDベース**のアクセス制御（葬家認証）
- **論理削除フラグ**による無効ユーザーの除外

### 5.3 監査ログ
- **最終ログイン日時**の記録
- **認証失敗**の記録（タイミング攻撃対策含む）
