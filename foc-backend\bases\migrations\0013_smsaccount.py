# Generated by Django 3.1.5 on 2021-01-08 08:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bases', '0012_auto_20201130_1636'),
    ]

    operations = [
        migrations.CreateModel(
            name='SmsAccount',
            fields=[
                (
                    'company',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='sms_account',
                        serialize=False,
                        to='bases.base',
                        verbose_name='company',
                    ),
                ),
                ('token', models.TextField(verbose_name='token')),
                ('client_id', models.TextField(verbose_name='client id')),
                (
                    'sms_code',
                    models.CharField(blank=True, max_length=5, null=True, verbose_name='SMS code'),
                ),
            ],
            options={
                'db_table': 'm_sms_account',
            },
        ),
    ]
