from datetime import date

from dateutil.relativedelta import relativedelta
from django.test import TestCase

from masters.models import Wareki
from masters.tests.factories import WarekiFactory


class WarekiModelTest(TestCase):
    def setUp(self):
        super().setUp()

        # 時系列は wareki_3 → wareki_2 → wareki_1 → wareki_4
        self.wareki_1 = WarekiFactory()
        self.wareki_2 = WarekiFactory(
            begin_date=self.wareki_1.begin_date + relativedelta(years=-1),
            end_date=self.wareki_1.begin_date,
        )
        self.wareki_3 = WarekiFactory(
            begin_date=self.wareki_2.begin_date + relativedelta(years=-1),
            end_date=self.wareki_2.begin_date,
        )
        self.wareki_4 = WarekiFactory(
            begin_date=self.wareki_1.end_date,
            end_date=self.wareki_1.end_date + relativedelta(years=2),
        )

    def test_japanese_era(self) -> None:
        """日付から和暦表示の年を返す"""

        # 元年表記あり
        self.assertEqual(Wareki.japanese_era(self.wareki_1.begin_date), f'{self.wareki_1.name}元')

        # 元年表記なし
        self.assertEqual(
            Wareki.japanese_era(self.wareki_1.begin_date, use_gannen=False),
            f'{self.wareki_1.name}1',
        )

        # 元年表記ありの2年目
        self.assertEqual(
            Wareki.japanese_era(self.wareki_4.begin_date + relativedelta(years=+1)),
            f'{self.wareki_4.name}2',
        )

        # 元年表記なしの2年目
        self.assertEqual(
            Wareki.japanese_era(
                self.wareki_4.begin_date + relativedelta(years=+1), use_gannen=False
            ),
            f'{self.wareki_4.name}2',
        )

        # begin_dateの前日は前の元号
        the_date: date = self.wareki_1.begin_date + relativedelta(days=-1)
        year_label: str = str(the_date.year - self.wareki_2.begin_date.year + 1)
        self.assertEqual(
            Wareki.japanese_era(the_date, use_gannen=False), f'{self.wareki_2.name}{year_label}'
        )

        # 完全な日付の値
        self.assertEqual(
            Wareki.japanese_era(the_date, full_date=True, use_gannen=False),
            f'{self.wareki_2.name}{year_label}年{the_date.month}月{the_date.day}日',
        )
        year_label = '元' if year_label == '1' else year_label
        self.assertEqual(
            Wareki.japanese_era(the_date, full_date=True),
            f'{self.wareki_2.name}{year_label}年{the_date.month}月{the_date.day}日',
        )

        # 最小の元号より前はValueError
        with self.assertRaises(ValueError):
            Wareki.japanese_era(self.wareki_3.begin_date + relativedelta(days=-1))
