from typing import Dict, Tuple

from django.db import transaction
from drf_extra_fields.relations import PresentablePrimaryKeyRelatedField

from hoyo.models import HoyoSchedule, HoyoSeko
from hoyo.serializers import HoyoSekoSerializerBase
from hoyo.serializers.hoyo_mail import HoyoMailSerializerBase, HoyoMailTargetSerializerBase
from seko.models import Seko
from seko.serializers import SekoScheduleRelatedSerializer, SekoSerializer


class SimpleHoyoMailSerializer(HoyoMailSerializerBase):
    class Meta(HoyoMailSerializerBase.Meta):
        fields = '__all__'


class HoyoMailTargetBySekoSerializer(HoyoMailTargetSerializerBase):
    hoyo_mail = SimpleHoyoMailSerializer()

    class Meta(HoyoMailTargetSerializerBase.Meta):
        exclude = ['seko']


class SekoForHoyoSekoSerializer(SekoSerializer):
    schedules = SekoScheduleRelatedSerializer(many=True, required=False)
    hoyo_mail_targets = HoyoMailTargetBySekoSerializer(many=True, required=False)


class HoyoSekoSerializer(HoyoSekoSerializerBase):
    seko = PresentablePrimaryKeyRelatedField(
        queryset=Seko.objects.all(),
        presentation_serializer=SekoForHoyoSekoSerializer,
        required=False,
    )

    class Meta(HoyoSekoSerializerBase.Meta):
        fields = '__all__'
        extra_kwargs = {
            'seko': {'required': False},
        }

    @transaction.atomic
    def create(self, validated_data: Dict) -> HoyoSeko:
        schedules_data = validated_data.pop('schedules', [])
        validated_data['hoyo'] = None
        validated_data['hoyo_planned_date'] = None

        instance = super().create(validated_data)
        for schedule_dict in schedules_data:
            HoyoSchedule.objects.create(hoyo_seko=instance, **schedule_dict)

        return instance

    @transaction.atomic
    def update(self, instance: HoyoSeko, validated_data: Dict):
        ignore_fields: Tuple = ('seko', 'hoyo', 'hoyo_name', 'hoyo_planned_date')
        for ignore_field in ignore_fields:
            if ignore_field in validated_data:
                validated_data[ignore_field] = getattr(instance, ignore_field, None)

        update_schedules = 'schedules' in validated_data

        schedules_data = validated_data.pop('schedules', [])

        instance = super().update(instance, validated_data)
        if not self.partial or update_schedules:
            instance.update_schedules(schedules_data)

        return instance
