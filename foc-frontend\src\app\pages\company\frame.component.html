
<ng-container *ngIf="appView?.type===1">
<header>
  <div class="main-header">
    <div class="logo" routerLink="/foc/top">FOC</div>
    <div class="menu-area" [class.hide]="!login_info?.staff">
    <!--
      <div class="ui icon top center pointing dropdown button small top-menu setting">
        <i class="bars icon" title="個人設定"></i>
        <div class="menu">
          <div class="item"><i class="lock icon"></i>パスワード変更</div>
        </div>
      </div>
    -->
      <div class="ui icon top center pointing dropdown button small top-menu account">
        <i class="user icon" title="アカウント"></i>
        <div class="menu static">
          <div class="item">{{login_info?.staff?.name}}</div>
    <!--
          <div class="item">前回ログイン日時</div>
          <div class="item">{{login_info?.staff.updated_at|date:'yyyy/MM/dd HH:mm:ss'}}</div>
    -->
        </div>
      </div>
      <div class="ui icon top center pointing dropdown button small top-menu logout">
        <i class="power off icon" title="ログアウト"></i>
        <div class="menu">
          <div class="item" (click)="logout()"><i class="sign out icon"></i>ログアウト</div>
        </div>
      </div>
    </div>
  </div>
</header>
<div class="messageArea">
  <div class="ui message {{message?.type}}" [class.visible]="!clearing_msg && !!message" [class.hidden]="clearing_msg || !message" (click)="closeMessage()">
    <i class="close icon" (click)="closeMessage()" *ngIf="!message?.force"></i>
    <div class="ui center aligned header">
      <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
      {{message?.title}}
    </div>
    <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
      {{message?.msg}}
    </div>
  </div>
</div>
<div class="routerArea">
<app-com-login *ngIf="appView.tag==='app-com-login'"></app-com-login>
<app-com-top *ngIf="appView.tag==='app-com-top'"></app-com-top>
<app-com-seko *ngIf="appView.tag==='app-com-seko'"></app-com-seko>
<app-com-seko-edit *ngIf="appView.tag==='app-com-seko-edit'"></app-com-seko-edit>
<app-com-base *ngIf="appView.tag==='app-com-base'"></app-com-base>
<app-com-supplier *ngIf="appView.tag==='app-com-supplier'"></app-com-supplier>
<app-com-staff *ngIf="appView.tag==='app-com-staff'"></app-com-staff>
<app-com-fuho-sample *ngIf="appView.tag==='app-com-fuho-sample'"></app-com-fuho-sample>
<app-com-chobun-daishi *ngIf="appView.tag==='app-com-chobun-daishi'"></app-com-chobun-daishi>
<app-com-item-service *ngIf="appView.tag==='app-com-item-service'"></app-com-item-service>
<app-com-chobun *ngIf="appView.tag==='app-com-chobun'"></app-com-chobun>
<app-com-kumotsu *ngIf="appView.tag==='app-com-kumotsu'"></app-com-kumotsu>
<app-com-koden *ngIf="appView.tag==='app-com-koden'"></app-com-koden>
<app-com-message *ngIf="appView.tag==='app-com-message'"></app-com-message>
<app-com-henreihin *ngIf="appView.tag==='app-com-henreihin'"></app-com-henreihin>
<app-com-order-henreihin *ngIf="appView.tag==='app-com-order-henreihin'"></app-com-order-henreihin>
<app-com-monthly-summary *ngIf="appView.tag==='app-com-monthly-summary'"></app-com-monthly-summary>
<app-com-monthly-confirm *ngIf="appView.tag==='app-com-monthly-confirm'"></app-com-monthly-confirm>
<app-com-sales-summary *ngIf="appView.tag==='app-com-sales-summary'"></app-com-sales-summary>
<app-com-invoice *ngIf="appView.tag==='app-com-invoice'"></app-com-invoice>
<app-com-af-group *ngIf="appView.tag==='app-com-af-group'"></app-com-af-group>
<app-com-af-contact *ngIf="appView.tag==='app-com-af-contact'"></app-com-af-contact>
<app-com-hoyo-master *ngIf="appView.tag==='app-com-hoyo-master'"></app-com-hoyo-master>
<app-com-hoyo-sample *ngIf="appView.tag==='app-com-hoyo-sample'"></app-com-hoyo-sample>
<app-com-hoyo-mail-template *ngIf="appView.tag==='app-com-hoyo-mail-template'"></app-com-hoyo-mail-template>
<app-com-seko-af *ngIf="appView.tag==='app-com-seko-af'"></app-com-seko-af>
<app-com-hoyo-guidance *ngIf="appView.tag==='app-com-hoyo-guidance'"></app-com-hoyo-guidance>
<app-com-hoyo-filter-ki *ngIf="appView.tag==='app-com-hoyo-filter-ki'"></app-com-hoyo-filter-ki>
<app-com-hoyo-filter-bon *ngIf="appView.tag==='app-com-hoyo-filter-bon'"></app-com-hoyo-filter-bon>
<app-com-event-guidance *ngIf="appView.tag==='app-com-event-guidance'"></app-com-event-guidance>
<app-com-event-filter *ngIf="appView.tag==='app-com-event-filter'"></app-com-event-filter>
<app-com-faq *ngIf="appView.tag==='app-com-faq'"></app-com-faq>
<app-com-advertise *ngIf="appView.tag==='app-com-advertise'"></app-com-advertise>
<app-com-soke-menu *ngIf="appView.tag==='app-com-soke-menu'"></app-com-soke-menu>
<app-com-koden-summary *ngIf="appView.tag==='app-com-koden-summary'"></app-com-koden-summary>
</div>
</ng-container>

