version: "3.8"
services:
  traefik:
    image: traefik:v2.3
    ports:
      - 80:80
      - 8080:8080
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--accesslog=true"
      - "--log.level=INFO"

  db:
    image: postgres:12
    ports:
      - 5432:5432
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: foc
      POSTGRES_PASSWORD: foc

  backend:
    image: foc-backend
    build:
      context: ../../../../backend/
    environment:
      DATABASE_URL: ****************************/foc
      SECRET_KEY: aa86+4f$$zs+u2n375vk9&0#=-#sg%hpr^xifw@^aw4nbx29p-t
      DEBUG: 1
      ALLOWED_HOSTS: '*'
      CORS_ORIGIN_ALLOW_ALL: 'true'
      FRONTEND_HOSTNAME: http://localhost:8000/
      JWT_EXPIRATION_MINUTES: 5
      JWT_REFRESH_EXPIRATION_DAYS: 1
      JWT_ISSUER: Focus inc.
      JWT_AUDIENCE: FOC dev team
      JWT_PRIVATE_KEY_FILE: /usr/src/app/jwt-rsa
      ROOT_PATH: /api
      STATIC_ROOT: /usr/src/app/static
      MEDIA_ROOT: /usr/src/app/media
      MEDIA_URL: /media/
      WEB_CONCURRENCY: 2
      FORCE_SCRIPT_NAME: /api
    volumes:
      - type: bind
        source: ../../../../backend/jwt-rsa
        target: /usr/src/app/jwt-rsa
      - media-store:/usr/src/app/media
    depends_on:
      - db
    labels:
      traefik.enable: "true"
      traefik.http.routers.backend.rule: PathPrefix(`/api`)
      traefik.http.routers.backend.entrypoints: web
      traefik.http.routers.backend.middlewares: deployApi
      traefik.http.middlewares.deployApi.stripPrefix.prefixes: /api

  frontend:
    image: foc-frontend
    build:
      context: ../../../../frontend
    environment:
      DAPHNE_HOST: backend
      DAPHNE_PORT: 8000
      URLS: api|static|_ping
      TIMEOUT: 600
      DEBUG_LOG: "true"
      API_URL: /api
      CARD_TOKEN_URL: https://beta.epsilon.jp/js/token.js
    labels:
      traefik.enable: "true"
      traefik.http.routers.frontend.rule: PathPrefix(`/`)
      traefik.http.routers.frontend.entrypoints: web
    volumes:
      - media-store:/usr/share/nginx/html/media:ro
    depends_on:
      - backend

volumes:
  postgres-data:
  media-store:
