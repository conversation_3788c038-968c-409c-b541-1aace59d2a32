# マスターデータAPI仕様書

## 概要

FOCシステムのマスターデータ関連APIの詳細仕様です。日程、施行形式、郵便番号、和暦、サービス、税率、関係性、サンプルデータ等の各種マスターデータの取得機能を提供します。

## 1. 基本マスターデータAPI

### 1.1 日程マスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/schedules/` |
| **機能** | 日程項目一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `ScheduleFullListView` |
| **シリアライザー** | `ScheduleSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "name": "通夜",
    "required_flg": true
  },
  {
    "id": 2,
    "name": "告別式",
    "required_flg": true
  },
  {
    "id": 3,
    "name": "火葬",
    "required_flg": false
  },
  {
    "id": 4,
    "name": "初七日",
    "required_flg": false
  }
]
```

**リクエスト例:**
```bash
curl -X GET http://localhost:8000/masters/schedules/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
```

### 1.2 施行形式マスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/seko_styles/` |
| **機能** | 施行形式一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `SekoStyleFullListView` |
| **シリアライザー** | `SekoStyleSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "name": "一般葬",
    "wareki_use_flg": true,
    "hoyo_style": {
      "id": 1,
      "name": "一般法要",
      "wareki_use_flg": true
    }
  },
  {
    "id": 2,
    "name": "家族葬",
    "wareki_use_flg": true,
    "hoyo_style": {
      "id": 1,
      "name": "一般法要",
      "wareki_use_flg": true
    }
  },
  {
    "id": 3,
    "name": "直葬",
    "wareki_use_flg": false,
    "hoyo_style": {
      "id": 2,
      "name": "簡易法要",
      "wareki_use_flg": false
    }
  }
]
```

### 1.3 郵便番号検索API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/zipcode/` |
| **機能** | 郵便番号検索 |
| **認証** | 不要 |
| **権限** | IsAuthenticatedOrReadOnly（読み取り専用） |
| **実装クラス** | `ZipCodeQueryView` |
| **シリアライザー** | `ZipCodeSerializer` |

**HTTPメソッド:** `GET`

**クエリパラメータ:**
| パラメータ | 型 | 必須 | 説明 | 使用例 |
|---|---|---|---|---|
| `search` | string | ✓ | 郵便番号（部分一致） | `?search=100` |

**レスポンス例:**
```json
[
  {
    "id": 1,
    "zip_code": "1000001",
    "prefecture": "東京都",
    "address_1": "千代田区",
    "address_2": "千代田"
  },
  {
    "id": 2,
    "zip_code": "1000002",
    "prefecture": "東京都",
    "address_1": "千代田区",
    "address_2": "皇居外苑"
  }
]
```

**リクエスト例:**
```bash
curl -X GET "http://localhost:8000/masters/zipcode/?search=100"
```

### 1.4 和暦マスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/wareki/` |
| **機能** | 和暦一覧取得 |
| **認証** | 不要 |
| **権限** | IsAuthenticatedOrReadOnly（読み取り専用） |
| **実装クラス** | `WarekiListView` |
| **シリアライザー** | `WarekiSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "name": "令和",
    "begin_date": "2019-05-01",
    "end_date": "9999-12-31",
    "minus_years": 2018
  },
  {
    "id": 2,
    "name": "平成",
    "begin_date": "1989-01-08",
    "end_date": "2019-04-30",
    "minus_years": 1988
  },
  {
    "id": 3,
    "name": "昭和",
    "begin_date": "1926-12-25",
    "end_date": "1989-01-07",
    "minus_years": 1925
  }
]
```

### 1.5 サービスマスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/services/` |
| **機能** | サービス一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `ServiceListView` |
| **シリアライザー** | `ServiceSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "service_name": "祭壇装飾",
    "service_category": "装飾",
    "unit_price": 50000,
    "tax_rate": 10,
    "active_flg": true
  },
  {
    "id": 2,
    "service_name": "司会進行",
    "service_category": "人的サービス",
    "unit_price": 30000,
    "tax_rate": 10,
    "active_flg": true
  },
  {
    "id": 3,
    "service_name": "写真撮影",
    "service_category": "記録",
    "unit_price": 20000,
    "tax_rate": 10,
    "active_flg": true
  }
]
```

### 1.6 税率マスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/taxes/` |
| **機能** | 税率一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `TaxListView` |
| **シリアライザー** | `TaxSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "tax_name": "標準税率",
    "tax_rate": 10.0,
    "effective_date": "2019-10-01",
    "active_flg": true
  },
  {
    "id": 2,
    "tax_name": "軽減税率",
    "tax_rate": 8.0,
    "effective_date": "2019-10-01",
    "active_flg": true
  }
]
```

### 1.7 関係性マスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/relationship/` |
| **機能** | 関係性一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `RelationshipListView` |
| **シリアライザー** | `RelationshipSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "relationship_name": "配偶者",
    "display_order": 1
  },
  {
    "id": 2,
    "relationship_name": "長男",
    "display_order": 2
  },
  {
    "id": 3,
    "relationship_name": "長女",
    "display_order": 3
  },
  {
    "id": 4,
    "relationship_name": "次男",
    "display_order": 4
  },
  {
    "id": 5,
    "relationship_name": "次女",
    "display_order": 5
  }
]
```

## 2. サンプル・テンプレートAPI

### 2.1 訃報サンプルマスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/fuho_sample/` |
| **機能** | 訃報サンプル一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `FuhoSampleMasterListView` |
| **シリアライザー** | `FuhoSampleMasterSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "sample_title": "一般的な訃報文",
    "sample_content": "○○○○様のご逝去を悼み、謹んでお知らせいたします。\n\n故人の生前中に賜りましたご厚誼に深く感謝申し上げます。",
    "category": "一般",
    "display_order": 1
  },
  {
    "id": 2,
    "sample_title": "家族葬の訃報文",
    "sample_content": "○○○○様が永眠いたしました。\n\n葬儀は家族のみで執り行わせていただきます。",
    "category": "家族葬",
    "display_order": 2
  }
]
```

### 2.2 弔文台紙マスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/chobun_daishi/` |
| **機能** | 弔文台紙一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `ChobunDaishiMasterListView` |
| **シリアライザー** | `ChobunDaishiMasterSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "daishi_name": "桜の台紙",
    "daishi_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "category": "季節",
    "display_order": 1
  },
  {
    "id": 2,
    "daishi_name": "シンプル台紙",
    "daishi_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "category": "標準",
    "display_order": 2
  }
]
```

### 2.3 弔文サンプルAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/chobun_sample/` |
| **機能** | 弔文サンプル一覧取得 |
| **認証** | 不要 |
| **権限** | IsAuthenticatedOrReadOnly（読み取り専用） |
| **実装クラス** | `ChobunSampleListView` |
| **シリアライザー** | `ChobunSampleSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "sample_title": "一般的な弔文",
    "sample_content": "○○様のご逝去を悼み、心からお悔やみ申し上げます。\n\nご生前のご厚誼に深く感謝いたします。",
    "category": "一般",
    "display_order": 1
  },
  {
    "id": 2,
    "sample_title": "親族向け弔文",
    "sample_content": "○○様のご逝去に際し、心からお悔やみ申し上げます。\n\nご家族の皆様のお気持ちを察し、お慰めの言葉もございません。",
    "category": "親族",
    "display_order": 2
  }
]
```

## 3. パラメータ設定API

### 3.1 返礼品パラメータAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/henrei_params/` |
| **機能** | 返礼品パラメータ取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `HenreihinParameterDetail` |
| **シリアライザー** | `HenreihinParameterSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
{
  "id": 1,
  "default_rate": 30,
  "min_rate": 10,
  "max_rate": 50,
  "calculation_method": "percentage",
  "rounding_rule": "round_down",
  "updated_at": "2024-06-27T10:00:00+09:00"
}
```

**パラメータ詳細:**
| フィールド | 型 | 説明 |
|---|---|---|
| `default_rate` | integer | デフォルト返礼品率（%） |
| `min_rate` | integer | 最小返礼品率（%） |
| `max_rate` | integer | 最大返礼品率（%） |
| `calculation_method` | string | 計算方法（percentage/fixed_amount） |
| `rounding_rule` | string | 端数処理（round_up/round_down/round） |

### 3.2 香典パラメータAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/koden_params/` |
| **機能** | 香典パラメータ取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `KodenParameterDetail` |
| **シリアライザー** | `KodenParameterSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
{
  "id": 1,
  "min_amount": 3000,
  "max_amount": 100000,
  "default_amount": 10000,
  "increment_unit": 1000,
  "relationship_multiplier": {
    "配偶者": 2.0,
    "親": 1.5,
    "子": 1.5,
    "兄弟": 1.0,
    "親戚": 0.8,
    "友人": 0.5
  },
  "updated_at": "2024-06-27T10:00:00+09:00"
}
```

**パラメータ詳細:**
| フィールド | 型 | 説明 |
|---|---|---|
| `min_amount` | integer | 最小香典金額 |
| `max_amount` | integer | 最大香典金額 |
| `default_amount` | integer | デフォルト香典金額 |
| `increment_unit` | integer | 増減単位 |
| `relationship_multiplier` | object | 関係性別倍率 |

## 4. 法要関連API

### 4.1 法要形式マスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/hoyo_style/` |
| **機能** | 法要形式一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `HoyoStyleList` |
| **シリアライザー** | `HoyoStyleSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "name": "一般法要",
    "wareki_use_flg": true
  },
  {
    "id": 2,
    "name": "簡易法要",
    "wareki_use_flg": false
  },
  {
    "id": 3,
    "name": "家族法要",
    "wareki_use_flg": true
  }
]
```

### 4.2 法要初期値マスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/hoyo_default/` |
| **機能** | 法要初期値マスタ一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `HoyoDefaultMasterList` |
| **シリアライザー** | `HoyoDefaultMasterSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "name": "初七日",
    "elapsed_time": 7,
    "unit": 1,
    "hoyo_style": {
      "id": 1,
      "name": "一般法要",
      "wareki_use_flg": true
    }
  },
  {
    "id": 2,
    "name": "四十九日",
    "elapsed_time": 49,
    "unit": 1,
    "hoyo_style": {
      "id": 1,
      "name": "一般法要",
      "wareki_use_flg": true
    }
  },
  {
    "id": 3,
    "name": "一周忌",
    "elapsed_time": 1,
    "unit": 3,
    "hoyo_style": {
      "id": 1,
      "name": "一般法要",
      "wareki_use_flg": true
    }
  },
  {
    "id": 4,
    "name": "三回忌",
    "elapsed_time": 2,
    "unit": 3,
    "hoyo_style": {
      "id": 1,
      "name": "一般法要",
      "wareki_use_flg": true
    }
  }
]
```

**単位（unit）の値:**
| 値 | 意味 |
|---|---|
| 1 | 日 |
| 2 | 月 |
| 3 | 年 |

### 4.3 法要サンプルマスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/hoyo_sample/` |
| **機能** | 法要サンプルマスタ一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `HoyoSampleMasterList` |
| **シリアライザー** | `HoyoSampleMasterSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "sentence": "○○様の初七日法要を執り行わせていただきます。\n\nご参列いただきますよう、ご案内申し上げます。"
  },
  {
    "id": 2,
    "sentence": "○○様の四十九日法要を執り行わせていただきます。\n\n心ばかりの供養をさせていただきたく、ご案内申し上げます。"
  },
  {
    "id": 3,
    "sentence": "○○様の一周忌法要を執り行わせていただきます。\n\nお忙しい中恐縮ですが、ご参列いただければ幸いです。"
  }
]
```

## 5. 葬家サイト関連API

### 5.1 葬家サイトメニューAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/soke_menu/` |
| **機能** | 葬家サイトメニュー一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `SokeMenuListView` |
| **シリアライザー** | `SokeMenuSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "menu_name": "訃報",
    "menu_url": "/fuho",
    "disp_no": 1,
    "active_flg": true,
    "icon_class": "fas fa-newspaper"
  },
  {
    "id": 2,
    "menu_name": "弔文",
    "menu_url": "/chobun",
    "disp_no": 2,
    "active_flg": true,
    "icon_class": "fas fa-pen"
  },
  {
    "id": 3,
    "menu_name": "アルバム",
    "menu_url": "/album",
    "disp_no": 3,
    "active_flg": true,
    "icon_class": "fas fa-images"
  },
  {
    "id": 4,
    "menu_name": "お問い合わせ",
    "menu_url": "/inquiry",
    "disp_no": 4,
    "active_flg": true,
    "icon_class": "fas fa-envelope"
  }
]
```

**パラメータ詳細:**
| フィールド | 型 | 説明 |
|---|---|---|
| `menu_name` | string | メニュー名 |
| `menu_url` | string | メニューURL |
| `disp_no` | integer | 表示順序 |
| `active_flg` | boolean | 有効フラグ |
| `icon_class` | string | アイコンCSSクラス |

## 6. 決済関連API

### 6.1 決済方法マスタAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /masters/payment_types/` |
| **機能** | 決済方法一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `PaymentTypeListView` |
| **シリアライザー** | `PaymentTypeSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 1,
    "payment_name": "現金",
    "payment_code": "CASH",
    "active_flg": true,
    "display_order": 1
  },
  {
    "id": 2,
    "payment_name": "クレジットカード",
    "payment_code": "CREDIT",
    "active_flg": true,
    "display_order": 2
  },
  {
    "id": 3,
    "payment_name": "銀行振込",
    "payment_code": "BANK_TRANSFER",
    "active_flg": true,
    "display_order": 3
  },
  {
    "id": 4,
    "payment_name": "電子マネー",
    "payment_code": "E_MONEY",
    "active_flg": false,
    "display_order": 4
  }
]
```

## 7. エラーコード一覧

| ステータスコード | エラーメッセージ | 発生条件 |
|---|---|---|
| 400 | Invalid search parameter | 検索パラメータが無効 |
| 401 | Authentication credentials were not provided. | 認証情報なし |
| 403 | You do not have permission to perform this action. | 権限不足 |
| 404 | Not found. | 指定されたリソースが存在しない |
| 404 | No {ModelName} matches the given query. | パラメータデータが存在しない |

## 8. セキュリティ考慮事項

### 8.1 データ保護
- **読み取り専用**: マスターデータは基本的に読み取り専用
- **キャッシュ対応**: 頻繁にアクセスされるデータのキャッシュ機能
- **権限制御**: 認証が必要なデータと不要なデータの適切な分離

### 8.2 パフォーマンス最適化
- **インデックス**: 検索対象フィールドへの適切なインデックス設定
- **並び順**: display_order、disp_no等による効率的な並び順制御
- **select_related**: 関連オブジェクトの効率的な取得

### 8.3 データ整合性
- **外部キー制約**: 関連データの整合性保証
- **有効フラグ**: active_flgによる論理的な有効/無効制御
- **表示順序**: display_order、disp_noによる一貫した表示順序管理

## 9. 使用例

### 9.1 施行形式選択画面での使用
```javascript
// 施行形式一覧取得
const sekoStyles = await fetch('/masters/seko_styles/', {
  headers: { 'Authorization': `Bearer ${token}` }
}).then(res => res.json());

// 法要形式でグループ化
const groupedStyles = sekoStyles.reduce((acc, style) => {
  const hoyoStyleName = style.hoyo_style.name;
  if (!acc[hoyoStyleName]) acc[hoyoStyleName] = [];
  acc[hoyoStyleName].push(style);
  return acc;
}, {});
```

### 9.2 住所入力での郵便番号検索
```javascript
// 郵便番号検索
const searchZipCode = async (zipCode) => {
  const results = await fetch(`/masters/zipcode/?search=${zipCode}`)
    .then(res => res.json());

  if (results.length > 0) {
    const address = results[0];
    // 住所フィールドに自動入力
    document.getElementById('prefecture').value = address.prefecture;
    document.getElementById('address1').value = address.address_1;
    document.getElementById('address2').value = address.address_2;
  }
};
```

### 9.3 法要スケジュール自動設定
```javascript
// 法要初期値マスタから自動スケジュール生成
const generateHoyoSchedule = async (deathDate, hoyoStyleId) => {
  const hoyoDefaults = await fetch('/masters/hoyo_default/', {
    headers: { 'Authorization': `Bearer ${token}` }
  }).then(res => res.json());

  return hoyoDefaults
    .filter(def => def.hoyo_style.id === hoyoStyleId)
    .map(def => {
      const scheduledDate = new Date(deathDate);

      switch (def.unit) {
        case 1: // 日
          scheduledDate.setDate(scheduledDate.getDate() + def.elapsed_time);
          break;
        case 2: // 月
          scheduledDate.setMonth(scheduledDate.getMonth() + def.elapsed_time);
          break;
        case 3: // 年
          scheduledDate.setFullYear(scheduledDate.getFullYear() + def.elapsed_time);
          break;
      }

      return {
        name: def.name,
        scheduled_date: scheduledDate.toISOString().split('T')[0]
      };
    });
};
```

### 9.4 返礼品計算での使用
```javascript
// 返礼品パラメータ取得と計算
const calculateHenreihin = async (kodenAmount) => {
  const params = await fetch('/masters/henrei_params/', {
    headers: { 'Authorization': `Bearer ${token}` }
  }).then(res => res.json());

  let henreihinAmount = kodenAmount * (params.default_rate / 100);

  // 端数処理
  switch (params.rounding_rule) {
    case 'round_up':
      henreihinAmount = Math.ceil(henreihinAmount);
      break;
    case 'round_down':
      henreihinAmount = Math.floor(henreihinAmount);
      break;
    case 'round':
      henreihinAmount = Math.round(henreihinAmount);
      break;
  }

  return Math.max(params.min_rate * kodenAmount / 100,
                  Math.min(henreihinAmount, params.max_rate * kodenAmount / 100));
};
```

### 9.5 弔文テンプレート選択での使用
```javascript
// 弔文サンプル取得とカテゴリ分類
const loadChobunSamples = async () => {
  const samples = await fetch('/masters/chobun_sample/')
    .then(res => res.json());

  // カテゴリ別にグループ化
  const categorizedSamples = samples.reduce((acc, sample) => {
    if (!acc[sample.category]) acc[sample.category] = [];
    acc[sample.category].push(sample);
    return acc;
  }, {});

  // 表示順でソート
  Object.keys(categorizedSamples).forEach(category => {
    categorizedSamples[category].sort((a, b) => a.display_order - b.display_order);
  });

  return categorizedSamples;
};
```

## 10. 関連API連携パターン

### 10.1 施行作成時のマスターデータ活用
```javascript
// 施行作成時に必要なマスターデータを一括取得
const loadMasterDataForSeko = async () => {
  const [schedules, sekoStyles, relationships, services] = await Promise.all([
    fetch('/masters/schedules/', { headers: { 'Authorization': `Bearer ${token}` } }),
    fetch('/masters/seko_styles/', { headers: { 'Authorization': `Bearer ${token}` } }),
    fetch('/masters/relationship/', { headers: { 'Authorization': `Bearer ${token}` } }),
    fetch('/masters/services/', { headers: { 'Authorization': `Bearer ${token}` } })
  ]).then(responses => Promise.all(responses.map(res => res.json())));

  return { schedules, sekoStyles, relationships, services };
};
```

### 10.2 和暦変換機能
```javascript
// 和暦マスタを使用した日付変換
const convertToWareki = async (gregorianDate) => {
  const warekiList = await fetch('/masters/wareki/')
    .then(res => res.json());

  const targetDate = new Date(gregorianDate);
  const wareki = warekiList.find(w =>
    new Date(w.begin_date) <= targetDate && targetDate <= new Date(w.end_date)
  );

  if (wareki) {
    const warekiYear = targetDate.getFullYear() - wareki.minus_years;
    return `${wareki.name}${warekiYear}年${targetDate.getMonth() + 1}月${targetDate.getDate()}日`;
  }

  return null;
};
```
