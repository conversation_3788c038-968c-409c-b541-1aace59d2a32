import factory
import factory.fuzzy as fuzzy
from django.utils import timezone
from factory.django import DjangoModelFactory

from bases.tests.factories import BaseFactory
from staffs.models import Staff

DEFAULT_PASSWORD = 'the-dummy-password'


class StaffFactory(DjangoModelFactory):
    class Meta:
        model = Staff

    id = factory.Sequence(lambda n: n)
    base = factory.SubFactory(BaseFactory)
    company_code = factory.SelfAttribute('base.company_code')
    name = factory.Faker('name', locale='ja_JP')
    login_id = fuzzy.FuzzyText(length=10)
    password = factory.PostGenerationMethodCall('set_password', DEFAULT_PASSWORD)
    mail_address = factory.Faker('ascii_safe_email')
    retired_flg = False
    del_flg = False
    fdn_code = fuzzy.FuzzyText(length=10)
    created_at = factory.Faker(
        'past_datetime', start_date='-30d', tzinfo=timezone.get_current_timezone()
    )
    create_user_id = 0
    updated_at = factory.Faker(
        'past_datetime', start_date='-30d', tzinfo=timezone.get_current_timezone()
    )
    update_user_id = 0
