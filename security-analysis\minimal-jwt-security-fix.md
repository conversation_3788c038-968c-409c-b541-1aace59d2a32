# FOCシステム JWT セキュリティ最小限改修案

## 概要

JWTトークンに必要な会社コード・base_typeを追加し、共通でセッションストレージとの整合性をチェックする最小限の改修案です。

## 1. 改修対象ファイル

### バックエンド（2ファイル）
- `foc-backend/utils/authentication.py` - JWT生成・検証処理
- `foc-backend/utils/permissions.py` - 共通権限チェック（新規）

### フロントエンド（3ファイル）
- `foc-frontend/src/app/service/session-security.service.ts` - セッション整合性チェック（新規）
- `foc-frontend/src/app/service/http-client.service.ts` - HTTPインターセプター改修
- `foc-frontend/src/app/guard/authF.guard.ts` - 認証ガード改修

## 2. バックエンド改修

### 2.1 JWT生成・検証処理の改修

```python
# foc-backend/utils/authentication.py

from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import AuthenticationFailed, InvalidToken
from rest_framework_simplejwt.settings import api_settings
from rest_framework_simplejwt.state import User
from rest_framework_simplejwt.tokens import RefreshToken

from seko.models import Moshu


class ClassableRefreshToken(RefreshToken):
    @classmethod
    def for_user(cls, user):
        if isinstance(user, Moshu):
            setattr(user, 'id', user.seko.pk)
        token = super().for_user(user)
        token['user_class'] = user._meta.object_name
        
        # 【追加】セキュリティ情報をJWTに含める
        if isinstance(user, Moshu):
            token['company_code'] = user.seko.base.company_code
            token['base_type'] = user.seko.base.base_type
            token['user_name'] = user.name
        else:  # Staff
            token['company_code'] = user.company_code
            token['base_type'] = user.base.base_type
            token['user_name'] = user.name
            
        if isinstance(user, Moshu):
            delattr(user, 'id')
        return token


class ClassableJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        try:
            user_id = validated_token[api_settings.USER_ID_CLAIM]
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user identification'))

        try:
            user_class: str = validated_token['user_class']
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user class'))

        try:
            if user_class == 'Moshu':
                user = Moshu.objects.get(Q(pk=user_id) & Q(seko__del_flg=False))
            else:
                user = User.objects.get(**{api_settings.USER_ID_FIELD: user_id})
        except (Moshu.DoesNotExist, User.DoesNotExist):
            raise AuthenticationFailed(_('User not found'), code='user_not_found')

        if not user.is_active:
            raise AuthenticationFailed(_('User is inactive'), code='user_inactive')

        # 【追加】JWTとユーザー情報の整合性チェック
        self.validate_jwt_user_integrity(validated_token, user)

        return user
    
    def validate_jwt_user_integrity(self, validated_token, user):
        """JWTトークンとユーザー情報の整合性を検証"""
        jwt_company_code = validated_token.get('company_code')
        jwt_base_type = validated_token.get('base_type')
        jwt_user_name = validated_token.get('user_name')
        
        # 会社コードの検証
        if isinstance(user, Moshu):
            user_company_code = user.seko.base.company_code
            user_base_type = user.seko.base.base_type
        else:  # Staff
            user_company_code = user.company_code
            user_base_type = user.base.base_type
            
        if (jwt_company_code != user_company_code or 
            jwt_base_type != user_base_type or
            jwt_user_name != user.name):
            raise AuthenticationFailed(
                'JWT token integrity check failed', 
                code='token_integrity_error'
            )
```

### 2.2 共通権限チェック処理の追加

```python
# foc-backend/utils/permissions.py

from rest_framework.permissions import SAFE_METHODS, BasePermission
from rest_framework.exceptions import PermissionDenied
from django.db.models import Q

from seko.models import Moshu
from staffs.models import Staff
from bases.models import Base


class IsStaff(BasePermission):
    def has_permission(self, request, view):
        return bool(
            request.user and request.user.is_authenticated and isinstance(request.user, Staff)
        )


class IsMoshu(BasePermission):
    def has_permission(self, request, view):
        return bool(
            request.user and request.user.is_authenticated and isinstance(request.user, Moshu)
        )


class ReadNeedAuth(BasePermission):
    def has_permission(self, request, view):
        return bool(
            request.method in SAFE_METHODS and request.user and request.user.is_authenticated
        )


# 【新規追加】セキュアな会社コードフィルタリング
class SecureCompanyFilterMixin:
    """会社コードによる安全なデータフィルタリング"""
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return self.apply_company_filter(queryset)
    
    def apply_company_filter(self, queryset):
        """ユーザーの会社コードでクエリセットをフィルタリング"""
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return queryset.none()
            
        user = self.request.user
        
        # システム管理会社の場合
        if self.is_system_admin(user):
            # クエリパラメータで指定された会社のデータのみ
            requested_company = self.get_requested_company_code()
            if requested_company:
                return self.filter_by_company_code(queryset, requested_company)
            return queryset.none()  # 会社指定なしの場合は空
            
        # テナント会社の場合は自社データのみ
        user_company_code = self.get_user_company_code(user)
        return self.filter_by_company_code(queryset, user_company_code)
    
    def is_system_admin(self, user):
        """システム管理会社かどうかを判定"""
        if isinstance(user, Moshu):
            return user.seko.base.base_type == Base.OrgType.ADMIN
        else:  # Staff
            return user.base.base_type == Base.OrgType.ADMIN
    
    def get_user_company_code(self, user):
        """ユーザーの会社コードを取得"""
        if isinstance(user, Moshu):
            return user.seko.base.company_code
        else:  # Staff
            return user.company_code
    
    def get_requested_company_code(self):
        """リクエストパラメータから会社コードを取得"""
        company_params = ['seko_company', 'company_id', 'company']
        for param in company_params:
            value = self.request.GET.get(param)
            if value:
                return value
        return None
    
    def filter_by_company_code(self, queryset, company_code):
        """会社コードでクエリセットをフィルタリング（サブクラスでオーバーライド）"""
        # デフォルト実装（各APIで適切にオーバーライドする）
        if hasattr(queryset.model, 'seko_company'):
            return queryset.filter(seko_company__company_code=company_code)
        elif hasattr(queryset.model, 'company_code'):
            return queryset.filter(company_code=company_code)
        return queryset
    
    def validate_company_access(self):
        """会社コードアクセス権限の検証"""
        requested_company = self.get_requested_company_code()
        
        if not requested_company:
            return True
            
        user = self.request.user
        
        # システム管理会社は全テナントアクセス可能
        if self.is_system_admin(user):
            return True
            
        # テナント会社は自社のみ
        user_company_code = self.get_user_company_code(user)
        if str(requested_company) != str(user_company_code):
            raise PermissionDenied(
                f'Access denied: requested company {requested_company}, '
                f'user company {user_company_code}'
            )
        return True
```

## 3. フロントエンド改修

### 3.1 セッション整合性チェックサービス（新規作成）

```typescript
// foc-frontend/src/app/service/session-security.service.ts

import { Injectable } from '@angular/core';
import { SessionService } from './session.service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class SessionSecurityService {

  constructor(
    private sessionSvc: SessionService,
    private router: Router
  ) { }

  /**
   * JWTトークンをデコードしてペイロードを取得
   */
  private decodeJWT(token: string): any {
    try {
      const payload = token.split('.')[1];
      const decoded = atob(payload);
      return JSON.parse(decoded);
    } catch (error) {
      console.error('JWT decode error:', error);
      return null;
    }
  }

  /**
   * セッションストレージとJWTトークンの整合性をチェック
   */
  validateSessionIntegrity(): boolean {
    const loginInfo = this.sessionSvc.get('staff_login_info');
    
    if (!loginInfo || !loginInfo.token || !loginInfo.staff) {
      return false;
    }

    const jwtPayload = this.decodeJWT(loginInfo.token.access);
    if (!jwtPayload) {
      return false;
    }

    // JWTとセッションの整合性チェック
    const sessionStaff = loginInfo.staff;
    
    if (sessionStaff.id !== jwtPayload.user_id ||
        sessionStaff.company_code !== jwtPayload.company_code ||
        sessionStaff.base.base_type !== jwtPayload.base_type ||
        sessionStaff.name !== jwtPayload.user_name) {
      
      console.error('Session integrity check failed');
      this.handleIntegrityFailure();
      return false;
    }

    return true;
  }

  /**
   * 整合性チェック失敗時の処理
   */
  private handleIntegrityFailure(): void {
    // セッションをクリアしてログイン画面にリダイレクト
    this.sessionSvc.clearAll();
    this.router.navigate(['/foc/login/']);
  }

  /**
   * JWTトークンから安全にユーザー情報を取得
   */
  getSecureUserInfo(): any {
    if (!this.validateSessionIntegrity()) {
      return null;
    }
    
    const loginInfo = this.sessionSvc.get('staff_login_info');
    return loginInfo.staff;
  }

  /**
   * 安全な会社コード取得
   */
  getSecureCompanyCode(): string | null {
    const userInfo = this.getSecureUserInfo();
    return userInfo ? userInfo.company_code : null;
  }

  /**
   * 安全な拠点タイプ取得
   */
  getSecureBaseType(): number | null {
    const userInfo = this.getSecureUserInfo();
    return userInfo ? userInfo.base.base_type : null;
  }
}
```

### 3.2 HTTPクライアントサービスの改修

```typescript
// foc-frontend/src/app/service/http-client.service.ts
// 既存のHTTPクライアントサービスに以下を追加

import { SessionSecurityService } from './session-security.service';

export class HttpClientService {

  constructor(
    private http: HttpClient,
    private router: Router,
    private sessionSvc: SessionService,
    private loaderSvc: LoaderService,
    private configSvc: RuntimeConfigLoaderService,
    private sessionSecuritySvc: SessionSecurityService  // 追加
  ) { }

  // 【改修】API呼び出し前のセキュリティチェック
  private validateSecurityBeforeRequest(): boolean {
    // セッション整合性チェック
    if (!this.sessionSecuritySvc.validateSessionIntegrity()) {
      console.error('Session integrity validation failed');
      return false;
    }
    return true;
  }

  // 【改修】認証付きPOSTメソッド
  private async postWithToken<T, K>(method: string, body: T): Promise<K> {
    // セキュリティチェック追加
    if (!this.validateSecurityBeforeRequest()) {
      return Promise.reject('Security validation failed');
    }

    await this.refreshToken();
    if (document.execCommand) {
      document.execCommand('ClearAuthenticationCache', false);
    }
    const login_info = this.getLoginInfo();
    let str = '';
    if (login_info && login_info.token) {
      str = 'Bearer ' + login_info.token.access;
    }
    this.headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': str
    });
    return this.http.post(this.host + method, body, { headers: this.headers })
      .toPromise()
      .then((res) => {
        const response: any = res;
        return response;
      })
      .catch((err) => {
        this.checkErrorType(err);
        return Promise.reject(err.error || 'error');
      });
  }

  // 【改修】認証付きGETメソッド
  private async getWithToken<T>(method: string, params: HttpParams): Promise<T> {
    // セキュリティチェック追加
    if (!this.validateSecurityBeforeRequest()) {
      return Promise.reject('Security validation failed');
    }

    await this.refreshToken();
    if (document.execCommand) {
      document.execCommand('ClearAuthenticationCache', false);
    }
    const login_info = this.getLoginInfo();
    let str = '';
    if (login_info && login_info.token) {
      str = 'Bearer ' + login_info.token.access;
    }
    this.headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': str
    });
    return this.http.get(this.host + method, { headers: this.headers, params: params })
      .toPromise()
      .then((res) => {
        const response: any = res;
        return response;
      })
      .catch((err) => {
        this.checkErrorType(err);
        return Promise.reject(err.error || 'error');
      });
  }

  // 【改修】認証付きPUTメソッド
  private async putWithToken<T, K>(method: string, body: T): Promise<K> {
    // セキュリティチェック追加
    if (!this.validateSecurityBeforeRequest()) {
      return Promise.reject('Security validation failed');
    }

    await this.refreshToken();
    if (document.execCommand) {
      document.execCommand('ClearAuthenticationCache', false);
    }
    const login_info = this.getLoginInfo();
    let str = '';
    if (login_info && login_info.token) {
      str = 'Bearer ' + login_info.token.access;
    }
    this.headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': str
    });
    return this.http.put(this.host + method, body, { headers: this.headers })
      .toPromise()
      .then((res) => {
        const response: any = res;
        return response;
      })
      .catch((err) => {
        this.checkErrorType(err);
        return Promise.reject(err.error || 'error');
      });
  }

  // 【改修】認証付きPATCHメソッド
  private async patchWithToken<T, K>(method: string, body: T): Promise<K> {
    // セキュリティチェック追加
    if (!this.validateSecurityBeforeRequest()) {
      return Promise.reject('Security validation failed');
    }

    await this.refreshToken();
    if (document.execCommand) {
      document.execCommand('ClearAuthenticationCache', false);
    }
    const login_info = this.getLoginInfo();
    let str = '';
    if (login_info && login_info.token) {
      str = 'Bearer ' + login_info.token.access;
    }
    this.headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': str
    });
    return this.http.patch(this.host + method, body, { headers: this.headers })
      .toPromise()
      .then((res) => {
        const response: any = res;
        return response;
      })
      .catch((err) => {
        this.checkErrorType(err);
        return Promise.reject(err.error || 'error');
      });
  }

  // 【改修】認証付きDELETEメソッド
  private async deleteWithToken<T>(method: string): Promise<T> {
    // セキュリティチェック追加
    if (!this.validateSecurityBeforeRequest()) {
      return Promise.reject('Security validation failed');
    }

    await this.refreshToken();
    if (document.execCommand) {
      document.execCommand('ClearAuthenticationCache', false);
    }
    const login_info = this.getLoginInfo();
    let str = '';
    if (login_info && login_info.token) {
      str = 'Bearer ' + login_info.token.access;
    }
    this.headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': str
    });
    return this.http.delete(this.host + method, { headers: this.headers })
      .toPromise()
      .then((res) => {
        const response: any = res;
        return response;
      })
      .catch((err) => {
        this.checkErrorType(err);
        return Promise.reject(err.error || 'error');
      });
  }
}
```

### 3.3 認証ガードの改修

```typescript
// foc-frontend/src/app/guard/authF.guard.ts

import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { SessionService } from '../service/session.service';
import { SessionSecurityService } from '../service/session-security.service';  // 追加
import { HttpClientService } from '../service/http-client.service';

@Injectable({
  providedIn: 'root',
})
export class AuthFGuard implements CanActivate {
  constructor(
    private sessionSvc: SessionService,
    private sessionSecuritySvc: SessionSecurityService,  // 追加
    private router: Router,
    private httpClientService: HttpClientService
  ) { }

  canActivate(): Observable<boolean> | Promise<boolean> | boolean {
    // 【改修】セッション整合性チェックを追加
    if (!this.sessionSecuritySvc.validateSessionIntegrity()) {
      console.error('Session integrity check failed in AuthFGuard');
      this.router.navigate(['foc/login/']);
      return false;
    }

    const login_info = this.sessionSvc.get('staff_login_info');
    if (!login_info || !login_info.staff) {
      this.router.navigate(['foc/login/']);
      return false;
    }

    // JWT自動更新
    this.httpClientService.refreshToken().catch(err => {
      console.error('Token refresh failed:', err);
      this.router.navigate(['foc/login/']);
    });

    return true;
  }
}
```

### 3.4 SessionServiceの改修

```typescript
// foc-frontend/src/app/service/session.service.ts
// 既存のSessionServiceに以下のメソッドを追加

export class SessionService {

  // 【追加】全セッションデータのクリア
  clearAll(): void {
    sessionStorage.clear();
    localStorage.clear();
  }

  // 【追加】安全なログイン情報取得
  getSecureLoginInfo(): any {
    const loginInfo = this.get('staff_login_info');
    if (!loginInfo || !loginInfo.token || !loginInfo.staff) {
      return null;
    }
    return loginInfo;
  }
}
```

## 4. 実装手順

### Phase 1: バックエンド改修（1-2時間）
1. `utils/authentication.py`の改修
   - `ClassableRefreshToken.for_user()`にセキュリティ情報追加
   - `ClassableJWTAuthentication.get_user()`に整合性チェック追加

2. `utils/permissions.py`の新規作成
   - `SecureCompanyFilterMixin`の実装

### Phase 2: フロントエンド改修（2-3時間）
1. `session-security.service.ts`の新規作成
2. `http-client.service.ts`の改修
3. `authF.guard.ts`の改修
4. `session.service.ts`の改修

### Phase 3: テスト・検証（1-2時間）
1. ログイン機能のテスト
2. セッション改ざんテスト
3. API呼び出しテスト

## 5. 期待される効果

### セキュリティ向上
- **JWTトークン改ざん検知**: トークンとセッションの不整合を検出
- **セッション改ざん防止**: ブラウザでのセッション操作を無効化
- **権限昇格攻撃防止**: 不正な権限変更を阻止

### 最小限の影響
- **既存機能への影響なし**: 既存のAPI呼び出しは変更不要
- **段階的導入可能**: バックエンド→フロントエンドの順で導入
- **ロールバック容易**: 問題発生時は簡単に元に戻せる

## 6. 注意事項

### 既存ユーザーへの影響
- **再ログイン必要**: JWT構造変更により既存ログインユーザーは再ログインが必要
- **セッション無効化**: 改修後は既存のセッションストレージが無効になる

### 運用上の考慮事項
- **ログ監視**: セキュリティエラーのログ監視体制を整備
- **ユーザー通知**: 改修時の再ログイン要求をユーザーに事前通知

この改修により、最小限のコード変更でFOCシステムの重大なセキュリティ脆弱性を解決できます。
```
