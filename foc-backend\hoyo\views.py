from base64 import b64encode
from io import Bytes<PERSON>
from typing import Dict, List
from urllib.parse import urljoin

import qrcode
from django.contrib.staticfiles import finders
from django.db.models import Prefetch, Q
from django.http import Http404
from django.http.response import FileResponse
from django.template.loader import get_template
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters
from rest_framework import generics, status, views
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.response import Response
from weasyprint import CSS, HTML

from bases.models import Base
from hoyo.models import (
    Hoyo,
    HoyoMail,
    HoyoMailTarget,
    HoyoMailTemplate,
    HoyoSample,
    HoyoSchedule,
    HoyoSeko,
)
from hoyo.serializers import HoyoMailTemplateSerializer, HoyoSampleSerializer, HoyoSerializer
from hoyo.serializers.hoyo_mail import HoyoMailSerializer
from hoyo.serializers.hoyo_seko import HoyoSekoSerializer
from masters.models import ChobunDaishiMaster, Service
from seko.models import Kojin, Moshu, SekoSchedule
from service_reception_terms.models import ServiceReceptionTerm
from utils.permissions import IsStaff
from utils.requests import request_origin
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class HoyoList(AddContextMixin, generics.ListCreateAPIView):

    queryset = Hoyo.objects.filter(Q(del_flg=False)).order_by('company', 'display_num', 'pk').all()
    serializer_class = HoyoSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['company']
    permission_classes = [IsStaff]


class HoyoDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 法要を取得します
    """

    queryset = Hoyo.objects.filter(Q(del_flg=False)).all()
    serializer_class = HoyoSerializer
    permission_classes = [IsStaff]


class HoyoSampleList(AddContextMixin, generics.ListCreateAPIView):

    queryset = (
        HoyoSample.objects.filter(Q(del_flg=False)).order_by('company', 'display_num', 'pk').all()
    )
    serializer_class = HoyoSampleSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['company']
    permission_classes = [IsStaff]


class HoyoSampleDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 法要を取得します
    """

    queryset = HoyoSample.objects.filter(Q(del_flg=False)).all()
    serializer_class = HoyoSampleSerializer
    permission_classes = [IsStaff]


class HoyoMailTemplateList(AddContextMixin, generics.ListCreateAPIView):

    queryset = (
        HoyoMailTemplate.objects.filter(Q(del_flg=False))
        .order_by('company', 'display_num', 'pk')
        .all()
    )
    serializer_class = HoyoMailTemplateSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['company']
    permission_classes = [IsStaff]


class HoyoMailTemplateDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 法要を取得します
    """

    queryset = HoyoMailTemplate.objects.filter(Q(del_flg=False)).all()
    serializer_class = HoyoMailTemplateSerializer
    permission_classes = [IsStaff]


class HoyoSekoListFilter(filters.FilterSet):
    hoyo = filters.NumberFilter(field_name='hoyo')
    hoyo_planned_date = filters.DateFromToRangeFilter(field_name='hoyo_planned_date')
    seko_department = filters.NumberFilter(method='filter_seko_department')
    seko_company = filters.NumberFilter(field_name='seko__seko_company')

    def filter_seko_department(self, queryset, name, value):
        departments = Base.objects.filter(pk=value).get_cached_trees()
        if not departments:
            error_message: str = _(
                'Select a valid choice. That choice is not one of the available choices.'
            )
            raise ValidationError({'seko_department': error_message})
        department_ids: List[int] = [
            base.pk for base in departments[0].get_descendants(include_self=True)
        ]
        return queryset.filter(Q(seko__seko_department__in=department_ids))


class HoyoSekoList(generics.ListCreateAPIView):

    queryset = (
        HoyoSeko.objects.filter(Q(del_flg=False) & Q(seko__del_flg=False))
        .select_related('seko', 'hoyo', 'hoyo__style', 'staff', 'staff__base', 'hall')
        .prefetch_related(
            Prefetch('schedules', queryset=HoyoSchedule.objects.order_by('display_num')),
            Prefetch('seko__moshu'),
            Prefetch(
                'seko__kojin',
                queryset=Kojin.objects.filter(Q(del_flg=False)).order_by('kojin_num'),
            ),
            Prefetch(
                'seko__schedules',
                queryset=SekoSchedule.objects.select_related('schedule', 'hall')
                .prefetch_related(
                    Prefetch(
                        'hall__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
                    ),
                    Prefetch('hall__services', queryset=Service.objects.all()),
                    Prefetch(
                        'hall__service_reception_terms',
                        queryset=ServiceReceptionTerm.objects.all(),
                    ),
                )
                .order_by('display_num'),
            ),
            Prefetch(
                'staff__base__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
            ),
            Prefetch('staff__base__services', queryset=Service.objects.all()),
            Prefetch(
                'staff__base__service_reception_terms', queryset=ServiceReceptionTerm.objects.all()
            ),
            Prefetch('hall__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()),
            Prefetch('hall__services', queryset=Service.objects.all()),
            Prefetch('hall__service_reception_terms', queryset=ServiceReceptionTerm.objects.all()),
            Prefetch(
                'seko__hoyo_mail_targets',
                queryset=HoyoMailTarget.objects.select_related(
                    'hoyo_mail', 'hoyo_mail__staff', 'hoyo_mail__staff__base'
                )
                .prefetch_related(
                    Prefetch(
                        'hoyo_mail__staff__base__chobun_daishi_masters',
                        queryset=ChobunDaishiMaster.objects.all(),
                    ),
                    Prefetch('hoyo_mail__staff__base__services', queryset=Service.objects.all()),
                    Prefetch(
                        'hoyo_mail__staff__base__service_reception_terms',
                        queryset=ServiceReceptionTerm.objects.all(),
                    ),
                )
                .all(),
            ),
        )
        .order_by('hoyo_planned_date', 'hoyo_activity_date', 'pk')
        .all()
    )
    serializer_class = HoyoSekoSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = HoyoSekoListFilter
    permission_classes = [IsStaff]


class HoyoSekoDetail(generics.RetrieveUpdateDestroyAPIView):

    queryset = (
        HoyoSeko.objects.select_related('seko')
        .filter(Q(del_flg=False) & Q(seko__del_flg=False))
        .prefetch_related(
            Prefetch('schedules', queryset=HoyoSchedule.objects.order_by('display_num')),
            Prefetch('seko__moshu'),
            Prefetch(
                'seko__kojin',
                queryset=Kojin.objects.filter(Q(del_flg=False)).order_by('kojin_num'),
            ),
        )
        .order_by('hoyo_planned_date', 'hoyo_activity_date', 'pk')
        .all()
    )
    serializer_class = HoyoSekoSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]

    def destroy(self, request, *args, **kwargs):
        instance: HoyoSeko = self.get_object()
        instance.disable()
        return Response(status=status.HTTP_204_NO_CONTENT)


class HoyoPdf(views.APIView):
    def get(self, request, pk, format=None):
        hoyo_seko: HoyoSeko = (
            HoyoSeko.objects.filter(pk=pk)
            .select_related('hall')
            .prefetch_related(
                Prefetch('schedules', queryset=HoyoSchedule.objects.order_by('display_num'))
            )
            .first()
        )
        if not hoyo_seko:
            raise Http404(f'No {HoyoSeko._meta.object_name} matches the given query.')

        hoyo_url: str = urljoin(request_origin(self.request), 'hoyo/')
        qr_url: str = urljoin(hoyo_url, f'{hoyo_seko.hall.id}-{hoyo_seko.id}/')
        qr_image = qrcode.make(qr_url)

        static_images: Dict[str, str] = {'smaho': 'pdf/smaho.png', 'pc': 'pdf/pc.png'}
        image_encoded: Dict[str, str] = {}
        for key, image_name in static_images.items():
            physical_path: str = finders.find(image_name)
            with open(physical_path, 'rb') as image_file:
                encoded: str = b64encode(image_file.read()).decode('ascii')
                image_encoded[key] = f'data:image/png;base64,{encoded}'

        html_text = get_template('pdf/hoyo.html').render(
            {
                'hoyo_seko': hoyo_seko,
                'qr_image': qr_image,
                'today': timezone.now(),
                'hoyo_url': hoyo_url,
                'images': image_encoded,
            }
        )
        css_text = get_template('pdf/fuhoshi.css').render()

        buffer = BytesIO()
        HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
            buffer,
            stylesheets=[CSS(string=css_text, base_url=request.build_absolute_uri())],
        )
        buffer.seek(0)

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'法要案内状-{hoyo_seko.pk}.pdf',
            content_type='application/pdf',
        )
        return response


class HoyoMailListFilter(filters.FilterSet):
    hoyo = filters.NumberFilter(field_name='hoyo')
    send_date = filters.DateFromToRangeFilter(field_name='send_ts')
    company = filters.NumberFilter(field_name='targets__seko__seko_company', distinct=True)


class HoyoMailList(AddContextMixin, generics.ListCreateAPIView):

    queryset = (
        HoyoMail.objects.select_related('hoyo', 'hoyo__style', 'staff', 'staff__base')
        .prefetch_related(
            Prefetch(
                'staff__base__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
            ),
            Prefetch('staff__base__services', queryset=Service.objects.all()),
            Prefetch(
                'staff__base__service_reception_terms', queryset=ServiceReceptionTerm.objects.all()
            ),
            Prefetch(
                'targets', queryset=HoyoMailTarget.objects.select_related('seko').order_by('seko')
            ),
            Prefetch('targets__seko__moshu', queryset=Moshu.objects.all()),
            Prefetch('targets__seko__kojin', queryset=Kojin.objects.order_by('kojin_num')),
            Prefetch(
                'targets__seko__schedules', queryset=SekoSchedule.objects.order_by('display_num')
            ),
        )
        .order_by('hoyo__company', 'hoyo__display_num', 'select_ts')
        .all()
    )
    serializer_class = HoyoMailSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = HoyoMailListFilter
    permission_classes = [IsStaff]
