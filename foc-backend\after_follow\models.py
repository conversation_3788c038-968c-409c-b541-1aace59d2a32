from typing import Dict, List

from django.db import models
from django.utils.translation import gettext_lazy as _

from bases.models import Base
from seko.models import Seko


class AfGroup(models.Model):
    department = models.ForeignKey(
        Base, models.PROTECT, verbose_name=_('department'), related_name='af_groups'
    )
    name = models.TextField(_('name'))
    display_num = models.IntegerField(_('display order'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_af_group'

    def disable(self) -> None:
        """AF項目グループを即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()


class AfterFollow(models.Model):
    af_group = models.ForeignKey(
        AfGroup, models.CASCADE, verbose_name=_('group'), related_name='after_follows'
    )
    name = models.TextField(_('name'))
    display_num = models.IntegerField(_('display order'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_af'

    def disable(self) -> None:
        """AF項目を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()


class AfContactWay(models.Model):
    company = models.ForeignKey(
        Base, models.PROTECT, verbose_name=_('company'), related_name='af_contact_ways'
    )
    contact_way = models.TextField(_('contact way'))
    display_num = models.IntegerField(_('display order'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_af_contact_way'

    def disable(self) -> None:
        """AF連絡方法を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()


class SekoAf(models.Model):
    seko = models.OneToOneField(
        Seko,
        models.CASCADE,
        db_column='id',
        primary_key=True,
        verbose_name=_('seko'),
        related_name='seko_af',
    )
    contact_way = models.ForeignKey(
        AfContactWay, models.PROTECT, verbose_name=_('contact way'), blank=True, null=True
    )
    note = models.TextField(_('note'), blank=True, null=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_af'

    def update_wishes(self, wishes_data: List[Dict]) -> None:
        self.wishes.all().delete()
        new_wishes: List[AfWish] = []
        for wish_dict in wishes_data:
            new_wishes.append(AfWish(**wish_dict))
        self.wishes.set(new_wishes, bulk=False)


class ProposalStatusType(models.IntegerChoices):
    NON_PROPOSED = 0, _('not proposed')
    PROPOSED = 1, _('proposed')


class OrderStatusType(models.IntegerChoices):
    NEGOTIATING = 1, _('in negotiating')
    ORDERED = 2, _('received order')
    FAILURED = 3, _('failured to receiv order')


class OrderChanceType(models.IntegerChoices):
    NO_PROSPECT = 0, _('no prospect')
    PROSPECTIVE = 1, _('prospective')


class AfWish(models.Model):
    seko_af = models.ForeignKey(
        SekoAf,
        models.CASCADE,
        db_column='af_id',
        verbose_name=_('after follow'),
        related_name='wishes',
    )
    af_type = models.ForeignKey(AfterFollow, models.PROTECT, verbose_name=_('after follow type'))
    answered_flg = models.BooleanField(_('answered'))
    proposal_status = models.IntegerField(
        _('proposal status'), choices=ProposalStatusType.choices, blank=True, null=True
    )
    order_status = models.IntegerField(
        _('order status'), choices=OrderStatusType.choices, blank=True, null=True
    )
    order_date = models.DateField(_('ordered on'), blank=True, null=True)
    order_chance = models.IntegerField(
        _('order chance'), choices=OrderChanceType.choices, blank=True, null=True
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_af_wish'


class AfActivityDetail(models.Model):
    af_wish = models.ForeignKey(
        AfWish, models.CASCADE, verbose_name=_('wish'), related_name='activities'
    )
    activity_ts = models.DateTimeField(_('act at'))
    activity = models.TextField(_('activity'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_af_activity_detail'
