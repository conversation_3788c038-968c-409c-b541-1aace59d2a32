from decimal import Decimal
from distutils.util import strtobool
from io import BytesIO
from operator import itemgetter
from typing import Dict, List, Optional
from urllib.parse import urljoin

from django.conf import settings
from django.core.mail import EmailMessage
from django.db import transaction
from django.db.models import Prefetch, Q
from django.http import HttpResponseRedirect
from django.http.response import FileResponse
from django.template.loader import get_template
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters
from rest_framework import exceptions, generics, status, views
from rest_framework.exceptions import NotFound, PermissionDenied, ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from weasyprint import HTML

from bases.models import Base
from henrei.models import HenreihinKoden, HenreihinKumotsu
from orders.epsilon import ContractType, ResponsePayment, register_payment, register_payment2
from orders.models import (
    Entry,
    EntryDetail,
    EntryDetailChobun,
    EntryDetailKoden,
    EntryDetailKumotsu,
    EntryDetailMsg,
    PaymentResult,
)
from orders.serializers import (
    EntryCancelSerializer,
    EntryDetailKumotsuUpdateStatusSerializer,
    EntryDownwardSerializer,
    EntrySerializer,
    FdnOrderListSerializer,
    PaymentResultSerializer,
    ReceiptSerializer,
    SimpleEntryDetailChobunSerializer,
    SimpleEntryDetailKodenSerializer,
    SimpleEntryDetailKumotsuSerializer,
    SimpleEntryDetailMsgSerializer,
)
from seko.models import Kojin, SekoSchedule
from utils.filters import InListFilter
from utils.permissions import IsMoshu
from utils.requests import request_origin


class EntryListFilter(filters.FilterSet):
    seko_company = filters.NumberFilter(field_name='seko__seko_company')
    entry_ts_from = filters.DateFilter(field_name='entry_ts', lookup_expr='date__gte')
    entry_ts_to = filters.DateFilter(field_name='entry_ts', lookup_expr='date__lte')
    kumotsu_henrei_from = filters.DateFilter(
        field_name='details__kumotsu__henrei_kumotsu__created_at', lookup_expr='date__gte'
    )
    kumotsu_henrei_to = filters.DateFilter(
        field_name='details__kumotsu__henrei_kumotsu__created_at', lookup_expr='date__lte'
    )
    koden_henrei_from = filters.DateFilter(
        field_name='details__koden__henrei_koden__created_at', lookup_expr='date__gte'
    )
    koden_henrei_to = filters.DateFilter(
        field_name='details__koden__henrei_koden__created_at', lookup_expr='date__lte'
    )


class EntryList(generics.ListCreateAPIView):
    queryset = (
        Entry.objects.select_related('seko', 'payment')
        .prefetch_related(Prefetch('details', queryset=EntryDetail.objects.order_by('pk')))
        .filter(Q(seko__del_flg=False))
        .order_by('pk')
        .all()
    )
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = EntryListFilter
    serializer_class = EntrySerializer

    def send_complete_mail(self, instance: Entry, epsilon_order_number: Optional[int]) -> None:
        main_kojin: Kojin = instance.seko.kojin.filter(Q(kojin_num=1)).first()
        seko_schedule: SekoSchedule = instance.seko.schedules.filter(Q(schedule_id=2)).first()

        # 合計金額は単価✕数量をすべて足す、税額は商品、香典の分も合わせて税率毎に集計する
        total_amount: Decimal = Decimal('0')
        tax_subtotals: Dict[int, Decimal] = {}
        for detail in instance.details.all():
            total_amount += Decimal(str(detail.item_unit_price)) * Decimal(str(detail.quantity))
            tax_subtotals[detail.item_tax_pct] = (
                Decimal(str(tax_subtotals.get(detail.item_tax_pct, 0)))
                + Decimal(str(detail.item_tax))
                + Decimal(str(detail.tax_adjust))
            )
            if hasattr(detail, 'koden'):
                total_amount += Decimal(str(detail.koden.koden_commission))
                tax_subtotals[detail.koden.koden_commission_tax_pct] = (
                    Decimal(str(tax_subtotals.get(detail.koden.koden_commission_tax_pct, 0)))
                    + Decimal(str(detail.koden.koden_commission_tax))
                    + Decimal(str(detail.koden.tax_adjust))
                )

        # 税率0のものは行ごと削除する
        if 0 in tax_subtotals:
            del tax_subtotals[0]

        bcc_mail_address = [val for val in settings.ORDER_EMAIL_BCC]
        if instance.seko.seko_department.base_type != Base.OrgType.DEPARTMENT:
            if instance.seko.seko_department.order_mail_address:
                bcc_mail_address += instance.seko.seko_department.order_mail_address.split(',')
            hall = Base.objects.filter(pk=instance.seko.seko_department.id).first()
            if hall and hall.parent and hall.parent.order_mail_address:
                bcc_mail_address += hall.parent.order_mail_address.split(',')
        elif instance.seko.seko_department.order_mail_address:
            bcc_mail_address += instance.seko.seko_department.order_mail_address.split(',')

        if instance.seko.seko_company.order_mail_address:
            bcc_mail_address += instance.seko.seko_company.order_mail_address.split(',')
        mail_body = (
            get_template('mail/entry_complete.txt')
            .render(
                {
                    'object': instance,
                    'main_kojin': main_kojin,
                    'seko_schedule': seko_schedule,
                    'total_amount': total_amount,
                    'tax_subtotals': sorted(tax_subtotals.items(), key=lambda x: x[0]),
                    'receipt_pdf_url': urljoin(
                        request_origin(self.request), f'order/{instance.pk}/receipt/'
                    ),
                    'order_number': epsilon_order_number,
                }
            )
            .replace('  ', '　')
        )
        bcc_mail_address = list(set(bcc_mail_address))
        if instance.entry_mail_address in bcc_mail_address:
            bcc_mail_address.remove(instance.entry_mail_address)

        message = EmailMessage(
            subject=f'てれ葬儀こころ【{instance.seko.soke_name}家】：お申込みを受け付けました',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=[instance.entry_mail_address],
            bcc=bcc_mail_address,
        )
        message.send(fail_silently=False)

    def perform_create(self, serializer) -> int:
        con_count: int = serializer.validated_data.pop('con_count')
        contract_type: Optional[int] = serializer.validated_data.pop('contract_type', None)
        epsilon_token: str = serializer.validated_data.pop('epsilon_token', '')
        billing_amount: int = serializer.validated_data.pop('billing_amount')
        order_number: Optional[int] = serializer.validated_data.pop('order_number', None)
        if con_count == 1:
            epsilon_result: Optional[ResponsePayment] = None
            if settings.ENABLE_EPSILON_PAYMENT and billing_amount > 0:
                epsilon_params: Dict = {
                    'contract_code': ContractType(contract_type).contract_code(
                        serializer.validated_data['seko']
                    ),
                    'token': epsilon_token,
                    'user_name': serializer.validated_data['entry_name'],
                    'user_mail_address': serializer.validated_data['entry_mail_address'],
                    'price': billing_amount,
                    'user_agent': self.request.META.get('USER_AGENT', 'non-existent agent'),
                    'gmo_test_flg': (
                        serializer.validated_data['seko'].seko_company.focfee.gmo_test_flg
                        if hasattr(serializer.validated_data['seko'].seko_company, 'focfee')
                        else False
                    ),
                    'secure_chk_flg': (
                        serializer.validated_data['seko'].seko_company.focfee.secure_chk_flg
                        if hasattr(serializer.validated_data['seko'].seko_company, 'focfee')
                        else False
                    ),
                }
                epsilon_result = register_payment(**epsilon_params)
                result_code = epsilon_result.result_code if epsilon_result is not None else None
                epsilon_order_number = (
                    epsilon_result.order_number if epsilon_result is not None else None
                )
                if result_code == '1':
                    super().perform_create(serializer)
                    self.send_complete_mail(
                        instance=serializer.instance, epsilon_order_number=epsilon_order_number
                    )
                    ret_data = serializer.data
                    ret_data['order_number'] = epsilon_order_number
                    ret_data['result_code'] = result_code
                    return ret_data
                if result_code == '6':
                    ret_data = serializer.data
                    tds2_url = epsilon_result.tds2_url if epsilon_result is not None else None
                    ret_data['tds2_url'] = tds2_url
                    pareq = epsilon_result.pareq if epsilon_result is not None else None
                    ret_data['pareq'] = pareq
                    trans_code = epsilon_result.trans_code if epsilon_result is not None else None
                    ret_data['order_number'] = epsilon_order_number
                    ret_data['trans_code'] = trans_code
                    ret_data['result_code'] = result_code
                    return ret_data
            else:
                # メッセージのみ
                super().perform_create(serializer)
                ret_data = serializer.data
                ret_data['result_code'] = '1'
                self.send_complete_mail(instance=serializer.instance, epsilon_order_number=None)
                return ret_data
        else:
            super().perform_create(serializer)
            self.send_complete_mail(
                instance=serializer.instance, epsilon_order_number=order_number
            )
            return serializer.data

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        ret_data = self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(ret_data, status=status.HTTP_201_CREATED, headers=headers)


class EntryDetailView(generics.RetrieveAPIView):
    queryset = Entry.objects.filter(cancel_ts=None)
    serializer_class = EntryDownwardSerializer
    # permission_classes = [IsAuthenticated]


class EntryCancelView(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, pk, format=None):
        entry: Entry = Entry.objects.filter(Q(cancel_ts=None) & Q(pk=pk)).first()
        if not entry:
            raise NotFound(f'No {Entry._meta.object_name} matches the given query.')

        serializer = EntryCancelSerializer(instance=entry, data=request.data)
        serializer.is_valid(raise_exception=True)
        instance: Entry = serializer.save()
        read_serializer = EntrySerializer(instance)
        return Response(read_serializer.data, status=status.HTTP_200_OK)


class ReceiptPDF(generics.RetrieveAPIView):
    queryset = Entry.objects.filter(cancel_ts=None).select_related(
        'seko__seko_company', 'seko__seko_staff'
    )
    serializer_class = ReceiptSerializer

    def retrieve(self, request, *args, **kwargs):
        atena = request.query_params.get('atena', None)
        if not atena:
            raise exceptions.ParseError()
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        data = serializer.data

        html_text = get_template('pdf/receipt.html').render(data)

        buffer = BytesIO()
        HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
            buffer,
        )
        buffer.seek(0)

        # 発行回数更新
        instance.increase_receipt_count()

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f"領収書-{data['id']}.pdf",
            content_type='application/pdf',
        )
        return response


class ChobunPDF(generics.RetrieveAPIView):
    queryset = EntryDetail.objects.select_related('entry', 'chobun', 'chobun__daishi')

    def retrieve(self, request, *args, **kwargs):
        entry_detail = self.get_object()
        if not hasattr(entry_detail, 'chobun'):
            raise NotFound(f'No {EntryDetailChobun._meta.object_name} matches the given query.')

        html_text = get_template('pdf/chobun.html').render(
            {
                'entry': entry_detail.entry,
                'chobun': entry_detail.chobun,
            }
        )

        buffer = BytesIO()
        HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
            buffer,
        )
        buffer.seek(0)

        # 弔文印刷済更新
        entry_detail.chobun.set_printed()

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'弔文-{entry_detail.id}.pdf',
            content_type='application/pdf',
        )
        return response


class KumotsuListFilter(filters.FilterSet):
    company_id = filters.NumberFilter(field_name='entry_detail__entry__seko__seko_company_id')
    seko_department = filters.NumberFilter(method='filter_seko_department')
    entry_id = filters.NumberFilter(field_name='entry_detail__entry')
    supplier_id = filters.NumberFilter()
    okurinushi_name = filters.CharFilter(field_name='okurinushi_name', lookup_expr='icontains')
    entry_name = filters.CharFilter(
        field_name='entry_detail__entry__entry_name', lookup_expr='icontains'
    )
    entry_tel = filters.CharFilter(
        field_name='entry_detail__entry__entry_tel', lookup_expr='icontains'
    )
    renmei = filters.CharFilter(method='filter_renmei')
    okurinushi_company = filters.CharFilter(lookup_expr='icontains')
    okurinushi_title = filters.CharFilter(lookup_expr='icontains')
    soke_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__soke_name', lookup_expr='icontains'
    )
    kojin_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__kojin__name', lookup_expr='icontains', distinct=True
    )
    moshu_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__moshu__name', lookup_expr='icontains'
    )
    order_status = InListFilter(field_name='order_status')
    is_cancel = filters.BooleanFilter(method='filter_is_cancel')

    class Meta:
        model = EntryDetailKumotsu
        fields = '__all__'

    def filter_renmei(self, queryset, name, value):
        return queryset.filter(Q(renmei1__icontains=value) | Q(renmei2__icontains=value))

    def filter_is_cancel(self, queryset, name, value):
        if value:
            return queryset.filter(entry_detail__entry__cancel_ts__isnull=False)
        return queryset.filter(entry_detail__entry__cancel_ts__isnull=True)

    def filter_seko_department(self, queryset, name, value):
        departments = Base.objects.filter(pk=value).get_cached_trees()
        if not departments:
            error_message: str = _(
                'Select a valid choice. That choice is not one of the available choices.'
            )
            raise ValidationError({'seko_department': error_message})
        department_ids: List[int] = [
            base.pk for base in departments[0].get_descendants(include_self=True)
        ]
        return queryset.filter(Q(entry_detail__entry__seko__seko_department__in=department_ids))


class KumotsuList(generics.ListAPIView):
    queryset = (
        EntryDetailKumotsu.objects.select_related(
            'entry_detail',
            'entry_detail__entry',
            'entry_detail__entry__seko',
            'entry_detail__entry__seko__moshu',
            'supplier',
        )
        .prefetch_related('entry_detail__entry__seko__kojin')
        .filter(
            entry_detail__entry__seko__del_flg=False,
            entry_detail__entry__seko__kojin__kojin_num=1,
        )
        .order_by('entry_detail__entry')
    )

    serializer_class = SimpleEntryDetailKumotsuSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = KumotsuListFilter

    permission_classes = [IsAuthenticated]


class KumotsuDetail(generics.RetrieveUpdateAPIView):

    queryset = EntryDetailKumotsu.objects.all()
    serializer_class = SimpleEntryDetailKumotsuSerializer
    permission_classes = [IsAuthenticated]


class KumotsuUpdateStatus(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, format=None):
        serializer = EntryDetailKumotsuUpdateStatusSerializer(data=request.data)
        serializer.context['staff'] = request.user
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        instances: List[EntryDetailKumotsu] = serializer.save()
        read_serializer = SimpleEntryDetailKumotsuSerializer(instances, many=True)
        return Response(read_serializer.data)


def make_kumotsu_pdf(detail_kumotsu: EntryDetailKumotsu, base_url: str) -> BytesIO:
    html_text = get_template('pdf/kumotsu.html').render(
        {
            'detail_kumotsu': detail_kumotsu,
            'seko': detail_kumotsu.entry_detail.entry.seko,
            'kojin': detail_kumotsu.entry_detail.entry.seko.kojin_1,
            'schedules': {
                schedule.schedule_id: schedule
                for schedule in detail_kumotsu.entry_detail.entry.seko.schedule_12
            },
        }
    )

    buffer = BytesIO()
    HTML(string=html_text, base_url=base_url).write_pdf(buffer)
    buffer.seek(0)

    return buffer


class KumotsuPDF(generics.RetrieveAPIView):
    queryset = (
        EntryDetailKumotsu.objects.select_related(
            'entry_detail',
            'entry_detail__entry',
            'entry_detail__entry__seko',
            'entry_detail__entry__seko__seko_company',
            'entry_detail__entry__seko__moshu',
            'supplier',
            'order_staff',
        )
        .prefetch_related(
            Prefetch(
                'entry_detail__entry__seko__kojin',
                queryset=Kojin.objects.filter(kojin_num=1),
                to_attr='kojin_1',
            ),
            Prefetch(
                'entry_detail__entry__seko__schedules',
                queryset=SekoSchedule.objects.filter(schedule__id__in=[1, 2]),
                to_attr='schedule_12',
            ),
        )
        .filter(entry_detail__entry__seko__del_flg=False)
    )

    def retrieve(self, request, *args, **kwargs):
        detail_kumotsu = self.get_object()
        buffer = make_kumotsu_pdf(
            detail_kumotsu=detail_kumotsu, base_url=request.build_absolute_uri()
        )

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'供花・供物発注書-{detail_kumotsu.pk}.pdf',
            content_type='application/pdf',
        )
        return response


class KumotsuPDFFax(views.APIView):

    permission_classes = [IsAuthenticated]

    @transaction.atomic
    def post(self, request, pk, format=None):
        detail_kumotsu = KumotsuPDF.queryset.filter(Q(pk=pk)).first()
        if not detail_kumotsu:
            raise NotFound(f'No {EntryDetailKumotsu._meta.object_name} matches the given query.')

        detail_kumotsu.set_order_status(2, request.user)
        sender_address = detail_kumotsu.entry_detail.entry.seko.seko_company.tokusho.from_name
        if detail_kumotsu.entry_detail.entry.seko.order_staff.mail_address:
            order_staff_mail_address = [
                detail_kumotsu.entry_detail.entry.seko.order_staff.mail_address
            ]
        else:
            order_staff_mail_address = []

        if detail_kumotsu.supplier.order_mail_send_flg and detail_kumotsu.supplier.mail_address:
            recipient_address = detail_kumotsu.supplier.mail_address
            mail_body = get_template('mail/order.txt').render(
                {
                    'company': detail_kumotsu.entry_detail.entry.seko.seko_company.base_name,
                }
            )
            message = EmailMessage(
                subject='注文書の送付',
                body=mail_body,
                from_email=sender_address,
                to=[recipient_address],
                bcc=order_staff_mail_address,
            )
        else:
            recipient_address = f'{detail_kumotsu.supplier.fax_digits()}@cl1.faximo.jp'
            message = EmailMessage(
                subject='===<<<',
                body=None,
                from_email=sender_address,
                to=[recipient_address],
            )
        buffer = make_kumotsu_pdf(
            detail_kumotsu=detail_kumotsu, base_url=request.build_absolute_uri()
        )
        message.attach(
            filename=f'kumotsu_{pk}.pdf', content=buffer.getvalue(), mimetype='application/pdf'
        )
        message.send(fail_silently=False)
        return Response(status=status.HTTP_200_OK)


class ChobunListFilter(filters.FilterSet):
    company_id = filters.NumberFilter(field_name='entry_detail__entry__seko__seko_company_id')
    seko_department = filters.NumberFilter(method='filter_seko_department')
    entry_id = filters.NumberFilter(field_name='entry_detail__entry')
    okurinushi_name = filters.CharFilter(field_name='okurinushi_name', lookup_expr='icontains')
    entry_name = filters.CharFilter(
        field_name='entry_detail__entry__entry_name', lookup_expr='icontains'
    )
    entry_tel = filters.CharFilter(
        field_name='entry_detail__entry__entry_tel', lookup_expr='icontains'
    )
    renmei = filters.CharFilter(method='filter_renmei')
    okurinushi_company = filters.CharFilter(lookup_expr='icontains')
    okurinushi_title = filters.CharFilter(lookup_expr='icontains')
    soke_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__soke_name', lookup_expr='icontains'
    )
    kojin_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__kojin__name', lookup_expr='icontains', distinct=True
    )
    moshu_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__moshu__name', lookup_expr='icontains'
    )
    printed_flg = filters.BooleanFilter(field_name='printed_flg')
    is_cancel = filters.BooleanFilter(method='filter_is_cancel')

    class Meta:
        model = EntryDetailChobun
        fields = '__all__'

    def filter_renmei(self, queryset, name, value):
        return queryset.filter(Q(renmei1__icontains=value) | Q(renmei2__icontains=value))

    def filter_is_cancel(self, queryset, name, value):
        if value:
            return queryset.filter(entry_detail__entry__cancel_ts__isnull=False)
        return queryset.filter(entry_detail__entry__cancel_ts__isnull=True)

    def filter_seko_department(self, queryset, name, value):
        departments = Base.objects.filter(pk=value).get_cached_trees()
        if not departments:
            error_message: str = _(
                'Select a valid choice. That choice is not one of the available choices.'
            )
            raise ValidationError({'seko_department': error_message})
        department_ids: List[int] = [
            base.pk for base in departments[0].get_descendants(include_self=True)
        ]
        return queryset.filter(Q(entry_detail__entry__seko__seko_department__in=department_ids))


class ChobunList(generics.ListAPIView):
    queryset = (
        EntryDetailChobun.objects.select_related(
            'entry_detail',
            'entry_detail__entry',
            'entry_detail__entry__seko',
            'entry_detail__entry__seko__moshu',
        )
        .prefetch_related('entry_detail__entry__seko__kojin')
        .filter(
            entry_detail__entry__seko__del_flg=False,
            entry_detail__entry__seko__kojin__kojin_num=1,
        )
        .order_by('entry_detail__entry')
    )

    serializer_class = SimpleEntryDetailChobunSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = ChobunListFilter

    permission_classes = [IsAuthenticated]


class ChobunDetail(generics.RetrieveUpdateAPIView):

    queryset = EntryDetailChobun.objects.all()
    serializer_class = SimpleEntryDetailChobunSerializer
    permission_classes = [IsAuthenticated]


class KodenListFilter(filters.FilterSet):
    company_id = filters.NumberFilter(field_name='entry_detail__entry__seko__seko_company_id')
    seko_department = filters.NumberFilter(method='filter_seko_department')
    entry_id = filters.NumberFilter(field_name='entry_detail__entry')
    entry_name = filters.CharFilter(
        field_name='entry_detail__entry__entry_name', lookup_expr='icontains'
    )
    entry_tel = filters.CharFilter(
        field_name='entry_detail__entry__entry_tel', lookup_expr='icontains'
    )
    soke_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__soke_name', lookup_expr='icontains'
    )
    kojin_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__kojin__name', lookup_expr='icontains', distinct=True
    )
    moshu_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__moshu__name', lookup_expr='icontains'
    )
    is_cancel = filters.BooleanFilter(method='filter_is_cancel')

    class Meta:
        model = EntryDetailKoden
        fields = '__all__'

    def filter_is_cancel(self, queryset, name, value):
        if value:
            return queryset.filter(entry_detail__entry__cancel_ts__isnull=False)
        return queryset.filter(entry_detail__entry__cancel_ts__isnull=True)

    def filter_seko_department(self, queryset, name, value):
        departments = Base.objects.filter(pk=value).get_cached_trees()
        if not departments:
            error_message: str = _(
                'Select a valid choice. That choice is not one of the available choices.'
            )
            raise ValidationError({'seko_department': error_message})
        department_ids: List[int] = [
            base.pk for base in departments[0].get_descendants(include_self=True)
        ]
        return queryset.filter(Q(entry_detail__entry__seko__seko_department__in=department_ids))


class KodenList(generics.ListAPIView):
    queryset = (
        EntryDetailKoden.objects.select_related(
            'entry_detail',
            'entry_detail__entry',
            'entry_detail__entry__seko',
            'entry_detail__entry__seko__moshu',
        )
        .prefetch_related('entry_detail__entry__seko__kojin')
        .filter(
            entry_detail__entry__seko__del_flg=False,
            entry_detail__entry__seko__kojin__kojin_num=1,
        )
        .order_by('entry_detail__entry')
    )

    serializer_class = SimpleEntryDetailKodenSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = KodenListFilter

    permission_classes = [IsAuthenticated]


class MsgListFilter(filters.FilterSet):
    company_id = filters.NumberFilter(field_name='entry_detail__entry__seko__seko_company_id')
    seko_department = filters.NumberFilter(method='filter_seko_department')
    entry_id = filters.NumberFilter(field_name='entry_detail__entry')
    entry_name = filters.CharFilter(
        field_name='entry_detail__entry__entry_name', lookup_expr='icontains'
    )
    entry_tel = filters.CharFilter(
        field_name='entry_detail__entry__entry_tel', lookup_expr='icontains'
    )
    soke_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__soke_name', lookup_expr='icontains'
    )
    kojin_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__kojin__name', lookup_expr='icontains', distinct=True
    )
    moshu_name = filters.CharFilter(
        field_name='entry_detail__entry__seko__moshu__name', lookup_expr='icontains'
    )
    release_status = InListFilter(field_name='release_status')

    class Meta:
        model = EntryDetailMsg
        fields = '__all__'

    def filter_seko_department(self, queryset, name, value):
        departments = Base.objects.filter(pk=value).get_cached_trees()
        if not departments:
            error_message: str = _(
                'Select a valid choice. That choice is not one of the available choices.'
            )
            raise ValidationError({'seko_department': error_message})
        department_ids: List[int] = [
            base.pk for base in departments[0].get_descendants(include_self=True)
        ]
        return queryset.filter(Q(entry_detail__entry__seko__seko_department__in=department_ids))


class MsgList(generics.ListAPIView):
    queryset = (
        EntryDetailMsg.objects.select_related(
            'entry_detail',
            'entry_detail__entry',
            'entry_detail__entry__seko',
            'entry_detail__entry__seko__moshu',
        )
        .prefetch_related('entry_detail__entry__seko__kojin')
        .filter(
            entry_detail__entry__seko__del_flg=False,
            entry_detail__entry__seko__kojin__kojin_num=1,
        )
        .order_by('entry_detail__entry')
    )

    serializer_class = SimpleEntryDetailMsgSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = MsgListFilter

    permission_classes = [IsAuthenticated]


class MsgDetail(generics.RetrieveUpdateAPIView):

    queryset = EntryDetailMsg.objects.filter(Q(entry_detail__entry__seko__del_flg=False))
    serializer_class = SimpleEntryDetailMsgSerializer
    permission_classes = [IsMoshu]

    def get_object(self) -> EntryDetailMsg:
        instance: EntryDetailMsg = super().get_object()
        if instance.entry_detail.entry.seko.moshu != self.request.user:
            raise PermissionDenied
        return instance


class FdnOrderListFilter(filters.FilterSet):
    term_from = filters.DateTimeFilter(field_name='term', lookup_expr='gte')
    term_to = filters.DateTimeFilter(field_name='term', lookup_expr='lte')


class FdnOrderList(generics.ListAPIView):

    serializer_class = FdnOrderListSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        company: Base = Base.objects.filter(
            Q(del_flg=False),
            Q(base_type=Base.OrgType.COMPANY),
            Q(company_code=self.request.user.company_code),
        ).first()
        if not company:
            raise ValidationError(f'Base not found: company_code={self.request.user.company_code}')

        order_list_columns: List[str] = [
            'seko_id',
            'term',
            'fdn_seko_code',
            'soke_name',
            'kojin_name',
            'kojin_birth_date',
            'kojin_death_date',
            'moshu_name',
            'moshu_tel',
            'moshu_mobile',
            'entry_id',
            'entry_name',
            'entry_name_kana',
            'entry_zip_code',
            'entry_prefecture',
            'entry_address_1',
            'entry_address_2',
            'entry_address_3',
            'entry_tel',
            'entry_mail_address',
            'entry_ts',
            'cancel_ts',
            'service_id',
            'service_name',
            'entry_detail_id',
            'item_id',
            'fdn_item_code',
            'item_hinban',
            'item_name',
            'item_unit_price',
            'quantity',
            'item_tax',
            'item_tax_adjust',
            'item_tax_pct',
            'keigen_flg',
            'okurinushi_company',
            'okurinushi_company_kana',
            'okurinushi_title',
            'okurinushi_name',
            'okurinushi_zip_code',
            'okurinushi_prefecture',
            'okurinushi_address_1',
            'okurinushi_address_2',
            'okurinushi_address_3',
            'okurinushi_tel',
            'renmei1',
            'renmei2',
            'update_ts',
        ]
        # 弔文
        chobun_qs = EntryDetailChobun.fdn_order_list.get_queryset(company.id).values_list(
            *order_list_columns
        )
        chobun_filter = FdnOrderListFilter(
            data=self.request.query_params, queryset=chobun_qs, request=self.request
        )
        chobun_list = list(chobun_filter.qs.values())
        # 供花・供物
        kumotsu_qs = EntryDetailKumotsu.fdn_order_list.get_queryset(company.id).values_list(
            *order_list_columns
        )
        kumotsu_filter = FdnOrderListFilter(
            data=self.request.query_params, queryset=kumotsu_qs, request=self.request
        )
        kumotsu_list = list(kumotsu_filter.qs.values())
        # 香典
        koden_qs = EntryDetailKoden.fdn_order_list.get_queryset(company.id).values_list(
            *order_list_columns
        )
        koden_filter = FdnOrderListFilter(
            data=self.request.query_params, queryset=koden_qs, request=self.request
        )
        koden_list = list(koden_filter.qs.values())
        # 香典システム利用料
        koden_commssion_qs = EntryDetailKoden.fdn_commission_list.get_queryset(
            company.id
        ).values_list(*order_list_columns)
        koden_commssion_filter = FdnOrderListFilter(
            data=self.request.query_params, queryset=koden_commssion_qs, request=self.request
        )
        koden_commssion_list = list(koden_commssion_filter.qs.values())
        # 供花・供物返礼品
        kumotsu_henrei_qs = HenreihinKumotsu.fdn_henrei_list.get_queryset(company.id).values_list(
            *order_list_columns
        )
        kumotsu_henrei_filter = FdnOrderListFilter(
            data=self.request.query_params, queryset=kumotsu_henrei_qs, request=self.request
        )
        kumotsu_henrei_list = list(kumotsu_henrei_filter.qs.values())
        # 香典返礼品
        koden_henrei_qs = HenreihinKoden.fdn_henrei_list.get_queryset(company.id).values_list(
            *order_list_columns
        )
        koden_henrei_filter = FdnOrderListFilter(
            data=self.request.query_params, queryset=koden_henrei_qs, request=self.request
        )
        koden_henrei_list = list(koden_henrei_filter.qs.values())

        result = (
            chobun_list
            + kumotsu_list
            + koden_list
            + koden_commssion_list
            + kumotsu_henrei_list
            + koden_henrei_list
        )
        result = sorted(
            result, key=itemgetter('seko_id', 'entry_id', 'entry_detail_id', 'service_id')
        )
        for order in result:
            seko_schedule_tsuya = SekoSchedule.objects.filter(
                Q(seko=order['seko_id']), Q(schedule=1)
            ).first()
            if seko_schedule_tsuya:
                order['tsuya_hall_name'] = seko_schedule_tsuya.hall_name
                order['tsuya_date'] = seko_schedule_tsuya.schedule_date
                order['tsuya_begin_time'] = seko_schedule_tsuya.begin_time
            else:
                order['tsuya_hall_name'] = None
                order['tsuya_date'] = None
                order['tsuya_begin_time'] = None
            seko_schedule_sogi = SekoSchedule.objects.filter(
                Q(seko=order['seko_id']), Q(schedule=2)
            ).first()
            if seko_schedule_sogi:
                order['sogi_hall_name'] = seko_schedule_sogi.hall_name
                order['sogi_date'] = seko_schedule_sogi.schedule_date
                order['sogi_begin_time'] = seko_schedule_sogi.begin_time
            else:
                order['sogi_hall_name'] = None
                order['sogi_date'] = None
                order['sogi_begin_time'] = None
        return result


class EpsilonReturn(views.APIView):
    @transaction.atomic
    def post(self, request, format=None):
        pares = request.data.get('PaRes', None)
        md = request.data.get('MD', None)
        mds = md.split(',')
        data = {}
        data['contract_code'] = str(mds[0])
        data['order_number'] = int(mds[1])
        gmo_test_flg = strtobool(mds[2])

        epsilon_params: Dict = {
            'contract_code': data['contract_code'],
            'tds_pares': pares,
            'order_number': data['order_number'],
            'gmo_test_flg': gmo_test_flg,
        }
        epsilon_result = register_payment2(**epsilon_params)
        result_code = epsilon_result.result_code if epsilon_result is not None else None
        data['result_code'] = result_code
        if result_code != '1':
            data['err_code'] = epsilon_result.err_code if epsilon_result is not None else None
            data['err_detail'] = epsilon_result.err_detail if epsilon_result is not None else None

        serializer = PaymentResultSerializer(data=data)
        if not serializer.is_valid():
            order_reject_url: str = urljoin(settings.FRONTEND_HOSTNAME, 'order/complete/?mode=ng')
            return HttpResponseRedirect(order_reject_url)
        serializer.save()
        order_complete_url: str = urljoin(settings.FRONTEND_HOSTNAME, 'order/complete/?mode=3d')
        return HttpResponseRedirect(order_complete_url)


class PaymentResultListView(generics.ListAPIView):
    queryset = PaymentResult.objects.order_by('id').all()
    serializer_class = PaymentResultSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['contract_code', 'order_number']
