from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.models import AfGroup
from after_follow.tests.factories import AfGroupFactory, AfterFollowFactory
from bases.models import Base
from bases.tests.factories import BaseFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class AfGroupDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.af_group = AfGroupFactory(department=base)
        self.after_follows = [
            AfterFollowFactory(af_group=self.af_group, display_num=2),
            AfterFollowFactory(af_group=self.af_group, display_num=1),
        ]

        self.api_client = APIClient()
        self.staff = StaffFactory(base=base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_af_group_detail_succeed(self) -> None:
        """AF項目グループ詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_detail', kwargs={'pk': self.af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.af_group.pk)
        self.assertEqual(record['department'], self.af_group.department.pk)
        self.assertEqual(record['name'], self.af_group.name)
        self.assertEqual(record['display_num'], self.af_group.display_num)
        self.assertEqual(record['del_flg'], self.af_group.del_flg)
        self.assertEqual(record['created_at'], self.af_group.created_at.astimezone(tz).isoformat())
        self.assertEqual(record['create_user_id'], self.af_group.create_user_id)
        self.assertEqual(record['updated_at'], self.af_group.updated_at.astimezone(tz).isoformat())
        self.assertEqual(record['update_user_id'], self.af_group.update_user_id)
        for record_follow, self_follow in zip(
            record['after_follows'], reversed(self.after_follows)
        ):
            self.assertEqual(record_follow['id'], self_follow.pk)
            self.assertEqual(record_follow['name'], self_follow.name)
            self.assertEqual(record_follow['display_num'], self_follow.display_num)

    def test_af_group_detail_failed_by_notfound(self) -> None:
        """AF項目グループ詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_af_group: AfGroup = AfGroupFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_detail', kwargs={'pk': non_saved_af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_af_group_detail_ignores_deleted(self) -> None:
        """AF項目グループ詳細APIは無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.af_group.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_detail', kwargs={'pk': self.af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_af_group_detail_failed_without_auth(self) -> None:
        """AF項目グループ詳細APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_detail', kwargs={'pk': self.af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class AfGroupUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()
        self.af_group = AfGroupFactory(department=self.base)
        self.new_af_group_data = AfGroupFactory.build(department=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'department': self.new_af_group_data.department.pk,
            'name': self.new_af_group_data.name,
            'display_num': self.new_af_group_data.display_num,
        }

    def test_af_group_update_succeed(self) -> None:
        """AF項目グループを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('after_follow:group_detail', kwargs={'pk': self.af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.af_group.pk)
        self.assertEqual(record['department'], self.new_af_group_data.department.pk)
        self.assertEqual(record['name'], self.new_af_group_data.name)
        self.assertEqual(record['display_num'], self.new_af_group_data.display_num)
        self.assertEqual(record['del_flg'], self.new_af_group_data.del_flg)
        self.assertEqual(record['created_at'], self.af_group.created_at.astimezone(tz).isoformat())
        self.assertEqual(record['create_user_id'], self.af_group.create_user_id)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_af_group_update_failed_by_notfound(self) -> None:
        """AF項目グループ更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_af_group: AfGroup = AfGroupFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_detail', kwargs={'pk': non_saved_af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_af_group_update_ignores_deleted(self) -> None:
        """AF項目グループ更新APIは無効化された法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.af_group.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_detail', kwargs={'pk': self.af_group.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_af_group_update_failed_without_auth(self) -> None:
        """AF項目グループ更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('after_follow:group_detail', kwargs={'pk': self.af_group.department.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class AfGroupDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.department = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.af_group: AfGroup = AfGroupFactory(department=self.department)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.department)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_af_group_delete_succeed(self) -> None:
        """AF項目グループを論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:group_detail', kwargs={'pk': self.af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # AF項目グループのdel_flgがTrueになるだけ
        db_af_group: AfGroup = AfGroup.objects.get(pk=self.af_group.pk)
        self.assertTrue(db_af_group.del_flg)
        self.assertEqual(db_af_group.create_user_id, self.af_group.create_user_id)
        self.assertEqual(db_af_group.update_user_id, self.staff.pk)

    def test_af_group_delete_failed_by_notfound(self) -> None:
        """AF項目グループ削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_af_group: AfGroup = AfGroupFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:group_detail', kwargs={'pk': non_saved_af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_af_group_delete_failed_by_already_deleted(self) -> None:
        """AF項目グループ削除APIが論理削除済みの法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.af_group.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:group_detail', kwargs={'pk': self.af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_af_group_delete_failed_without_auth(self) -> None:
        """AF項目グループ削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:group_detail', kwargs={'pk': self.af_group.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
