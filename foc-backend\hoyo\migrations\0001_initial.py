# Generated by Django 3.1.5 on 2021-01-12 09:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bases', '0013_smsaccount'),
        ('masters', '0014_hoyodefaultmaster'),
    ]

    operations = [
        migrations.CreateModel(
            name='Hoyo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('name', models.TextField(verbose_name='name')),
                (
                    'elapsed_time',
                    models.IntegerField(blank=True, null=True, verbose_name='elapsed time'),
                ),
                (
                    'unit',
                    models.IntegerField(
                        blank=True,
                        choices=[(1, 'days'), (2, 'months'), (3, 'years')],
                        null=True,
                        verbose_name='unit',
                    ),
                ),
                ('display_num', models.IntegerField(verbose_name='display order')),
                ('del_flg', models.BooleanField(default=False, verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('create_user_id', models.IntegerField(verbose_name='created by')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('update_user_id', models.IntegerField(verbose_name='updated by')),
                (
                    'company',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='hoyo',
                        to='bases.base',
                        verbose_name='company',
                    ),
                ),
                (
                    'style',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='hoyo',
                        to='masters.hoyostyle',
                        verbose_name='hoyo style',
                    ),
                ),
            ],
            options={
                'db_table': 'm_hoyo',
            },
        ),
    ]
