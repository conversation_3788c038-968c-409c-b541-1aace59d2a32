
<div class="container">
  <div class="inner with_footer">
    <div class="contents">
      <div class="menu_title">
        <i class="monument icon big"></i>
        <i class="filter icon combo"></i>
        法要案内抽出（盆）
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(departComboEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchSeko()">
          <i class="filter icon"></i>抽出
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label class="required">盆日付</label>
          <com-calendar #bonDateEm [settings]="calendarOptionDate" id="bon_date" [(value)]="form_data.bon_date"></com-calendar>
          <label>部門</label>
          <com-dropdown #departComboEm [settings]="departCombo" [(selectedValue)]="form_data.seko_department_id"></com-dropdown>
          <div class="ui toggle checkbox">
            <input type="checkbox" name="is_first" [(ngModel)]="form_data.is_first">
            <label>初盆</label>
          </div>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="seko_list?.length">
          全{{seko_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?seko_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="check">
                <div class="ui checkbox" [class.disabled]="!seko_list?.length">
                  <input type="checkbox" [(ngModel)]="all_check" (change)="checkAllItem()">
                  <label></label>
                </div>
              </th>
              <th class="seko_id">施行ID</th>
              <th class="soke_name">葬家名</th>
              <th class="kojin_name">故人名</th>
              <th class="moshu_name">喪主名</th>
              <th class="contact_name">連絡先名</th>
              <th class="death_date">逝去日</th>
              <th class="seko_date">施行日</th>
              <th class="hall_name">式場</th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body" [class.no-page-nav]="pagination.pages.length===1">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let seko of seko_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" (click)="checkItem($event, seko)">
              <td class="center aligned check">
                <div class="ui checkbox">
                  <input type="checkbox" [(ngModel)]="seko.selected">
                  <label></label>
                </div>
              </td>
              <td class="center aligned seko_id" title="{{seko.id}}">{{seko.id}}</td>
              <td class="soke_name" title="{{seko.soke_name}}">{{seko.soke_name}}</td>
              <td class="kojin_name" title="{{seko.kojin[0].name}}">{{seko.kojin[0].name}}</td>
              <td class="moshu_name" title="{{seko.moshu.name}}">{{seko.moshu.name}}</td>
              <td class="contact_name" title="{{seko.seko_contact?.name}}">{{seko.seko_contact?.name}}</td>
              <td class="center aligned death_date" title="{{seko.death_date | date: 'yyyy/MM/dd'}}">
                {{seko.death_date | date: 'yyyy/MM/dd'}}</td>
              <td class="center aligned seko_date" title="{{seko.seko_date | date: 'yyyy/MM/dd'}}">
                {{seko.seko_date | date: 'yyyy/MM/dd'}}</td>
              <td class="hall_name" title="{{getPlace(seko)}}">{{getPlace(seko)}}</td>
            </tr>
            </ng-container>
            <tr *ngIf="!seko_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="10">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="input_area">
        <div class="line row2">
          <label class="required">メール本文</label>
          <div class="ui icon input textarea">
              <i class="search icon link" (click)="showMailSample()"></i>
              <textarea rows="2" cols="35" [(ngModel)]="hoyo_mail.content"></textarea>
          </div>
          <div class="description">　　
            @k1：故人名（1人名）に置き換わります<br>
            @k2：故人名（2人名）に置き換わります<br>
            @m：連絡先名に置き換わります
          </div>
        </div>
        <div class="line">
          <label>備考</label>
          <div class="ui input full">
            <input type="text" autocomplete="off" [(ngModel)]="hoyo_mail.note">
          </div>
        </div>
      </div>

      <div class="ui modal small mail" id="mail-sample">
        <div class="header ui"><i class="mail icon large"></i>メール本文選択</div>
        <div class="scrolling content" *ngIf="mail_template_list?.length">
          <div class="ui segment" *ngFor="let mail_temp of mail_template_list" (click)="select_mail_text(mail_temp.sentence)">
            <pre>{{mail_temp.sentence}}</pre>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
		</div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group">
    <button class="ui labeled icon button mini light" (click)="exportCsv()">
      <i class="download icon"></i>CSV出力
    </button>
    <button class="ui labeled icon button mini light" [class.disabled]="buttonDisabled()" (click)="saveData()">
      <i class="sms icon"></i>SMS送信
    </button>
  </div>
</div>
