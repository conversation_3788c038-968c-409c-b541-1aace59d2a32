
<div class="container">
  <div class="inner">
    <div class="contents header">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="contents with-background-image description">
      <h2>ご香典お預かりサービス</h2>
      <div class="pre-wrap">{{description}}</div>
      <div class="remark">
        ※別途システム利用料を申し受けます。<br>
        ※御香典金額の領収書は発行しておりません。システム利用料のみの発行となります。
      </div>
      <div class="seko_company">
        葬儀に関するお問い合わせ<br>
        &nbsp;&nbsp;{{seko_info.fuho_contact_name}} <br>
        &nbsp;&nbsp;TEL：{{seko_info.fuho_contact_tel}} 
      </div>
      <div class="seko_company">
        操作に関するお問い合わせ<br>
        &nbsp;&nbsp;葬儀オンラインサービスこころサポートセンター <br>
        &nbsp;&nbsp;TEL：0120-691-484 <br>
        &nbsp;&nbsp;営業時間：09:00～21:00/年中無休
      </div>
      <div class="data-item">
        <div class="label">
          決済方法
        </div>
        <div class="data">
          クレジットカード決済
        </div>
      </div>
      <div class="title">ご利用できるクレジットカード</div>
      <div class="card-images">
        <div class="visa"></div>
        <div class="master"></div>
      </div>
      <div class="notice">
        <span class="important">【重要なお知らせ】3Dセキュア（本人認証サービス）対応のお願い<br>
このたび、クレジットカード決済の安全性向上を目的として、2025年4月1日より「3Dセキュア（本人認証サービス）」の登録が義務化されました。<br>
これにより、当サイトにてクレジットカードをご利用いただく際には、3Dセキュアに対応したカード、および本人認証の登録が必須となります。<br>
※全てのご注文で本人認証が求められるわけではなく、カード会社の判断によりワンタイムパスワード等の追加認証が行われます。<br>
【ご注意】<br>
※「3Dセキュア2.0」非対応のクレジットカードはご利用になれません。対応状況についてはカード発行会社にご確認ください。<br>
※本人認証の方法や、表示される本人認証画面の使い方は、カード会社により異なります。<br>
　詳しくはカード発行会社にご確認ください。<br>
何卒、ご理解とご協力を賜りますようお願い申し上げます。</span>
      </div>
      <div class="data-item">
        <div class="label">
          締切
        </div>
        <div class="data" *ngIf="service?.display_limit_ts">
          {{service.display_limit_ts}}まで
        </div>
      </div>
      <div class="message-area" *ngIf="message">
        {{message}}
      </div>
    </div>
    <ng-container *ngIf="!isExpierd() && !message">
    <div class="navigation">
      <div class="item current"><span>御香典</span><span>金額入力</span></div>
      <div class="arrow"></div>
      <div class="item"><span>返礼品</span><span>選択</span></div>
      <div class="arrow"></div>
      <div class="item"><span>カート</span><span>内容確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>注文</span><span>手続き</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込み</span><span>完了</span></div>
    </div>
    <div class="contents" *ngIf="koden_edit">
      <div class="input-area" *ngIf="koden_edit && koden_edit.koden">
        <div class="line">
          <div class="label required">御香典金額</div>
          <div class="input price" #priceEm [class.error]="isErrorField(priceEm)">
            <com-dropdown class="small align right" [settings]="priceCombo" [(selectedValue)]="koden_edit.koden.selected_price" (selectedValueChange)="selectPriceChange($event)"></com-dropdown>
            <div>,000円</div>
          </div>
        </div>
        <div class="line">
          <div class="label"></div>
          <div class="description">
            「その他」をお選びの場合、1〜{{price_max | number}}（1,000〜{{price_max | number}},000円）の範囲で金額をご入力ください。
          </div>
        </div>
        <div class="line">
          <div class="label"></div>
          <div class="input other_price" #otherPriceEm [class.error]="isErrorField(otherPriceEm)">
            <input class="small" type="number" min="1" [max]="price_max" [(ngModel)]="koden_edit.koden.other_price" [disabled]="!price_input" (input)="otherPriceChange()">
            <div>,000円</div>
          </div>
        </div>
        <div class="commission">
          <div class="koden_fee">
            システム利用料（{{seko_info.seko_company.focfee.koden_fee}}%）
          </div>
          <div class="koden_commission">
            {{koden_edit.koden.koden_commission | number}}円（税込）
          </div>
        </div>
        <div class="total">
          計　{{this.koden_edit.item_unit_price + koden_edit.koden.koden_commission | number}}円
        </div>
      </div>
    </div>
    </ng-container>
    <div class="button-area">
      <a class="button" (click)="back()">< TOPへ戻る</a>
      <a class="button grey" (click)="saveData()" *ngIf="!isExpierd() && !message">次へ ></a>
    </div>
  </div>
</div>
