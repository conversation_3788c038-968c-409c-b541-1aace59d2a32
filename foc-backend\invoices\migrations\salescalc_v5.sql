CREATE OR REPLACE PROCEDURE foc_salescalc
--CREATE OR REPLACE FUNCTION  foc_salescalc
(arg_yyyymm       char(6)
 )
--returns text
 as $$


DECLARE

  -- 変数宣言
  v_kekka       text;           --実行結果
  v_err_1       text;
  v_err_2       text;
  v_err_3       text;

  v_cnt         integer;

  v_fiscal_year integer;       -- 会計年度

  -- 定数宣言
  c_tax_pct          numeric(4,2);   -- 消費税率 
  c_koden_commission numeric(3,2);   -- 香典コミッションレート

BEGIN
  BEGIN
    v_kekka := '月次集計完了 ' || to_char(now(), 'YYYY/MM/DD HH:MI:SS'); 

    -- 定数値設定
    c_tax_pct := 10;
    c_koden_commission := 3.04;

    /* 未確定の集計レコードを削除 */
    -- 売上明細
    delete from sum_sales_detail A
    using
      sum_sales_seko_department B,
      sum_sales_company C,
      sum_sales_admin D
    where D.id = C.sales_admin_id
    and   C.id = B.sales_company_id
    and   B.id = A.sales_seko_dept_id
    and   D.sales_yymm = arg_yyyymm
    and   C.confirm_ts is null;

    -- 部門売上
    delete from sum_sales_seko_department A
    using
      sum_sales_company B,
      sum_sales_admin C
    where C.id = B.sales_admin_id
    and   B.id = A.sales_company_id
    and   C.sales_yymm = arg_yyyymm
    and   B.confirm_ts is null;

    -- 葬儀社売上
    delete from sum_sales_company A
    using
      sum_sales_admin B
    where B.id = A.sales_admin_id
    and   B.sales_yymm = arg_yyyymm
    and   A.confirm_ts is null;

    /* 月度売上管理テーブルの新規登録・更新 */
    select count(*) into v_cnt from sum_sales_admin where sales_yymm = arg_yyyymm;

    -- 新規の場合はInsert
    if v_cnt = 0 then

      -- 会計年度変換
      if substr(arg_yyyymm, 5, 2) in ('01','02','03') then
        v_fiscal_year := to_number(arg_yyyymm, '9999') - 1;
      else
        v_fiscal_year := to_number(arg_yyyymm, '9999');
      end if;


      delete from sum_sales_admin
      where  sales_yymm = arg_yyyymm;

      insert into sum_sales_admin
       (id, sales_yymm, fiscal_year, sum_ts)
      values
       (nextval('sum_sales_admin_id_seq'),arg_yyyymm,v_fiscal_year,now());

    -- 既存レコードが存在する場合はUpdate
    else
      update sum_sales_admin
      set    sum_ts = now()
      where  sales_yymm = arg_yyyymm;      

    end if;

/******************************************************************************************************************
  葬儀社集計
******************************************************************************************************************/
    insert into sum_sales_company
     (id,
      sales_admin_id, company_id, company_name,
      zip_code, address_1, address_2, tel,
      invoice_date, pay_date, 
      monthly_fee,
      chobun_fee,    chobun_fee_unit,    chobun_qty,       chobun_order_price,    chobun_tax,               chobun_order_fee,
      kumotsu_fee,   kumotsu_fee_unit,   kumotsu_qty,      kumotsu_order_price,   kumotsu_tax,              kumotsu_order_fee,
      henreihin_fee, henreihin_fee_unit, henreihin_qty,    henreihin_order_price, henreihin_tax,            henreihin_order_fee,
      koden_qty,     koden_order_price,  koden_order_fee,  koden_fee_commission,  koden_fee_commission_tax, koden_commission_pct,
      sales_price,   invoice_tax_pct,    invoice_tax,      created_at
      )
    select
      nextval('sum_sales_company_id_seq')                 "ID",
      iv1.sales_admin_id                                  "管理ID",
      MAIN.id                                             "葬儀社ID",
      MAIN.base_name                                      "葬儀社名",
      MAIN.zip_code                                       "郵便番号",
      MAIN.prefecture || MAIN.address_1 || MAIN.address_2 "住所1",
      MAIN.address_3                                      "住所2",
      MAIN.tel                                            "電話番号",

      (select cast(date_trunc('month',to_date(arg_yyyymm, 'YYYYMM'))  + '1 month' + '-1 days' as date)) "請求日",
      (select cast(date_trunc('month',to_date(arg_yyyymm, 'YYYYMM'))  + '2 month' + '-1 days' as date)) "支払日",

      M1.monthly_fee                                   "月額利用料",

      M1.chobun_fee                                    "弔文手数料",    
      case M1.chobun_fee_unit
        when 1 then '%'
        when 2 then '円'
      end                                              "弔文手数料単位",
      S21.order_quantity                               "弔文件数",
      S21.order_price                                  "弔文受注額",
      S21.order_tax                                    "弔文消費税",
      case M1.chobun_fee_unit
        when 1 then S21.order_price * chobun_fee / 100
        when 2 then S21.order_quantity * chobun_fee
      end                                              "弔文受注手数料",

      M1.kumotsu_fee                                   "供花供物手数料",
      case M1.kumotsu_fee_unit
        when 1 then '%'
        when 2 then '円'
      end                                              "供花供物手数料単位",
      S22.order_quantity                               "供花供物件数",
      S22.order_price                                  "供花供物受注額",
      S22.order_tax                                    "供花供物消費税",
      case M1.kumotsu_fee_unit
        when 1 then S22.order_price * kumotsu_fee / 100
        when 2 then S22.order_quantity * kumotsu_fee
      end                                              "供花供物受注手数料",

      M1.henreihin_fee                                                   "返礼品手数料",
      case M1.henreihin_fee_unit
        when 1 then '%'
        when 2 then '円'
      end                                                                "返礼品手数料単位",
      coalesce(S23.order_quantity,0) + coalesce(S24.order_quantity,0)    "返礼品件数",
      coalesce(S23.order_price,0) + coalesce(S24.order_price,0)          "返礼品受注額",
      coalesce(S23.order_tax,0) + coalesce(S24.order_tax,0)              "返礼品消費税",
      case M1.henreihin_fee_unit
        when 1 then (coalesce(S23.order_price,0) + coalesce(S24.order_price,0)) * henreihin_fee / 100
        when 2 then (coalesce(S23.order_quantity,0) + coalesce(S24.order_quantity,0)) * henreihin_fee
      end                                                                "返礼品受注手数料",

      S25.order_quantity                                                                     "香典件数",
      S25.order_price                                                                        "香典受注額",
      S25.koden_commission                                                                   "香典利用料",
      S25.koden_commission - trunc((S25.order_price + S25.koden_commission) * (c_koden_commission / 100), 0)
                                                                                             "香典利用手数料",

      (S25.koden_commission - trunc((S25.order_price + S25.koden_commission) * (c_koden_commission / 100), 0)) - 
      (
       (S25.koden_commission - trunc((S25.order_price + S25.koden_commission) * (c_koden_commission / 100), 0)) / (1 + c_tax_pct / 100) 
       )                                                                                     "香典利用手数料消費税",

      c_koden_commission                                                                     "香典手数料率",

      M1.monthly_fee
      +
      case M1.chobun_fee_unit
        when 1 then coalesce(S21.order_price,0) * coalesce(chobun_fee,0) / 100
        when 2 then coalesce(S21.order_quantity,0) * coalesce(chobun_fee,0)
      end
      +
      case M1.kumotsu_fee_unit
        when 1 then coalesce(S22.order_price,0) * coalesce(kumotsu_fee,0) / 100
        when 2 then coalesce(S22.order_quantity,0) * coalesce(kumotsu_fee,0)
      end
      +
      case M1.henreihin_fee_unit
        when 1 then (coalesce(S23.order_price,0) + coalesce(S24.order_price,0)) * henreihin_fee / 100
        when 2 then (coalesce(S23.order_quantity,0) + coalesce(S24.order_quantity,0)) * henreihin_fee
      end
                                                                                                "税抜売上合計",
      c_tax_pct                                                                                 "消費税率",

      trunc(
     (
      M1.monthly_fee
      +
      case M1.chobun_fee_unit
        when 1 then coalesce(S21.order_price,0) * coalesce(chobun_fee,0) / 100
        when 2 then coalesce(S21.order_quantity,0) * coalesce(chobun_fee,0)
      end
      +
      case M1.kumotsu_fee_unit
        when 1 then coalesce(S22.order_price,0) * kumotsu_fee / 100
        when 2 then S22.order_quantity * kumotsu_fee
      end
      +
      case M1.henreihin_fee_unit
        when 1 then (coalesce(S23.order_price,0) + coalesce(S24.order_price,0)) * coalesce(henreihin_fee,0) / 100
        when 2 then (coalesce(S23.order_quantity,0) + coalesce(S24.order_quantity,0)) * coalesce(henreihin_fee,0)
      end
      ) * c_tax_pct / 100
      , 0)                                            "消費税額",

     now()                                            "登録日時"

    from
      m_base MAIN
        join m_foc_fee M1
          on M1.company_id = MAIN.id

        join
          (select distinct MAIN.seko_company_id as seko_company_id
           from   tr_seko MAIN
           where  MAIN.del_flg = 'false'
           ) S1
             on S1.seko_company_id = MAIN.id

        -- 弔文
        left join
          (select MAIN.seko_company_id as seko_company_id,
                  sum(S2.item_unit_price * S2.quantity - (S2.item_tax + S2.tax_adjust)) as order_price,
                  sum(S2.item_tax + S2.tax_adjust) as order_tax,
                  sum(S2.quantity) as order_quantity
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                      and to_char(S1.entry_ts, 'YYYYMM') = arg_yyyymm
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_entry_detail_chobun S3
                      on S3.id = S2.id
           group by 1
           ) S21
             on S21.seko_company_id = MAIN.id

        -- 供花・供物
        left join
          (select MAIN.seko_company_id as seko_company_id,
                  sum(S2.item_unit_price * S2.quantity - (S2.item_tax + S2.tax_adjust)) as order_price,
                  sum(S2.item_tax + S2.tax_adjust) as order_tax,
                  sum(S2.quantity) as order_quantity    
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                      and to_char(S1.entry_ts, 'YYYYMM') = arg_yyyymm
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_entry_detail_kumotsu S3
                      on S3.id = S2.id
           group by 1
           ) S22
             on S22.seko_company_id = MAIN.id

        -- 返礼品（供花・供物）
        left join
          (select MAIN.seko_company_id as seko_company_id,
                  sum(S3.henreihin_price - S3.henreihin_tax) as order_price,
                  sum(S3.henreihin_tax) as order_tax,
                  count(*) as order_quantity    
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_henreihin_kumotsu S3
                      on   S3.id = S2.id
                      and to_char(S3.created_at, 'YYYYMM') = arg_yyyymm
           group by 1
           ) S23
             on S23.seko_company_id = MAIN.id

        -- 返礼品（香典）
        left join
          (select MAIN.seko_company_id as seko_company_id,
                  sum(S3.henreihin_price - S3.henreihin_tax) as order_price,
                  sum(S3.henreihin_tax) as order_tax,
                  count(*) as order_quantity       
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_henreihin_koden S3
                      on   S3.id = S2.id
                      and to_char(S3.created_at, 'YYYYMM') = arg_yyyymm
           group by 1
           ) S24
             on S24.seko_company_id = MAIN.id

        -- 香典、香典システム利用料
        left join
          (select MAIN.seko_company_id as seko_company_id,
                  count(*) as order_quantity,
                  sum(S2.item_unit_price * S2.quantity - (S2.item_tax + S2.tax_adjust)) as order_price,
                  sum(S3.koden_commission) as koden_commission
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                      and to_char(S1.entry_ts, 'YYYYMM') = arg_yyyymm
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_entry_detail_koden S3
                      on S3.id = S2.id
           group by 1
           ) S25
             on S25.seko_company_id = MAIN.id
       ,
        -- 上位テーブル（売上管理レコード）と紐づけ
       (select id as sales_admin_id
        from   sum_sales_admin
        where  sales_yymm = arg_yyyymm
        ) iv1

    -- 確定済の葬儀社は集計から除外
    where not exists
            (select 'a'
             from   sum_sales_company A
                      join sum_sales_admin B
                        on  B.id = A.sales_admin_id
                        and B.sales_yymm = arg_yyyymm
             where  A.confirm_ts is not null
             and    A.company_id = MAIN.id
             )

    order by 3
    ;


/******************************************************************************************************************
  施行部門集計
******************************************************************************************************************/
    insert into sum_sales_seko_department
     (id,
      sales_company_id,
      seko_department_id,
      base_name,
      chobun_qty,
      chobun_order_price,
      chobun_tax,
      kumotsu_qty,
      kumotsu_order_price,
      kumotsu_tax,
      henreihin_qty,
      henreihin_order_price,
      henreihin_tax,
      koden_qty,
      koden_order_price,
      koden_tax,
      koden_order_fee,
      created_at
      )

    select
      nextval('sum_sales_seko_department_id_seq'),
      iv1.sales_company_id,
      MAIN.id,
      MAIN.base_name,       
      S21.order_quantity,
      S21.order_price,
      S21.tax,
      S22.order_quantity,
      S22.order_price,
      S22.tax,
      coalesce(S23.order_quantity,0) + coalesce(S24.order_quantity,0),
      coalesce(S23.order_price,0) + coalesce(S24.order_price,0),
      coalesce(S23.tax,0) + coalesce(S24.tax,0),
      S25.order_quantity,
      S25.order_price,
      S25.tax,
      S25.koden_commission,
      now()
    from
      m_base MAIN
        join
          (select distinct MAIN.seko_department_id as seko_department_id
           from   tr_seko MAIN
           where  MAIN.del_flg = 'false'
           ) S1
             on S1.seko_department_id = MAIN.id

        -- 弔文
        left join
          (select MAIN.seko_department_id as seko_department_id,
                  sum(S2.item_unit_price * S2.quantity - (S2.item_tax + S2.tax_adjust)) as order_price,
                  sum(S2.quantity) as order_quantity,
                  sum(S2.item_tax + S2.tax_adjust) as tax
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                      and to_char(S1.entry_ts, 'YYYYMM') = arg_yyyymm
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_entry_detail_chobun S3
                      on S3.id = S2.id
           group by 1
           ) S21
             on S21.seko_department_id = MAIN.id

        -- 供花・供物
        left join
          (select MAIN.seko_department_id as seko_department_id,
                  sum(S2.item_unit_price * S2.quantity - (S2.item_tax + S2.tax_adjust)) as order_price,
                  sum(S2.quantity) as order_quantity,
                  sum(S2.item_tax + S2.tax_adjust) as tax
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                      and to_char(S1.entry_ts, 'YYYYMM') = arg_yyyymm
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_entry_detail_kumotsu S3
                      on S3.id = S2.id
           group by 1
           ) S22
             on S22.seko_department_id = MAIN.id

        -- 返礼品（供花・供物）
        left join
          (select MAIN.seko_department_id as seko_department_id,
                  sum(S3.henreihin_price - S3.henreihin_tax) as order_price,
                  count(*) as order_quantity,
                  sum(S3.henreihin_tax) as tax
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_henreihin_kumotsu S3
                      on   S3.id = S2.id
                      and to_char(S3.created_at, 'YYYYMM') = arg_yyyymm
           group by 1
           ) S23
             on S23.seko_department_id = MAIN.id

        -- 返礼品（香典）
        left join
          (select MAIN.seko_department_id as seko_department_id,
                  sum(S3.henreihin_price - S3.henreihin_tax) as order_price,
                  count(*) as order_quantity,
                  sum(S3.henreihin_tax) as tax
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_henreihin_koden S3
                      on   S3.id = S2.id
                      and to_char(S3.created_at, 'YYYYMM') = arg_yyyymm
           group by 1
           ) S24
             on S24.seko_department_id = MAIN.id

        -- 香典、香典システム利用料
        left join
          (select MAIN.seko_department_id as seko_department_id,
                  sum(S2.item_unit_price * S2.quantity - (S2.item_tax + S2.tax_adjust)) as order_price,
                  sum(S2.quantity) as order_quantity,
                  sum(S2.item_tax + S2.tax_adjust) as tax,
                  sum(S3.koden_commission) as koden_commission
           from   tr_seko MAIN
                    join tr_entry S1
                      on  S1.seko_id = MAIN.id
                      and S1.cancel_ts is null
                      and to_char(S1.entry_ts, 'YYYYMM') = arg_yyyymm
                    join tr_entry_detail S2
                      on S2.entry_id = S1.id
                    join tr_entry_detail_koden S3
                      on S3.id = S2.id
           group by 1
           ) S25
             on S25.seko_department_id = MAIN.id

        -- 上位テーブル（葬儀社レコード）と紐づけ、確定済の葬儀社は集計から除外
        join
        (select MAIN.id         as sales_company_id,
                MAIN.company_id as company_id
         from   sum_sales_company MAIN
                  join sum_sales_admin S1
                    on  S1.id = MAIN.sales_admin_id
                    and S1.sales_yymm = arg_yyyymm 
         where  MAIN.confirm_ts is null
         ) iv1
           on (iv1.company_id = MAIN.parent_base_id
           or  iv1.company_id = (select parent_base_id 
                                from   m_base
                                where  id = (select parent_base_id
                                             from   m_base
                                             where  id = MAIN.id
                                             )
                                )
               )
    order by MAIN.display_num
    ;

/******************************************************************************************************************
  請求明細集計
******************************************************************************************************************/
    insert into sum_sales_detail
     (
      id,
      sales_seko_dept_id,
      seko_id,
      seko_date,
      soke_name,
      kojin_name,
      moshu_name,
      entry_id,
      entry_name,
      entry_ts,
      entry_detail_id,
      service_id,
      service_name,
      item_id,
      item_name,
      quantity,
      item_price,
      item_tax,
      item_tax_pct,
      item_taxed_price,
      keigen_flg,
      created_at
      )

    -- 弔文、供花・供物、香典
    select
      nextval('sum_sales_detail_id_seq'),
      iv1.sales_seko_department_id,
      MAIN.id,
      MAIN.seko_date,
      MAIN.soke_name || '家',
      S1.name,
      S2.name,
      S3.id,
      S3.entry_name,
      S3.entry_ts,  
      S4.id,
      M2.id,
      M2.name,
      S4.item_id,
      S4.item_hinban || ' ' || S4.item_name,
      S4.quantity,
      S4.item_unit_price * S4.quantity - (S4.item_tax + S4.tax_adjust),
      S4.item_tax + S4.tax_adjust,
      S4.item_tax_pct,
      S4.item_unit_price,
      S4.keigen_flg,
      now()
    from
      tr_seko MAIN
        join tr_kojin S1
          on  S1.seko_id = MAIN.id
          and S1.kojin_num = 1
        join tr_moshu S2
          on S2.id = MAIN.id
        join tr_entry S3
          on  S3.seko_id = MAIN.id
          and to_char(S3.entry_ts, 'YYYYMM') = arg_yyyymm
          and S3.cancel_ts is null
        join tr_entry_detail S4
          on S4.entry_id = S3.id
        join m_item M1
          on M1.id = S4.item_id
        join mm_service M2
          on  M2.id = M1.service_id
          and M2.id in (10,20,30)

        join
        (select MAIN.id                 as sales_seko_department_id,
                MAIN.seko_department_id as seko_department_id
         from   sum_sales_seko_department MAIN
                  join sum_sales_company S1
                    on  S1.id = MAIN.sales_company_id
                    and S1.confirm_ts is null
                  join sum_sales_admin S2
                    on  S2.id = S1.sales_admin_id
                    and S2.sales_yymm = arg_yyyymm 
         ) iv1
           on iv1.seko_department_id = MAIN.seko_department_id
    where  MAIN.del_flg = 'false'

    UNION ALL
    -- 香典システム利用料
    select
      nextval('sum_sales_detail_id_seq'),
      iv1.sales_seko_department_id,
      MAIN.id,
      MAIN.seko_date,
      MAIN.soke_name || '家',
      S1.name,
      S2.name,
      S3.id,
      null,
      S3.entry_ts,  
      S4.id,
      M2.id,
      M2.name,
      M1.id,
      '香典システム利用料',
      1,
      S5.koden_commission - (S5.koden_commission_tax + S5.tax_adjust),
      S5.koden_commission_tax + S5.tax_adjust,
      S5.koden_commission_tax_pct,
      S5.koden_commission,
      'false',
      now()
    from
      tr_seko MAIN
        join tr_kojin S1
          on  S1.seko_id = MAIN.id
          and S1.kojin_num = 1
        join tr_moshu S2
          on S2.id = MAIN.id
        join tr_entry S3
          on  S3.seko_id = MAIN.id
          and to_char(S3.entry_ts, 'YYYYMM') = arg_yyyymm
          and S3.cancel_ts is null
        join tr_entry_detail S4
          on S4.entry_id = S3.id
        join tr_entry_detail_koden S5
          on S5.id = S4.id
        join m_item M1
          on M1.id = S4.item_id
        join mm_service M2
          on  M2.id = M1.service_id
        join
        (select MAIN.id                 as sales_seko_department_id,
                MAIN.seko_department_id as seko_department_id
         from   sum_sales_seko_department MAIN
                  join sum_sales_company S1
                    on  S1.id = MAIN.sales_company_id
                    and S1.confirm_ts is null
                  join sum_sales_admin S2
                    on  S2.id = S1.sales_admin_id
                    and S2.sales_yymm = arg_yyyymm 
         ) iv1
           on iv1.seko_department_id = MAIN.seko_department_id
    where  MAIN.del_flg = 'false'

    UNION ALL
    -- 返礼品（供花・供物）
    select
      nextval('sum_sales_detail_id_seq'),
      iv1.sales_seko_department_id,
      MAIN.id,
      MAIN.seko_date,
      MAIN.soke_name || '家',
      S1.name,
      S2.name,
      S3.id,
      S3.entry_name,
      S3.entry_ts,  
      S4.id,
      M2.id,
      M2.name,
      S5.henreihin_id,
      S5.henreihin_hinban || ' ' || S5.henreihin_name,
      1,
      S5.henreihin_price - S5.henreihin_tax,
      S5.henreihin_tax,
      S5.henreihin_tax_pct,
      S5.henreihin_price,
      S5.keigen_flg,
      now()
    from
      tr_seko MAIN
        join tr_kojin S1
          on  S1.seko_id = MAIN.id
          and S1.kojin_num = 1
        join tr_moshu S2
          on S2.id = MAIN.id
        join tr_entry S3
          on  S3.seko_id = MAIN.id
          and S3.cancel_ts is null
        join tr_entry_detail S4
          on S4.entry_id = S3.id
        join tr_henreihin_kumotsu S5
          on   S5.id = S4.id
          and to_char(S5.created_at, 'YYYYMM') = arg_yyyymm
        join m_item M1
          on M1.id = S5.henreihin_id
        join mm_service M2
          on M2.id = M1.service_id

        join
        (select MAIN.id                 as sales_seko_department_id,
                MAIN.seko_department_id as seko_department_id
         from   sum_sales_seko_department MAIN
                  join sum_sales_company S1
                    on  S1.id = MAIN.sales_company_id
                    and S1.confirm_ts is null
                  join sum_sales_admin S2
                    on  S2.id = S1.sales_admin_id
                    and S2.sales_yymm = arg_yyyymm 
         ) iv1
           on iv1.seko_department_id = MAIN.seko_department_id
    where  MAIN.del_flg = 'false'

    UNION ALL
    -- 返礼品（香典）
    select
      nextval('sum_sales_detail_id_seq'),
      iv1.sales_seko_department_id,
      MAIN.id,
      MAIN.seko_date,
      MAIN.soke_name || '家',
      S1.name,
      S2.name,
      S3.id,
      S3.entry_name,
      S3.entry_ts,  
      S4.id,
      M2.id,
      M2.name,
      S5.henreihin_id,
      S5.henreihin_hinban || ' ' || S5.henreihin_name,
      1,
      S5.henreihin_price - S5.henreihin_tax,
      S5.henreihin_tax,
      S5.henreihin_tax_pct,
      S5.henreihin_price,
      S5.keigen_flg,
      now()
    from
      tr_seko MAIN
        join tr_kojin S1
          on  S1.seko_id = MAIN.id
          and S1.kojin_num = 1
        join tr_moshu S2
          on S2.id = MAIN.id
        join tr_entry S3
          on  S3.seko_id = MAIN.id
          and S3.cancel_ts is null
        join tr_entry_detail S4
          on S4.entry_id = S3.id
        join tr_henreihin_koden S5
          on   S5.id = S4.id
          and to_char(S5.created_at, 'YYYYMM') = arg_yyyymm
        join m_item M1
          on M1.id = S5.henreihin_id
        join mm_service M2
          on M2.id = M1.service_id

        -- 上位テーブル（部門集計）と紐づけ、確定済葬儀社は集計から除外
        join
        (select MAIN.id                 as sales_seko_department_id,
                MAIN.seko_department_id as seko_department_id
         from   sum_sales_seko_department MAIN
                  join sum_sales_company S1
                    on  S1.id = MAIN.sales_company_id
                    and S1.confirm_ts is null
                  join sum_sales_admin S2
                    on  S2.id = S1.sales_admin_id
                    and S2.sales_yymm = arg_yyyymm 
         ) iv1
           on iv1.seko_department_id = MAIN.seko_department_id
    where  MAIN.del_flg = 'false'
    order by 2, 3, 12, 14
    ;

/******************************************************************************************************************
  明細を持たない部門集計レコードを削除
******************************************************************************************************************/
    delete from sum_sales_seko_department A
    where  not exists (select 'a'
                       from   sum_sales_detail B
                       where  B.sales_seko_dept_id = A.id
                       );

/******************************************************************************************************************
  例外処理
******************************************************************************************************************/
    EXCEPTION WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS
        v_err_1 = MESSAGE_TEXT,
        v_err_2 = PG_EXCEPTION_DETAIL,
        v_err_3 = PG_EXCEPTION_HINT;

      v_kekka := v_err_1 || v_err_2 ||v_err_3;

  END;

  RAISE INFO '結果：%', v_kekka;
--return v_kekka;

END;
$$ language 'plpgsql'
;


