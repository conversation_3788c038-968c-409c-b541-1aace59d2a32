@import "src/assets/scss/customer/setting";
.container .inner .contents {
  >div {
    max-width: 700px;
  }
  padding: 15px;
  @media screen and (max-width: 360px) {
    padding: 10px;
  }
  .service-label {
    font-size: 1rem;
    line-height: 1;
    padding-bottom: 10px;
    @media screen and (max-width: 560px) {
      padding: 10px;
    }
    @media screen and (max-width: 360px) {
      padding: 10px;
    }
  }
  .image {
    width: 15px;
    height: 15px;
    margin: 0 7px 0 -4px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 15px auto;
    &.chobun{
      background-image: url(../../../../assets/img/family/icon_01.png);
    }
    &.kumotsu {
      background-image: url(../../../../assets/img/family/icon_02.png);
    }
    &.koden {
      background-image: url(../../../../assets/img/family/icon_03.png);
    }
    &.msg {
      background-image: url(../../../../assets/img/family/icon_04.png);
    }
  }
  .service-check {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding-bottom: 10px;
    >div {
      margin-left: 30px;
      @media screen and (max-width: 560px) {
        margin-left: 20px;
        margin-right: 20px;
      }
      @media screen and (max-width: 360px) {
        margin-left: 10px;
        margin-right: 10px;
      }
      font-size: 1rem;
      line-height: 1;
      font-weight: bold;
      display: flex;
      padding-bottom: 10px;
      input {
        margin: auto 0;
      }
      label {
        display: flex;
        font-weight: normal;
      }
    }
  }
  .title_area, .data_area .ui.accordion {
    padding: 10px;
    line-height: 1.2;
    table {
      width: 100%;
      margin: auto;
      tr td {
        &.entry_name {
          width: 30%;
        }
        &.service {
          width: 20%;
          >div {
            @media screen and (max-width: 560px) {
              .name {
                display: none;
              }
            }
            display: flex;
            .image{
              margin: auto 5px;
              @media screen and (max-width: 560px) {
                margin: auto;
              }
            }
          }
        }
        &.detail {
          padding-left: 0;
          text-align: center;
          width: 60px;
          @media screen and (max-width: 560px) {
            width: 40px;
          }
        }
      }
    }
  }
  .title_area table tr td {
    padding-left: 10px;
  }
  .data_area {
    .ui.accordion {
      padding: 0 !important;      
    }
    padding: 0 10px;
    font-size: 0.9rem;
    border-bottom: solid 1px $border-grey;
    &:nth-last-child(2) {
      border-bottom-width: 2px;
    }
    table {
      margin: auto;
      tr td {
        line-height: 1.5;
        &.label {
          text-align: right;
          padding-right: 20px;
          width: 20%;
          min-width: 80px;
          vertical-align: top;
        }
        .honbun {
          white-space: pre-wrap;
        }
      }
    }
  }
  .title_area {
    font-size: 0.9rem;
    @media screen and (max-width: 560px) {
      font-size: 0.8rem;
    }
    border-bottom: solid 2px $border-grey;
  }
  .total_area {
    text-align: right;
    display: flex;
    justify-content: flex-end;
    .price {
      min-width: 70px;
    }
  }
  .ui.accordion {
    padding: 5px !important;
    .title {
      .chevron.up.icon {
        display: none;
      }
      &.active {
        .chevron.down.icon {
          display: none;
        }
        .chevron.up.icon {
          display: inline;
        }
      }
    }
    .content {
      padding-top: 0px !important;
      padding-bottom: 10px;
      .ui.radio.checkbox {
        margin-right: 10px;
      }
      .button {
        margin-top: 5px;
        width: 130px;
        border-radius: 5px;
        height: 23px;
        line-height: 18px;

      }
    }
  }
}

.inner >.button-area {
  flex-wrap: wrap;
  .button {
    width: 180px;
    border-radius: 5px;
    margin: 5px !important;
    @media screen and (max-width: 560px) {
      width: 160px;
    }
    @media screen and (max-width: 380px) {
      width: 130px;
    }
  }
}