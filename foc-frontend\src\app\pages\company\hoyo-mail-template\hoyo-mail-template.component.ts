import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from 'src/app/pages/common/confirm-dialog/confirm-dialog.component';
import { SessionService } from 'src/app/service/session.service';
import { HttpClientService } from 'src/app/service/http-client.service';
import { LoaderService } from 'src/app/service/loader.service';
import { Utils } from 'src/app/const/func-util';
import { CommonConstants } from 'src/app/const/const';
import { HoyoMailTemplateGetRequest, HoyoMailTemplateGetResponse, HoyoMailTemplatePostRequest } from 'src/app/interfaces/hoyo';
declare var $;

@Component({
  selector: 'app-com-hoyo-mail-template',
  templateUrl: './hoyo-mail-template.component.html',
  styleUrls: ['./hoyo-mail-template.component.scss']
})
export class HoyoMailTemplateComponent implements OnInit {

  Const = CommonConstants;

  hoyo_mail_template_mst_list = this.sessionSvc.get('hoyo_mail_template_mst_list');
  login_company = Utils.getLoginCompany();
  companyCombo = { values: [], clearable: false, showOnFocus: false, disableSearch: true };
  departCombo = { values: [], clearable: false, showOnFocus: false };
  company_list = Utils.getCompanyList();
  company_id = null;

  item_list = null;

  item_edit: HoyoMailTemplateGetResponse = new HoyoMailTemplateGetResponse();
  edit_type = 1;

  message = null;

  sentence_max_len = 50;

  page_per_count = 5;
  pagination = {
    pages: new Array(),
    current: 0
  };

  processing = false;

  constructor(
    private httpClientService: HttpClientService,
    private sessionSvc: SessionService,
    private loaderSvc: LoaderService,
    private dialog: MatDialog,
  ) {
    this.loaderSvc.call();
  }

  ngOnInit(): void {
    this.initControl();
    this.getData(this.company_id);
  }

  initControl() {
    if (this.company_list) {
      this.companyCombo.values = this.company_list.map(value => ({ name: value.base_name, value: value.id, data: value }));
      if (this.login_company) {
        if (this.login_company.base_type === CommonConstants.BASE_TYPE_COMPANY) {
          this.company_id = this.login_company.id;
        } else {
          this.company_id = this.company_list[0].id;
        }
      }
    }
  }

  getData(company_id) {
    const request = new HoyoMailTemplateGetRequest();
    request.company = company_id;
    if (!this.loaderSvc.isLoading) {
      this.loaderSvc.call();
    }
    this.httpClientService.getHoyoMailTemplateList(request).then((response) => {
      Utils.log(response);
      this.item_list = response;
      this.calcPagination();
      this.loaderSvc.dismiss();

    }).catch(error => {
      this.loaderSvc.dismiss();
    });
  }
  calcPagination() {
    this.pagination.pages = new Array();
    this.pagination.current = 0;
    if (!this.item_list || !this.item_list.length) {
      return;
    }
    const count = Math.ceil(this.item_list.length / this.page_per_count);
    for (let i = 1; i <= count; i++) {
      this.pagination.pages.push(i);
    }
    this.pagination.current = 1;
  }

  companyChange(event) {
    if (!event || !event.data) {
      return;
    }
    this.getData(event.data.id);
  }

  showData(event, item = null) {
    this.message = null;
    if (!item) {
      this.edit_type = 1;
      this.item_edit = new HoyoMailTemplateGetResponse();
      this.item_edit.company = this.company_id;
    } else {
      this.edit_type = 2;
      if (event.target.classList.contains('operation') || event.target.classList.contains('icon')) {
        return;
      }
      this.item_edit = JSON.parse(JSON.stringify(item));
    }
    $('#item-edit').modal({
      centered: false,
      closable: false,
      detachable: false,
      transition: 'fade'
    }).modal('show');
  }
  saveData() {
    this.sessionSvc.clear('message');
    if (this.processing) {
      return;
    }
    this.processing = true;
    if (!this.dataValidate()) {
      this.processing = false;
      return;
    }
    const postData = this.getPostData();
    this.loaderSvc.call();
    if (postData.id) {
      this.httpClientService.updateHoyoMailTemplate(postData.id, postData)
        .then(async (response) => {
          Utils.log(response);
          this.sessionSvc.save('message', { type: 'info', title: '法要メールテンプレートを保存しました。' });
          this.getData(this.company_id);
          this.loaderSvc.dismiss();
          this.processing = false;
        })
        .catch(error => {
          this.sessionSvc.save('message', { type: 'error', title: '法要メールテンプレートの保存が失敗しました。' });
          this.loaderSvc.dismiss();
          this.processing = false;
        });
    } else {
      this.httpClientService.addHoyoMailTemplate(postData)
        .then(async (response) => {
          Utils.log(response);
          this.sessionSvc.save('message', { type: 'info', title: '法要メールテンプレートを保存しました。' });
          this.getData(this.company_id);
          this.loaderSvc.dismiss();
          this.processing = false;
        })
        .catch(error => {
          this.sessionSvc.save('message', { type: 'error', title: '法要メールテンプレートの保存が失敗しました。' });
          this.loaderSvc.dismiss();
          this.processing = false;
        });
    }
    $('#item-edit').modal('hide');

  }
  dataValidate() {
    const msgs = new Array();
    this.message = null;
    if (!this.item_edit.display_num) {
      msgs.push('表示順');
    }
    this.item_edit.sentence = this.item_edit.sentence.trim();
    if (!this.item_edit.sentence) {
      msgs.push('文面');
    }

    if (msgs.length > 0) {
      this.message = {
        type: 'warning',
        title: '必須項目が入力されておりません。',
        msg: msgs.join(',')
      };
      return false;
    }

    // if (this.item_edit.sentence.length > this.sentence_max_len) {
    //   this.message = { type: 'warning', title: '文面は' + this.sentence_max_len + '文字以内で入力してください。' };
    //   return false;
    // }
    return true;
  }

  getPostData(): HoyoMailTemplatePostRequest {
    const postRequest = new HoyoMailTemplatePostRequest();
    postRequest.id = this.item_edit.id;
    postRequest.company = this.item_edit.company;
    postRequest.display_num = this.item_edit.display_num;
    postRequest.sentence = this.item_edit.sentence;
    return postRequest;
  }


  pageTo(pagination, page_num) {
    pagination.current = page_num;
  }

  closeMessage() {
    this.message = null;
  }

  deleteData(id) {
    this.sessionSvc.clear('message');
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        msg1: '法要メールテンプレートを削除します、よろしいですか？',
      },
      width: '100%',
      maxWidth: '450px',
      maxHeight: '90%'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.loaderSvc.call();
        this.httpClientService.deleteHoyoMailTemplate(id)
          .then((response) => {
            Utils.log(response);
            this.sessionSvc.save('message', { type: 'info', title: '法要メールテンプレートを削除しました。' });
            this.getData(this.company_id);
          })
          .catch(error => {
            this.sessionSvc.save('message', { type: 'error', title: '法要メールテンプレートの削除が失敗しました。' });
            this.loaderSvc.dismiss();
          });
      }
    });
  }

}
