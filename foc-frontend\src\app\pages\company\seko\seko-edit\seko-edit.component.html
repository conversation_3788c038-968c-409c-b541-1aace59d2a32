
<ng-container *ngIf="seko_info">
  <div class="container">
    <div class="inner with_footer">
      <div class="contents">
        <div class="menu_title">
          <i class="id badge icon big"></i>
          施行情報
        </div>
        <div class="ui attached tabular menu">
          <div class="active item" data-tab="base-info"><i class="address card outline icon"></i>基本情報</div>
          <div class="item" data-tab="accessory-info"><i class="linkify icon"></i>付帯情報</div>
          <div class="item" data-tab="video-info"><i class="video icon"></i>配信動画</div>
          <div class="item" data-tab="album-info"><i class="images icon"></i>アルバム</div>
          <div class="item" [class.disabled]="!shareImageUsingFlg" data-tab="share-info"><i class="images icon"></i>共有画像</div>
          <div class="item" [class.disabled]="!haveService(Const.SERVICE_ID_KUMOTSU)" data-tab="kumotsu-info"><i class="spa icon"></i>供花供物</div>
          <div class="item" [class.disabled]="!haveService(Const.SERVICE_ID_HENREIHIN)" data-tab="gift-info"><i class="gift icon"></i>返礼品</div>
          <div class="item" [class.disabled]="!seko_id" data-tab="homei-list"><i class="clipboard list icon"></i>芳名・香典帳</div>
          <div class="item" [class.disabled]="!seko_id" data-tab="hoyo-list"><i class="monument icon"></i>法要</div>
          <div class="item" [class.disabled]="!seko_id" data-tab="af-list"><i class="hand holding medical icon"></i>AF</div>
        </div>
        <div class="ui attached active tab segment" data-tab="base-info">
          <div class="disabled_overlay" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN"></div>
          <div class="input_area">
            <div class="line">
              <label class="required">葬家名</label>
              <div class="ui input">
                <input type="text" [class.error]="errField.get('soke_name')" autocomplete="off" id="soke_name" [(ngModel)]="seko_info.soke_name" (change)="sokeNamechange(); seko_info.soke_kana = sokenameKanaEm.value;">
              </div>
              <label class="required">葬家名カナ</label>
              <div class="ui input">
                <input type="text" [class.error]="errField.get('soke_kana')" autocomplete="off" #sokenameKanaEm id="soke_kana" [(ngModel)]="seko_info.soke_kana">
              </div>
            </div>
            <div class="line">
              <label class="required">故人名</label>
              <div class="ui input">
                <input type="text" [class.error]="errField.get('kojin_name')" autocomplete="off" id="kojin_name" [(ngModel)]="seko_info.kojin[0].name" (change)="seko_info.kojin[0].kana = kojinnameKanaEm.value;">
              </div>
              <label class="required">故人名カナ</label>
              <div class="ui input">
                <input type="text" [class.error]="errField.get('kojin_kana')" autocomplete="off" #kojinnameKanaEm  id="kojin_kana" [(ngModel)]="seko_info.kojin[0].kana">
              </div>
              <label class="required">喪主からみた続柄</label>
              <div class="ui input">
                <input type="text" [class.error]="errField.get('kojin_moshu_kojin_relationship')" autocomplete="off" id="moshu_kojin_relationship" [(ngModel)]="seko_info.kojin[0].moshu_kojin_relationship">
              </div>
            </div>
            <div class="line">
              <label class="required">生年月日</label>
              <com-calendar [class.error]="errField.get('kojin_birth_date')" [settings]="calendarOptionDate" [(value)]="seko_info.kojin[0].birth_date" (valueChange)="calcAge(1)"></com-calendar>
              <label class="required">逝去日</label>
              <com-calendar [class.error]="errField.get('kojin_death_date')" [settings]="calendarOptionDate" [(value)]="seko_info.kojin[0].death_date" (valueChange)="calcAge(1)"></com-calendar>
              <label class="required">年齢</label>
              <div class="ui input tiny">
                <input [class.error]="errField.get('kojin_age_kbn')" type="text" autocomplete="off" [(ngModel)]="seko_info.kojin[0].age_kbn">
              </div>
              <div class="ui input tiny divided required">
                <input [class.error]="errField.get('kojin_age')" type="number" autocomplete="off" maxlength="3" min="0" [(ngModel)]="seko_info.kojin[0].age">
              </div>
              <label class="plane">才</label>
              <button class="ui labeled icon button mini right floated" (click)="showKojin2(kojin2BirthDateEm, kojin2DeathDateEm)">
                <i class="user plus icon"></i>追加故人
              </button>
            </div>
            <div class="line">
              <label class="required">受注担当者</label>
              <com-dropdown [class.error]="errField.get('order_staff')" [settings]="staffCombo" [(selectedValue)]="seko_info.order_staff" [(selectedName)]="seko_info.order_staff_name"></com-dropdown>
              <label class="required">施行担当者</label>
              <com-dropdown [class.error]="errField.get('seko_staff')" [settings]="staffCombo" [(selectedValue)]="seko_info.seko_staff" [(selectedName)]="seko_info.seko_staff_name"></com-dropdown>
              <label class="required">AF担当者</label>
              <com-dropdown [class.error]="errField.get('af_staff')" [settings]="staffCombo" [(selectedValue)]="seko_info.af_staff" [(selectedName)]="seko_info.af_staff_name"></com-dropdown>
            </div>
            <div class="line">
              <label class="required">喪主名</label>
              <div class="ui input">
                <input [class.error]="errField.get('moshu_name')" type="text" autocomplete="off" id="moshu_name" [(ngModel)]="seko_info.moshu.name" (change)="seko_info.moshu.kana = moshunameKanaEm.value;">
              </div>
              <label class="required">喪主名カナ</label>
              <div class="ui input">
                <input [class.error]="errField.get('moshu_kana')" type="text" autocomplete="off" #moshunameKanaEm id="moshu_kana" [(ngModel)]="seko_info.moshu.kana">
              </div>
              <label class="required">故人からみた続柄</label>
              <div class="ui input">
                <input [class.error]="errField.get('kojin_moshu_relationship')" type="text" autocomplete="off" id="kojin_moshu_relationship" [(ngModel)]="seko_info.moshu.kojin_moshu_relationship">
              </div>
            </div>
            <div class="line">
              <label class="required">住所</label>
              <div class="ui left icon input mini">
                <i class="tenge icon"></i>
                <input [class.error]="errField.get('moshu_zip_code')" type="tel" placeholder="ハイフンなし" autocomplete="off" id="moshu_zip_code" maxlength="7" [(ngModel)]="seko_info.moshu.zip_code" (input)="moshuZipcodeChange()">
              </div>
              <div class="ui input mini divided required">
                <input [class.error]="errField.get('moshu_prefecture')" type="text" autocomplete="off" id="moshu_prefecture" [(ngModel)]="seko_info.moshu.prefecture">
              </div>
              <div class="ui input divided required">
                <input [class.error]="errField.get('moshu_address_1')" type="text" autocomplete="off" id="moshu_address_1" [(ngModel)]="seko_info.moshu.address_1">
              </div>
              <div class="ui input divided">
                <input [class.error]="errField.get('moshu_address_2')" type="text" autocomplete="off" id="moshu_address_2" [(ngModel)]="seko_info.moshu.address_2">
              </div>
              <div class="ui input divided">
                <input [class.error]="errField.get('moshu_address_3')" type="text" autocomplete="off" id="moshu_address_3" [(ngModel)]="seko_info.moshu.address_3">
              </div>
            </div>
            <div class="line">
              <label>電話番号</label>
              <div class="ui left icon input">
                <i class="phone alternate icon"></i>
                <input [class.error]="errField.get('moshu_tel')" type="tel" autocomplete="off" id="moshu_tel" maxlength="15" [(ngModel)]="seko_info.moshu.tel">
              </div>
              <label>携帯番号</label>
              <div class="ui left icon input">
                <i class="mobile alternate icon"></i>
                <input [class.error]="errField.get('moshu_mobile_num')" type="tel" autocomplete="off" id="moshu_mobile_num" maxlength="15" [(ngModel)]="seko_info.moshu.mobile_num">
              </div>
              <label>メールアドレス</label>
              <div class="ui left icon input large">
                <i class="envelope icon"></i>
                <input [class.error]="errField.get('moshu_mail_address')" type="email" autocomplete="off" id="moshu_mail_address" [(ngModel)]="seko_info.moshu.mail_address">
              </div>
            </div>
            <div class="line">
              <label class="required">喪主出力位置</label>
              <com-dropdown [settings]="moshuDisplayPlaceCombo" [(selectedValue)]="seko_info.moshu_display_place"></com-dropdown>
            </div>
            <div class="line">
              <label>ラベル1</label>
              <div class="ui input">
                <input [class.error]="errField.get('free1_label')" type="text" autocomplete="off" id="free1_label" [(ngModel)]="seko_info.free1_label">
              </div>
              <label>名称1</label>
              <div class="ui input large">
                <input [class.error]="errField.get('free1_data')" type="text" autocomplete="off" id="free1_data" [(ngModel)]="seko_info.free1_data">
              </div>
            </div>
            <div class="line">
              <label>ラベル2</label>
              <div class="ui input">
                <input [class.error]="errField.get('free2_label')" type="text" autocomplete="off" id="free2_label" [(ngModel)]="seko_info.free2_label">
              </div>
              <label>名称2</label>
              <div class="ui input large">
                <input [class.error]="errField.get('free2_data')" type="text" autocomplete="off" id="free2_data" [(ngModel)]="seko_info.free2_data">
              </div>
            </div>
            <div class="line">
              <label>ラベル3</label>
              <div class="ui input">
                <input [class.error]="errField.get('free3_label')" type="text" autocomplete="off" id="free3_label" [(ngModel)]="seko_info.free3_label">
              </div>
              <label>名称3</label>
              <div class="ui input large">
                <input [class.error]="errField.get('free3_data')" type="text" autocomplete="off" id="free3_data" [(ngModel)]="seko_info.free3_data">
              </div>
            </div>
            <div class="line">
              <label>ラベル4</label>
              <div class="ui input">
                <input [class.error]="errField.get('free4_label')" type="text" autocomplete="off" id="free4_label" [(ngModel)]="seko_info.free4_label">
              </div>
              <label>名称4</label>
              <div class="ui input large">
                <input [class.error]="errField.get('free4_data')" type="text" autocomplete="off" id="free4_data" [(ngModel)]="seko_info.free4_data">
              </div>
            </div>
            <div class="ui segment seko-contact">
              <div class="title">
                連絡先
                <button class="ui labeled icon button mini right floated" (click)="setMoshuInfoToSekoContact()">
                  <i class="copy icon"></i>喪主と同じ
                </button>
              </div>
              <div class="input_area">
                <div class="line">
                  <label  class="required">氏名</label>
                  <div class="ui input">
                    <input [class.error]="errField.get('seko_contact_name')" type="text" autocomplete="off" id="seko_contact_name" [(ngModel)]="seko_info.seko_contact.name">
                  </div>
                  <label  class="required">故人からみた続柄</label>
                  <div class="ui input">
                    <input [class.error]="errField.get('seko_contact_kojin_relationship')" type="text" autocomplete="off" id="seko_contact_kojin_relationship" [(ngModel)]="seko_info.seko_contact.kojin_relationship">
                  </div>
                </div>
                <div class="line">
                  <label  class="required">住所</label>
                  <div class="ui left icon input mini">
                    <i class="tenge icon"></i>
                    <input [class.error]="errField.get('seko_contact_zip_code')" type="tel" placeholder="ハイフンなし" autocomplete="off" id="seko_contact_zip_code" maxlength="7" [(ngModel)]="seko_info.seko_contact.zip_code" (input)="sekoContactZipcodeChange()">
                  </div>
                  <div class="ui input required　mini divided">
                    <input [class.error]="errField.get('seko_contact_prefecture')" type="text" autocomplete="off" id="seko_contact_prefecture" [(ngModel)]="seko_info.seko_contact.prefecture">
                  </div>
                  <div class="ui input　required divided">
                    <input [class.error]="errField.get('seko_contact_address_1')" type="text" autocomplete="off" id="seko_contact_address_1" [(ngModel)]="seko_info.seko_contact.address_1">
                  </div>
                  <div class="ui input divided">
                    <input [class.error]="errField.get('seko_contact_address_2')" type="text" autocomplete="off" id="seko_contact_address_2" [(ngModel)]="seko_info.seko_contact.address_2">
                  </div>
                  <div class="ui input divided">
                    <input [class.error]="errField.get('seko_contact_address_3')" type="text" autocomplete="off" id="seko_contact_address_3" [(ngModel)]="seko_info.seko_contact.address_3">
                  </div>
                </div>
                <div class="line">
                  <label>電話番号</label>
                  <div class="ui left icon input">
                    <i class="phone alternate icon"></i>
                    <input [class.error]="errField.get('seko_contact_tel')" type="tel" autocomplete="off" id="seko_contact_tel" maxlength="15" [(ngModel)]="seko_info.seko_contact.tel">
                  </div>
                  <label>携帯番号</label>
                  <div class="ui left icon input">
                    <i class="mobile alternate icon"></i>
                    <input [class.error]="errField.get('seko_contact_mobile_num')" type="tel" autocomplete="off" id="seko_contact_mobile_num" maxlength="15" [(ngModel)]="seko_info.seko_contact.mobile_num">
                  </div>
                  <label class="required">メールアドレス</label>
                  <div class="ui left icon input large">
                    <i class="envelope icon"></i>
                    <input [class.error]="errField.get('seko_contact_mail_address')" type="email" autocomplete="off" id="seko_contact_mail_address" [(ngModel)]="seko_info.seko_contact.mail_address">
                  </div>
                </div>
              </div>
            </div>
            <div class="line">
              <label>備考</label>
              <div class="ui input full">
                <input [class.error]="errField.get('note')" type="text" autocomplete="off" id="note" [(ngModel)]="seko_info.note">
              </div>
            </div>
            <div class="line">
              <div class="ui toggle checkbox">
                <input type="checkbox" [(ngModel)]="seko_info.moshu.soke_site_del_flg">
                <label>葬家専用ページ削除</label>
              </div>
              <div class="ui toggle checkbox">
                <input type="checkbox" [(ngModel)]="seko_info.moshu.mail_not_flg">
                <label>案内メール不要</label>
              </div>
            </div>
            <div class="line">
              <label>FDN施行番号</label>
              <label class="data">{{seko_info.fdn_code?seko_info.fdn_code:'未連携'}}</label>
            </div>
            <!--
            <div class="line">
              <label>遺影写真</label>
              <div #portraitEm class="ui card image" (dragover)="onDragOverPortrait($event)" (drop)="onDropPortrait($event)">
                <div class="ui image center aligned" (click)="selectFile(1)" title="遺影写真追加">
                  <ng-container *ngIf="!seko_info.kojin[0].iei_file && !seko_info.kojin[0].iei_file_name">
                  <i class="id badge outline icon huge"></i>
                  <div class="noimage">No image</div>
                  <div class="desc">この枠をクリックしてファイルを選択するか、ファイルをこの枠にドラッグ・アンド・ドロップしてください。</div>
                  </ng-container>
                  <img src="{{seko_info.kojin[0].iei_file?seko_info.kojin[0].iei_file:seko_info.kojin[0].iei_file_name}}" *ngIf="seko_info.kojin[0].iei_file || seko_info.kojin[0].iei_file_name">
                </div>
                <div class="clear" *ngIf="seko_info.kojin[0].iei_file || seko_info.kojin[0].iei_file_name"><i class="delete icon" (click)="clearPortrait()" title="写真クリア"></i></div>
              </div>
            </div>
            -->
          </div>
        </div>
        <div class="ui attached tab segment" data-tab="accessory-info">
          <div class="disabled_overlay" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN"></div>
          <div class="sub_title">
            <i class="calendar alternate outline icon large"></i>
            日程情報
          </div>
          <div class="ui segment schedule">
            <table class="ui very basic celled unstackable table">
              <thead>
                <tr class="center aligned">
                  <th class="schedule_name">日程項目</th>
                  <th class="display_name">表示名</th>
                  <th class="place">式場名</th>
                  <th class="phone">電話番号</th>
                  <th class="zip_code">郵便番号</th>
                  <th class="address">住所</th>
                  <th class="date">日付</th>
                  <th class="start_time">開始</th>
                  <th class="end_time">終了</th>
                  <th class="function"></th>
                </tr>
              </thead>
              <tbody *ngIf="seko_info?.schedules?.length">
                <ng-container *ngFor="let temp of seko_info.schedules; index as index">
                <ng-container *ngFor="let sekoSchedule of seko_info.schedules">
                <tr *ngIf="sekoSchedule.display_num===index+1" id="schedule{{sekoSchedule.display_num}}" class="transition-schedule{{sekoSchedule.display_num}}">
                  <td class="center aligned" [class.required_right]="sekoSchedule?.schedule?.required_flg">{{sekoSchedule?.schedule?.name}}</td>
                  <td [class.required_left]="sekoSchedule?.schedule?.required_flg">
                    <div class="ui input fluid">
                      <input [class.error]="errField.get('schedule'+index+'schedule_name')" type="text" autocomplete="off" class="center aligned" [(ngModel)]="sekoSchedule.schedule_name">
                    </div>
                  </td>
                  <td [class.disabled]="!sekoSchedule.schedule_name">
                    <ng-container *ngIf="sekoSchedule?.schedule?.id === 2">
                      <com-dropdown [class.error]="errField.get('schedule'+index+'hall_name')" #hallComboEm2 [settings]="hallCombo" [(selectedValue)]="sekoSchedule.hall.id" [(selectedName)]="sekoSchedule.hall_name" (selectedItemChange)="hallChange($event, sekoSchedule)"></com-dropdown>
                    </ng-container>
                    <ng-container *ngIf="sekoSchedule?.schedule?.id !== 2">
                      <com-dropdown [class.error]="errField.get('schedule'+index+'hall_name')" [settings]="hallCombo" [(selectedValue)]="sekoSchedule.hall.id" [(selectedName)]="sekoSchedule.hall_name" (selectedItemChange)="hallChange($event, sekoSchedule)"></com-dropdown>
                    </ng-container>
                  </td>
                  <td [class.disabled]="!sekoSchedule.schedule_name">
                    <div class="ui input fluid">
                      <input [class.error]="errField.get('schedule'+index+'hall_tel')" type="tel" autocomplete="off" maxlength="13" [(ngModel)]="sekoSchedule.hall_tel" (change)="scheduleItemChange(sekoSchedule, 'hall_tel')">
                    </div>
                  </td>
                  <td [class.disabled]="!sekoSchedule.schedule_name">
                    <div class="ui input fluid">
                      <input [class.error]="errField.get('schedule'+index+'hall_zip_code')" type="tel"  placeholder="ハイフンなし" autocomplete="off" maxlength="7" [(ngModel)]="sekoSchedule.hall_zip_code" (input)="scheduleZipcodeChange(sekoSchedule)" (change)="scheduleItemChange(sekoSchedule, 'hall_zip_code')">
                    </div>
                  </td>
                  <td [class.disabled]="!sekoSchedule.schedule_name">
                    <div class="ui input fluid">
                      <input [class.error]="errField.get('schedule'+index+'hall_address')" type="text" autocomplete="off" [(ngModel)]="sekoSchedule.hall_address" (change)="scheduleItemChange(sekoSchedule, 'hall_address')">
                    </div>
                  </td>
                  <td [class.disabled]="!sekoSchedule.schedule_name">
                    <ng-container *ngIf="sekoSchedule?.schedule?.id === 2">
                    <com-calendar [class.error]="errField.get('schedule'+index+'schedule_date')" #scheduleDateEm [settings]="calendarOptionDate" id="schedule1" [showIcon]="false" [(value)]="sekoSchedule.schedule_date" (blur)="scheduleDateChenge($event, sekoSchedule)"></com-calendar>
                    </ng-container>
                    <ng-container *ngIf="sekoSchedule?.schedule?.id !== 2">
                    <com-calendar [class.error]="errField.get('schedule'+index+'schedule_date')" [settings]="calendarOptionDate" id="schedule1" [showIcon]="false" [(value)]="sekoSchedule.schedule_date" (blur)="scheduleDateChenge($event, sekoSchedule)"></com-calendar>
                    </ng-container>
                  </td>
                  <td [class.disabled]="!sekoSchedule.schedule_name">
                    <com-time-input [class.error]="errField.get('schedule'+index+'begin_time')" [(value)]="sekoSchedule.begin_time"></com-time-input>
                  </td>
                  <td [class.disabled]="!sekoSchedule.schedule_name">
                    <com-time-input [class.error]="errField.get('schedule'+index+'end_time')" [(value)]="sekoSchedule.end_time"></com-time-input>
                  </td>
                  <td class="center aligned button">
                    <i class="large icon" *ngIf="sekoSchedule.display_num===1"></i>
                    <i *ngIf="sekoSchedule.display_num!=1" class="large angle double up icon" title="一つ上に移動" (click)="schedulePositionChange(sekoSchedule.display_num, false)"></i>
                    <i *ngIf="sekoSchedule.display_num<seko_info.schedules.length" class="large angle double down icon" title="一つ下に移動" (click)="schedulePositionChange(sekoSchedule.display_num)"></i>
                    <i class="large icon" *ngIf="sekoSchedule.display_num===seko_info.schedules.length"></i>
                  </td>
                </tr>
                </ng-container>
                </ng-container>
              </tbody>
            </table>
            <div class="input_area">
              <div class="line">
                <label class="required">施行拠点</label>
                <com-dropdown [class.error]="errField.get('seko_department')" #sekoBaseComboEm [settings]="sekoBaseCombo" [(selectedValue)]="seko_info.seko_department.id"  (selectedItemChange)="sekoDepartChange($event)"></com-dropdown>
              </div>
            </div>
          </div>
          <div class="sub_title">
            <i class="map outline icon large"></i>
            訃報情報
          </div>
          <div class="ui segment">
            <div class="input_area">
              <div class="line">
                <label class="required">施行形式</label>
                <com-dropdown [class.error]="errField.get('seko_style')" [settings]="sekoStyleCombo" [(selectedValue)]="seko_info.seko_style.id" (selectedItemChange)="sekoStyleChange($event)"></com-dropdown>
                <label class="required">表示名</label>
                <div class="ui input">
                  <input [class.error]="errField.get('seko_style_name')" type="text" autocomplete="off" id="seko_style_name" [(ngModel)]="seko_info.seko_style_name">
                </div>
                <label class="required">訃報ページ掲載終了日</label>
                <com-calendar [class.error]="errField.get('fuho_site_end_date')" #siteEndDateEm [settings]="calendarOptionDate" [(value)]="seko_info.fuho_site_end_date"></com-calendar>
              </div>
            <div class="line">
              <div class="ui toggle checkbox">
                <input type="checkbox" [(ngModel)]="seko_info.seireki_display_flg">
                <label>逝去日西暦表示</label>
              </div>
            </div>
              <div class="line row5">
                <label class="required">訃報文</label>
                <div class="ui icon input textarea">
                    <i class="search icon link" (click)="showFuhoSample()"></i>
                    <textarea [class.error]="errField.get('fuho_sentence')" id="fuho_sentence" rows="4" cols="35" [(ngModel)]="seko_info.fuho_sentence"></textarea>
                </div>
              </div>
              <div class="line">
                <label class="required">お問い合わせ先</label>
                <com-dropdown [class.error]="errField.get('fuho_contact_name')"  #contactComboEm [settings]="sekoBaseCombo" [(selectedName)]="seko_info.fuho_contact_name" (selectedItemChange)="contactChange($event)"></com-dropdown>
                <label class="required">電話番号</label>
                <div class="ui left icon input">
                  <i class="phone alternate icon"></i>
                  <input [class.error]="errField.get('fuho_contact_tel')" type="tel" autocomplete="off" id="fuho_contact_tel" maxlength="13" [(ngModel)]="seko_info.fuho_contact_tel">
                </div>
              </div>
            </div>
          </div>
          <div class="sub_title">
            <i class="gifts icon large"></i>
            利用サービス
          </div>
          <div class="ui segment service">
            <ng-container *ngFor="let service of service_list">
              <div class="ui checkbox service">
                <input type="checkbox"[(ngModel)]="service.selected">
                <label>{{service.name}}</label>
              </div>
              <div class="ui checkbox service henreihin_select" [class.show]="service.id === Const.SERVICE_ID_KODEN && service.selected">
                <input type="checkbox" [(ngModel)]="service.henreihin_select_flg">
                <label>顧客が返礼品を選択</label>
              </div>
              <ng-container *ngIf="service.id === Const.SERVICE_ID_HENREIHIN && service.selected && haveService(Const.SERVICE_ID_KODEN)">
                <div class="rate">(香典比率 1/</div>
                <com-dropdown class="tiny" [settings]="rateCombo" [(selectedValue)]="seko_info.henreihin_rate"></com-dropdown>
                <div class="rate">)</div>
              </ng-container>
            </ng-container>
          </div>
          <div class="sub_title">
            <i class="file invoice dollar icon large"></i>
            請求書
          </div>
          <div class="ui segment invoice">
            <div class="input_area">
              <div class="line">
                <label class="wide">請求書ファイル</label>
                <div class="ui right left action input fluid full">
                  <button class="ui icon button" (click)="selectInvoiceFile()" title="ファイル選択">
                    <i class="upload icon"></i>
                  </button>
                  <input type="text" readonly class="ui readonly" [value]="seko_info?.invoice_file?.name">
                  <a [href]="seko_info.invoice_file_name" target="_blank" *ngIf="!seko_info.invoice_file && seko_info.invoice_file_name">請求書PDFファイル</a>
                  <button class="ui icon button" [class.disabled]="!seko_info.invoice_file_name" (click)="sendNoticeMail('請求書')" title="通知メール送信">
                    <i class="mail icon"></i>
                  </button>
                </div>
                <div class="clear" *ngIf="seko_info.invoice_file || seko_info.invoice_file_name"><i class="delete icon" (click)="clearInvoice(0)" title="ファイルクリア"></i></div>
              </div>
              <div class="line">
                <label class="wide">その他添付ファイル</label>
                <div class="ui input">
                  <input [class.error]="errField.get('attached_file1_name')" type="text" placeholder="タイトル" autocomplete="off" id="attached_file1_name" [(ngModel)]="seko_info.attached_file1_name">
                </div>
                <div class="ui right left action input fluid full">
                  <button class="ui icon button" (click)="selectInvoiceFile(1)">
                    <i class="upload icon"></i>
                  </button>
                  <input type="text" readonly class="ui readonly" [value]="seko_info?.attached_file1_buf?.name">
                  <a [href]="seko_info.attached_file1" target="_blank" *ngIf="!seko_info.attached_file1_buf && seko_info.attached_file1">添付PDFファイル</a>
                  <button class="ui icon button" [class.disabled]="!seko_info.attached_file1" (click)="sendNoticeMail(seko_info.attached_file1_name)" title="通知メール送信">
                    <i class="mail icon"></i>
                  </button>
                </div>
                <div class="clear" *ngIf="seko_info.attached_file1_buf || seko_info.attached_file1"><i class="delete icon" (click)="clearInvoice(1)" title="ファイルクリア"></i></div>
              </div>
              <div class="line">
                <label class="wide">その他添付ファイル</label>
                <div class="ui input">
                  <input [class.error]="errField.get('attached_file2_name')" type="text" placeholder="タイトル" autocomplete="off" id="attached_file2_name" [(ngModel)]="seko_info.attached_file2_name">
                </div>
                <div class="ui right left action input fluid full">
                  <button class="ui icon button" (click)="selectInvoiceFile(2)">
                    <i class="upload icon"></i>
                  </button>
                  <input type="text" readonly class="ui readonly" [value]="seko_info?.attached_file2_buf?.name">
                  <a [href]="seko_info.attached_file2" target="_blank" *ngIf="!seko_info.attached_file2_buf && seko_info.attached_file2">添付PDFファイル</a>
                  <button class="ui icon button" [class.disabled]="!seko_info.attached_file2" (click)="sendNoticeMail(seko_info.attached_file2_name)" title="通知メール送信">
                    <i class="mail icon"></i>
                  </button>
                </div>
                <div class="clear" *ngIf="seko_info.attached_file2_buf || seko_info.attached_file2"><i class="delete icon" (click)="clearInvoice(2)" title="ファイルクリア"></i></div>
              </div>
              <div class="line">
                <label class="wide">その他添付ファイル</label>
                <div class="ui input">
                  <input [class.error]="errField.get('attached_file3_name')" type="text" placeholder="タイトル" autocomplete="off" id="attached_file3_name" [(ngModel)]="seko_info.attached_file3_name">
                </div>
                <div class="ui right left action input fluid full">
                  <button class="ui icon button" (click)="selectInvoiceFile(3)">
                    <i class="upload icon"></i>
                  </button>
                  <input type="text" readonly class="ui readonly" [value]="seko_info?.attached_file3_buf?.name">
                  <a [href]="seko_info.attached_file3" target="_blank" *ngIf="!seko_info.attached_file3_buf && seko_info.attached_file3">添付PDFファイル</a>
                  <button class="ui icon button" [class.disabled]="!seko_info.attached_file3" (click)="sendNoticeMail(seko_info.attached_file3_name)" title="通知メール送信">
                    <i class="mail icon"></i>
                  </button>
                </div>
                <div class="clear" *ngIf="seko_info.attached_file3_buf || seko_info.attached_file3"><i class="delete icon" (click)="clearInvoice(3)" title="ファイルクリア"></i></div>
              </div>
            </div>
          </div>
          <div class="sub_title">
            <i class="ribbon icon large"></i>
            返礼品掛け紙
          </div>
          <div class="ui segment">
            <div class="input_area">
              <div class="line">
                <label>表書き</label>
                <div class="ui input">
                  <input [class.error]="errField.get('omotegaki')" type="text" autocomplete="off" id="omotegaki" [(ngModel)]="seko_info.henrei_kakegami.omotegaki">
                </div>
                <label>水引</label>
                <div class="ui input">
                  <input [class.error]="errField.get('mizuhiki')" type="text" autocomplete="off" id="mizuhiki" [(ngModel)]="seko_info.henrei_kakegami.mizuhiki">
                </div>
                <label>包装</label>
                <div class="ui input">
                  <input [class.error]="errField.get('package_type_name')" type="text" autocomplete="off" id="package_type_name" [(ngModel)]="seko_info.henrei_kakegami.package_type_name">
                </div>
                <label>送り主名</label>
                <div class="ui input">
                  <input [class.error]="errField.get('okurinushi_name')" type="text" autocomplete="off" id="okurinushi_name" [(ngModel)]="seko_info.henrei_kakegami.okurinushi_name">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="ui attached tab segment video" data-tab="video-info">
          <div class="disabled_overlay" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN"></div>
          <table class="ui celled unstackable structured table">
            <thead>
              <tr class="center aligned">
                <th class="title">タイトル</th>
                <th class="type">種類</th>
                <th class="youtube_code">Youtubeコード</th>
                <th class="live_begin_ts">ライブ開始</th>
                <th class="live_end_ts">ライブ終了</th>
                <th class="delivery_end_ts">配信終了</th>
                <th class="function"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let video of seko_info.videos; index as i" title="編集" (click)="showVideo($event, i, liveBeginTsEm, liveEndTsEm, DeliveryEndTsEm)">
                <td>{{video?.title}}</td>
                <td class="center aligned">{{video.live_begin_ts?'ライブ配信':'広告'}}</td>
                <td>{{video?.youtube_code}}</td>
                <td class="center aligned">{{ video?.live_begin_ts | date : 'yyyy/MM/dd H:mm'}}</td>
                <td class="center aligned">{{ video?.live_end_ts | date : 'yyyy/MM/dd H:mm'}}</td>
                <td class="center aligned">{{ video?.delivery_end_ts | date : 'yyyy/MM/dd H:mm'}}</td>
                <td class="center aligned button"><i class="large trash alternate icon" title="削除" (click)="deleteVideo(i)"></i></td>
              </tr>
            </tbody>
            <tbody>
              <tr title="新規追加" (click)="showVideo($event, -1, liveBeginTsEm, liveEndTsEm)">
                <td colspan="7" class="center aligned"><i class="large add icon"></i></td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="ui attached tab segment album" data-tab="album-info" (dragover)="onDragOverAlbum($event)" (drop)="onDropAlbum($event)">
          <div class="disabled_overlay" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN"></div>
            <div class="message">アルバムには最大{{albumCountMax}}枚まで（1ファイル上限{{albumFileSizeMax}}MB）登録可能です。</div>
            <div class="desc" *ngIf="!albumItems?.length"><span>ファイルをこの枠にドラッグ・アンド・ドロップしてください。</span></div>
            <div #campusEm cdkDropListGroup>
              <div
                *ngFor="let itemsRow of getAlbumItemsTable(campusEm); index as row"
                cdkDropList
                cdkDropListOrientation="horizontal"
                [cdkDropListData]="itemsRow"
                (cdkDropListDropped)="reorderDroppedItem($event)"
                style="display:flex;"
              >
                <div *ngFor="let item of itemsRow; index as col" cdkDrag class="drag-box">
                  <div class="drag-placeholder" *cdkDragPlaceholder></div>
                  <div class="album-item-box" (click)="showAlbumItem(item)">
                    <div class="clear" (click)="deleteAlbumItem(row, col)">
                      <i class="delete icon" title="削除"></i>
                    </div>
                    <img [src]="item.image_file?item.image_file:item.file_name">
                  </div>
                </div>
              </div>
            </div>
            <div class="item-upload" (click)="selectFile(2)" title="写真追加">
              <i class="plus icon huge center aligned"></i>
            </div>
        </div>
        <div class="ui attached tab segment kumotsu" data-tab="kumotsu-info">
          <div class="disabled_overlay" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN"></div>
          <div class="top_button_area">
            <div class="title">葬儀前：</div>
            <button class="ui labeled icon button mini" (click)="selectItems(Const.SERVICE_ID_KUMOTSU, true)">
              <i class="square outline check icon"></i>全選択
            </button>
            <button class="ui labeled icon button mini" (click)="selectItems(Const.SERVICE_ID_KUMOTSU, false)">
              <i class="square outline icon"></i>全解除
            </button>
          </div>
          <div class="canvas" *ngIf="haveService(Const.SERVICE_ID_KUMOTSU)">
            <ng-container *ngFor="let item of item_list">
              <ng-container *ngIf="!item.disabled">
                <div class="item-box" *ngIf="item.service === Const.SERVICE_ID_KUMOTSU" (click)="selectItem1(item)">
                  <div class="image-box" [class.selected]="item.selected1 || item.selected2">
                    <img [src]="item.image_file" *ngIf="item.image_file" (error)="imageLoadError(item)">
                    <ng-container *ngIf="!item.image_file">
                      <div class="no-image">
                        <i class="image icon huge"></i>
                        <div class="noimage">No image</div>
                      </div>
                    </ng-container>

                  </div>
                  <div class="checkbox kumotsu">
                    <div class="title">葬儀前</div>
                    <div class="ui checkbox">
                      <input type="checkbox" [(ngModel)]="item.selected1">
                      <label></label>
                    </div>
                    <div class="title">葬儀後</div>
                    <div class="ui checkbox">
                      <input type="checkbox" [(ngModel)]="item.selected2">
                      <label></label>
                    </div>
                  </div>
                  <div class="name">{{item.name}}</div>
                  <div class="hinban">{{item.hinban}}</div>
                  <div class="price">{{item.item_price | number}}円(税込)</div>
                </div>
              </ng-container>
            </ng-container>
          </div>
        </div>
        <div class="ui attached tab segment henreihin" data-tab="gift-info">
          <div class="disabled_overlay" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN"></div>
          <div class="top_button_area">
            <button class="ui labeled icon button mini" (click)="selectItems(Const.SERVICE_ID_HENREIHIN, true)">
              <i class="square outline check icon"></i>全選択
            </button>
            <button class="ui labeled icon button mini" (click)="selectItems(Const.SERVICE_ID_HENREIHIN, false)">
              <i class="square outline icon"></i>全解除
            </button>
          </div>
          <div class="canvas" *ngIf="haveService(Const.SERVICE_ID_HENREIHIN)">
            <ng-container *ngFor="let item of item_list">
              <ng-container *ngIf="!item.disabled">
                <div class="item-box" *ngIf="item.service === Const.SERVICE_ID_HENREIHIN" (click)="selectItem(item)">
                  <div class="image-box" [class.selected]="item.selected1">
                    <img [src]="item.image_file" *ngIf="item.image_file" (error)="imageLoadError(item)">
                    <ng-container *ngIf="!item.image_file">
                      <div class="no-image">
                        <i class="image icon huge"></i>
                        <div class="noimage">No image</div>
                      </div>
                    </ng-container>

                  </div>
                  <div class="checkbox">
                    <div class="ui checkbox">
                      <input type="checkbox" [(ngModel)]="item.selected1">
                      <label></label>
                    </div>
                  </div>
                  <div class="name">{{item.name}}</div>
                  <div class="hinban">{{item.hinban}}</div>
                  <div class="price">{{item.item_price | number}}円(税込)</div>
                </div>
              </ng-container>
            </ng-container>
          </div>
        </div>
        <div class="ui attached tab segment share" data-tab="share-info">
          <div class="disabled_overlay" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN"></div>
          <div class="sub_title">
            葬儀社アップロード画像
          </div>
          <div class="message">※共有画像は最大{{shareCountMax}}枚まで（1ファイル上限{{shareFileSizeMax}}MB）登録可能です。</div>
          <div class="ui segment canvas" (dragover)="onDragOverShare($event)" (drop)="onDropShare($event)">
            <ng-container *ngFor="let item of shareItems; index as i">
              <div class="image-box" (click)="showShareItem(item)">
                <div class="clear" (click)="deleteShareItem(i)">
                  <i class="delete icon" title="削除"></i>
                </div>
                <img [src]="item.image_file?item.image_file:item.file_name">
              </div>
            </ng-container>
            <div class="item-upload" (click)="selectFile(3)" title="写真追加">
              <i class="plus icon huge center aligned"></i>
            </div>
          </div>
          <div class="sub_title">
            葬家様アップロード画像
          </div>
          <div class="ui segment canvas">
            <ng-container *ngIf="!sokeShareItems?.length">
              <span>共有された画像がございません。</span>
            </ng-container>
            <ng-container *ngFor="let item of sokeShareItems">
              <div class="image-box" (click)="showSokeShareItem(item)">
                <img [src]="item.image_file?item.image_file:item.file_name">
              </div>
            </ng-container>
          </div>
        </div>
        <div class="ui attached tab segment homeicho" data-tab="homei-list">
          <div class="top_button_area">
            <button class="ui labeled icon button mini" (click)="downloadHomeiPdf(Const.SERVICE_ID_KUMOTSU)">
              <i class="file pdf icon"></i>芳名帳(供花供物)出力
            </button>
            <button class="ui labeled icon button mini" (click)="downloadHomeiPdf(Const.SERVICE_ID_CHOBUN)">
              <i class="file pdf icon"></i>芳名帳(弔文)出力
            </button>
            <button class="ui labeled icon button mini" (click)="downloadHomeiPdf(Const.SERVICE_ID_MESSAGE)">
              <i class="file pdf icon"></i>芳名帳(追悼MSG)出力
            </button>
            <button class="ui labeled icon button mini" (click)="downloadHomeiPdf(Const.SERVICE_ID_KODEN)">
              <i class="file pdf icon"></i>香典帳出力
            </button>
            <button class="ui labeled icon button mini" (click)="downloadMsgPdf()">
              <i class="file pdf icon"></i>追悼MSG出力
            </button>
          </div>
          <div class="search_area">
            <div class="line">
              <div class="ui toggle checkbox homei">
                <input type="checkbox" [(ngModel)]="homei_filter.kumotsu" (change)="filterHomeiList()">
                <label>供花・供物</label>
              </div>
              <div class="ui toggle checkbox homei">
                <input type="checkbox" [(ngModel)]="homei_filter.chobun" (change)="filterHomeiList()">
                <label>弔文</label>
              </div>
              <div class="ui toggle checkbox homei">
                <input type="checkbox" [(ngModel)]="homei_filter.msg" (change)="filterHomeiList()">
                <label>追悼メッセージ</label>
              </div>
              <div class="ui toggle checkbox homei">
                <input type="checkbox" [(ngModel)]="homei_filter.koden" (change)="filterHomeiList()">
                <label>香典</label>
              </div>
            </div>
          </div>
          <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
            <div class="count" *ngIf="homei_list?.length">
              全{{homei_list?.length}}件中
              {{(pagination.current-1)*page_per_count+1}}件
              ～
              {{pagination.current===pagination.pages.length?homei_list?.length:pagination.current*page_per_count}}件表示
            </div>
            <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
              <i class="left chevron icon"></i>
            </a>
            <ng-container *ngFor="let page of pagination.pages">
              <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
            </ng-container>
            <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
              <i class="right chevron icon"></i>
            </a>
          </div>
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned">
                  <th class="entry_id">申込番号</th>
                  <th class="entry_name">申込者</th>
                  <th class="entry_ts">申込日時</th>
                  <th class="okurinushi_name">送り主名</th>
                  <th class="service_name">サービス</th>
                  <th class="item_name">品目</th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngFor="let homei of homei_list; index as i">
                <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
                  <td class="center aligned entry_id" title="{{homei.entry_id}}">{{homei.entry_id}}</td>
                  <td class="entry_name" title="{{homei.entry_name}}">{{homei.entry_name}}</td>
                  <td class="center aligned entry_ts" title="{{homei.entry_ts|date:'yyyy/MM/dd H:mm'}}">{{homei.entry_ts|date:'yyyy/MM/dd H:mm'}}</td>
                  <td class="okurinushi_name" title="{{homei.okurinushi_name}}">{{homei.okurinushi_name}}</td>
                  <td class="service_name" title="{{homei.service_name}}">{{homei.service_name}}</td>
                  <td class="item_name" title="{{homei.item_name}}">{{homei.item_name}}</td>
                </tr>
                </ng-container>
                <tr *ngIf="!homei_list?.length" class="no_data">
                  <td class="center aligned" colspan="8">対象データがありません。</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="ui attached tab segment hoyo" data-tab="hoyo-list">
          <div class="ui left floated left pagination menu" *ngIf="hoyo_pagination.pages.length > 1">
            <div class="count" *ngIf="seko_info.hoyo_seko?.length">
              全{{seko_info.hoyo_seko?.length}}件中
              {{(hoyo_pagination.current-1)*page_per_count+1}}件
              ～
              {{hoyo_pagination.current===hoyo_pagination.pages.length?hoyo_master_list?.length:hoyo_pagination.current*page_per_count}}件表示
            </div>
            <a class="icon item" [class.disabled]="hoyo_pagination.current===1" (click)="hoyoPageTo(hoyo_pagination, hoyo_pagination.current-1)">
              <i class="left chevron icon"></i>
            </a>
            <ng-container *ngFor="let page of hoyo_pagination.pages">
              <a class="item" [class.current]="page===hoyo_pagination.current" (click)="hoyoPageTo(hoyo_pagination, page)">{{page}}</a>
            </ng-container>
            <a class="icon item" [class.disabled]="hoyo_pagination.current===hoyo_pagination.pages.length" (click)="hoyoPageTo(hoyo_pagination, hoyo_pagination.current+1)">
              <i class="right chevron icon"></i>
            </a>
          </div>
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!seko_info.hoyo_seko?.length">
                  <th class="hoyo_name">名称</th>
                  <th class="hoyo_planned_date">目安日</th>
                  <th class="hoyo_activity_date">予定・実施日</th>
                  <th class="operation"></th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngFor="let hoyo of seko_info.hoyo_seko; index as i">
                  <tr (click)="showHoyoData($event, hoyo)" *ngIf="i >= (hoyo_pagination.current-1)*page_per_count && i < hoyo_pagination.current*page_per_count" >
                    <td class="hoyo_name" title="{{hoyo.hoyo_name}}">{{hoyo.hoyo_name}}</td>
                    <td class="center aligned hoyo_planned_date" title="{{hoyo.hoyo_planned_date | date: 'yyyy/MM/dd'}}">{{hoyo.hoyo_planned_date | date: 'yyyy/MM/dd'}}</td>
                    <td class="center aligned hoyo_activity_date" title="{{hoyo.hoyo_activity_date | date: 'yyyy/MM/dd'}}">{{hoyo.hoyo_activity_date | date: 'yyyy/MM/dd'}}</td>
                    <td class="center aligned button operation">
                      <a target="_blank" href="/hoyo/{{hoyo.hall?.id}}-{{hoyo.id}}/" *ngIf="hoyo.hoyo_activity_date">
                        <i class="large globe　icon" title="法要Web"></i>
                      </a>
                      <i class="large file pdf icon" title="案内PDF" (click)="downloadHoyoPDF(hoyo)" *ngIf="hoyo.hoyo_activity_date"></i>
                      <i class="large trash alternate icon" title="削除" (click)="deleteHoyoData(hoyo)" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN"></i>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
          <div class="table_fixed body" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN">
            <table class="ui celled structured unstackable table">
              <tbody>
                <tr title="新規追加" (click)="showHoyoData($event)">
                  <td colspan="4" class="center aligned"><i class="large add icon"></i></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="ui attached tab segment after-follow" data-tab="af-list">
          <div class="top_button_area">
            <button class="ui labeled icon button mini" [class.disabled]="login_company.base_type === Const.BASE_TYPE_ADMIN || !seko_info.seko_af?.wishes?.length" (click)="saveAfAnswered()">
              <i class="save icon"></i>回答状況保存
            </button>
          </div>
          <div class="input_area">
            <div class="line">
              <label class="large">コンタクト方法</label>
              <label class="data contact_way" *ngIf="seko_info?.seko_af && seko_info?.seko_af?.contact_way">{{seko_info.seko_af.contact_way.contact_way}}</label>
            </div>
            <div class="line">
              <label class="large">備考</label>
              <label class="data note" *ngIf="seko_info?.seko_af">{{seko_info.seko_af.note}}</label>
            </div>
          </div>
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!seko_info.seko_af?.wishes?.length">
                  <th class="af_group_name">AF項目グループ名</th>
                  <th class="af_name">AF項目名</th>
                  <th class="proposal_status">提案済</th>
                  <th class="order_status">状況</th>
                  <th class="order_chance">見込</th>
                  <th class="order_date">受注日</th>
                  <th class="answered_flg">回答状況</th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngIf="seko_info.seko_af">
                <ng-container *ngFor="let wish of seko_info.seko_af.wishes">
                  <tr (click)="showAfWishData($event, wish)" >
                    <td class="af_group_name" title="{{wish.af_type.af_group.name}}">{{wish.af_type.af_group.name}}</td>
                    <td class="af_name" title="{{wish.af_type.name}}">{{wish.af_type.name}}</td>
                    <td class="center aligned proposal_status">
                      <i class="large check icon" *ngIf="wish.proposal_status===Const.PROPOSAL_STATUS_ID_PROPOSED"></i>
                    </td>
                    <td class="center aligned order_status" title="{{Utils.getAfOrderStatusName(wish.order_status)}}">{{Utils.getAfOrderStatusName(wish.order_status)}}</td>
                    <td class="center aligned order_chance">
                      <i class="large circle outline icon" *ngIf="wish.order_chance===Const.AF_ORDER_CHANCE_STATUS_ID_PROSPECTIVE"></i>
                      <i class="large times icon" *ngIf="wish.order_chance===Const.AF_ORDER_CHANCE_STATUS_ID_NO_PROSPECT"></i>
                    </td>
                    <td class="center aligned order_date" title="{{wish.order_date | date: 'yyyy/MM/dd'}}">{{wish.order_date | date: 'yyyy/MM/dd'}}</td>
                    <td class="center aligned answered_flg">
                      <div class="ui checkbox answered">
                        <input type="checkbox" [(ngModel)]="wish.answered_flg">
                        <label class="checkbox_answered"></label>
                      </div>
                    </td>
                  </tr>
                </ng-container>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
        <input type="file" #imageFileUpload id="imageFileUpload" name="imageFileUpload" style="display:none;" />
        <input type="file" #invoiceFileUpload id="invoiceFileUpload" name="invoiceFileUpload" style="display:none;" />

        <div class="ui modal small kojin" id="additional-kojin">
          <div class="header ui"><i class="user add icon large"></i>追加故人</div>
          <div class="messageArea">
            <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
              <i class="close icon" (click)="closeMessage()"></i>
              <div class="ui center aligned header">
                <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
                {{message?.title}}
              </div>
              <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
                {{message?.msg}}
              </div>
            </div>
          </div>
          <div class="scrolling content">
            <div class="input_area">
              <div class="line">
                <label class="required">故人名</label>
                <div class="ui input">
                  <input autocomplete="off" type="text" id="add_kojin_name" [(ngModel)]="kojin_edit.name" (change)="kojin_edit.kana=addkojinnameKanaEm.value;">
                </div>
                <label class="required">故人名カナ</label>
                <div class="ui input">
                  <input autocomplete="off" type="text" #addkojinnameKanaEm id="add_kojin_kana" [(ngModel)]="kojin_edit.kana">
                </div>
              </div>
              <div class="line">
                <label class="required">喪主からみた続柄</label>
                <div class="ui input">
                  <input autocomplete="off" type="text" [(ngModel)]="kojin_edit.moshu_kojin_relationship">
                </div>
              </div>
              <div class="line">
                <label class="required">生年月日</label>
                <com-calendar #kojin2BirthDateEm [settings]="calendarOptionDate" [(value)]="kojin_edit.birth_date" (valueChange)="calcAge(2)"></com-calendar>
                <label class="required">逝去日</label>
                <com-calendar #kojin2DeathDateEm [settings]="calendarOptionDate" [(value)]="kojin_edit.death_date" (valueChange)="calcAge(2)"></com-calendar>
              </div>
              <div class="line">
                <label class="required">年齢</label>
                <div class="ui input tiny">
                  <input autocomplete="off" type="text" [(ngModel)]="kojin_edit.age_kbn">
                </div>
                <div class="ui input tiny divided">
                  <input autocomplete="off" type="number" maxlength="3" min="0" [(ngModel)]="kojin_edit.age">
                </div>
                <label class="plane">才</label>
              </div>
              <!--
              <div class="line">
                <label>遺影写真</label>
                <div class="ui card image" (dragover)="onDragOverPortrait($event)" (drop)="onDropPortrait($event, 3)">
                  <div class="ui image center aligned" (click)="selectFile(3)" title="遺影写真追加">
                    <ng-container *ngIf="!kojin_edit.iei_file && !kojin_edit.iei_file_name">
                    <i class="id badge outline icon huge"></i>
                    <div class="noimage">No image</div>
                    <div class="desc">この枠をクリックしてファイルを選択するか、ファイルをこの枠にドラッグ・アンド・ドロップしてください。</div>
                    </ng-container>
                    <img src="{{kojin_edit.iei_file?kojin_edit.iei_file:kojin_edit.iei_file_name}}" *ngIf="kojin_edit.iei_file || kojin_edit.iei_file_name">
                  </div>
                  <div class="clear" *ngIf="kojin_edit.iei_file || kojin_edit.iei_file_name"><i class="delete icon" (click)="clearPortrait(3)" title="写真クリア"></i></div>
                </div>
              </div>
              -->
            </div>
          </div>
          <div class="actions">
            <button class="ui labeled icon button mini" (click)="saveKojin2(kojin2BirthDateEm, kojin2DeathDateEm)">
              <i class="check circle icon"></i>確定
            </button>
            <button class="ui labeled icon button mini approve" [class.disabled]="!(seko_info?.kojin?.length >= 2)" (click)="deleteKojin2(kojin2BirthDateEm, kojin2DeathDateEm)">
              <i class="times circle icon"></i>クリア
            </button>
            <button class="ui labeled icon button mini light cancel" (click)="closeKojin2(kojin2BirthDateEm, kojin2DeathDateEm)">
              <i class="delete icon"></i>閉じる
            </button>
          </div>
        </div>

        <div class="ui modal small pattern" id="fuho-sample">
          <div class="header ui"><i class="list icon large"></i>訃報文パターン選択</div>
          <div class="scrolling content" *ngIf="fuho_sample_list?.length">
            <div class="ui segment" *ngFor="let fuho_sample of fuho_sample_list" (click)="select_fuho_sample(fuho_sample.sentence)">
              <pre>{{fuho_sample.sentence}}</pre>
            </div>
          </div>
        </div>

        <div class="ui modal small video" id="video-edit">
          <div class="header ui"><i class="youtube icon large"></i>配信動画</div>
          <div class="messageArea">
            <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
              <i class="close icon" (click)="closeMessage()"></i>
              <div class="ui center aligned header">
                <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
                {{message?.title}}
              </div>
              <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
                {{message?.msg}}
              </div>
            </div>
          </div>
          <div class="content">
            <div class="input_area">
              <div class="line">
                <label class="required large">タイトル</label>
                <div class="ui input full">
                  <input autocomplete="off" type="text" [(ngModel)]="video_edit.title">
                </div>
              </div>
              <div class="line">
                <label class="required large">Youtubeコード</label>
                <div class="ui input full">
                  <input autocomplete="off" type="url" [(ngModel)]="video_edit.youtube_code">
                </div>
              </div>
              <div class="line">
                <label class="large" [class.required]="video_edit.type===1">ライブ開始</label>
                <com-calendar #liveBeginTsEm [settings]="calendarOptionDateTime" [(value)]="video_edit.live_begin_ts"></com-calendar>
                <label class="large" [class.required]="video_edit.type===1">ライブ終了</label>
                <com-calendar #liveEndTsEm [settings]="calendarOptionDateTime" [(value)]="video_edit.live_end_ts"></com-calendar>
              </div>
              <div class="line">
                <label class="required large">配信終了</label>
                <com-calendar #DeliveryEndTsEm [settings]="calendarOptionDateTime" [(value)]="video_edit.delivery_end_ts"></com-calendar>
                <label class="required large">種類</label>
                <div class="ui radio checkbox">
                  <input type="radio" name="video_type" [value]="1" [(ngModel)]="video_edit.type" [checked]="video_edit.type===1" (change)="videoTypeChange(1, liveBeginTsEm, liveEndTsEm)">
                  <label>ライブ配信</label>
                </div>
                <div class="ui radio checkbox">
                  <input type="radio" name="video_type" [value]="2" [(ngModel)]="video_edit.type" [checked]="video_edit.type===2" (change)="videoTypeChange(2, liveBeginTsEm, liveEndTsEm)">
                  <label>広告</label>
                </div>
              </div>
            </div>
          </div>
          <div class="actions">
            <button class="ui labeled icon button mini" (click)="saveVideo(liveBeginTsEm, liveEndTsEm, DeliveryEndTsEm)">
              <i class="check circle icon"></i>確定
            </button>
            <button class="ui labeled icon button mini light cancel" (click)="closeVideo(liveBeginTsEm, liveEndTsEm, DeliveryEndTsEm)">
              <i class="delete icon"></i>閉じる
            </button>
          </div>
        </div>

        <div class="ui modal album small" id="album-item">
          <div class="content">
            <div class="ui segment">
              <img src="{{albumSeletedItem?.image_file?albumSeletedItem?.image_file:albumSeletedItem?.file_name}}">
            </div>
          </div>
        </div>

        <div class="ui modal album small" id="share-item">
          <div class="content">
            <div class="ui segment">
              <img src="{{shareSeletedItem?.image_file?shareSeletedItem?.image_file:shareSeletedItem?.file_name}}">
            </div>
          </div>
        </div>

        <div class="ui modal album small" id="soke-share-item">
          <div class="content">
            <div class="ui segment">
              <img src="{{sokeShareSeletedItem?.image_file?sokeShareSeletedItem?.image_file:sokeShareSeletedItem?.file_name}}">
            </div>
          </div>
        </div>

        <div class="ui modal hoyo big" id="hoyo-edit">
          <div class="header ui"><i class="monument icon large"></i>法要登録</div>
          <div class="messageArea">
            <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
              <i class="close icon" (click)="closeMessage()"></i>
              <div class="ui center aligned header">
                <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
                {{message?.title}}
              </div>
              <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
                {{message?.msg}}
              </div>
            </div>
          </div>
          <div class="content">
            <div class="input_area" *ngIf="hoyo_edit">
              <div class="line">
                <label class="required">法要名</label>
                <div class="ui input">
                  <input autocomplete="off" type="text" id="hoyo_name" [(ngModel)]="hoyo_edit.hoyo_name">
                </div>
              </div>
              <ng-container *ngIf="seko_info?.kojin.length>0">
                <div class="line">
                  <label>故人名</label>
                  <label class="data name">{{seko_info.kojin[0].name}}</label>
                  <label class="plane">戒名</label>
                  <div class="ui input">
                    <input autocomplete="off" type="text" id="kojin1_kaimyo" [(ngModel)]="seko_info.kojin[0].kaimyo">
                  </div>
                  <label class="plane">逝去日</label>
                  <label class="data">{{seko_info.kojin[0].death_date | date: 'yyyy/MM/dd'}} </label>
                  <label class="plane">{{seko_info.kojin[0].age_kbn}}</label>
                  <label class="data">{{seko_info.kojin[0].age}}才</label>
                </div>
              </ng-container>
              <ng-container *ngIf="seko_info?.kojin.length>1">
                <div class="line">
                  <label>故人名</label>
                  <label class="data name">{{seko_info.kojin[1].name}}</label>
                  <label class="plane">戒名</label>
                  <div class="ui input">
                    <input autocomplete="off" type="text" id="kojin2_kaimyo" [(ngModel)]="seko_info.kojin[1].kaimyo">
                  </div>
                  <label class="plane">逝去日</label>
                  <label class="data">{{seko_info.kojin[1].death_date | date: 'yyyy/MM/dd'}} </label>
                  <label class="plane">{{seko_info.kojin[1].age_kbn}}</label>
                  <label class="data">{{seko_info.kojin[1].age}}才</label>
                </div>
              </ng-container>
              <div class="line row5">
                <label class="required">案内文</label>
                <div class="ui icon input textarea">
                    <i class="search icon link" (click)="showHoyoSample()"></i>
                    <textarea [class.error]="errField.get('hoyo_sentence')" id="hoyo_sentence" rows="4" cols="35" [(ngModel)]="hoyo_edit.hoyo_sentence"></textarea>
                </div>
              </div>
              <div class="line">
                <label class="required">法要日時</label>
                <com-calendar [settings]="calendarOptionDate" [(value)]="hoyo_edit.hoyo_activity_date"></com-calendar>
                <label class="plane blank required"></label>
                <com-time-input [(value)]="hoyo_edit.begin_time"></com-time-input>
                <label class="plane">〜</label>
                <com-time-input [(value)]="hoyo_edit.end_time"></com-time-input>
                <label class="required">返信期限日</label>
                <com-calendar [settings]="calendarOptionDate" [(value)]="hoyo_edit.reply_limit_date"></com-calendar>
              </div>
              <div class="line">
                <label class="required">式場</label>
                <com-dropdown [settings]="selfHallCombo1" [(selectedValue)]="hoyo_edit.hall.id" [(selectedName)]="hoyo_edit.hall.base_name" (selectedItemChange)="hoyoHallChange($event)"></com-dropdown>
                <div class="ui toggle checkbox dine">
                  <input type="checkbox" [(ngModel)]="hoyo_edit.dine_flg">
                  <label>会食有</label>
                </div>
                <label class="required">法要ページ掲載終了日</label>
                <com-calendar [settings]="calendarOptionDate" [(value)]="hoyo_edit.hoyo_site_end_date"></com-calendar>
              </div>
              <div class="line">
                <label>式次第</label>
                <div class="ui segment hoyo-schedule">
                  <table class="ui very basic celled unstackable table">
                    <thead>
                      <tr class="center aligned">
                        <th class="schedule_name">項目</th>
                        <th class="place">式場名</th>
                        <th class="start_time">開始</th>
                        <th class="end_time">終了</th>
                      </tr>
                    </thead>
                    <tbody *ngIf="hoyo_edit?.schedules?.length">
                      <ng-container *ngFor="let hoyoSchedule of hoyo_edit.schedules; index as i">
                      <tr>
                        <td>
                          <div class="ui input fluid">
                            <input type="text" autocomplete="off" [(ngModel)]="hoyoSchedule.schedule_name" (input)="hoyoScheduleNameChange($event, hoyoSchedule)">
                          </div>
                        </td>
                        <td [class.disabled]="!hoyoSchedule.schedule_name">
                          <ng-container *ngIf="hoyoSchedule.schedule_name">
                            <com-dropdown [settings]="selfHallCombo2" [(selectedValue)]="hoyoSchedule.hall.id" [(selectedName)]="hoyoSchedule.hall_name"></com-dropdown>
                          </ng-container>
                        </td>
                        <td [class.disabled]="!hoyoSchedule.schedule_name">
                          <ng-container *ngIf="hoyoSchedule.schedule_name">
                            <com-time-input [(value)]="hoyoSchedule.begin_time"></com-time-input>
                          </ng-container>
                        </td>
                        <td [class.disabled]="!hoyoSchedule.schedule_name">
                          <ng-container *ngIf="hoyoSchedule.schedule_name">
                            <com-time-input [(value)]="hoyoSchedule.end_time"></com-time-input>
                          </ng-container>
                        </td>
                      </tr>
                      </ng-container>
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="line">
                <label class="required">施主</label>
                <div class="ui input">
                  <input autocomplete="off" type="text" [(ngModel)]="hoyo_edit.seshu_name">
                </div>
                <label class="required">故人との関係</label>
                <div class="ui input">
                  <input autocomplete="off" type="text" [(ngModel)]="hoyo_edit.kojin_seshu_relationship">
                </div>
              </div>
              <div class="line">
                <label class="required">住所</label>
                <div class="ui left icon input mini">
                  <i class="tenge icon"></i>
                  <input type="tel" placeholder="ハイフンなし" autocomplete="off" id="hoyo_zip_code" maxlength="7" [(ngModel)]="hoyo_edit.zip_code" (input)="hoyoZipcodeChange()">
                </div>
                <div class="ui input mini divided required">
                  <input type="text" autocomplete="off" id="hoyo_prefecture" [(ngModel)]="hoyo_edit.prefecture">
                </div>
                <div class="ui input divided required">
                  <input type="text" autocomplete="off" id="hoyo_address_1" [(ngModel)]="hoyo_edit.address_1">
                </div>
                <div class="ui input divided">
                  <input type="text" autocomplete="off" id="hoyo_address_2" [(ngModel)]="hoyo_edit.address_2">
                </div>
                <div class="ui input divided">
                  <input type="text" autocomplete="off" id="hoyo_address_3" [(ngModel)]="hoyo_edit.address_3">
                </div>
              </div>
              <div class="line">
                <label class="required">電話番号</label>
                <div class="ui left icon input">
                  <i class="phone alternate icon"></i>
                  <input type="tel" autocomplete="off" id="moshu_tel" maxlength="15" [(ngModel)]="hoyo_edit.tel">
                </div>
                <label class="required">担当者</label>
                <com-dropdown [settings]="staffCombo" [(selectedValue)]="hoyo_edit.staff.id"></com-dropdown>
                <label>司式者</label>
                <div class="ui input">
                  <input autocomplete="off" type="text" [(ngModel)]="hoyo_edit.shishiki_name">
                </div>
              </div>
              <div class="line">
                <label>備考</label>
                <div class="ui input full">
                  <input type="text" autocomplete="off" id="note" [(ngModel)]="hoyo_edit.note">
                </div>
              </div>
            </div>
          </div>
          <div class="actions">
            <button class="ui labeled icon button mini" (click)="downloadHoyoPDF(hoyo_edit)" [class.disabled]="!canDownloadHoyoPdf(hoyo_edit)">
              <i class="file pdf icon"></i>案内状PDF出力
            </button>
            <button class="ui labeled icon button mini" (click)="saveHoyoData()" [class.disabled]="login_company.base_type === Const.BASE_TYPE_ADMIN">
              <i class="save icon"></i>保存
            </button>
            <button class="ui labeled icon button mini light cancel" (click)="closeHoyoData()">
              <i class="delete icon"></i>閉じる
            </button>
          </div>
        </div>

        <div class="ui modal small pattern" id="hoyo-sample">
          <div class="header ui"><i class="list icon large"></i>法要案内文選択</div>
          <div class="scrolling content" *ngIf="hoyo_sample_list?.length">
            <div class="ui segment" *ngFor="let hoyo_sample of hoyo_sample_list" (click)="select_hoyo_sample(hoyo_sample.sentence)">
              <pre>{{hoyo_sample.sentence}}</pre>
            </div>
          </div>
          <div class="actions">
            <button class="ui labeled icon button mini light cancel">
              <i class="delete icon"></i>閉じる
            </button>
          </div>
        </div>

        <div class="ui modal af-wish" id="af-wish-edit">
          <div class="header ui">
            <span><i class="hand holding medical icon large"></i>AF活動状況登録</span>
            <span class="title" *ngIf="af_wish_edit">【相談項目:<span class="data">{{af_wish_edit.af_type.name}}</span>】</span>
          </div>
          <div class="messageArea">
            <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
              <i class="close icon" (click)="closeMessage()"></i>
              <div class="ui center aligned header">
                <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
                {{message?.title}}
              </div>
              <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
                {{message?.msg}}
              </div>
            </div>
          </div>
          <div class="content" *ngIf="af_wish_edit">
            <div class="input_area">
              <div class="line">
                <label>提案済</label>
                <div class="ui checkbox proposed">
                  <input type="checkbox" [(ngModel)]="af_wish_edit.proposed_flg">
                  <label></label>
                </div>
                <label>状況</label>
                <com-dropdown class="mini" [settings]="afStatusCombo" [(selectedValue)]="af_wish_edit.order_status"></com-dropdown>
                <label>受注見込み</label>
                <com-dropdown class="tiny" [settings]="afChanceCombo" [(selectedValue)]="af_wish_edit.order_chance"></com-dropdown>
                <label>受注日</label>
                <com-calendar [settings]="calendarOptionDate" [(value)]="af_wish_edit.order_date"></com-calendar>
              </div>
            </div>
            <div class="sub_title">活動状況一覧</div>
            <div class="table_fixed head">
              <table class="ui celled structured unstackable table">
                <thead>
                  <tr class="center aligned" [class.no-data]="!af_wish_edit?.activities?.length">
                    <th class="activity_ts">活動日</th>
                    <th class="activity">活動内容</th>
                  </tr>
                </thead>
              </table>
            </div>
            <div class="table_fixed body">
              <table class="ui celled structured unstackable table">
                <tbody>
                  <ng-container *ngFor="let activity of af_wish_edit?.activities">
                    <tr (click)="showAfActivityData($event, activity)" >
                      <td class="center aligned activity_ts" title="{{activity.activity_ts | date: 'yyyy/MM/dd'}}">{{activity.activity_ts | date: 'yyyy/MM/dd'}}</td>
                      <td class="activity" title="{{activity.activity}}">{{activity.activity}}</td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
            <div class="table_fixed body" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN">
              <table class="ui celled structured unstackable table">
                <tbody>
                  <tr title="新規追加" (click)="showAfActivityData($event)">
                    <td colspan="2" class="center aligned"><i class="large add icon"></i></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="actions">
            <button class="ui labeled icon button mini" (click)="saveAfWishData()" [class.disabled]="login_company.base_type === Const.BASE_TYPE_ADMIN">
              <i class="save icon"></i>保存
            </button>
            <button class="ui labeled icon button mini light cancel" (click)="closeAfWishData()">
              <i class="delete icon"></i>閉じる
            </button>
          </div>
        </div>

        <div class="ui modal small af-activity" id="af-activity-edit">
          <div class="header ui">
            <span><i class="hand holding medical icon large"></i>AF活動内容登録</span>
            <span class="title" *ngIf="af_wish_edit">【相談項目:<span class="data">{{af_wish_edit.af_type.name}}</span>】</span>
          </div>
          <div class="messageArea">
            <div class="ui message {{message_sub?.type}}" [class.visible]="!!message_sub" [class.hidden]="!message_sub" (click)="closeMessageSub()">
              <i class="close icon" (click)="closeMessageSub()"></i>
              <div class="ui center aligned header">
                <i class="{{message_sub?.type==='info'?'info circle':message_sub?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
                {{message_sub?.title}}
              </div>
              <div class="ui center aligned basic mini segment" *ngIf="message_sub?.msg">
                {{message_sub?.msg}}
              </div>
            </div>
          </div>
          <div class="content" *ngIf="af_activity_edit">
            <div class="input_area">
              <div class="line">
                <label class="required">活動日時</label>
                <com-calendar [settings]="calendarOptionDateTime" [(value)]="af_activity_edit.activity_ts"></com-calendar>
              </div>
              <div class="line row5">
                <label class="required">内容</label>
                <div class="ui icon input textarea">
                  <textarea id="sentence" rows="4" cols="35" [(ngModel)]="af_activity_edit.activity"></textarea>
                </div>
              </div>
            </div>
          </div>
          <div class="actions">
            <button class="ui labeled icon button mini" (click)="saveAfActivityData()">
              <i class="save icon"></i>保存
            </button>
            <button class="ui labeled icon button mini light cancel" (click)="closeAfActivityData()">
              <i class="delete icon"></i>閉じる
            </button>
          </div>
        </div>

      </div>
    </div>
  </div>

  </ng-container>

  <div class="footer_button_area">
    <div class="button_group">
      <button class="ui labeled icon button mini light" (click)="saveData()" [class.disabled]="login_company.base_type === Const.BASE_TYPE_ADMIN || !seko_info">
        <i class="save icon"></i>保存
      </button>
      <a class="ui labeled icon button mini light" [class.disabled]="!seko_id || !seko_info || lack_flg" target="_blank" rel="opener" href="/fuho/{{seko_info?.seko_company.id}}-{{seko_id}}/">
        <i class="globe icon"></i>訃報ページ確認
      </a>
      <!--
      <button class="ui labeled icon button mini light" [class.disabled]="!seko_id || !seko_info || lack_flg" (click)="downloadFuhoPDF()">
        <i class="file pdf icon"></i>訃報紙PDF出力
      </button>
      -->
      <button class="ui labeled icon button mini light" [class.disabled]="login_company.base_type === Const.BASE_TYPE_ADMIN || !seko_id || !seko_info || lack_flg" (click)="sendFuhoURL()">
        <i class="envelope icon"></i>葬家専用ページURL送信
      </button>
      <button class="ui labeled icon button mini light" [class.disabled]="!seko_id || !seko_info || lack_flg" (click)="downloadQRCode()">
        <i class="file qrcode icon"></i>QRコード出力
      </button>
      <button class="ui labeled icon button mini light" [class.disabled]="login_company.base_type === Const.BASE_TYPE_ADMIN || !seko_id || !seko_info" (click)="deleteData()">
        <i class="trash alternate icon"></i>削除
      </button>
      <button class="ui labeled icon button mini light" (click)="closeWindow()">
        <i class="delete icon"></i>閉じる
      </button>
    </div>
  </div>
