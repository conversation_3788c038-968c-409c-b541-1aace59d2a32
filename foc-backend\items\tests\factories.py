import factory
import factory.fuzzy as fuzzy
from django.utils import timezone
from factory.django import DjangoModelFactory

from bases.tests.factories import BaseFactory
from items.models import Item, ItemSupplier
from masters.tests.factories import ServiceFactory, TaxFactory
from suppliers.tests.factories import SupplierFactory

tz = timezone.get_current_timezone()


class ItemFactory(DjangoModelFactory):
    class Meta:
        model = Item

    id = factory.Sequence(lambda n: n)
    service = factory.SubFactory(ServiceFactory)
    base = factory.SubFactory(BaseFactory)
    hinban = factory.Faker('bothify', text='##??')
    name = factory.Faker('word', locale='ja_JP')
    item_price = fuzzy.FuzzyInteger(10000000)
    tax = factory.SubFactory(TaxFactory)
    begin_date = factory.Faker('past_date', start_date='-30d', tzinfo=tz)
    end_date = factory.Faker('future_date', end_date='+30d', tzinfo=tz)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0
    fdn_code = factory.Faker('bothify', text='##??')
    display_num = factory.Sequence(lambda n: n)


class ItemSupplierFactory(DjangoModelFactory):
    class Meta:
        model = ItemSupplier

    item = factory.SubFactory(ItemFactory)
    supplier = factory.SubFactory(SupplierFactory)
    default_supplier_flg = False
