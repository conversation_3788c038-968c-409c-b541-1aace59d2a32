from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory
from faqs.tests.factories import FaqFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class FaqListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.faq_11 = FaqFactory(company=self.company1, display_num=3)
        self.faq_12 = FaqFactory(company=self.company1, display_num=1)
        self.faq_13 = FaqFactory(company=self.company1, display_num=2)

        company2 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.faq_21 = FaqFactory(company=company2, display_num=1)
        self.faq_22 = FaqFactory(company=company2, display_num=2)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_faq_list_succeed(self) -> None:
        """FAQ一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('faqs:faq_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)
        # 順番確認 (12 > 13 > 11 > 21 > 22)
        self.assertEqual(records[0]['id'], self.faq_12.pk)
        self.assertEqual(records[1]['id'], self.faq_13.pk)
        self.assertEqual(records[2]['id'], self.faq_11.pk)
        self.assertEqual(records[3]['id'], self.faq_21.pk)
        self.assertEqual(records[4]['id'], self.faq_22.pk)

    def test_faq_list_filter_by_company(self) -> None:
        """FAQ一覧APIで拠点の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'company': self.company1.pk}
        response = self.api_client.get(reverse('faqs:faq_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()

        self.assertEqual(len(records), 3)
        for record, correct_faq in zip(records, [self.faq_12, self.faq_13, self.faq_11]):
            self.assertEqual(record['id'], correct_faq.pk)
            self.assertEqual(record['company'], self.company1.pk)
            self.assertEqual(record['display_num'], correct_faq.display_num)
            self.assertEqual(record['question'], correct_faq.question)
            self.assertEqual(record['answer'], correct_faq.answer)
            self.assertEqual(record['del_flg'], correct_faq.del_flg)
            self.assertIsNotNone(record['created_at'])
            self.assertEqual(record['create_user_id'], correct_faq.create_user_id)
            self.assertIsNotNone(record['updated_at'])
            self.assertEqual(record['update_user_id'], correct_faq.update_user_id)

    def test_faq_list_ignores_deleted(self) -> None:
        """FAQ一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.faq_11.disable()
        self.faq_12.disable()
        self.faq_21.disable()

        params: Dict = {}
        response = self.api_client.get(reverse('faqs:faq_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)
        faq_dict13: Dict = records[0]
        faq_dict22: Dict = records[1]
        self.assertEqual(faq_dict13['id'], self.faq_13.pk)
        self.assertEqual(faq_dict22['id'], self.faq_22.pk)

    def test_faq_list_failed_without_auth(self) -> None:
        """FAQ一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('faqs:faq_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_faq_list_allow_from_moshu(self) -> None:
        """FAQ一覧APIが喪主からの呼び出しを許可する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('faqs:faq_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class FaqCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        company = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.faq_data = FaqFactory.build(company=company)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.faq_data.company.pk,
            'question': self.faq_data.question,
            'answer': self.faq_data.answer,
            'display_num': self.faq_data.display_num,
        }

    def test_faq_create_succeed(self) -> None:
        """FAQを追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('faqs:faq_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['id'])
        self.assertEqual(record['company'], self.faq_data.company.pk)
        self.assertEqual(record['question'], self.faq_data.question)
        self.assertEqual(record['answer'], self.faq_data.answer)
        self.assertEqual(record['display_num'], self.faq_data.display_num)
        self.assertEqual(record['del_flg'], self.faq_data.del_flg)
        self.assertIsNotNone(record['created_at'])
        self.assertEqual(record['create_user_id'], self.staff.pk)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_faq_create_fail_with_non_existent_seko(self) -> None:
        """FAQ追加APIは存在しない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        non_existent_company = BaseFactory.build(base_type=Base.OrgType.COMPANY)
        params['company'] = non_existent_company.pk
        response = self.api_client.post(reverse('faqs:faq_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['company'])

    def test_faq_create_failed_without_auth(self) -> None:
        """FAQ追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(reverse('faqs:faq_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_faq_create_deny_from_moshu(self) -> None:
        """FAQ一覧APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('faqs:faq_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
