from unittest import mock

from django.test import TestCase
from rest_framework.exceptions import ValidationError

from bases.models import FocFee
from bases.tests.factories import FocFeeFactory
from orders.epsilon import ContractType, ResponsePayment, left_in_bytes, register_payment
from seko.models import Seko
from seko.tests.factories import SekoFactory


class ContractTypeTest(TestCase):
    def test_contract_code(self) -> None:
        """施行から指定した区分のEpsilon契約コードを取得します"""

        # 拠点にFOCFee設定がない場合はエラー
        seko: Seko = SekoFactory()
        with self.assertRaises(ValueError):
            ContractType.BASIC.contract_code(seko)

        focfee: FocFee = FocFeeFactory(company=seko.seko_company)
        self.assertEqual(ContractType.BASIC.contract_code(seko), focfee.gmo_code)
        self.assertEqual(ContractType.KODEN.contract_code(seko), focfee.gmo_code_koden)


class RegisterPaymentTest(TestCase):
    @mock.patch('orders.epsilon.requests.post')
    def test_register_payment_succeed(self, mock_post) -> None:
        """Epsilon決済登録が成功する"""
        mock_post.return_value.status_code = 200
        mock_post.return_value.text = """\
<?xml version="1.0" encoding="x-sjis-cp932"?>
<Epsilon_result>
    <result acsurl="" />
    <result err_code="" />
    <result err_detail="" />
    <result kari_flag="0" />
    <result pareq="" />
    <result result="1" />
    <result trans_code="1589043" />
</Epsilon_result>
"""

        payment_kwargs = {
            'contract_code': 'dummy',
            'token': 'dummy',
            'user_name': 'dummy',
            'user_mail_address': 'dummy',
            'price': 'dummy',
            'user_agent': 'dummy',
        }
        result: ResponsePayment = register_payment(**payment_kwargs)
        self.assertEqual(result.trans_code, '1589043')

    @mock.patch('orders.epsilon.requests.post')
    def test_register_payment_failed(self, mock_post) -> None:
        """Epsilon決済登録が失敗したらValidationError"""
        mock_post.return_value.status_code = 200
        mock_post.return_value.text = """\
<?xml version="1.0" encoding="x-sjis-cp932"?>
<Epsilon_result>
    <result acsurl="" />
    <result err_code="618" />
    <result err_detail="有効期限（年）が不正です。" />
    <result pareq="" />
    <result result="9" />
    <result trans_code="" />
</Epsilon_result>
"""

        payment_kwargs = {
            'contract_code': 'dummy',
            'token': 'dummy',
            'user_name': 'dummy',
            'user_mail_address': 'dummy',
            'price': 'dummy',
            'user_agent': 'dummy',
        }
        with self.assertRaises(ValidationError):
            register_payment(**payment_kwargs)

    @mock.patch('orders.epsilon.requests.post')
    def test_register_payment_unexpected_return(self, mock_post) -> None:
        """Epsilon決済登録で予期しない値が返ってきたらValidationError"""
        mock_post.return_value.status_code = 200
        mock_post.return_value.text = """\
<?xml version="1.0" encoding="x-sjis-cp932"?>
<Epsilon_result>
    <result acsurl="" />
    <result err_code="" />
    <result err_detail="" />
    <result pareq="" />
    <result result="5" />
    <result trans_code="" />
</Epsilon_result>
"""

        payment_kwargs = {
            'contract_code': 'dummy',
            'token': 'dummy',
            'user_name': 'dummy',
            'user_mail_address': 'dummy',
            'price': 'dummy',
            'user_agent': 'dummy',
        }
        with self.assertRaises(ValidationError):
            register_payment(**payment_kwargs)

    @mock.patch('orders.epsilon.requests.post')
    def test_register_payment_failed_illegal_xml(self, mock_post) -> None:
        """Epsilon決済登録で予期しないXMLが返ってきたらLookupError"""
        mock_post.return_value.status_code = 200
        mock_post.return_value.text = """\
<?xml version="1.0" encoding="x-sjis-cp932"?>
<Epsilon_result>
</Epsilon_result>
"""

        payment_kwargs = {
            'contract_code': 'dummy',
            'token': 'dummy',
            'user_name': 'dummy',
            'user_mail_address': 'dummy',
            'price': 'dummy',
            'user_agent': 'dummy',
        }
        with self.assertRaises(LookupError):
            register_payment(**payment_kwargs)


class UtilityFunctionTest(TestCase):
    def test_left_in_bytes(self) -> None:
        """文字列を指定した文字コードのバイト数で切り捨てる"""
        mb_string: str = 'あ1い2う3え4お'
        self.assertEqual(left_in_bytes(mb_string, 6), 'あ1い2')
        self.assertEqual(left_in_bytes(mb_string, 7), 'あ1い2')
        self.assertEqual(left_in_bytes(mb_string, 8), 'あ1い2う')
        self.assertEqual(left_in_bytes(mb_string, 9), 'あ1い2う3')
        self.assertEqual(left_in_bytes(mb_string, 7, 'utf-8'), 'あ1い')
        self.assertEqual(left_in_bytes(mb_string, 8, 'utf-8'), 'あ1い2')
        self.assertEqual(left_in_bytes(mb_string, 9, 'utf-8'), 'あ1い2')
        self.assertEqual(left_in_bytes(mb_string, 10, 'utf-8'), 'あ1い2')
        self.assertEqual(left_in_bytes(mb_string, 11, 'utf-8'), 'あ1い2う')
