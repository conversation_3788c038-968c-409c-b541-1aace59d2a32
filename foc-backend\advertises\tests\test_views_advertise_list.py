from base64 import b64encode
from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from advertises.tests.factories import AdvertiseFactory
from bases.models import Base
from bases.tests.factories import BaseFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class AdvertiseListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.advertise_11 = AdvertiseFactory(company=self.company1)
        self.advertise_12 = AdvertiseFactory(company=self.company1)
        self.advertise_13 = AdvertiseFactory(company=self.company1)

        company2 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.advertise_21 = AdvertiseFactory(company=company2)
        self.advertise_22 = AdvertiseFactory(company=company2)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_advertise_list_succeed(self) -> None:
        """広告一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'company': self.company1.pk}
        response = self.api_client.get(
            reverse('advertises:advertise_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        # 順番確認 (11 > 12 > 13 > 21 > 22)
        self.assertEqual(records[0]['id'], self.advertise_11.pk)
        self.assertEqual(records[1]['id'], self.advertise_12.pk)
        self.assertEqual(records[2]['id'], self.advertise_13.pk)

    def test_advertise_list_filter_by_company(self) -> None:
        """広告一覧APIで拠点の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'company': self.company1.pk}
        response = self.api_client.get(
            reverse('advertises:advertise_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        for record, correct_advertise in zip(
            records, [self.advertise_11, self.advertise_12, self.advertise_13]
        ):
            self.assertEqual(record['id'], correct_advertise.pk)
            self.assertEqual(record['company'], self.company1.pk)
            self.assertTrue(record['banner_file'].endswith(correct_advertise.banner_file.url))
            self.assertEqual(record['url'], correct_advertise.url)
            self.assertTrue(record['link_file'].endswith(correct_advertise.link_file.url))
            self.assertEqual(record['begin_date'], correct_advertise.begin_date.isoformat())
            self.assertEqual(record['end_date'], correct_advertise.end_date.isoformat())
            self.assertEqual(record['del_flg'], correct_advertise.del_flg)
            self.assertIsNotNone(record['created_at'])
            self.assertEqual(record['create_user_id'], correct_advertise.create_user_id)
            self.assertIsNotNone(record['updated_at'])
            self.assertEqual(record['update_user_id'], correct_advertise.update_user_id)

    def test_advertise_list_ignores_deleted(self) -> None:
        """広告一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.advertise_11.disable()
        self.advertise_12.disable()
        self.advertise_21.disable()

        params: Dict = {'company': self.company1.pk}
        response = self.api_client.get(
            reverse('advertises:advertise_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)
        advertise_dict13: Dict = records[0]
        self.assertEqual(advertise_dict13['id'], self.advertise_13.pk)

    def test_advertise_list_failed_without_auth(self) -> None:
        """広告一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('advertises:advertise_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_advertise_list_allow_from_moshu(self) -> None:
        """広告一覧APIが喪主からの呼び出しを許可する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {'company': self.company1.pk}
        response = self.api_client.get(
            reverse('advertises:advertise_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class AdvertiseCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        company = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.advertise_data = AdvertiseFactory.build(company=company)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.advertise_data.company.pk,
            'banner_file': b64encode(self.advertise_data.banner_file.read()),
            'url': self.advertise_data.url,
            'link_file': b64encode(self.advertise_data.link_file.read()),
            'begin_date': self.advertise_data.begin_date,
            'end_date': self.advertise_data.end_date,
        }

    def test_advertise_create_succeed(self) -> None:
        """広告を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('advertises:advertise_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['id'])
        self.assertEqual(record['company'], self.advertise_data.company.pk)
        self.assertIsNotNone(record['banner_file'])
        self.assertEqual(record['url'], self.advertise_data.url)
        self.assertIsNotNone(record['link_file'])
        self.assertEqual(record['begin_date'], self.advertise_data.begin_date.isoformat())
        self.assertEqual(record['end_date'], self.advertise_data.end_date.isoformat())
        self.assertEqual(record['del_flg'], self.advertise_data.del_flg)
        self.assertIsNotNone(record['created_at'])
        self.assertEqual(record['create_user_id'], self.staff.pk)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_advertise_create_fail_with_non_existent_seko(self) -> None:
        """広告追加APIは存在しない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        non_existent_company = BaseFactory.build(base_type=Base.OrgType.COMPANY)
        params['company'] = non_existent_company.pk
        response = self.api_client.post(
            reverse('advertises:advertise_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['company'])

    def test_advertise_create_failed_without_auth(self) -> None:
        """広告追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('advertises:advertise_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_advertise_create_deny_from_moshu(self) -> None:
        """広告一覧APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('advertises:advertise_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
