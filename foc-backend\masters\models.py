from datetime import date

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


class Schedule(models.Model):
    name = models.TextField(_('schedule name'))
    required_flg = models.BooleanField(_('required flg'))

    class Meta:
        db_table = 'mm_schedule'


class HoyoStyle(models.Model):
    name = models.TextField(_('hoyo name'))
    wareki_use_flg = models.BooleanField(_('use era name'))

    class Meta:
        db_table = 'mm_hoyo_style'


class SekoStyle(models.Model):
    hoyo_style = models.ForeignKey(HoyoStyle, models.DO_NOTHING)
    name = models.TextField(_('seko name'))
    wareki_use_flg = models.BooleanField(_('use era name'))

    class Meta:
        db_table = 'mm_seko_style'


class ZipCode(models.Model):
    zip_code = models.CharField(_('zipcode'), max_length=7, db_index=True)
    prefecture = models.TextField(_('prefecture'))
    address_1 = models.TextField(_('address 1'))
    address_2 = models.TextField(_('address 2'))

    class Meta:
        db_table = 'mm_zip_code'


class Wareki(models.Model):
    name = models.TextField(_('era'))
    begin_date = models.DateField(_('term from'), db_index=True)
    end_date = models.DateField(_('term until'))
    minus_years = models.IntegerField(_('minus years'))

    class Meta:
        db_table = 'mm_wareki'

    @classmethod
    def japanese_era(
        cls,
        the_date: date,
        use_gannen: bool = True,
        full_date: bool = False,
        kanji_date: bool = False,
    ) -> str:
        era: Wareki = cls.objects.filter(begin_date__lte=the_date).order_by('-begin_date').first()
        if not era:
            raise ValueError(f'No wareki found for {the_date}')
        the_date = timezone.localdate(the_date)
        year_in_era: int = the_date.year - era.begin_date.year + 1
        year_label: str = (
            '元'
            if use_gannen and year_in_era == 1
            else (cls.int2kanji(year_in_era) if kanji_date else str(year_in_era))
        )
        result = f'{era.name}{year_label}'

        if full_date:
            month = cls.int2kanji(the_date.month) if kanji_date else str(the_date.month)
            day = cls.int2kanji(the_date.day) if kanji_date else str(the_date.day)
            result = f'{result}年{month}月{day}日'

        return result

    @classmethod
    def int2kanji(cls, num) -> str:
        if num > 99:
            return ''
        suji = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九']
        kugiri = ['', '十']

        num = list(map(int, list(str(num))))
        kansuji = []

        for k, v in zip(range(len(num)), reversed(num)):
            keta = []
            keta.append(suji[v if v > 1 else 0 if k % 4 else v])
            keta.append(kugiri[k % 4 if v > 0 else 0])
            kansuji.append(''.join(keta))

        kansuji = ''.join(reversed(kansuji))

        return kansuji if kansuji else '零'


class Service(models.Model):
    name = models.TextField(_('name'))
    single_item_flg = models.BooleanField(_('single item flg'))
    cart_flg = models.BooleanField(_('cart flg'))
    payment_flg = models.BooleanField(_('payment flg'))

    class Meta:
        db_table = 'mm_service'


class Tax(models.Model):
    tax_pct = models.IntegerField(_('tax pct'))
    keigen_flg = models.BooleanField(_('keigen flg'))

    class Meta:
        db_table = 'mm_tax'


class FuhoSampleMaster(models.Model):
    sentence = models.TextField(_('sentence'))

    class Meta:
        db_table = 'mm_fuho_sample'


class ChobunDaishiMaster(models.Model):
    name = models.TextField(_('name'))
    file_name = models.ImageField(_('file name'), upload_to='chobun_daishi', blank=True, null=True)

    class Meta:
        db_table = 'mm_chobun_daishi'
        ordering = ['id']


class ChobunSample(models.Model):
    type_id = models.IntegerField(_('type id'))
    name = models.TextField(_('name'))
    sentence = models.TextField(_('sentence'))

    class Meta:
        db_table = 'mm_chobun_sample'


class Relationship(models.Model):
    name = models.TextField(_('name'))

    class Meta:
        db_table = 'mm_relationship'


class PaymentType(models.Model):
    name = models.TextField(_('name'))
    commission_pct = models.DecimalField(
        _('commission percentage'), max_digits=3, decimal_places=1, blank=True, null=True
    )

    class Meta:
        db_table = 'mm_payment'


class HenreihinParameter(models.Model):
    henreihin_omote_default = models.TextField()
    henreihin_mizuhiki_default = models.TextField()
    henreihin_hoso_default = models.TextField()

    class Meta:
        db_table = 'mm_henreihin_parameter'


class KodenParameter(models.Model):
    koden_commission_tax_pct = models.IntegerField()
    koden_upper_limit = models.IntegerField()

    class Meta:
        db_table = 'mm_koden_parameter'


class TermUnitType(models.IntegerChoices):
    DAYS = 1, _('days')
    MONTHS = 2, _('months')
    YEARS = 3, _('years')


class HoyoDefaultMaster(models.Model):
    hoyo_style = models.ForeignKey(
        HoyoStyle, models.PROTECT, verbose_name=_('hoyo default'), related_name='defaults'
    )
    name = models.TextField(_('name'))
    elapsed_time = models.IntegerField(_('elapsed time'))
    unit = models.IntegerField(_('unit'), choices=TermUnitType.choices)

    class Meta:
        db_table = 'mm_hoyo_default'


class HoyoSampleMaster(models.Model):
    sentence = models.TextField(_('sentence'))

    class Meta:
        db_table = 'mm_hoyo_sample'


class SokeMenu(models.Model):
    name = models.TextField(_('name'))
    url = models.TextField(_('url'))
    disp_no = models.IntegerField(_('disp number'), blank=True, null=True)
    required_flg = models.BooleanField(_('required flg'))

    class Meta:
        db_table = 'mm_soke_menu'
