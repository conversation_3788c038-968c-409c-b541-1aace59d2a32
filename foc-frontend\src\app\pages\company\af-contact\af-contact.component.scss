.contents {
  max-width: 500px !important;
  .search_area .line com-dropdown {
    width: 50%;
  }
  .menu_title {
    .icon.big {
      margin-right: 20px;
    }
    .icon.combo {
      position: absolute;
      left: 45px;
    }
  }
  .table_fixed.body {
    max-height: calc(100% - 180px) !important;
  }
  >.table_fixed tr {
    .display_num {
      width: 60px;
    }
    .operation {
      width: 60px;
    }
  }
}
.ui.modal.mini {
  width: 470px !important;
  .ui.header {
    display: flex;
    .icon.large {
      margin-right: 15px;
    }
    .icon.combo {
      position: absolute;
      left: 40px;
    }
    >span {
      min-width: 167px;
    }
  }
  .title {
    font-size: smaller;
    margin-left: 20px;
    border: none;
    padding-bottom: 0;
    .data {
      margin-left: 10px;
      font-weight: normal;
    }
  }

}