[tool.poetry]
name = "foc-backend"
version = "0.1.0"
description = "Funeral Online system Connect Backend"
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]

[tool.poetry.dependencies]
python = ">=3.8,<3.10"
cryptography = "^3.3.1"
Django = "^3.1.14"
django-cors-headers = "^3.6.0"
django-environ = "^0.4.5"
django-filter = "^2.4.0"
django-mptt = "^0.11.0"
djangorestframework = "^3.12.2"
djangorestframework-simplejwt = "^4.6.0"
django-sendgrid-v5 = "^0.9.0"
django-sequences = "^2.5"
drf-extra-fields = "^3.0.4"
gunicorn = "^20.0.4"
Markdown = "^3.3.3"
Pillow = "^9.0.1"
psycopg2-binary = "^2.8.6"
Pygments = "^2.7.3"
python-dateutil = "^2.8.1"
qrcode = "^6.1"
requests = "^2.25.1"
uvicorn = {extras = ["standard"], version = "^0.13.3"}
WeasyPrint = "^52.2"
whitenoise = "^5.2.0"

[tool.poetry.dev-dependencies]
coverage = {extras = ["toml"], version = "^5.3.1"}
django-silk = "^4.1.0"
factory-boy = "^3.2.0"
ffffff = "^2020.8.31"
flake8 = "^3.8.4"
invoke = "^1.5.0"
isort = {extras = ["pyproject"], version = "^5.7.0"}
radon = {extras = ["flake8"], version = "^4.3.2"}

[tool.black]
line-length = 99
target-version = ['py38']
include = '\.pyi?$'
exclude = '''
(
  /(
      \.eggs         # exclude a few common directories in the
    | \.git          # root of the project
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
)
'''

[tool.isort]
line_length = 99
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
skip_glob = ['.venv/*']

[tool.coverage.run]
source = ['.']
omit = [
    '.venv/*',
    '*/tests/*',
    '*/migrations/*',
    'manage.py',
    'tasks.py',
    '*/apps.py',
    '*/admin.py',
    'foc/*',
]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
