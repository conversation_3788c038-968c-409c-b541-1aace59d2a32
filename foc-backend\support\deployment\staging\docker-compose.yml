version: "3.8"
services:
  traefik:
    image: traefik:v2.3
    ports:
      - 80:80
      - 443:443
      - 8080:8080
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik-dynamic.yml:/etc/traefik/dynamic.yml:ro
      - traefik-acme:/etc/traefik/acme
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.file.filename=/etc/traefik/dynamic.yml"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      # - "--accesslog=true"
      - "--log.level=INFO"
      - "--certificatesresolvers.focresolver.acme.httpchallenge=true"
      - "--certificatesresolvers.focresolver.acme.httpchallenge.entrypoint=web"
      - "--certificatesresolvers.focresolver.acme.keyType=EC256"
      # - "--certificatesresolvers.focresolver.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory"
      - "--certificatesresolvers.focresolver.acme.email=<EMAIL>"
      - "--certificatesresolvers.focresolver.acme.storage=/etc/traefik/acme/acme.json"
    restart: unless-stopped

  db:
    image: postgres:12
    ports:
      - 5432:5432
    volumes:
      - type: bind
        source: /var/lib/postgresql/data
        target: /var/lib/postgresql/data
    environment:
      POSTGRES_USER: foc
      POSTGRES_PASSWORD: RsP3HExHVC6caLY2ZdWuncBo
    restart: unless-stopped

  backend:
    image: foc-backend
    build:
      context: ./backend/
    environment:
      DATABASE_URL: *************************************************/foc
      SECRET_KEY: l&uc^39mry24&#le^cj0=0oviq8+4&wgl0iyxtx9uy%wvtz4_(
      DEBUG: 1
      ALLOWED_HOSTS: "*"
      CORS_ORIGIN_ALLOW_ALL: 1
      FRONTEND_HOSTNAME: https://stg.foc.jpn.com/
      JWT_EXPIRATION_MINUTES: 5
      JWT_REFRESH_EXPIRATION_DAYS: 1
      JWT_ISSUER: Focus inc.
      JWT_AUDIENCE: FOC dev team
      JWT_PRIVATE_KEY_FILE: /usr/src/app/jwt-rsa
      ROOT_PATH: /api
      STATIC_ROOT: /usr/src/app/static
      MEDIA_ROOT: /usr/src/app/media
      MEDIA_URL: /media/
      USE_TLS: 1
      EMAIL_VIA_SENDGRID: 1
      SENDGRID_API_KEY: *********************************************************************
      SENDGRID_SANDBOX_MODE_IN_DEBUG: 0
      SENDGRID_TRACK_EMAIL_OPENS: 0
      SENDGRID_TRACK_CLICKS_HTML: 0
      SENDGRID_TRACK_CLICKS_PLAIN: 0
      EMAIL_FROM: <EMAIL>
      WEB_CONCURRENCY: 2
      FORCE_SCRIPT_NAME: /api
    volumes:
      - type: bind
        source: ./jwt-rsa
        target: /usr/src/app/jwt-rsa
      - type: bind
        source: /opt/foc/uploads
        target: /usr/src/app/media
    depends_on:
      - db
    labels:
      traefik.enable: "true"
      traefik.http.routers.backend.rule: Host(`stg.foc.jpn.com`) && PathPrefix(`/api`)
      traefik.http.routers.backend.entrypoints: web
      traefik.http.routers.backend.middlewares: https_redirect,deployApi
      traefik.http.routers.backend-tls.rule: Host(`stg.foc.jpn.com`) && PathPrefix(`/api`)
      traefik.http.routers.backend-tls.entrypoints: websecure
      traefik.http.routers.backend-tls.tls.certresolver: focresolver
      traefik.http.routers.backend-tls.middlewares: deployApi
      traefik.http.middlewares.deployApi.stripPrefix.prefixes: /api
      traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
      traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
    restart: unless-stopped

  frontend:
    image: foc-frontend
    build:
      context: ./frontend
    environment:
      DAPHNE_HOST: backend
      DAPHNE_PORT: 8000
      URLS: api|static|_ping
      TIMEOUT: 600
      DEBUG_LOG: "true"
      API_URL: /api
      CARD_TOKEN_URL: https://beta.epsilon.jp/js/token.js
    labels:
      traefik.enable: "true"
      traefik.http.routers.frontend.rule: Host(`stg.foc.jpn.com`)
      traefik.http.routers.frontend.entrypoints: web
      traefik.http.routers.frontend.middlewares: https_redirect, test-auth
      traefik.http.middlewares.test-auth.basicauth.users: msi:$$apr1$$Bl.RBMJo$$GDShnvFEncoZU5mMB054d/
      traefik.http.routers.frontend-tls.rule: Host(`stg.foc.jpn.com`)
      traefik.http.routers.frontend-tls.entrypoints: websecure
      traefik.http.routers.frontend-tls.tls.certresolver: focresolver
      traefik.http.routers.frontend-tls.middlewares: test-auth
      traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
      traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
    volumes:
      - type: bind
        source: /opt/foc/uploads
        target: /usr/share/nginx/html/media
        read_only: true
    restart: unless-stopped

volumes:
  traefik-acme:
