# Generated by Django 3.1.2 on 2020-10-30 02:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bases', '0008_delete_fuhosample'),
        ('masters', '0007_service_tax'),
    ]

    operations = [
        migrations.CreateModel(
            name='Item',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('hinban', models.TextField(verbose_name='hinban')),
                ('name', models.TextField(verbose_name='name')),
                ('item_price', models.IntegerField(verbose_name='item price')),
                ('begin_date', models.DateField(verbose_name='begin date')),
                ('end_date', models.DateField(verbose_name='end date')),
                ('del_flg', models.BooleanField(default=False, verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('create_user_id', models.IntegerField(verbose_name='created by')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('update_user_id', models.IntegerField(verbose_name='updated by')),
                (
                    'base',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='items',
                        to='bases.base',
                        verbose_name='base company',
                    ),
                ),
                (
                    'service',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='items',
                        to='masters.service',
                        verbose_name='service',
                    ),
                ),
                (
                    'tax',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='items',
                        to='masters.tax',
                        verbose_name='tax',
                    ),
                ),
            ],
            options={
                'db_table': 'm_item',
            },
        ),
    ]
