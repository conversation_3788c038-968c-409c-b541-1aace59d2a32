import factory
import faker
from django.utils import timezone
from factory.django import DjangoModelFactory
from factory.fuzzy import FuzzyInteger

from bases.tests.factories import BaseFactory
from faqs.models import Faq

tz = timezone.get_current_timezone()
fake_provider = faker.Faker('ja_JP')


class FaqFactory(DjangoModelFactory):
    class Meta:
        model = Faq

    id = factory.Sequence(lambda n: n)
    company = factory.SubFactory(BaseFactory)
    question = factory.Faker('sentence', locale='ja_JP')
    answer = factory.Faker('paragraph', locale='ja_JP')
    display_num = factory.Faker('pyint', min_value=0, max_value=1000)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = FuzzyInteger(1)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = FuzzyInteger(1)
