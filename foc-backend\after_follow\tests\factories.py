import factory
from django.utils import timezone
from factory.django import DjangoModelFactory

from after_follow.models import (
    AfActivityDetail,
    AfContactWay,
    AfGroup,
    AfterFollow,
    AfWish,
    OrderChanceType,
    OrderStatusType,
    ProposalStatusType,
    SekoAf,
)
from bases.tests.factories import BaseFactory
from seko.tests.factories import SekoFactory

tz = timezone.get_current_timezone()


class AfGroupFactory(DjangoModelFactory):
    class Meta:
        model = AfGroup

    id = factory.Sequence(lambda n: n)
    department = factory.SubFactory(BaseFactory)
    name = factory.Faker('word', locale='ja_JP')
    display_num = factory.Faker('pyint', min_value=0, max_value=1000)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0


class AfterFollowFactory(DjangoModelFactory):
    class Meta:
        model = AfterFollow

    id = factory.Sequence(lambda n: n)
    af_group = factory.SubFactory(AfGroupFactory)
    name = factory.Faker('word', locale='ja_JP')
    display_num = factory.Faker('pyint', min_value=0, max_value=1000)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0


class AfContactWayFactory(DjangoModelFactory):
    class Meta:
        model = AfContactWay

    id = factory.Sequence(lambda n: n)
    company = factory.SubFactory(BaseFactory)
    contact_way = factory.Faker('word', locale='ja_JP')
    display_num = factory.Faker('pyint', min_value=0, max_value=1000)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0


class SekoAfFactory(DjangoModelFactory):
    class Meta:
        model = SekoAf

    seko = factory.SubFactory(SekoFactory)
    contact_way = factory.SubFactory(AfContactWayFactory)
    note = factory.Faker('paragraph', locale='ja_JP')
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class AfWishFactory(DjangoModelFactory):
    class Meta:
        model = AfWish

    id = factory.Sequence(lambda n: n)
    seko_af = factory.SubFactory(SekoAfFactory)
    af_type = factory.SubFactory(AfterFollowFactory)
    answered_flg = factory.Faker('random_element', elements=[True, False])
    proposal_status = factory.Faker('random_element', elements=ProposalStatusType.values)
    order_status = factory.Faker('random_element', elements=OrderStatusType.values)
    order_date = factory.Faker('past_date', start_date='-3d', tzinfo=tz)
    order_chance = factory.Faker('random_element', elements=OrderChanceType.values)
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class AfActivityDetailFactory(DjangoModelFactory):
    class Meta:
        model = AfActivityDetail

    id = factory.Sequence(lambda n: n)
    af_wish = factory.SubFactory(AfWishFactory)
    activity_ts = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    activity = factory.Faker('sentence', locale='ja_JP')
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
