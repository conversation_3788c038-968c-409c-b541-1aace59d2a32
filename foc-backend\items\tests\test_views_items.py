from typing import Dict, List

import factory
from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from items.models import Base, Item
from items.tests.factories import ItemFactory, ItemSupplierFactory
from masters.tests.factories import ServiceFactory, TaxFactory
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


class ItemListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.base_1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.base_2 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.service_1 = ServiceFactory()
        self.service_2 = ServiceFactory()
        self.item_1 = ItemFactory(base=self.base_1, service=self.service_1, hinban='1')
        self.item_2 = ItemFactory(base=self.base_1, service=self.service_2, hinban='1')
        self.item_3 = ItemFactory(base=self.base_2, service=self.service_1, hinban='2')
        self.item_4 = ItemFactory(base=self.base_2, service=self.service_2, hinban='1')

        self.staff = StaffFactory(base=self.base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_item_list_succeed(self) -> None:
        """商品一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('items:item_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)
        # id_list = [record['id'] for record in records]
        # sorted_id_list = sorted(id_list)
        # self.assertEqual(id_list, sorted_id_list)

    def test_item_list_succeed_by_company(self) -> None:
        """拠点で絞り込みした商品一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'base': self.base_1.pk,
        }
        response = self.api_client.get(reverse('items:item_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        for record in records:
            self.assertEqual(record['base'], self.base_1.pk)

    def test_item_list_succeed_by_service(self) -> None:
        """サービスで絞り込みした商品一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'service': self.service_1.pk,
        }
        response = self.api_client.get(reverse('items:item_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        for record in records:
            self.assertEqual(record['service'], self.service_1.pk)

    def test_item_list_ignores_deleted(self) -> None:
        """商品一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.item_1.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('items:item_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_item_list_succeed_by_hinban(self) -> None:
        """品番で絞り込みした商品一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'hinban': '1',
        }
        response = self.api_client.get(reverse('items:item_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        for record in records:
            self.assertEqual(record['hinban'], '1')

    def test_item_list_failed_without_auth(self) -> None:
        """商品一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('items:item_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class ItemDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.item = ItemFactory(base=base)
        ItemSupplierFactory(item=self.item)
        ItemSupplierFactory(item=self.item, default_supplier_flg=True)
        ItemSupplierFactory(item=self.item)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_item_detail_succeed(self) -> None:
        """商品詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('items:item_detail', kwargs={'pk': self.item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        saved_item = model_to_dict(self.item, exclude=['suppliers'])
        saved_item['begin_date'] = str(saved_item['begin_date'])
        saved_item['end_date'] = str(saved_item['end_date'])

        self.assertEqual(record['tax']['id'], saved_item['tax'])

        del saved_item['tax']
        for attr, value in saved_item.items():
            self.assertEqual(value, record[attr])

        saved_supplier_ids: List[int] = list(self.item.suppliers.values_list('id', flat=True))
        self.assertListEqual(record['suppliers'], saved_supplier_ids)

        self.assertEqual(len(record['item_suppliers']), 3)
        for record_item_supplier, saved_item_supplier in zip(
            record['item_suppliers'], self.item.item_suppliers.order_by('supplier__id')
        ):
            self.assertEqual(
                record_item_supplier['default_supplier_flg'],
                saved_item_supplier.default_supplier_flg,
            )
            record_supplier: Dict = record_item_supplier['supplier']
            self.assertEqual(record_supplier['id'], saved_item_supplier.supplier.pk)
            self.assertEqual(record_supplier['name'], saved_item_supplier.supplier.name)

    def test_item_detail_failed_by_notfound(self) -> None:
        """商品詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_item: Item = ItemFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('items:item_detail', kwargs={'pk': non_saved_item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_item_detail_ignores_deleted(self) -> None:
        """商品一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.item.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('items:item_detail', kwargs={'pk': self.item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_item_detail_failed_without_auth(self) -> None:
        """商品詳細取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('items:item_detail', kwargs={'pk': self.item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class ItemCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.service = ServiceFactory()
        self.tax = TaxFactory()
        self.supplier_1 = SupplierFactory(company=self.base)
        self.supplier_2 = SupplierFactory(company=self.base)
        self.default_supplier = self.supplier_2.pk

        self.item_data: Item = ItemFactory.build(
            base=self.base, service=self.service, tax=self.tax
        )

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'service': self.item_data.service.pk,
            'base': self.item_data.base.pk,
            'hinban': self.item_data.hinban,
            'name': self.item_data.name,
            'item_price': self.item_data.item_price,
            'tax': self.item_data.tax.pk,
            'begin_date': str(self.item_data.begin_date),
            'end_date': str(self.item_data.end_date),
            'del_flg': self.item_data.del_flg,
            'supplier_ids': [
                self.supplier_1.pk,
                self.supplier_2.pk,
            ],
            'default_supplier': self.default_supplier,
            'fdn_code': self.item_data.fdn_code,
            'display_num': self.item_data.display_num,
        }

    def test_item_create_succeed(self) -> None:
        """商品を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('items:item_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()

        self.assertEqual(record['tax']['id'], params['tax'])

        del params['tax']
        for attr, value in params.items():
            if attr not in ['supplier_ids', 'default_supplier']:
                self.assertEqual(value, record[attr])

        db_item: Item = Item.objects.get(pk=record['id'])
        self.assertEqual(len(record['item_suppliers']), 2)
        for record_item_supplier, saved_item_supplier in zip(
            record['item_suppliers'], db_item.item_suppliers.order_by('id')
        ):
            self.assertEqual(
                record_item_supplier['default_supplier_flg'],
                saved_item_supplier.default_supplier_flg,
            )
            record_supplier: Dict = record_item_supplier['supplier']
            self.assertEqual(record_supplier['id'], saved_item_supplier.supplier.pk)
            self.assertEqual(record_supplier['name'], saved_item_supplier.supplier.name)

        self.assertEqual(record['base'], self.base.pk)
        self.assertEqual(record['create_user_id'], self.staff.pk)
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_item_create_succeed_with_none_field(self) -> None:
        """空のフィールドで商品を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['hinban'] = None
        response = self.api_client.post(
            reverse('items:item_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_item_create_failed_by_unique_constraint(self) -> None:
        """存在してる一意な組の制約を違反して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.item_data.save()
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('items:item_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_item_create_failed_without_auth(self) -> None:
        """商品追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = model_to_dict(self.item_data)
        response = self.api_client.post(
            reverse('items:item_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class ItemUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.service = ServiceFactory()
        self.tax = TaxFactory()
        self.item = ItemFactory(base=self.base, service=self.service, tax=self.tax)

        self.supplier_1 = SupplierFactory(company=self.base)
        self.supplier_2 = SupplierFactory(company=self.base)
        self.item.suppliers.add(self.supplier_1.pk)
        self.item.suppliers.add(self.supplier_2.pk)

        self.item_data: Item = ItemFactory.build(
            base=self.base, service=self.service, tax=self.tax
        )

        self.new_item_data = ItemFactory.build(base=self.base, service=self.service, tax=self.tax)
        self.new_supplier = SupplierFactory(company=self.base)
        self.default_supplier = self.new_supplier.pk

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'service': self.new_item_data.service.pk,
            'base': self.new_item_data.base.pk,
            'hinban': self.new_item_data.hinban,
            'name': self.new_item_data.name,
            'item_price': self.new_item_data.item_price,
            'tax': self.new_item_data.tax.pk,
            'begin_date': str(self.new_item_data.begin_date),
            'end_date': str(self.new_item_data.end_date),
            'del_flg': self.new_item_data.del_flg,
            'display_num': self.new_item_data.display_num,
            'supplier_ids': [
                self.supplier_1.pk,
                self.new_supplier.pk,
            ],
            'default_supplier': self.default_supplier,
        }

    def test_item_update_succeed(self) -> None:
        """商品を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('items:item_detail', kwargs={'pk': self.item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        self.assertEqual(record['tax']['id'], params['tax'])

        del params['tax']
        for attr, value in params.items():
            if attr not in ['supplier_ids', 'default_supplier']:
                self.assertEqual(value, record[attr])

        db_item: Item = Item.objects.get(pk=record['id'])
        self.assertEqual(len(record['item_suppliers']), 2)
        for record_item_supplier, saved_item_supplier in zip(
            record['item_suppliers'], db_item.item_suppliers.order_by('id')
        ):
            self.assertEqual(
                record_item_supplier['default_supplier_flg'],
                saved_item_supplier.default_supplier_flg,
            )
            record_supplier: Dict = record_item_supplier['supplier']
            self.assertEqual(record_supplier['id'], saved_item_supplier.supplier.pk)
            self.assertEqual(record_supplier['name'], saved_item_supplier.supplier.name)

        self.assertEqual(record['create_user_id'], self.item.create_user_id)
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_item_update_failed_by_notfound(self) -> None:
        """商品更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_item: Item = ItemFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('items:item_detail', kwargs={'pk': non_saved_item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_item_update_ignores_deleted(self) -> None:
        """商品更新APIが無効化された商品を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.item.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('items:item_detail', kwargs={'pk': self.item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_item_update_failed_by_unique_constraint(self) -> None:
        """存在してる一意な組の制約を違反して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()

        saved_item = ItemFactory()
        params['base'] = saved_item.base.pk
        params['hinban'] = saved_item.hinban
        response = self.api_client.put(
            reverse('items:item_detail', kwargs={'pk': self.item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_item_update_failed_without_auth(self) -> None:
        """商品更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('items:item_detail', kwargs={'pk': self.item.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class ItemDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.item: Item = ItemFactory(base=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_item_delete_succeed(self) -> None:
        """商品を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('items:item_detail', kwargs={'pk': self.item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 商品のdel_flgがTrueになるだけ
        db_item: Item = Item.objects.get(pk=self.item.pk)
        self.assertTrue(db_item.del_flg)
        self.assertEqual(db_item.create_user_id, self.item.create_user_id)
        self.assertEqual(db_item.update_user_id, self.staff.pk)

    def test_item_delete_failed_by_notfound(self) -> None:
        """商品削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_item: Item = ItemFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('items:item_detail', kwargs={'pk': non_saved_item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_item_delete_failed_by_already_deleted(self) -> None:
        """商品削除APIが論理削除済みの商品を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.item.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('items:item_detail', kwargs={'pk': self.item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_item_delete_failed_without_auth(self) -> None:
        """商品削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('items:item_detail', kwargs={'pk': self.item.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class ItemBulkUpsertViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.item_11: Item = ItemFactory(base=self.base_1, hinban='1')
        self.item_12: Item = ItemFactory(base=self.base_1, hinban='2')

        self.base_2 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.item_21: Item = ItemFactory(base=self.base_2, hinban='1')
        self.item_22: Item = ItemFactory(base=self.base_2, hinban='2', del_flg=True)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> List:
        # 存在してる「拠点ID、品番」で更新データを作成する
        self.item_11_update = factory.build(
            dict,
            FACTORY_CLASS=ItemFactory,
            base=self.item_11.base.pk,
            hinban=self.item_11.hinban,
            service=self.item_12.service.pk,
            tax=self.item_12.tax.pk,
        )
        del self.item_11_update['id']
        del self.item_11_update['created_at']
        del self.item_11_update['create_user_id']
        del self.item_11_update['updated_at']
        del self.item_11_update['update_user_id']

        # 存在してない「拠点ID、品番」で登録データを作成する
        self.item_23_create = factory.build(
            dict,
            FACTORY_CLASS=ItemFactory,
            base=self.base_2.pk,
            hinban='2',
            service=self.item_12.service.pk,
            tax=self.item_12.tax.pk,
        )
        del self.item_23_create['id']
        del self.item_23_create['created_at']
        del self.item_23_create['create_user_id']
        del self.item_23_create['updated_at']
        del self.item_23_create['update_user_id']

        return [
            self.item_11_update,
            self.item_23_create,
        ]

    def test_item_bulk_upsert_succeed(self) -> None:
        """一括で商品を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: List = self.basic_params()
        response = self.api_client.put(
            reverse('items:item_bulk_upsert'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        updated_items = [model_to_dict(item) for item in Item.objects.all().order_by('id')]

        param_items = [
            self.item_11_update,
            model_to_dict(self.item_12),
            model_to_dict(self.item_21),
            model_to_dict(self.item_22),
            self.item_23_create,
        ]

        for updated_item, param_item in zip(updated_items, param_items):
            for attr, value in param_item.items():
                self.assertEqual(value, updated_item[attr])

    def test_item_bulk_upsert_failed_with_not_exist_base(self) -> None:
        """存在してない拠点で一括で商品を追加して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        not_exist_base = BaseFactory.build(base_type=Base.OrgType.COMPANY)
        params: List = self.basic_params()
        params[0]['base'] = not_exist_base.pk
        response = self.api_client.put(
            reverse('items:item_bulk_upsert'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_item_bulk_upsert_failed_with_by_none_hinban(self) -> None:
        """品番がNoneで一括で商品追加して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: List = self.basic_params()
        params[0]['hinban'] = None
        response = self.api_client.put(
            reverse('items:item_bulk_upsert'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('hinban'))

    def test_item_bulk_upsert_failed_without_auth(self) -> None:
        """一括商品登録APIがAuthorizationヘッダがなくて失敗する"""
        params: List = self.basic_params()
        response = self.api_client.delete(
            reverse('items:item_bulk_upsert'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
