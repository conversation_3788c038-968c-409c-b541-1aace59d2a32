
<div class="container">
  <div class="inner">
    <div class="contents header">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="contents description">
      <h2>申込者情報登録</h2>
      <div class="pre-wrap">{{description}}</div>
    </div>
    <div class="navigation">
      <div class="item"><span>カート</span><span>内容確認</span></div>
      <div class="arrow"></div>
      <div class="item current"><span>申込者</span><span>情報登録</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込内容</span><span>確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>お支払い</span><span>手続き</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込み</span><span>完了</span></div>
    </div>
    <div class="contents">
      <div class="input-area big">
        <div class="line">
          <div class="label required">氏名</div>
          <div class="input entry_name" #entryNameEm [class.error]="isErrorField(entryNameEm)">
            <input type="text" placeholder="田中　太郎" [(ngModel)]="entry.entry_name">
          </div>
        </div>
        <div class="line">
          <div class="label required">氏名カナ</div>
          <div class="input entry_name_kana" #entryNameKanaEm [class.error]="isErrorField(entryNameKanaEm)">
            <input type="text" placeholder="タナカ　タロウ" [(ngModel)]="entry.entry_name_kana">
          </div>
        </div>
        <div class="description">
          ※海外の住所はご入力いただけません。
        </div>
        <div class="line">
          <div class="label required">郵便番号</div>
          <div class="input entry_zip_code" #entryZipCodeEm [class.error]="isErrorField(entryZipCodeEm)">
            <input type="tel" placeholder="1234567" autocomplete="off" maxlength="7" [(ngModel)]="entry.entry_zip_code" (input)="zipcodeChange()">
          </div>
        </div>
        <div class="line">
          <div class="label required">都道府県</div>
          <div class="input entry_prefecture" #entryPrefectureEm [class.error]="isErrorField(entryPrefectureEm)">
            <input type="text" placeholder="東京都" [(ngModel)]="entry.entry_prefecture">
          </div>
        </div>
        <div class="line">
          <div class="label required">市区町村</div>
          <div class="input entry_address_1" #entryAddress1Em [class.error]="isErrorField(entryAddress1Em)">
            <input type="text" placeholder="新宿区新宿" [(ngModel)]="entry.entry_address_1">
          </div>
        </div>
        <div class="line">
          <div class="label required">所番地</div>
          <div class="input entry_address_2" #entryAddress2Em [class.error]="isErrorField(entryAddress2Em)">
            <input type="text" placeholder="1-1-1" [(ngModel)]="entry.entry_address_2">
          </div>
        </div>
        <div class="line">
          <div class="label">建屋名</div>
          <div class="input">
            <input type="text" placeholder="新宿こころビル１階" [(ngModel)]="entry.entry_address_3">
          </div>
        </div>
        <div class="line">
          <div class="label required">電話番号</div>
          <div class="input entry_tel" #entryTelEm [class.error]="isErrorField(entryTelEm)">
            <input type="tel" placeholder="03-1234-5678" autocomplete="off" maxlength="15" [(ngModel)]="entry.entry_tel">
          </div>
        </div>
        <div class="line">
          <div class="label required huge">メールアドレス</div>
          <div class="input entry_mail_address" #entryMailAddressEm [class.error]="isErrorField(entryMailAddressEm)">
            <input type="email" placeholder="<EMAIL>" [(ngModel)]="entry.entry_mail_address">
          </div>
        </div>
        <div class="line">
          <div class="label required huge">メールアドレス(確認用)</div>
          <div class="input entry_mail_address_confirm" #entryMailAddressConfirmEm [class.error]="isErrorField(entryMailAddressConfirmEm)">
            <input type="email" placeholder="<EMAIL>" [(ngModel)]="entry.entry_mail_address_confirm">
          </div>
        </div>
        <div class="line">
          <div class="label required huge">個人情報のお取扱いについて</div>
          <div class="input approve_flg" #approveFlgEm [class.error]="isErrorField(approveFlgEm)">
            <div class="description">個人情報の取扱いにきましては、<br>
            「<a target="_blank" routerLink="/privacypolicy">プライバシーポリシー</a>」をご参照ください。</div>
            <div class="ui checkbox">
              <input type="checkbox" [(ngModel)]="approve_flg">
              <label>個人情報の取扱いに同意する</label>
            </div>
          </div>
        </div>
        <div class="line">
          <div class="label huge"></div>
          <div class="input approve_use_flg" #approveUseFlgEm [class.error]="isErrorField(approveUseFlgEm)">
            <div class="description"><a target="_blank" routerLink="/userpolicy">ご利用規約</a></div>
            <div class="ui checkbox">
              <input type="checkbox" [(ngModel)]="approve_use_flg">
              <label>ご利用規約に同意する</label>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="button-area">
      <a class="button" (click)="back()">< 戻る</a>
      <a class="button grey" (click)="saveData()" [class.disabled]="!approve_flg || !approve_use_flg">次へ ></a>
    </div>
  </div>
</div>
