from django.urls import path

from henrei import views

app_name = 'henrei'
urlpatterns = [
    path('add/', views.OrderHenreiCreate.as_view(), name='henrei_create'),
    path('list/', views.HenreiList.as_view(), name='henrei_list'),
    path('orders/', views.OrderHenreiList.as_view(), name='order_list'),
    path('orders/<int:pk>/', views.OrderHenreiDetail.as_view(), name='order_detail'),
    path('orders/<int:pk>/pdf/', views.OrderHenreiPDF.as_view(), name='henrei_pdf'),
    path('orders/<int:pk>/fax/', views.OrderHenreiPDFFax.as_view(), name='henrei_fax'),
    path(
        'orders/order_status/',
        views.OrderHenreiUpdateStatus.as_view(),
        name='henrei_order_update_status',
    ),
    path('koden/', views.HenreiKodenList.as_view(), name='henrei_koden_list'),
    path('koden/<int:pk>/', views.HenreiKodenDetail.as_view(), name='henrei_koden_detail'),
    path('kumotsu/', views.HenreiKumotsuList.as_view(), name='henrei_kumotsu_list'),
    path('kumotsu/<int:pk>/', views.HenreiKumotsuDetail.as_view(), name='henrei_kumotsu_detail'),
    path('place/', views.PlaceHenreiOrder.as_view(), name='place_henrei_order'),
]
