from typing import Optional

import factory
import factory.fuzzy as fuzzy
import faker
from django.utils import timezone
from factory.django import DjangoModelFactory

from items.tests.factories import ItemFactory
from masters.tests.factories import ChobunDaishiMasterFactory, PaymentTypeFactory
from orders.models import (
    Entry,
    EntryDetail,
    EntryDetailChobun,
    EntryDetailKoden,
    EntryDetailKumotsu,
    EntryDetailMsg,
)
from seko.tests.factories import SekoFactory
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory

fake_provider = faker.Faker('ja_JP')
tz = timezone.get_current_timezone()


class EntryFactory(DjangoModelFactory):
    class Meta:
        model = Entry

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    payment = factory.SubFactory(PaymentTypeFactory)
    entry_name = factory.Faker('name', locale='ja_JP')
    entry_name_kana = factory.Faker('kana_name', locale='ja_JP')
    entry_zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    entry_prefecture = factory.Faker('prefecture', locale='ja_JP')
    entry_address_1 = factory.Faker('city', locale='ja_JP')
    entry_address_2 = factory.Faker('town', locale='ja_JP')
    entry_address_3 = factory.Faker('chome', locale='ja_JP')
    entry_tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    entry_mail_address = factory.Faker('ascii_safe_email')
    entry_ts = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    cancel_ts: Optional[timezone.datetime] = None
    receipt_count = 0
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class EntryDetailFactory(DjangoModelFactory):
    class Meta:
        model = EntryDetail

    id = factory.Sequence(lambda n: n)
    entry = factory.SubFactory(EntryFactory)
    item = factory.SubFactory(ItemFactory)
    item_hinban = factory.Faker('ean', locale='ja_JP', length=8)
    item_name = factory.Faker('word', locale='ja_JP')
    item_unit_price = factory.Faker('pyint', min_value=0, max_value=9999999)
    item_tax = factory.Faker('pyint', min_value=0, max_value=9999999)
    item_tax_pct = factory.Faker('pyint', min_value=0, max_value=20)
    keigen_flg = fuzzy.FuzzyChoice([True, False])
    quantity = factory.Faker('pyint', min_value=0, max_value=99)
    tax_adjust = factory.Faker('pyint', min_value=0, max_value=9999999)
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class EntryDetailChobunFactory(DjangoModelFactory):
    class Meta:
        model = EntryDetailChobun

    entry_detail = factory.SubFactory(EntryDetailFactory)
    okurinushi_company = factory.Faker('company', locale='ja_JP')
    okurinushi_title = factory.Faker('word', locale='ja_JP')
    okurinushi_company_kana = factory.Faker('kana_name', locale='ja_JP')
    okurinushi_name = factory.Faker('name', locale='ja_JP')
    okurinushi_kana = factory.Faker('kana_name', locale='ja_JP')
    okurinushi_zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    okurinushi_prefecture = factory.Faker('prefecture', locale='ja_JP')
    okurinushi_address_1 = factory.Faker('city', locale='ja_JP')
    okurinushi_address_2 = factory.Faker('town', locale='ja_JP')
    okurinushi_address_3 = factory.Faker('chome', locale='ja_JP')
    okurinushi_tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    renmei1 = factory.Faker('name', locale='ja_JP')
    renmei_kana1 = factory.Faker('kana_name', locale='ja_JP')
    renmei2 = factory.Faker('name', locale='ja_JP')
    renmei_kana2 = factory.Faker('kana_name', locale='ja_JP')
    atena = factory.Faker('name', locale='ja_JP')
    honbun = factory.Faker('text', max_nb_chars=500, locale='ja_JP')
    daishi = factory.SubFactory(ChobunDaishiMasterFactory)
    note = factory.Faker('sentence', locale='ja_JP')
    printed_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class EntryDetailKodenFactory(DjangoModelFactory):
    class Meta:
        model = EntryDetailKoden

    entry_detail = factory.SubFactory(EntryDetailFactory)
    koden_commission = factory.Faker('pyint', min_value=0, max_value=9999999)
    koden_commission_tax = factory.Faker('pyint', min_value=0, max_value=9999999)
    koden_commission_tax_pct = factory.Faker('pyint', min_value=1, max_value=20)
    tax_adjust = factory.Faker('pyint', min_value=0, max_value=9999999)
    henreihin_fuyo_flg = fuzzy.FuzzyChoice([True, False])
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class EntryDetailKumotsuFactory(DjangoModelFactory):
    class Meta:
        model = EntryDetailKumotsu

    entry_detail = factory.SubFactory(EntryDetailFactory)
    okurinushi_company = factory.Faker('company', locale='ja_JP')
    okurinushi_title = factory.Faker('word', locale='ja_JP')
    okurinushi_company_kana = factory.Faker('kana_name', locale='ja_JP')
    okurinushi_name = factory.Faker('name', locale='ja_JP')
    okurinushi_kana = factory.Faker('kana_name', locale='ja_JP')
    okurinushi_zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    okurinushi_prefecture = factory.Faker('prefecture', locale='ja_JP')
    okurinushi_address_1 = factory.Faker('city', locale='ja_JP')
    okurinushi_address_2 = factory.Faker('town', locale='ja_JP')
    okurinushi_address_3 = factory.Faker('chome', locale='ja_JP')
    okurinushi_tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    renmei1 = factory.Faker('name', locale='ja_JP')
    renmei_kana1 = factory.Faker('kana_name', locale='ja_JP')
    renmei2 = factory.Faker('name', locale='ja_JP')
    renmei_kana2 = factory.Faker('kana_name', locale='ja_JP')
    note = factory.Faker('sentence', locale='ja_JP')
    supplier = factory.SubFactory(SupplierFactory)
    order_status = 0
    order_ts = factory.Faker('date_time_between', start_date='-30d', end_date='-20d', tzinfo=tz)
    delivery_ts = factory.Faker('date_time_between', start_date='-10d', tzinfo=tz)
    order_staff = factory.SubFactory(StaffFactory)
    order_note = factory.Faker('text', locale='ja_JP')
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class EntryDetailMsgFactory(DjangoModelFactory):
    class Meta:
        model = EntryDetailMsg

    entry_detail = factory.SubFactory(EntryDetailFactory)
    relation_ship = factory.Faker('word', locale='ja_JP')
    honbun = factory.Faker('paragraph', locale='ja_JP')
    release_status = 1
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
