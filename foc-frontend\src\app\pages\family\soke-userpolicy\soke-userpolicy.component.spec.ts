import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SokeUserpolicyComponent } from './soke-userpolicy.component';

describe('SokeUserpolicyComponent', () => {
  let component: SokeUserpolicyComponent;
  let fixture: ComponentFixture<SokeUserpolicyComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SokeUserpolicyComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SokeUserpolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
