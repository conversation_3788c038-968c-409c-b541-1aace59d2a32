import json
from unittest import mock

import faker
from django.test import TestCase

from seko.models import Kojin, Moshu, Seko
from seko.tests.factories import KojinFactory, MoshuFactory, SekoFactory
from utils.sms import SmsResponseData, compile_message, register_send_sms

fake_provider = faker.Faker(locale='ja_JP')


class SmsRequestTest(TestCase):
    @mock.patch('utils.sms.requests.post')
    def test_send_sms_success(self, mock_post):
        """SMS送信登録成功のシミュレート"""
        phone_number = fake_provider.phone_number().replace('-', '')
        intl_phone_number = f"+81{phone_number.lstrip('0')}"
        message = fake_provider.text(max_nb_chars=60)

        mock_post.return_value.status_code = 200
        mock_post.return_value.text = (
            '{'
            '"responseCode":0,'
            '"responseMessage":"Success.",'
            f'"phoneNumber":"{intl_phone_number}",'
            f'"smsMessage":"{message}"'
            '}'
        )
        mock_post.return_value.json.return_value = json.loads(mock_post.return_value.text)
        result: SmsResponseData = register_send_sms(
            token=fake_provider.uuid4(cast_to=None).hex,
            client_id=fake_provider.numerify(text='###'),
            sms_code=fake_provider.numerify(text='#####'),
            message=message,
            phone_number=phone_number,
        )
        self.assertEqual(result.response_code, 0)
        self.assertEqual(result.phone_number, intl_phone_number)

    @mock.patch('utils.sms.requests.post')
    def test_send_sms_failure_by_message_too_long(self, mock_post):
        """SMS送信登録失敗のシミュレート"""
        phone_number = fake_provider.phone_number().replace('-', '')
        intl_phone_number = f"+81{phone_number.lstrip('0')}"
        message = fake_provider.text(max_nb_chars=100).replace('\n', '')

        mock_post.return_value.status_code = 200
        mock_post.return_value.text = (
            '{'
            '"responseCode":120,'
            '"responseMessage":"Success.",'
            f'"phoneNumber":"{intl_phone_number}",'
            f'"smsMessage":"{message}"'
            '}'
        )
        mock_post.return_value.json.return_value = json.loads(mock_post.return_value.text)
        result: SmsResponseData = register_send_sms(
            token=fake_provider.uuid4(cast_to=None).hex,
            client_id=fake_provider.numerify(text='###'),
            sms_code=fake_provider.numerify(text='#####'),
            message=message,
            phone_number=phone_number,
        )
        self.assertEqual(result.response_code, 120)
        self.assertEqual(result.phone_number, intl_phone_number)


class CompileMessageTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko: Seko = SekoFactory()
        self.moshu: Moshu = MoshuFactory(seko=self.seko)
        self.kojin_1: Kojin = KojinFactory(seko=self.seko, kojin_num=1)
        self.kojin_2: Kojin = KojinFactory(seko=self.seko, kojin_num=2)

    def test_compile_message(self) -> None:
        """メッセージの特定文字列を施行の情報に置換する"""
        message_template: str = 'moshu=@m,kojin1=@k1,kojin2=@k2'
        self.assertEqual(
            compile_message(message_template, self.seko),
            f'moshu={self.moshu.name},kojin1={self.kojin_1.name},kojin2={self.kojin_2.name}',
        )

        # 故人が登録されていない場合は空文字に置き換える
        self.kojin_1.delete()
        self.kojin_2.delete()
        self.assertEqual(
            compile_message(message_template, self.seko),
            f'moshu={self.moshu.name},kojin1=,kojin2=',
        )
