# Generated by Django 3.1.5 on 2021-01-13 02:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bases', '0013_smsaccount'),
        ('hoyo', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='HoyoSample',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('sentence', models.TextField(verbose_name='sentence')),
                ('display_num', models.IntegerField(verbose_name='display order')),
                ('del_flg', models.BooleanField(default=False, verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('create_user_id', models.IntegerField(verbose_name='created by')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('update_user_id', models.IntegerField(verbose_name='updated by')),
                (
                    'company',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='hoyo_samples',
                        to='bases.base',
                        verbose_name='company',
                    ),
                ),
            ],
            options={
                'db_table': 'm_hoyo_sample',
            },
        ),
    ]
