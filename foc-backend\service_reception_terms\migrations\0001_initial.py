# Generated by Django 3.1.2 on 2020-11-06 07:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('masters', '0008_relationship'),
        ('bases', '0011_auto_20201104_1540'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceReceptionTerm',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('limit_time', models.IntegerField(verbose_name='limit time')),
                (
                    'unit',
                    models.IntegerField(choices=[(1, 'Date'), (2, 'Hour')], verbose_name='unit'),
                ),
                ('limit_hour', models.IntegerField(verbose_name='limit hour')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('create_user_id', models.IntegerField(verbose_name='created by')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('update_user_id', models.IntegerField(verbose_name='updated by')),
                (
                    'department',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='service_reception_terms',
                        to='bases.base',
                        verbose_name='department',
                    ),
                ),
                (
                    'schedule',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='service_reception_terms',
                        to='masters.schedule',
                        verbose_name='schedule',
                    ),
                ),
                (
                    'service',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='service_reception_terms',
                        to='masters.service',
                        verbose_name='service',
                    ),
                ),
            ],
            options={
                'db_table': 'm_service_reception_term',
            },
        ),
    ]
