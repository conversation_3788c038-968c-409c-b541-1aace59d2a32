from base64 import b64encode
from typing import Dict, List

import factory
from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import (
    BaseFactory,
    BaseServiceFactory,
    FocFeeFactory,
    SmsAccountFactory,
    TokushoFactory,
)
from masters.tests.factories import ChobunDaishiMasterFactory, ScheduleFactory, ServiceFactory
from service_reception_terms.tests.factories import ServiceReceptionTermFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class BaseFullListViewTest(TestCase):
    def add_followings(self, base: Base) -> None:
        TokushoFactory(company=base)
        FocFeeFactory(company=base)
        SmsAccountFactory(company=base)
        service_1 = ServiceFactory()
        service_2 = ServiceFactory()
        BaseServiceFactory(base=base, service=service_2)
        BaseServiceFactory(base=base, service=service_1)

    def setUp(self):
        super().setUp()

        self.company_base_1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.add_followings(self.company_base_1)
        self.department_base_1 = BaseFactory(
            base_type=Base.OrgType.DEPARTMENT, parent=self.company_base_1
        )
        self.add_followings(self.department_base_1)
        self.department_base_2 = BaseFactory(
            base_type=Base.OrgType.DEPARTMENT, parent=self.company_base_1
        )
        self.add_followings(self.department_base_2)
        self.hall_base_1 = BaseFactory(base_type=Base.OrgType.HALL, parent=self.department_base_1)
        self.add_followings(self.hall_base_1)
        self.hall_base_2 = BaseFactory(base_type=Base.OrgType.HALL, parent=self.department_base_2)
        self.add_followings(self.hall_base_2)
        self.other_base_1 = BaseFactory(base_type=Base.OrgType.OTHER, parent=self.hall_base_1)
        self.add_followings(self.other_base_1)
        self.other_base_2 = BaseFactory(base_type=Base.OrgType.OTHER, parent=self.hall_base_2)
        self.add_followings(self.other_base_2)
        self.other_base_3 = BaseFactory(base_type=Base.OrgType.OTHER, parent=self.hall_base_2)
        self.add_followings(self.other_base_3)
        self.company_base_2 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.add_followings(self.company_base_2)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.company_base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_all_base_list_succeed(self) -> None:
        """拠点一覧APIは拠点を階層構造の形で返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('bases:list_all_bases'), data=params, format='json')

        # IDとchildrenの要素数を確認
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)
        for company in records:
            self.assertGreater(len(company.get('tokusho', {})), 0)
            self.assertGreater(len(company.get('focfee', {})), 0)
            self.assertGreater(len(company.get('sms_account', {})), 0)
            self.assertEqual(len(company['services']), 2)
            self.assertLess(company['services'][0]['id'], company['services'][1]['id'])

            departments: List = company.get('children', [])
            if company.get('id') == self.company_base_1.id:
                self.assertEqual(len(departments), 2)
                for department in departments:
                    self.assertGreater(len(department.get('tokusho', {})), 0)
                    self.assertGreater(len(department.get('focfee', {})), 0)
                    self.assertGreater(len(department.get('sms_account', {})), 0)
                    self.assertGreater(len(department.get('services', {})), 0)

                    halls: List = department.get('children', [])
                    self.assertEqual(len(halls), 1)
                    if department.get('id') == self.department_base_1.id:
                        self.assertEqual(len(halls[0].get('children', [])), 1)
                    else:
                        self.assertEqual(len(halls[0].get('children', [])), 2)

    def test_all_base_list_excludes_del_flg(self) -> None:
        """拠点一覧APIは論理削除済みの拠点を除外する"""
        self.department_base_1.del_flg = True
        self.department_base_1.save()
        self.company_base_2.del_flg = True
        self.company_base_2.save()

        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('bases:list_all_bases'), data=params, format='json')

        # company_base_2、department_base_1とその子拠点は除外される
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        company = records[0]
        self.assertEqual(company.get('id'), self.company_base_1.id)
        departments: List = company.get('children', [])
        self.assertEqual(len(departments), 1)

        department: Dict = departments[0]
        self.assertEqual(department.get('id'), self.department_base_2.id)
        halls: List = department.get('children', [])
        self.assertEqual(len(halls), 1)

        hall: Dict = halls[0]
        self.assertEqual(hall.get('id'), self.hall_base_2.id)
        others: List = hall.get('children', [])
        self.assertEqual(len(others), 2)

    def test_all_base_list_failed_without_auth(self) -> None:
        """拠点一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:list_all_bases'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class BaseCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_data = BaseFactory.build()
        self.chobun_daishi_master_1 = ChobunDaishiMasterFactory()
        self.chobun_daishi_master_2 = ChobunDaishiMasterFactory()
        self.chobun_daishi_master_3 = ChobunDaishiMasterFactory()
        self.service_1 = ServiceFactory()
        self.service_2 = ServiceFactory()
        self.service_3 = ServiceFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_base_create_succeed(self) -> None:
        """拠点を追加する(親拠点なし)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.base_data)
        params.pop('id')
        params['company_logo_file'] = b64encode(params['company_logo_file'].file.read())
        params['company_map_file'] = b64encode(params['company_map_file'].file.read())
        params.pop('create_user_id')
        params.pop('update_user_id')
        params['chobun_daishi_master_ids'] = [
            self.chobun_daishi_master_1.pk,
            self.chobun_daishi_master_2.pk,
            self.chobun_daishi_master_3.pk,
        ]
        params['service_ids'] = [
            self.service_1.pk,
            self.service_2.pk,
            self.service_3.pk,
        ]

        service_reception_term_1 = factory.build(
            dict,
            FACTORY_CLASS=ServiceReceptionTermFactory,
            service=ServiceFactory(),
            schedule=ScheduleFactory(),
        )
        service_reception_term_1['service'] = service_reception_term_1['service'].pk
        service_reception_term_1['schedule'] = service_reception_term_1['schedule'].pk
        del service_reception_term_1['id']
        del service_reception_term_1['department']
        del service_reception_term_1['created_at']
        del service_reception_term_1['create_user_id']
        del service_reception_term_1['updated_at']
        del service_reception_term_1['update_user_id']

        service_reception_term_2 = factory.build(
            dict,
            FACTORY_CLASS=ServiceReceptionTermFactory,
            service=ServiceFactory(),
            schedule=ScheduleFactory(),
        )
        service_reception_term_2['service'] = service_reception_term_2['service'].pk
        service_reception_term_2['schedule'] = service_reception_term_2['schedule'].pk
        del service_reception_term_2['id']
        del service_reception_term_2['department']
        del service_reception_term_2['created_at']
        del service_reception_term_2['create_user_id']
        del service_reception_term_2['updated_at']
        del service_reception_term_2['update_user_id']

        params['service_reception_terms'] = [
            service_reception_term_1,
            service_reception_term_2,
        ]
        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), self.base_data.base_type)
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), self.base_data.company_code)
        self.assertEqual(record.get('base_name'), self.base_data.base_name)
        self.assertEqual(record.get('zip_code'), self.base_data.zip_code)
        self.assertEqual(record.get('prefecture'), self.base_data.prefecture)
        self.assertEqual(record.get('address_1'), self.base_data.address_1)
        self.assertEqual(record.get('address_2'), self.base_data.address_2)
        self.assertEqual(record.get('address_3'), self.base_data.address_3)
        self.assertEqual(record.get('tel'), self.base_data.tel)
        self.assertEqual(record.get('fax'), self.base_data.fax)
        self.assertIsNotNone(record.get('company_logo_file'))
        self.assertIsNotNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), self.base_data.calc_type)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.staff.id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)
        self.assertSetEqual(
            set(record.get('chobun_daishi_master_ids', [])),
            set(params['chobun_daishi_master_ids']),
        )
        self.assertSetEqual(set(record.get('service_ids', [])), set(params['service_ids']))

    def test_base_create_succeed_with_related_field_empty(self) -> None:
        """拠点を追加する(空の関連フィールド)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.base_data)
        params.pop('id')
        params['company_logo_file'] = b64encode(params['company_logo_file'].file.read())
        params['company_map_file'] = b64encode(params['company_map_file'].file.read())
        params.pop('create_user_id')
        params.pop('update_user_id')
        params['chobun_daishi_master_ids'] = []
        params['service_ids'] = []
        params['service_reception_terms'] = []
        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record.get('chobun_daishi_master_ids'), [])
        self.assertEqual(record.get('service_ids'), [])
        self.assertEqual(record.get('service_reception_terms'), [])

    def test_base_with_parent_create_succeed(self) -> None:
        """拠点を追加する(親拠点あり)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        parent_base: Base = BaseFactory()

        params: Dict = model_to_dict(self.base_data)
        params.pop('id')
        params['parent'] = parent_base.id
        params['company_logo_file'] = b64encode(params['company_logo_file'].file.read())
        params['company_map_file'] = b64encode(params['company_map_file'].file.read())
        params.pop('create_user_id')
        params.pop('update_user_id')
        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), self.base_data.base_type)
        self.assertEqual(record.get('parent'), parent_base.id)
        self.assertEqual(record.get('company_code'), self.base_data.company_code)
        self.assertEqual(record.get('base_name'), self.base_data.base_name)
        self.assertEqual(record.get('zip_code'), self.base_data.zip_code)
        self.assertEqual(record.get('prefecture'), self.base_data.prefecture)
        self.assertEqual(record.get('address_1'), self.base_data.address_1)
        self.assertEqual(record.get('address_2'), self.base_data.address_2)
        self.assertEqual(record.get('address_3'), self.base_data.address_3)
        self.assertEqual(record.get('tel'), self.base_data.tel)
        self.assertEqual(record.get('fax'), self.base_data.fax)
        self.assertIsNotNone(record.get('company_logo_file'))
        self.assertIsNotNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), self.base_data.calc_type)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.staff.id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_base_create_succeed_with_some_empty_field(self) -> None:
        """拠点追加APIはロゴファイル、地図ファイルが空文字でも成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.base_data)
        params.pop('id')
        params['company_logo_file'] = ''
        params['company_map_file'] = ''
        params.pop('create_user_id')
        params.pop('update_user_id')
        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), self.base_data.base_type)
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), self.base_data.company_code)
        self.assertEqual(record.get('base_name'), self.base_data.base_name)
        self.assertEqual(record.get('zip_code'), self.base_data.zip_code)
        self.assertEqual(record.get('prefecture'), self.base_data.prefecture)
        self.assertEqual(record.get('address_1'), self.base_data.address_1)
        self.assertEqual(record.get('address_2'), self.base_data.address_2)
        self.assertEqual(record.get('address_3'), self.base_data.address_3)
        self.assertEqual(record.get('tel'), self.base_data.tel)
        self.assertEqual(record.get('fax'), self.base_data.fax)
        self.assertIsNone(record.get('company_logo_file'))
        self.assertIsNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), self.base_data.calc_type)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.staff.id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_base_create_succeed_with_none_on_file_fields(self) -> None:
        """拠点追加APIはロゴファイル、地図ファイルがnoneでも成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.base_data)
        params.pop('id')
        params['company_logo_file'] = None
        params['company_map_file'] = None
        params.pop('create_user_id')
        params.pop('update_user_id')
        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), self.base_data.base_type)
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), self.base_data.company_code)
        self.assertEqual(record.get('base_name'), self.base_data.base_name)
        self.assertEqual(record.get('zip_code'), self.base_data.zip_code)
        self.assertEqual(record.get('prefecture'), self.base_data.prefecture)
        self.assertEqual(record.get('address_1'), self.base_data.address_1)
        self.assertEqual(record.get('address_2'), self.base_data.address_2)
        self.assertEqual(record.get('address_3'), self.base_data.address_3)
        self.assertEqual(record.get('tel'), self.base_data.tel)
        self.assertEqual(record.get('fax'), self.base_data.fax)
        self.assertIsNone(record.get('company_logo_file'))
        self.assertIsNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), self.base_data.calc_type)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.staff.id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_base_create_succeed_without_file_fields(self) -> None:
        """拠点追加APIはロゴファイル、地図ファイルの項目がなくても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.base_data)
        params.pop('id')
        params.pop('company_logo_file')
        params.pop('company_map_file')
        params.pop('create_user_id')
        params.pop('update_user_id')
        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), self.base_data.base_type)
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), self.base_data.company_code)
        self.assertEqual(record.get('base_name'), self.base_data.base_name)
        self.assertEqual(record.get('zip_code'), self.base_data.zip_code)
        self.assertEqual(record.get('prefecture'), self.base_data.prefecture)
        self.assertEqual(record.get('address_1'), self.base_data.address_1)
        self.assertEqual(record.get('address_2'), self.base_data.address_2)
        self.assertEqual(record.get('address_3'), self.base_data.address_3)
        self.assertEqual(record.get('tel'), self.base_data.tel)
        self.assertEqual(record.get('fax'), self.base_data.fax)
        self.assertIsNone(record.get('company_logo_file'))
        self.assertIsNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), self.base_data.calc_type)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.staff.id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_base_create_failed_with_not_exist_chobun_daishi_master(self) -> None:
        """拠点追加APIは存在しない弔文台紙が指定された場合失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.base_data)
        params.pop('id')
        params['company_logo_file'] = b64encode(params['company_logo_file'].file.read())
        params['company_map_file'] = b64encode(params['company_map_file'].file.read())
        params.pop('create_user_id')
        params.pop('update_user_id')
        params['chobun_daishi_master_ids'] = [ChobunDaishiMasterFactory.build().pk]
        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('chobun_daishi_master_ids'))

    def test_base_create_failed_with_not_exist_service(self) -> None:
        """拠点追加APIは存在しないサービスが指定された場合失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.base_data)
        params.pop('id')
        params['company_logo_file'] = b64encode(params['company_logo_file'].file.read())
        params['company_map_file'] = b64encode(params['company_map_file'].file.read())
        params.pop('create_user_id')
        params.pop('update_user_id')
        params['service_ids'] = [ServiceFactory.build().pk]
        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('service_ids'))

    def test_base_create_failed_lack_of_params(self) -> None:
        """拠点追加APIがパラメータ不足で失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}

        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        required_fields: List[str] = [
            'base_type',
            'base_name',
            'zip_code',
            'prefecture',
            'address_1',
            'tel',
            'fax',
        ]
        for field_name in required_fields:
            self.assertIsNotNone(record.get(field_name))

    def test_base_create_failed_without_auth(self) -> None:
        """拠点追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}

        response = self.api_client.post(reverse('bases:base_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
