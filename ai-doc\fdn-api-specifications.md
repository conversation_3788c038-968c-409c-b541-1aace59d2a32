# FDN API詳細仕様書

## 概要

FOCシステムのFDN（Funeral Data Network）連携APIの詳細仕様について説明します。FDNは葬儀業界の基幹システムとFOCシステム間でのデータ連携を行うためのAPIです。

## 1. `/api/seko/fdn/` - FDN施行作成API

### 1.1 基本情報

| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /api/seko/fdn/` |
| **機能** | FDNシステムから施行情報を作成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `FdnSekoCreater` |

### 1.2 会社コード判定

**会社コードの判定方法:**
```python
# seko/views.py - FdnSekoCreater.validate_data()
company: Base = Base.objects.filter(
    Q(del_flg=False),
    Q(base_type=Base.OrgType.COMPANY),
    Q(company_code=self.request.user.company_code),  # JWTから取得したユーザーの会社コード
).first()
```

**判定フロー:**
1. JWT認証でrequest.userを取得
2. `request.user.company_code`で会社コードを取得
3. Baseモデルから該当する会社を検索
4. 会社が存在しない場合はValidationError

### 1.3 リクエストパラメータ

#### 必須パラメータ
```json
{
  "fdn_seko_code": "string",        // FDN施行番号（必須）
  "order_staff_code": "string",     // 受注担当者コード（必須）
  "seko_style": "integer",          // 施行スタイルID
  "soke_name": "string",            // 葬家名
  "soke_kana": "string",            // 葬家名カナ
  // ... 他の施行情報
}
```

#### オプションパラメータ
```json
{
  "seko_department_code": "string", // 施行部門コード
  "seko_staff_code": "string",      // 施行担当者コード
  "af_staff_code": "string",        // AF担当者コード
  "schedules": [                    // スケジュール情報
    {
      "hall_code": "string",        // 式場コード
      "schedule_date": "date",
      "begin_time": "time",
      // ...
    }
  ],
  "moshu": {                        // 喪主情報
    "name": "string",
    "kana": "string",
    // ...
  },
  "kojin": [                        // 故人情報
    {
      "name": "string",
      "kana": "string",
      // ...
    }
  ]
}
```

### 1.4 バリデーション処理

#### 1.4.1 FDN施行番号の重複チェック
```python
seko: Seko = Seko.objects.filter(
    Q(del_flg=False), 
    Q(seko_company=company.id), 
    Q(fdn_code=fdn_seko_code)
).first()
if seko:
    return {'code': 'E001', 'message': f'この施行情報(施行番号={fdn_seko_code})はすでに連携済みです。'}
```

#### 1.4.2 部門コードの存在チェック
```python
department: Base = Base.objects.filter(
    Q(del_flg=False),
    Q(company_code=self.request.user.company_code),
    Q(fdn_code=seko_department_code),
).first()
if not department:
    return {'code': 'E002', 'message': f'施行部門コード({code_only})がFOCシステムに存在しません。'}
```

#### 1.4.3 担当者コードの存在チェック
```python
# 受注担当者
order_staff: Staff = Staff.objects.filter(
    Q(del_flg=False),
    Q(company_code=self.request.user.company_code),
    Q(fdn_code=order_staff_code),
).first()
if not order_staff:
    return {'code': 'E003', 'message': f'受注担当者コード({order_staff_code})がFOCシステムに存在しません。'}

# 施行担当者
if seko_staff_code:
    seko_staff: Staff = Staff.objects.filter(
        Q(del_flg=False),
        Q(company_code=self.request.user.company_code),
        Q(fdn_code=seko_staff_code),
    ).first()
    if not seko_staff:
        return {'code': 'E004', 'message': f'施行担当者コード({seko_staff_code})がFOCシステムに存在しません。'}

# AF担当者
if af_staff_code:
    af_staff: Staff = Staff.objects.filter(
        Q(del_flg=False),
        Q(company_code=self.request.user.company_code),
        Q(fdn_code=af_staff_code),
    ).first()
    if not af_staff:
        return {'code': 'E005', 'message': f'AF担当者コード({af_staff_code})がFOCシステムに存在しません。'}
```

#### 1.4.4 式場コードの存在チェック
```python
for schedule_dict in schedules_data:
    hall_code = schedule_dict.pop('hall_code', None)
    if hall_code:
        hall: Base = Base.objects.filter(
            Q(del_flg=False),
            Q(company_code=self.request.user.company_code),
            Q(fdn_code=hall_code),
        ).first()
        if not hall:
            return {'code': 'E006', 'message': f'式場コード({code_only})がFOCシステムに存在しません。'}
```

### 1.5 レスポンス

#### 成功時
```json
{
  "status": 0,
  "data": {
    "seko_id": 123
  }
}
```

#### エラー時
```json
{
  "status": 1,
  "error": {
    "code": "E001",
    "message": "この施行情報(施行番号=FDN001)はすでに連携済みです。"
  }
}
```

### 1.6 使用シリアライザー

**FdnSekoCreateSerializer:**
```python
class FdnSekoCreateSerializer(SekoRelatedSerializer):
    seko_style = serializers.IntegerField(source='seko_style_id')
    schedules = SekoScheduleSerializer(many=True)

    @transaction.atomic
    def create(self, validated_data: Dict) -> Seko:
        moshu_data = validated_data.pop('moshu', {})
        kojin_data = validated_data.pop('kojin', [])
        schedules_data = validated_data.pop('schedules', [])

        instance = super().create(validated_data)
        if moshu_data:
            Moshu.objects.create(seko=instance, **moshu_data)
        for kojin_dict in kojin_data:
            Kojin.objects.create(seko=instance, **kojin_dict)
        for schedule_dict in schedules_data:
            SekoSchedule.objects.create(seko=instance, **schedule_dict)

        return instance
```

## 2. `/api/seko/fdn/{id}/` - FDN施行更新API

### 2.1 基本情報

| 項目 | 値 |
|---|---|
| **エンドポイント** | `PATCH /api/seko/fdn/{id}/` |
| **機能** | FDN施行情報のfdn_codeを更新 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `FdnSekoUpdate` |

### 2.2 会社コード判定

**FdnSekoCreaterと同じ方式:**
```python
company: Base = Base.objects.filter(
    Q(del_flg=False),
    Q(base_type=Base.OrgType.COMPANY),
    Q(company_code=self.request.user.company_code),
).first()
```

### 2.3 リクエストパラメータ

```json
{
  "fdn_seko_code": "string"  // 新しいFDN施行番号（オプション）
}
```

### 2.4 バリデーション処理

```python
def validate_data(self, request) -> str:
    fdn_seko_code = request.data.pop('fdn_seko_code', None)
    if not fdn_seko_code:
        request.data['fdn_code'] = fdn_seko_code
        return None

    # 重複チェック
    seko: Seko = Seko.objects.filter(
        Q(del_flg=False), Q(seko_company=company.id), Q(fdn_code=fdn_seko_code)
    ).first()
    if seko:
        return {'code': 'E001', 'message': f'この施行情報(施行番号={fdn_seko_code})はすでに連携済みです。'}
    
    request.data['fdn_code'] = fdn_seko_code
    return None
```

### 2.5 使用シリアライザー

**FdnSekoUpdateSerializer:**
```python
class FdnSekoUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Seko
        fields = ['fdn_code']  # fdn_codeのみ更新可能
```

## 3. `/api/orders/fdn/` - FDN注文一覧API

### 3.1 基本情報

| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /api/orders/fdn/` |
| **機能** | FDN連携用の注文データ一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `FdnOrderList` |

### 3.2 会社コード判定

**同じ方式でcompany_codeから会社を特定:**
```python
def get_queryset(self):
    company: Base = Base.objects.filter(
        Q(del_flg=False),
        Q(base_type=Base.OrgType.COMPANY),
        Q(company_code=self.request.user.company_code),
    ).first()
    if not company:
        raise ValidationError(f'Base not found: company_code={self.request.user.company_code}')
```

### 3.3 クエリパラメータ

```
GET /api/orders/fdn/?term_from=2024-01-01T00:00:00&term_to=2024-12-31T23:59:59
```

| パラメータ | 型 | 説明 |
|---|---|---|
| `term_from` | datetime | 期間開始日時 |
| `term_to` | datetime | 期間終了日時 |

### 3.4 取得データ種別

APIは以下の注文データを統合して返します：

1. **弔文（chobun）** - `EntryDetailChobun`
2. **供花・供物（kumotsu）** - `EntryDetailKumotsu`
3. **香典（koden）** - `EntryDetailKoden`
4. **香典システム利用料** - `EntryDetailKoden`（手数料）
5. **供花・供物返礼品** - `HenreihinKumotsu`
6. **香典返礼品** - `HenreihinKoden`

### 3.5 レスポンスフィールド

```python
order_list_columns = [
    'seko_id',                    # 施行ID
    'term',                       # 期間
    'fdn_seko_code',             # FDN施行番号
    'soke_name',                 # 葬家名
    'kojin_name',                # 故人名
    'kojin_birth_date',          # 故人生年月日
    'kojin_death_date',          # 故人死亡日
    'moshu_name',                # 喪主名
    'moshu_tel',                 # 喪主電話番号
    'moshu_mobile',              # 喪主携帯番号
    'entry_id',                  # エントリーID
    'entry_name',                # 注文者名
    'entry_name_kana',           # 注文者名カナ
    'entry_zip_code',            # 注文者郵便番号
    'entry_prefecture',          # 注文者都道府県
    'entry_address_1',           # 注文者住所1
    'entry_address_2',           # 注文者住所2
    'entry_address_3',           # 注文者住所3
    'entry_tel',                 # 注文者電話番号
    'entry_mail_address',        # 注文者メールアドレス
    'entry_ts',                  # 注文日時
    'cancel_ts',                 # キャンセル日時
    'service_id',                # サービスID
    'service_name',              # サービス名
    'entry_detail_id',           # 注文詳細ID
    'item_id',                   # 商品ID
    'fdn_item_code',             # FDN商品コード
    'item_hinban',               # 商品品番
    'item_name',                 # 商品名
    'item_unit_price',           # 商品単価
    'quantity',                  # 数量
    'item_tax',                  # 商品税額
    'item_tax_adjust',           # 税額調整
    'item_tax_pct',              # 税率
    'keigen_flg',                # 軽減税率フラグ
    'okurinushi_company',        # 送り主会社名
    'okurinushi_company_kana',   # 送り主会社名カナ
    'okurinushi_title',          # 送り主役職
    'okurinushi_name',           # 送り主名
    'okurinushi_zip_code',       # 送り主郵便番号
    'okurinushi_prefecture',     # 送り主都道府県
    'okurinushi_address_1',      # 送り主住所1
    'okurinushi_address_2',      # 送り主住所2
    'okurinushi_address_3',      # 送り主住所3
    'okurinushi_tel',            # 送り主電話番号
    'renmei1',                   # 連名1
    'renmei2',                   # 連名2
    'update_ts',                 # 更新日時
]
```

### 3.6 追加情報の付与

各注文データに通夜・葬儀の情報を追加：

```python
# 通夜情報
seko_schedule_tsuya = SekoSchedule.objects.filter(
    Q(seko=order['seko_id']), Q(schedule=1)  # schedule=1は通夜
).first()
if seko_schedule_tsuya:
    order['tsuya_hall_name'] = seko_schedule_tsuya.hall_name
    order['tsuya_date'] = seko_schedule_tsuya.schedule_date
    order['tsuya_begin_time'] = seko_schedule_tsuya.begin_time

# 葬儀情報
seko_schedule_sogi = SekoSchedule.objects.filter(
    Q(seko=order['seko_id']), Q(schedule=2)  # schedule=2は葬儀
).first()
if seko_schedule_sogi:
    order['sogi_hall_name'] = seko_schedule_sogi.hall_name
    order['sogi_date'] = seko_schedule_sogi.schedule_date
    order['sogi_begin_time'] = seko_schedule_sogi.begin_time
```

### 3.7 使用シリアライザー

**FdnOrderListSerializer:**
```python
class FdnOrderListSerializer(serializers.Serializer):
    seko_id = serializers.IntegerField()
    fdn_seko_code = serializers.CharField()
    soke_name = serializers.CharField()
    tsuya_hall_name = serializers.CharField()
    tsuya_date = serializers.DateField()
    tsuya_begin_time = serializers.TimeField()
    sogi_hall_name = serializers.CharField()
    sogi_date = serializers.DateField()
    sogi_begin_time = serializers.TimeField()
    kojin_name = serializers.CharField()
    kojin_birth_date = serializers.DateField()
    kojin_death_date = serializers.DateField()
    moshu_name = serializers.CharField()
    moshu_tel = serializers.CharField()
    moshu_mobile = serializers.CharField()
    entry_id = serializers.IntegerField()
    entry_name = serializers.CharField()
    entry_name_kana = serializers.CharField()
    # ... 他のフィールド
```

## 4. 会社コード判定の共通仕様

### 4.1 判定方法

**全てのFDN APIで共通の判定方式:**

1. **JWT認証**: `ClassableJWTAuthentication`でユーザー認証
2. **会社コード取得**: `request.user.company_code`でスタッフの会社コードを取得
3. **会社検索**: Baseモデルから該当する会社を検索
4. **権限確認**: 該当する会社のデータのみアクセス可能

### 4.2 実装パターン

```python
def validate_company(self):
    company: Base = Base.objects.filter(
        Q(del_flg=False),                    # 削除されていない
        Q(base_type=Base.OrgType.COMPANY),   # 会社タイプ
        Q(company_code=self.request.user.company_code),  # ユーザーの会社コード
    ).first()
    
    if not company:
        raise ValidationError(f'Base not found: company_code={self.request.user.company_code}')
    
    return company
```

### 4.3 セキュリティ考慮事項

- **データ分離**: 会社コードによる完全なデータ分離
- **認証必須**: 全てのAPIでJWT認証が必要
- **権限チェック**: スタッフ権限の確認
- **入力検証**: FDNコードの重複チェックと存在確認

## 5. エラーコード一覧

| コード | メッセージ | 発生条件 |
|---|---|---|
| E001 | この施行情報(施行番号=XXX)はすでに連携済みです | FDN施行番号の重複 |
| E002 | 施行部門コード(XXX)がFOCシステムに存在しません | 部門コードが見つからない |
| E003 | 受注担当者コード(XXX)がFOCシステムに存在しません | 受注担当者コードが見つからない |
| E004 | 施行担当者コード(XXX)がFOCシステムに存在しません | 施行担当者コードが見つからない |
| E005 | AF担当者コード(XXX)がFOCシステムに存在しません | AF担当者コードが見つからない |
| E006 | 式場コード(XXX)がFOCシステムに存在しません | 式場コードが見つからない |

## 6. まとめ

FDN APIは葬儀業界の基幹システムとFOCシステム間でのデータ連携を行う重要なAPIです。全てのAPIで会社コードによる厳密なデータ分離が実装されており、セキュリティと整合性が確保されています。
