from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import AuthenticationFailed, InvalidToken
from rest_framework_simplejwt.settings import api_settings
from rest_framework_simplejwt.state import User
from rest_framework_simplejwt.tokens import RefreshToken

from seko.models import Moshu


class ClassableRefreshToken(RefreshToken):
    @classmethod
    def for_user(cls, user):
        if isinstance(user, Moshu):
            setattr(user, 'id', user.seko.pk)
        token = super().for_user(user)
        token['user_class'] = user._meta.object_name
        if isinstance(user, <PERSON><PERSON>):
            delattr(user, 'id')
        return token


class ClassableJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        try:
            user_id = validated_token[api_settings.USER_ID_CLAIM]
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user identification'))

        try:
            user_class: str = validated_token['user_class']
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user class'))

        try:
            if user_class == 'Moshu':
                user = Moshu.objects.get(Q(pk=user_id) & Q(seko__del_flg=False))
            else:
                user = User.objects.get(**{api_settings.USER_ID_FIELD: user_id})
        except (Moshu.DoesNotExist, User.DoesNotExist):
            raise AuthenticationFailed(_('User not found'), code='user_not_found')

        if not user.is_active:
            raise AuthenticationFailed(_('User is inactive'), code='user_inactive')

        return user
