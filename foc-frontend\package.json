{"name": "foc-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "~10.0.8", "@angular/cdk": "^10.1.2", "@angular/common": "~10.0.8", "@angular/compiler": "~10.0.8", "@angular/core": "~10.0.8", "@angular/forms": "~10.0.8", "@angular/material": "^10.1.2", "@angular/platform-browser": "~10.0.8", "@angular/platform-browser-dynamic": "~10.0.8", "@angular/router": "~10.0.8", "classlist.js": "^1.1.20150312", "encoding-japanese": "^1.0.30", "fomantic-ui-css": "^2.8.7", "jquery": "^3.5.1", "ng2-pdfjs-viewer": "^6.0.1", "runtime-config-loader": "^3.0.0", "rxjs": "~6.6.2", "tslib": "^1.9.0", "web-animations-js": "^2.3.2", "zone.js": "~0.10.3"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1000.5", "@angular/cli": "~10.0.5", "@angular/compiler-cli": "~10.0.8", "@angular/language-service": "~10.0.8", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "~2.0.3", "@types/node": "^8.9.5", "codelyzer": "^5.0.0", "jasmine-core": "~3.4.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~6.3.16", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-jasmine": "~2.0.1", "karma-jasmine-html-reporter": "^1.4.0", "protractor": "~5.4.0", "ts-node": "~7.0.0", "tslint": "~5.15.0", "typescript": "~3.9.7"}}