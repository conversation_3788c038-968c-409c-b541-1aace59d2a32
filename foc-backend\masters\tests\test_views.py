from typing import Dict, List

from dateutil.relativedelta import relativedelta
from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from masters.models import <PERSON>koStyle, <PERSON><PERSON>
from masters.tests.factories import (
    ChobunDaishiMasterFactory,
    ChobunSampleFactory,
    FuhoSampleMasterFactory,
    HenreihinParameterFactory,
    HoyoDefaultMasterFactory,
    HoyoSampleMasterFactory,
    HoyoStyleFactory,
    KodenParameterFactory,
    RelationshipFactory,
    ScheduleFactory,
    SekoStyleFactory,
    ServiceFactory,
    TaxFactory,
    WarekiFactory,
    ZipCodeFactory,
)
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


class ScheduleFullListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.schedule_2 = ScheduleFactory()
        self.schedule_1 = ScheduleFactory()
        self.schedule_3 = ScheduleFactory()

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_all_schedule_list_succeed(self) -> None:
        """日程項目一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:list_all_schedules'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        record_ids: List[int] = []
        for schedule in records:
            record_ids.append(schedule['id'])
            if schedule.get('id') == self.schedule_1:
                self.assertEqual(schedule.get('name'), self.schedule_1.name)
                self.assertEqual(schedule.get('required_flg'), self.schedule_1.required_flg)

        # ID順で返ってくるか
        self.assertEqual(record_ids, [self.schedule_2.pk, self.schedule_1.pk, self.schedule_3.pk])

    def test_all_schedule_list_failed_without_auth(self) -> None:
        """日程項目一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:list_all_schedules'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoStyleFullListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.hoyo_style_2 = HoyoStyleFactory()
        self.hoyo_style_1 = HoyoStyleFactory()
        self.seko_style_1 = SekoStyleFactory(hoyo_style=self.hoyo_style_1)
        self.seko_style_3 = SekoStyleFactory(hoyo_style=self.hoyo_style_2)
        self.seko_style_2 = SekoStyleFactory(hoyo_style=self.hoyo_style_1)

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def assert_seko_style_equal(
        self, result_seko_style: Dict, correct_seko_style: SekoStyle
    ) -> None:
        self.assertEqual(result_seko_style.get('name'), correct_seko_style.name)
        self.assertEqual(
            result_seko_style.get('wareki_use_flg'), correct_seko_style.wareki_use_flg
        )
        result_hoyo_style = result_seko_style.get('hoyo_style', {})
        self.assertEqual(result_hoyo_style.get('id'), correct_seko_style.hoyo_style.id)
        self.assertEqual(
            result_hoyo_style.get('wareki_use_flg'), correct_seko_style.hoyo_style.wareki_use_flg
        )

    def test_all_seko_style_list_succeed(self) -> None:
        """施行形式一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:list_all_seko_styles'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        correct_seko_styles: Dict = {
            self.seko_style_1.id: self.seko_style_1,
            self.seko_style_2.id: self.seko_style_2,
            self.seko_style_3.id: self.seko_style_3,
        }
        record_ids: List[int] = []
        for seko_style in records:
            self.assert_seko_style_equal(seko_style, correct_seko_styles[seko_style['id']])
            record_ids.append(seko_style['id'])

        # ID順で返ってくるか
        self.assertEqual(
            record_ids, [self.seko_style_3.pk, self.seko_style_1.pk, self.seko_style_2.pk]
        )

    def test_all_schedule_list_failed_without_auth(self) -> None:
        """施行形式一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:list_all_seko_styles'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class ZipCodeQueryViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.zip_code_2 = ZipCodeFactory(zip_code='3456789')
        self.zip_code_1 = ZipCodeFactory(zip_code='1234567')
        self.zip_code_3 = ZipCodeFactory(zip_code='5555555')
        self.zip_code_4 = ZipCodeFactory(zip_code='5555555')

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_query_zipcode_succeed(self) -> None:
        """郵便番号から住所を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 完全一致で1件ヒット
        params: Dict = {'search': self.zip_code_2.zip_code}
        response = self.api_client.get(
            reverse('masters:query_zipcode'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        # 完全一致で2件ヒット
        params: Dict = {'search': self.zip_code_3.zip_code}
        response = self.api_client.get(
            reverse('masters:query_zipcode'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        # 部分一致で1件ヒット
        params: Dict = {'search': '678'}
        response = self.api_client.get(
            reverse('masters:query_zipcode'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        # 部分一致で2件ヒット
        params: Dict = {'search': '456'}
        response = self.api_client.get(
            reverse('masters:query_zipcode'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        # ID順で返ってくるか
        record_ids: List[int] = [rec['id'] for rec in records]
        self.assertEqual(record_ids, [self.zip_code_2.pk, self.zip_code_1.pk])

        # 部分一致でヒットなし、エラーにはしない
        params: Dict = {'search': '432'}
        response = self.api_client.get(
            reverse('masters:query_zipcode'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 0)

    def test_all_schedule_list_success_without_auth(self) -> None:
        """郵便番号検索APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {'search': self.zip_code_2.zip_code}
        response = self.api_client.get(
            reverse('masters:query_zipcode'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)


class WarekiListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        # 時系列は wareki_3 → wareki_2 → wareki_1 → wareki_4
        self.wareki_1 = WarekiFactory()
        self.wareki_2 = WarekiFactory(
            begin_date=self.wareki_1.begin_date + relativedelta(years=-1),
            end_date=self.wareki_1.begin_date,
        )
        self.wareki_3 = WarekiFactory(
            begin_date=self.wareki_2.begin_date + relativedelta(years=-1),
            end_date=self.wareki_2.begin_date,
        )
        self.wareki_4 = WarekiFactory(
            begin_date=self.wareki_1.end_date,
            end_date=self.wareki_1.end_date + relativedelta(years=2),
        )

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def assert_wareki_equal(self, result_wareki: Dict, correct_wareki: Wareki) -> None:
        self.assertEqual(result_wareki.get('name'), correct_wareki.name)
        self.assertEqual(
            result_wareki.get('begin_date'), correct_wareki.begin_date.strftime('%Y-%m-%d')
        )
        self.assertEqual(
            result_wareki.get('end_date'), correct_wareki.end_date.strftime('%Y-%m-%d')
        )
        self.assertEqual(result_wareki.get('minus_years'), correct_wareki.minus_years)

    def test_wareki_list_succeed(self) -> None:
        """和暦一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(reverse('masters:list_wareki'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

        # 一覧APIはbegin_dateの降順
        correct_wareki: List[Wareki] = [self.wareki_4, self.wareki_1, self.wareki_2, self.wareki_3]
        for result, correct in zip(records, correct_wareki):
            self.assert_wareki_equal(result, correct)


class FuhoSampleMasterListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        fuho_sample_3 = FuhoSampleMasterFactory()
        fuho_sample_2 = FuhoSampleMasterFactory()
        fuho_sample_4 = FuhoSampleMasterFactory()
        fuho_sample_1 = FuhoSampleMasterFactory()
        fuho_sample_5 = FuhoSampleMasterFactory()
        self.fuho_sample_list = [
            fuho_sample_3,
            fuho_sample_2,
            fuho_sample_4,
            fuho_sample_1,
            fuho_sample_5,
        ]

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_fuho_sample_master_list_succeed(self) -> None:
        """訃報サンプルマスタ一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:list_fuho_sample_master'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        saved_fuho_sample = [model_to_dict(fuho_sample) for fuho_sample in self.fuho_sample_list]
        for result, correct in zip(records, saved_fuho_sample):
            self.assertEqual(result, correct)


class ChobunDaishiMasterListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        chobun_daishi_3 = ChobunDaishiMasterFactory()
        chobun_daishi_2 = ChobunDaishiMasterFactory()
        chobun_daishi_4 = ChobunDaishiMasterFactory()
        chobun_daishi_1 = ChobunDaishiMasterFactory()
        chobun_daishi_5 = ChobunDaishiMasterFactory()
        self.chobun_daishi_list = [
            chobun_daishi_3,
            chobun_daishi_2,
            chobun_daishi_4,
            chobun_daishi_1,
            chobun_daishi_5,
        ]

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_chobun_daishi_list_succeed(self) -> None:
        """弔文台紙一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:list_chobun_daishi_master'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        saved_chobun_daishi = [
            model_to_dict(chobun_daishi) for chobun_daishi in self.chobun_daishi_list
        ]

        for result, correct in zip(records, saved_chobun_daishi):
            self.assertEqual(result['name'], correct['name'])


class ChobunSampleListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        chobun_sample_3 = ChobunSampleFactory()
        chobun_sample_2 = ChobunSampleFactory()
        chobun_sample_4 = ChobunSampleFactory()
        chobun_sample_1 = ChobunSampleFactory()
        chobun_sample_5 = ChobunSampleFactory()
        self.chobun_sample_list = [
            chobun_sample_3,
            chobun_sample_2,
            chobun_sample_4,
            chobun_sample_1,
            chobun_sample_5,
        ]

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_chobun_sample_list_succeed(self) -> None:
        """弔文サンプル一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:list_chobun_sample'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        saved_chobun_sample = [
            model_to_dict(chobun_sample) for chobun_sample in self.chobun_sample_list
        ]

        for result, correct in zip(records, saved_chobun_sample):
            for key in result.keys():
                self.assertEqual(result[key], correct[key])


class ServiceListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        service_3 = ServiceFactory()
        service_2 = ServiceFactory()
        service_4 = ServiceFactory()
        service_1 = ServiceFactory()
        service_5 = ServiceFactory()
        self.service_list = [service_3, service_2, service_4, service_1, service_5]

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_service_list_succeed(self) -> None:
        """サービス一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(reverse('masters:list_service'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        saved_service = [model_to_dict(service) for service in self.service_list]

        for result, correct in zip(records, saved_service):
            for key in result.keys():
                self.assertEqual(result[key], correct[key])


class TaxListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        tax_3 = TaxFactory()
        tax_2 = TaxFactory()
        tax_4 = TaxFactory()
        tax_1 = TaxFactory()
        tax_5 = TaxFactory()
        self.tax_list = [tax_3, tax_2, tax_4, tax_1, tax_5]

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_tax_list_succeed(self) -> None:
        """消費税率一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(reverse('masters:list_tax'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        saved_tax = [model_to_dict(tax) for tax in self.tax_list]

        for result, correct in zip(records, saved_tax):
            for key in result.keys():
                self.assertEqual(result[key], correct[key])


class RelationshipListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        relationship_3 = RelationshipFactory()
        relationship_2 = RelationshipFactory()
        relationship_4 = RelationshipFactory()
        relationship_1 = RelationshipFactory()
        relationship_5 = RelationshipFactory()
        self.relationship_list = [
            relationship_3,
            relationship_2,
            relationship_4,
            relationship_1,
            relationship_5,
        ]

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_relationship_list_succeed(self) -> None:
        """間柄一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:list_relationship'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        saved_relationship = [
            model_to_dict(relationship) for relationship in self.relationship_list
        ]

        for result, correct in zip(records, saved_relationship):
            for key in result.keys():
                self.assertEqual(result[key], correct[key])


class HenreihinParameterDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_henreihin_param_detail_succeed(self) -> None:
        """返礼品パラメータを取得する"""
        henreihin_param = HenreihinParameterFactory()

        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:henrei_params'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(
            record['henreihin_omote_default'], henreihin_param.henreihin_omote_default
        )
        self.assertEqual(
            record['henreihin_mizuhiki_default'], henreihin_param.henreihin_mizuhiki_default
        )
        self.assertEqual(record['henreihin_hoso_default'], henreihin_param.henreihin_hoso_default)

    def test_henreihin_param_detail_fail_by_notfound(self) -> None:
        """返礼品パラメータ取得APIがレコードなしで失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:henrei_params'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class KodenParameterDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_koden_param_detail_succeed(self) -> None:
        """返礼品パラメータを取得する"""
        koden_param = KodenParameterFactory()

        params: Dict = {}
        response = self.api_client.get(reverse('masters:koden_params'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['koden_commission_tax_pct'], koden_param.koden_commission_tax_pct)
        self.assertEqual(record['koden_upper_limit'], koden_param.koden_upper_limit)

    def test_koden_param_detail_fail_by_notfound(self) -> None:
        """返礼品パラメータ取得APIがレコードなしで失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('masters:koden_params'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class HoyoStyleListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        hoyo_style_3 = HoyoStyleFactory.build()
        hoyo_style_2 = HoyoStyleFactory.build()
        hoyo_style_4 = HoyoStyleFactory.build()
        hoyo_style_1 = HoyoStyleFactory.build()
        hoyo_style_5 = HoyoStyleFactory.build()
        hoyo_style_1.save()
        hoyo_style_2.save()
        hoyo_style_3.save()
        hoyo_style_4.save()
        hoyo_style_5.save()
        self.hoyo_style_list = [
            hoyo_style_3,
            hoyo_style_2,
            hoyo_style_4,
            hoyo_style_1,
            hoyo_style_5,
        ]

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_style_list_succeed(self) -> None:
        """法要形式一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:hoyo_style_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        for result, correct in zip(records, self.hoyo_style_list):
            self.assertEqual(result['id'], correct.pk)
            self.assertEqual(result['name'], correct.name)
            self.assertEqual(result['wareki_use_flg'], correct.wareki_use_flg)


class HoyoDefaultMasterListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        hoyo_style_1 = HoyoStyleFactory()
        hoyo_style_2 = HoyoStyleFactory()
        hoyo_default_master_1 = HoyoDefaultMasterFactory.build(hoyo_style=hoyo_style_1)
        hoyo_default_master_2 = HoyoDefaultMasterFactory.build(hoyo_style=hoyo_style_2)
        hoyo_default_master_3 = HoyoDefaultMasterFactory.build(hoyo_style=hoyo_style_1)
        hoyo_default_master_4 = HoyoDefaultMasterFactory.build(hoyo_style=hoyo_style_2)
        hoyo_default_master_5 = HoyoDefaultMasterFactory.build(hoyo_style=hoyo_style_1)
        hoyo_default_master_3.save()
        hoyo_default_master_2.save()
        hoyo_default_master_4.save()
        hoyo_default_master_1.save()
        hoyo_default_master_5.save()
        self.hoyo_default_master_list = [
            hoyo_default_master_1,
            hoyo_default_master_3,
            hoyo_default_master_5,
            hoyo_default_master_2,
            hoyo_default_master_4,
        ]

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_default_master_list_succeed(self) -> None:
        """法要初期値一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:hoyo_default_master_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        for result, correct in zip(records, self.hoyo_default_master_list):
            self.assertEqual(result['id'], correct.pk)
            self.assertEqual(result['name'], correct.name)
            self.assertEqual(result['elapsed_time'], correct.elapsed_time)
            self.assertEqual(result['unit'], correct.unit)
            self.assertEqual(result['hoyo_style']['name'], correct.hoyo_style.name)


class HoyoSampleMasterListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        hoyo_sample_master_1 = HoyoSampleMasterFactory.build()
        hoyo_sample_master_2 = HoyoSampleMasterFactory.build()
        hoyo_sample_master_3 = HoyoSampleMasterFactory.build()
        hoyo_sample_master_4 = HoyoSampleMasterFactory.build()
        hoyo_sample_master_5 = HoyoSampleMasterFactory.build()
        hoyo_sample_master_3.save()
        hoyo_sample_master_2.save()
        hoyo_sample_master_4.save()
        hoyo_sample_master_1.save()
        hoyo_sample_master_5.save()
        self.hoyo_sample_master_list = [
            hoyo_sample_master_1,
            hoyo_sample_master_2,
            hoyo_sample_master_3,
            hoyo_sample_master_4,
            hoyo_sample_master_5,
        ]

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_sample_master_list_succeed(self) -> None:
        """法要サンプルマスタ一覧取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('masters:hoyo_sample_master_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        for result, correct in zip(records, self.hoyo_sample_master_list):
            self.assertEqual(result['id'], correct.pk)
            self.assertEqual(result['sentence'], correct.sentence)
