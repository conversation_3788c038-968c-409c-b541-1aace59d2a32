from datetime import date

from django.test import TestCase
from django.utils import timezone

from invoices.templatetags import invoice_filters
from invoices.tests.factories import (
    SalesCompanyFactory,
    SalesDetailFactory,
    SalesIndexFactory,
    SalesSekoDepartmentFactory,
)


class FirstDayTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

    def test_first_day(self) -> None:
        """日付の月最初の日を取得"""

        today = date.today()
        fisrt_day = today.replace(day=1)
        self.assertEqual(invoice_filters.first_day(today), fisrt_day)


class IsFirstOfSekoTest(TestCase):
    def setUp(self):
        super().setUp()

        sales_index = SalesIndexFactory(sum_ts=timezone.localtime())
        sales_company = SalesCompanyFactory(sales_index=sales_index)

        self.sales_department = SalesSekoDepartmentFactory(sales_company=sales_company)

        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=1)
        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=1)
        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=2)
        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=3)

    def test_is_first_of_seko(self) -> None:
        """施行番号が初めてのデータかどうか判定"""

        self.assertEqual(
            invoice_filters.is_first_of_seko(self.sales_department.sales_details.all(), 1), True
        )
        self.assertEqual(
            invoice_filters.is_first_of_seko(self.sales_department.sales_details.all(), 2), False
        )
        self.assertEqual(
            invoice_filters.is_first_of_seko(self.sales_department.sales_details.all(), 3), True
        )
        self.assertEqual(
            invoice_filters.is_first_of_seko(self.sales_department.sales_details.all(), 4), True
        )


class IsFirstOfServiceTest(TestCase):
    def setUp(self):
        super().setUp()

        sales_index = SalesIndexFactory(sum_ts=timezone.localtime())
        sales_company = SalesCompanyFactory(sales_index=sales_index)

        self.sales_department = SalesSekoDepartmentFactory(sales_company=sales_company)

        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=1, service_id=10)
        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=1, service_id=10)
        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=1, service_id=20)
        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=2, service_id=20)
        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=2, service_id=30)
        SalesDetailFactory(sales_seko_department=self.sales_department, seko_id=2, service_id=30)

    def test_is_first_of_service(self) -> None:
        """サービスIDが初めてのデータかどうか判定"""

        self.assertEqual(
            invoice_filters.is_first_of_service(self.sales_department.sales_details.all(), 1), True
        )
        self.assertEqual(
            invoice_filters.is_first_of_service(self.sales_department.sales_details.all(), 2),
            False,
        )
        self.assertEqual(
            invoice_filters.is_first_of_service(self.sales_department.sales_details.all(), 3), True
        )
        self.assertEqual(
            invoice_filters.is_first_of_service(self.sales_department.sales_details.all(), 4), True
        )
        self.assertEqual(
            invoice_filters.is_first_of_service(self.sales_department.sales_details.all(), 5), True
        )
        self.assertEqual(
            invoice_filters.is_first_of_service(self.sales_department.sales_details.all(), 6),
            False,
        )


class DivideTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

    def test_divide(self) -> None:
        """割り算"""

        # 割り切れるパターン
        self.assertEqual(invoice_filters.divide(10, 2), 5)
        # 割り切れないパターン
        self.assertEqual(invoice_filters.divide(10, 3), 3)


class MultipleTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

    def test_multiple(self) -> None:
        """掛算"""

        self.assertEqual(invoice_filters.multiple(6, 2), 12)
