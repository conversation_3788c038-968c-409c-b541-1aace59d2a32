from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.tests.factories import AfActivityDetailFactory, AfWishFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class AfActivityDetailUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.af_activity_detail = AfActivityDetailFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:

        self.new_af_activity_detail_data = AfActivityDetailFactory.build()

        return {
            'activity_ts': self.new_af_activity_detail_data.activity_ts,
            'activity': self.new_af_activity_detail_data.activity,
        }

    def test_af_activity_detail_update_succeed(self) -> None:
        """AF活動内容を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.patch(
            reverse('after_follow:activity_detail', kwargs={'pk': self.af_activity_detail.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['af_wish'], self.af_activity_detail.af_wish.pk)
        self.assertEqual(
            record['activity_ts'],
            self.new_af_activity_detail_data.activity_ts.astimezone(tz).isoformat(),
        )
        self.assertEqual(record['activity'], self.new_af_activity_detail_data.activity)

    def test_af_activity_detail_update_ignore_af_wish(self) -> None:
        """AF活動内容更新APIはAF希望項目の更新を無視する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'af_wish': AfWishFactory().pk}
        response = self.api_client.patch(
            reverse('after_follow:activity_detail', kwargs={'pk': self.af_activity_detail.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['af_wish'], self.af_activity_detail.af_wish.pk)

    def test_af_activity_detail_update_failed_without_auth(self) -> None:
        """AF活動内容更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.patch(
            reverse('after_follow:activity_detail', kwargs={'pk': self.af_activity_detail.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_af_activity_detail_update_deny_call_from_moshu(self) -> None:
        """AF活動内容更新APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.patch(
            reverse('after_follow:activity_detail', kwargs={'pk': self.af_activity_detail.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
