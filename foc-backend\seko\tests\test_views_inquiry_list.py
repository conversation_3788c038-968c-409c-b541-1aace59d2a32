from typing import Dict, List

from django.core import mail
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from seko.models import Seko, SekoAnswer, SekoInquiry
from seko.tests.factories import MoshuFactory, SekoAnswerFactory, SekoFactory, SekoInquiryFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class SekoInquiryListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        self.inquiry_1: SekoInquiry = SekoInquiryFactory(seko=self.seko, query_group_id=1)
        self.answer_sample: SekoAnswer = SekoAnswerFactory(inquiry=self.inquiry_1)
        self.inquiry_2: SekoInquiry = SekoInquiryFactory(seko=self.seko, query_group_id=2)
        SekoAnswerFactory(inquiry=self.inquiry_2)
        SekoAnswerFactory(inquiry=self.inquiry_2)
        SekoAnswerFactory(inquiry=self.inquiry_2)
        self.inquiry_3: SekoInquiry = SekoInquiryFactory(seko=self.seko, query_group_id=1)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_inquiry_list_succeed(self) -> None:
        """施行問合せ一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:inquiry_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        # 問合せは2→3→1の順
        inquiry_ids = [record['id'] for record in records]
        self.assertEqual(inquiry_ids, [self.inquiry_1.pk, self.inquiry_3.pk, self.inquiry_2.pk])

        rec_inquiry: Dict = records[0]
        self.assertEqual(rec_inquiry['id'], self.inquiry_1.pk)
        self.assertEqual(rec_inquiry['seko'], self.inquiry_1.seko.pk)
        self.assertEqual(rec_inquiry['query_group_id'], self.inquiry_1.query_group_id)
        self.assertEqual(rec_inquiry['query'], self.inquiry_1.query)
        self.assertIsNotNone(rec_inquiry['created_at'])
        self.assertIsNotNone(rec_inquiry['updated_at'])

        self.assertEqual(len(rec_inquiry['answers']), 1)
        rec_answer: Dict = rec_inquiry['answers'][0]
        self.assertEqual(rec_answer['id'], self.answer_sample.pk)
        self.assertEqual(rec_answer['answer'], self.answer_sample.answer)
        self.assertEqual(rec_answer['staff']['id'], self.answer_sample.staff.pk)
        self.assertEqual(rec_answer['staff']['name'], self.answer_sample.staff.name)
        self.assertEqual(rec_answer['del_flg'], self.answer_sample.del_flg)
        self.assertIsNotNone(rec_answer['created_at'])
        self.assertIsNotNone(rec_answer['updated_at'])

    def test_seko_inquiry_list_ignores_disabled_answer(self) -> None:
        """施行問合せ一覧APIは無効化した回答は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        disabled_answer: SekoAnswer = self.inquiry_2.answers.all()[1]
        disabled_answer.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:inquiry_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        rec_inquiry: Dict = records[0]
        self.assertEqual(len(rec_inquiry['answers']), 1)

    def test_seko_inquiry_list_failed_by_seko_notfound(self) -> None:
        """施行問合せ一覧APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko_inquiry: SekoInquiry = SekoInquiryFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:inquiry_list', kwargs={'seko_id': non_saved_seko_inquiry.seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_inquiry_list_failed_without_auth(self) -> None:
        """施行問合せ一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:inquiry_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])


class SekoInquiryCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        MoshuFactory(seko=self.seko)
        self.seko_inquiry_data = SekoInquiryFactory.build(seko=self.seko)

        self.api_client = APIClient()
        self.moshu = MoshuFactory()
        refresh = RefreshToken.for_user(self.moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'query_group_id': self.seko_inquiry_data.query_group_id,
            'query': self.seko_inquiry_data.query,
        }

    def test_seko_inquiry_create_succeed(self) -> None:
        """施行問合せを追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('seko:inquiry_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertEqual(record['seko'], self.seko_inquiry_data.seko.pk)
        self.assertEqual(record['query_group_id'], self.seko_inquiry_data.query_group_id)
        self.assertEqual(record['query'], self.seko_inquiry_data.query)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, '【FOC】ご葬家からの問合せがあります')
        self.assertEqual(sent_mail.to, [self.seko.af_staff.mail_address])

    def test_seko_inquiry_create_failed_by_seko_notfound(self) -> None:
        """施行問合せ追加APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()

        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:inquiry_list', kwargs={'seko_id': non_saved_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_inquiry_create_failed_without_auth(self) -> None:
        """施行問合せ追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:inquiry_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_seko_inquiry_create_deny_from_staff(self) -> None:
        """施行問合せ追加APIが担当者からの呼び出しを拒否する"""
        staff = StaffFactory()
        refresh = RefreshToken.for_user(staff)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:inquiry_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
