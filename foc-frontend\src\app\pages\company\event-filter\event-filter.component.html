
<div class="container">
  <div class="inner with_footer">
    <div class="contents">
      <div class="menu_title">
        <i class="calendar alternate icon big"></i>
        <i class="filter icon combo"></i>
        イベント案内抽出
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(prefectureComboEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchMoshu()">
          <i class="filter icon"></i>抽出
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>都道府県</label>
          <com-dropdown #prefectureComboEm [settings]="prefectureCombo" [(selectedValue)]="form_data.prefecture"></com-dropdown>
          <label>市区町村</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.address_1">
          </div>
        </div>
      </div>
      <ng-container *ngFor="let af_group of af_group_list">
        <div class="af_group">{{af_group.name}}</div>
        <div class="af-check ui segment">
          <ng-container *ngFor="let after_follow of af_group.after_follows">
            <div class="ui checkbox">
              <input type="checkbox" [(ngModel)]="after_follow.selected">
              <label>{{after_follow.name}}</label>
            </div>
          </ng-container>
        </div>
      </ng-container>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="moshu_list?.length">
          全{{moshu_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?moshu_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="check">
                <div class="ui checkbox" [class.disabled]="!moshu_list?.length">
                  <input type="checkbox" [(ngModel)]="all_check" (change)="checkAllItem()">
                  <label></label>
                </div>
              </th>
              <th class="moshu_id">ID</th>
              <th class="relationship">続柄</th>
              <th class="moshu_name">氏名</th>
              <th class="mobile_num">電話番号</th>
              <th class="address">住所</th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body" [class.no-page-nav]="pagination.pages.length===1">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let moshu of moshu_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" (click)="checkItem($event, moshu)">
              <td class="center aligned check">
                <div class="ui checkbox">
                  <input type="checkbox" [(ngModel)]="moshu.selected">
                  <label></label>
                </div>
              </td>
              <td class="center aligned moshu_id" title="{{moshu.id}}">{{moshu.seko.id}}</td>
              <ng-container *ngIf="moshu.seko.seko_contact">
                <td class="relationship" title="{{moshu.seko.seko_contact.kojin_relationship}}">{{moshu.seko.seko_contact.kojin_relationship}}</td>
                <td class="moshu_name" title="{{moshu.seko.seko_contact.name}}">{{moshu.seko.seko_contact.name}}</td>
                <td class="mobile_num" title="{{moshu.seko.seko_contact.mobile_num}}">{{moshu.seko.seko_contact.mobile_num}}</td>
                <td class="address" title="{{moshu.seko.seko_contact.prefecture}}{{moshu.seko.seko_contact.address_1}}{{moshu.seko.seko_contact.address_2}}{{moshu.seko.seko_contact.address_3}}">
                  {{moshu.seko.seko_contact.prefecture}}{{moshu.seko.seko_contact.address_1}}{{moshu.seko.seko_contact.address_2}}{{moshu.seko.seko_contact.address_3}}
                </td>
              </ng-container>
              <ng-container *ngIf="!moshu.seko.seko_contact">
                <td class="relationship" title="{{moshu.kojin_moshu_relationship}}">{{moshu.kojin_moshu_relationship}}</td>
                <td class="moshu_name" title="{{moshu.name}}">{{moshu.name}}</td>
                <td class="mobile_num" title="{{moshu.mobile_num}}">{{moshu.mobile_num}}</td>
                <td class="address" title="{{moshu.prefecture}}{{moshu.address_1}}{{moshu.address_2}}{{moshu.address_3}}">
                  {{moshu.prefecture}}{{moshu.address_1}}{{moshu.address_2}}{{moshu.address_3}}
                </td>
              </ng-container>
            </tr>
            </ng-container>
            <tr *ngIf="!moshu_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="5">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="input_area">
        <div class="line">
          <label class="required">イベント名</label>
          <div class="ui input">
            <input type="text" autocomplete="off" [(ngModel)]="event_mail.event_name">
          </div>
        </div>
        <div class="line row2">
          <label class="required">メール本文</label>
          <div class="ui icon input textarea">
              <textarea rows="2" cols="35" [(ngModel)]="event_mail.content"></textarea>
          </div>
          <div class="description">　　
            @m：連絡先名に置き換わります
          </div>
        </div>
        <div class="line">
          <label>備考</label>
          <div class="ui input full">
            <input type="text" autocomplete="off" [(ngModel)]="event_mail.note">
          </div>
        </div>
      </div>

		</div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group">
    <button class="ui labeled icon button mini light" (click)="exportCsv()">
      <i class="download icon"></i>CSV出力
    </button>
    <button class="ui labeled icon button mini light" [class.disabled]="buttonDisabled()" (click)="saveData()">
      <i class="sms icon"></i>SMS送信
    </button>
  </div>
</div>
