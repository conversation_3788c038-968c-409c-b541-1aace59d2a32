# Generated by Django 3.1.1 on 2020-09-15 08:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('masters', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='HoyoStyle',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('name', models.TextField(verbose_name='hoyo name')),
                ('wareki_use_flg', models.BooleanField(verbose_name='use era name')),
            ],
            options={
                'db_table': 'mm_hoyo_style',
            },
        ),
        migrations.CreateModel(
            name='SekoStyle',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('name', models.TextField(verbose_name='seko name')),
                ('wareki_use_flg', models.BooleanField(verbose_name='use era name')),
                (
                    'hoyo_style',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING, to='masters.hoyostyle'
                    ),
                ),
            ],
            options={
                'db_table': 'mm_seko_style',
            },
        ),
    ]
