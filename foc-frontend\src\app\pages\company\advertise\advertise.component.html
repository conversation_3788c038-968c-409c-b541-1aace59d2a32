
<div class="container">
  <div class="inner with_footer">
    <div class="contents mini" *ngIf="!load_error">
      <div class="menu_title">
        <i class="ad icon big"></i>
        広告
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" [(selectedName)]="company_name" (selectedItemChange)="companyChange($event, departComboEm, hallComboEm)"></com-dropdown>
          <label>部門</label>
          <com-dropdown #departComboEm [settings]="departCombo" [(selectedValue)]="depart_id" (selectedItemChange)="departChange($event, hallComboEm)"></com-dropdown>
          <label>式場</label>
          <com-dropdown #hallComboEm [settings]="hallCombo" [(selectedValue)]="hall_id" (selectedItemChange)="hallChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="item_list?.length">
          全{{item_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?item_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination, pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(pagination, page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination, pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!item_list?.length">
              <th class="base_name">所属拠点</th>
              <th class="banner">バナー画像</th>
              <th class="url">URL</th>
              <th class="link_file">linkファイル</th>
              <th class="begin_date">掲載開始日</th>
              <th class="end_date">掲載終了日</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let item of item_list; index as i">
              <tr (click)="showData($event, item)" *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
                <td class="base_name" title="{{item.base_name}}">{{item.base_name}}</td>
                <td class="banner"><div class="image"><img src="{{item.banner_file}}" *ngIf="item.banner_file"></div></td>
                <td class="url" title="{{item.url}}"><a class="url_link" target="_blank" href="{{item.url}}" *ngIf="item.url">{{item.url}}</a></td>
                <td class="link_file" title="{{item.link_file}}"><a class="url_link" target="_blank" href="{{item.link_file}}" *ngIf="item.link_file">linkファイル</a></td>
                <td class="center aligned begin_date" title="{{item.begin_date | date: 'yyyy/MM/dd'}}">{{item.begin_date | date: 'yyyy/MM/dd'}}</td>
                <td class="center aligned end_date" title="{{item.end_date | date: 'yyyy/MM/dd'}}">{{item.end_date | date: 'yyyy/MM/dd'}}</td>
                <td class="center aligned button operation">
                  <i class="large trash alternate icon" title="削除" (click)="deleteData(item.id)"></i>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <tr title="新規追加" (click)="showData($event)">
              <td colspan="7" class="center aligned"><i class="large add icon"></i></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal small" id="item-edit">
        <div class="header ui"><i class="ad icon large"></i>
          広告{{edit_type===1?'登録':'編集'}}
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="item_edit">
            <div class="line">
              <label class="required">所属拠点(葬儀社)</label>
              <label class="plane">{{company_name}}</label>
            </div>
            <div class="line">
              <label>所属拠点(部門)</label>
              <com-dropdown class="divided full" #inputDepartComboEm [settings]="inputDepartCombo" [(selectedValue)]="item_edit.depart_id" (selectedItemChange)="inputDepartChange($event, inputHallComboEm)"></com-dropdown>
            </div>
            <div class="line">
              <label>所属拠点(式場)</label>
              <com-dropdown class="divided full" #inputHallComboEm [settings]="inputHallCombo" [(selectedValue)]="item_edit.hall_id"></com-dropdown>
            </div>
            <div class="line">
              <label class="required large">バナー画像</label>
              <div class="ui card image" (dragover)="onDragOverImage($event)" (drop)="onDropImage($event)">
                <div class="ui image center aligned" (click)="selectFile()" title="バナー画像ファイル追加">
                  <ng-container *ngIf="!item_edit.banner_file && !item_edit.banner_file_stream">
                  <i class="image icon huge"></i>
                  <div class="noimage">No image</div>
                  <div class="desc">この枠をクリックしてファイルを選択するか、ファイルをこの枠にドラッグ・アンド・ドロップしてください。</div>
                  </ng-container>
                  <img src="{{item_edit.banner_file_stream?item_edit.banner_file_stream:item_edit.banner_file}}" *ngIf="item_edit.banner_file_stream || item_edit.banner_file">
                </div>
                <div class="clear" *ngIf="item_edit.banner_file_stream || item_edit.banner_file"><i class="delete icon" (click)="clearImage()" title="クリア"></i></div>
              </div>
            </div>
            <div class="line">
              <label class="large">URL</label>
              <div class="ui input full required">
                <input type="url" autocomplete="off" id="url" [(ngModel)]="item_edit.url" placeholder="https://〜">
              </div>
            </div>
            <div class="line link">
              <label class="large">linkファイル</label>
              <div class="ui left action input full">
                <button class="ui icon button" (click)="selectLinkFile()">
                  <i class="upload icon"></i>
                </button>
                <input type="text" readonly class="ui readonly" [value]="item_edit?.link_file_name?item_edit?.link_file_name:''">
                <div class="desc">(pdfファイルのみ)</div>
              </div>

              <div class="clear" *ngIf="item_edit.link_file_stream || item_edit.link_file"><i class="delete icon" (click)="clearLinkFile()" title="linkファイルクリア"></i></div>
              <a [href]="item_edit.link_file" target="_blanc" *ngIf="!item_edit.link_file_stream && item_edit.link_file">linkファイル</a>
            </div>
            <div class="line">
              <label class="required large">適用開始日</label>
              <com-calendar #dateBeginEm [settings]="calendarOptionDate" [(value)]="item_edit.begin_date"></com-calendar>
              <label class="required">適用終了日</label>
              <com-calendar #dateEndEm [settings]="calendarOptionDate" [(value)]="item_edit.end_date"></com-calendar>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeItem()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
      <input type="file" #imageFileUpload id="imageFileUpload" name="imageFileUpload" style="display:none;" />
      <input type="file" #linkFileUpload id="linkFileUpload" name="linkFileUpload" style="display:none;" />

    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
