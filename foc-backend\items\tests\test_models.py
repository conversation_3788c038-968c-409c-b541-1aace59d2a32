from django.test import TestCase

from items.models import Item, ItemSupplier
from items.tests.factories import ItemFactory, ItemSupplierFactory


class ItemModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.item: Item = ItemFactory()

    def test_fuho_disable(self) -> None:
        """商品を無効化(論理削除)する"""
        self.item.disable()
        self.assertTrue(self.item.del_flg)

    def test_default_supplier(self) -> None:
        """デフォルト発注先フラグがTrueになっている発注先を返す"""
        ItemSupplierFactory(item=self.item)
        item_supplier_2: ItemSupplier = ItemSupplierFactory(
            item=self.item, default_supplier_flg=True
        )
        ItemSupplierFactory(item=self.item)

        self.assertEqual(self.item.default_supplier(), item_supplier_2.supplier)

    def test_default_supplier_no_sppliers(self) -> None:
        """デフォルト発注先は発注先が登録されていない場合はNoneを返す"""
        self.assertIsNone(self.item.default_supplier())

    def test_default_supplier_uses_min_id(self) -> None:
        """デフォルト発注先が複数登録されている場合はIDの最も小さいものを返す"""
        ItemSupplierFactory(item=self.item)
        item_supplier_2: ItemSupplier = ItemSupplierFactory(
            item=self.item, default_supplier_flg=True
        )
        ItemSupplierFactory(item=self.item, default_supplier_flg=True)

        self.assertEqual(self.item.default_supplier(), item_supplier_2.supplier)

    def test_default_supplier_with_non_default_suppliers(self) -> None:
        """デフォルト発注先は発注先が複数あってもデフォルト指定がない場合はNone"""
        ItemSupplierFactory(item=self.item)
        ItemSupplierFactory(item=self.item)
        ItemSupplierFactory(item=self.item)

        self.assertIsNone(self.item.default_supplier())
