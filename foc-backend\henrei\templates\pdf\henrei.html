<!DOCTYPE html>
<html>
<title>返礼品発注書</title>
<head>
<style type="text/css">
@page {
  margin: 1cm;
  @top-right {
    content: counter(page) "/" counter(pages);
    margin-top: -380px;
  }
}
@page {
  size: 210mm 295mm
}
@media print {
  header {
    position: fixed;
    margin-top: -450px;
    width: 100%;
    top: 0;
    left: 0;
  }
  footer {
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
  }
  @page {
    margin-top: 470px;
  }
  .new-page {
    page-break-after: always;
    border-bottom: none;
  }
}
body {
  font-family: "Noto Serif CJK JP", serif;
  color: #333;
}
.line-1 {
  border: none;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, white, white 25%, black 25%, black 75%, white 75%, white);
  background-size: 10px 100%;
  opacity: 0.5;
}

dt {
  float: left;
  min-width: 100px;
  font-weight: bold;
  text-align: left;
}

dt::after {
  content: "　";
}

dd {
  margin: 0 0 0 100px;
  min-height: 23px;
}

.supplier {
  width: 300px;
}
.entry {
  width: 250px;
  float: right;
}
.supplier dt, .entry dt {
  text-align: right;
  min-width: 80px;
}
.supplier dd, .entry dd {
  margin: 0 0 0 80px;
}
.items table {
  border-collapse: collapse;
  width: 100%;
}
.items tr {
  border-top: 1px dashed rgba(0,0,0,0.5);
  border-bottom: 1px dashed rgba(0,0,0,0.5);
}

.items td {
  padding: 5px;
  border-left: 1px dashed rgba(0,0,0,0.5);
}

</style>
</head>
<body>
<header>
<div>
  <br/>
  <div class="supplier">
    <span style="margin-left: 10px; border-bottom: 1px solid black;padding:2px;">{{ supplier.name }}　御中</span>
    <dl>
      <dt style="min-width:80px">TEL</dt>
      <dd>{{ supplier.tel }}</dd>
      <dt style="min-width:80px">FAX</dt>
      <dd>{{ supplier.fax }}</dd>
    </dl>
  </div>
  <div style="position:absolute; width:100%; text-align:center; margin-top:-90px;">
    <h1 style="display:inline-block;border-bottom: 1px solid black;padding-bottom:2px">返礼品発注書</h1>
  </div>
  <div class="entry">
    <dl>
      <dt>発注日時</dt>
      <dd>{{ order_henrei.order_ts|date:"Y/m/d H:i" }}</dd>
      <dt>発注番号</dt>
      <dd>{{ order_henrei.pk }}</dd>
    </dl>
  </div>
  <div style="clear: right"></div>
</div>
<hr class="line-1"/>
<div>
  <dl>
    <dt>施行会館</dt>
    <dd>{{ entry.seko.seko_company.base_name }}　{{ schedule.0.hall_name }}</dd>
    <dt>施行日</dt>
    <dd>{{ schedule.0.schedule_date|date:"Y/m/d" }}　{{ schedule.0.begin_time|date:"H:i" }}</dd>
  </dl>
</div>
<hr class="line-1"/>
<div>
  <dl>
    <dt>喪主名</dt>
    <dd>
      <div style="display:inline-block; min-width:200px;">{{ entry.seko.moshu.name|default_if_none:"" }}</div>
      <div style="display: inline-block;">
        <span style="margin: 0px 10px 0px 10px; font-weight: bold;">電話番号</span>
        <span style="margin-left: 10px">{{ entry.seko.moshu.tel|default_if_none:"" }}</span>
      </div>
    </dd>
    <dt>住所</dt>
    <dd>{{ entry.seko.moshu.prefecture }}{{ entry.seko.moshu.address_1 }}{{ entry.seko.moshu.address_2 }}　{{ entry.seko.moshu.address_3|default_if_none:"" }}</dd>
  </dl>
</div>
<hr/>
<div>
  <dl>
    <dt>表書き</dt>
    <dd>
      <div style="display:inline-block; min-width:200px;">{{ entry.seko.henrei_kakegami.omotegaki|default_if_none:"" }}</div>
      <div style="display: inline-block;">
        <span style="margin: 0px 10px 0px 10px; font-weight: bold;">水引</span>
        <span style="margin-left: 10px">{{ entry.seko.henrei_kakegami.mizuhiki|default_if_none:"" }}</span>
      </div>
    </dd>
    <dt>送り主名</dt>
    <dd>
      <div style="display:inline-block; min-width:200px;">{{ entry.seko.henrei_kakegami.okurinushi_name|default_if_none:"" }}</div>
      <div style="display: inline-block;">
        <span style="margin: 0px 10px 0px 10px; font-weight: bold;">包装</span>
        <span style="margin-left: 10px">{{ entry.seko.henrei_kakegami.package_type_name|default_if_none:"" }}&nbsp;</span>
      </div>
    </dd>
  </dl>
</div>
</header>
<div class="items" style="padding: 5px">
  <table>
    {% for item in item_list %}
    <tr>
      <td rowspan="2" style="text-align: center; border-left: none; width: 80px;">{{ forloop.counter }}</td>
      <td style="text-align: center; width: 100px;">商品名</td>
      <td >{{ item.henreihin_name }}（{{ item.henreihin_hinban }}）</td>
    </tr>
    <tr {% if item.new_page %} class="new-page" {% endif %} >
      <td style="text-align: center; width: 100px;">お届け先 {{item_list.new_page}}</td>
      <td>
        <div>
          <div style="width:10ch; display:inline-block">{{ item.entry_zip_code|slice:"0:3" }}-{{ item.entry_zip_code|slice:"3:" }}</div>{{ item.entry_prefecture }}{{ item.entry_address_1 }}{{ item.entry_address_2 }}</div>
        <div style="padding-left:10ch">{{ item.entry_address_3|default_if_none:"" }}</div>
        <div>
          <div style="display:inline-block; min-width:15ch;">{{ item.entry_name }} 様</div>
          <div style="display: inline-block;">{{ item.entry_tel }}</div>
        </div>
      </td>
    </tr>
    {% endfor %}
  </table>
</div>
<footer>
<hr/>
<div>
  <dl>
    <dt>備考</dt>
    <dd style="height:2.5ch; position:relative">
      <pre style="position: absolute; top:0; left:0; font-family: inherit; white-space: pre-wrap; margin:0; width:100%; height:100%; overflow:hidden">{{ order_henrei.order_note|default_if_none:"" }}</pre>
    </dd>
  </dl>
</div>
<hr class="line-1"/>
<div>
  <dl>
    <dt>発注担当</dt>
    <dd>{{ order_henrei.order_staff.name }}<span>&nbsp;</span></dd>
  </dl>
</div>
<hr class="line-1"/>
</footer>
</body>
</html>
