# FOCバックエンド ファイル読み込み順序

## 概要

FOCバックエンド（Django）でのファイル読み込み順序を詳細に説明します。Djangoアプリケーションの起動からAPIレスポンスまで、どのファイルがどの順序で読み込まれるかを理解できます。

## 1. Django アプリケーション起動フロー

### 1.1 サーバー起動段階

```
1. manage.py (エントリーポイント)
   ↓
2. foc/settings.py (設定読み込み)
   ↓
3. foc/wsgi.py (WSGIアプリケーション)
   ↓
4. Django Core 初期化
   ↓
5. INSTALLED_APPS 読み込み
   ↓
6. foc/urls.py (ルートURL設定)
```

#### 詳細説明

**1. manage.py**
```python
#!/usr/bin/env python
import os
import sys

def main():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'foc.settings')
    from django.core.management import execute_from_command_line
    execute_from_command_line(sys.argv)

if __name__ == '__main__':
    main()
```
- Djangoアプリケーションのエントリーポイント
- 環境変数 `DJANGO_SETTINGS_MODULE` を設定
- Django管理コマンドの実行

**2. foc/settings.py**
```python
# 環境変数読み込み
import environ
from django.utils.timezone import timedelta
import utils.jwt_keys as jwt_keys

# アプリケーション定義
INSTALLED_APPS = [
    # Django標準アプリ
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    # サードパーティアプリ
    'rest_framework',
    'corsheaders',
    'django_filters',
]

# プロジェクトアプリ
PROJECT_APPS = [
    'masters.apps.MastersConfig',
    'bases.apps.BasesConfig',
    'staffs.apps.StaffsConfig',
    'seko.apps.SekoConfig',
    # ... 他のアプリ
]
INSTALLED_APPS.extend(PROJECT_APPS)
```

**3. foc/wsgi.py**
```python
import os
from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'foc.settings')
application = get_wsgi_application()
```

### 1.2 アプリケーション読み込み順序

```python
# settings.py での INSTALLED_APPS 読み込み順序

# 1. Django標準アプリ
'django.contrib.admin',
'django.contrib.auth',
'django.contrib.contenttypes',
'django.contrib.sessions',
'django.contrib.messages',
'django.contrib.staticfiles',

# 2. サードパーティアプリ
'rest_framework',
'corsheaders',
'django_filters',
'mptt',

# 3. プロジェクトアプリ（依存関係順）
'masters.apps.MastersConfig',        # マスターデータ（基盤）
'bases.apps.BasesConfig',            # 拠点管理
'staffs.apps.StaffsConfig',          # スタッフ管理
'seko.apps.SekoConfig',              # 施行管理
'suppliers.apps.SuppliersConfig',    # 仕入先管理
'items.apps.ItemsConfig',            # 商品管理
'orders.apps.OrdersConfig',          # 注文管理
'henrei.apps.HenreiConfig',          # 返礼品管理
'hoyo.apps.HoyoConfig',              # 法要管理
# ... 他のアプリ
```

## 2. HTTP リクエスト処理フロー

### 2.1 API リクエスト時の読み込み順序

```
1. foc/urls.py (ルートURL解析)
   ↓
2. {app}/urls.py (アプリ別URL解析)
   ↓
3. utils/authentication.py (JWT認証)
   ↓
4. utils/permissions.py (権限チェック)
   ↓
5. {app}/views.py (ビュークラス)
   ↓
6. {app}/serializers.py (シリアライザー)
   ↓
7. {app}/models.py (モデル・データベース)
```

#### 例: `/api/staffs/me/` アクセス時

```python
# 1. foc/urls.py
urlpatterns = [
    path('staffs/', include('staffs.urls')),
    # ...
]

# 2. staffs/urls.py
urlpatterns = [
    path('me/', views.StaffMe.as_view(), name='me'),
    # ...
]

# 3. utils/authentication.py
class ClassableJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        # JWT認証処理
        user_class = validated_token['user_class']
        if user_class == 'Moshu':
            user = Moshu.objects.get(pk=user_id)
        else:
            user = User.objects.get(pk=user_id)
        return user

# 4. utils/permissions.py
class IsStaff(BasePermission):
    def has_permission(self, request, view):
        return isinstance(request.user, Staff)

# 5. staffs/views.py
class StaffMe(generics.RetrieveAPIView):
    serializer_class = StaffParentBasesSerializer
    permission_classes = [IsStaff]
    
    def get_object(self):
        return self.request.user

# 6. staffs/serializers.py
class StaffParentBasesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Staff
        fields = ['id', 'name', 'mail_address']

# 7. staffs/models.py
class Staff(BaseModel):
    name = models.CharField(max_length=100)
    mail_address = models.EmailField()
```

## 3. ミドルウェア処理順序

### 3.1 リクエスト処理時のミドルウェア

```python
# settings.py での MIDDLEWARE 設定順序
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',      # セキュリティ
    'whitenoise.middleware.WhiteNoiseMiddleware',         # 静的ファイル
    'django.contrib.sessions.middleware.SessionMiddleware', # セッション
    'django.middleware.common.CommonMiddleware',          # 共通処理
    'django.middleware.csrf.CsrfViewMiddleware',          # CSRF保護
    'django.contrib.auth.middleware.AuthenticationMiddleware', # 認証
    'django.contrib.messages.middleware.MessageMiddleware', # メッセージ
    'django.middleware.clickjacking.XFrameOptionsMiddleware', # クリックジャッキング保護
    'corsheaders.middleware.CorsMiddleware',              # CORS
]
```

### 3.2 ミドルウェア実行順序

```
リクエスト時（上から下へ）:
1. SecurityMiddleware
2. WhiteNoiseMiddleware
3. SessionMiddleware
4. CommonMiddleware
5. CsrfViewMiddleware
6. AuthenticationMiddleware
7. MessageMiddleware
8. XFrameOptionsMiddleware
9. CorsMiddleware

レスポンス時（下から上へ）:
9. CorsMiddleware
8. XFrameOptionsMiddleware
7. MessageMiddleware
6. AuthenticationMiddleware
5. CsrfViewMiddleware
4. CommonMiddleware
3. SessionMiddleware
2. WhiteNoiseMiddleware
1. SecurityMiddleware
```

## 4. アプリケーション別ファイル構造

### 4.1 各アプリの標準ファイル構成

```
{app_name}/
├── __init__.py          # Pythonパッケージ定義
├── apps.py              # アプリケーション設定
├── models.py            # データモデル定義
├── serializers.py       # APIシリアライザー
├── views.py             # ビュー（APIエンドポイント）
├── urls.py              # URL設定
├── admin.py             # Django管理画面設定
├── migrations/          # データベースマイグレーション
└── tests/               # テストファイル
```

### 4.2 アプリ読み込み時のファイル順序

```
1. {app}/apps.py (アプリケーション設定)
   ↓
2. {app}/models.py (モデル定義)
   ↓
3. {app}/__init__.py (パッケージ初期化)
   ↓
4. リクエスト時に動的読み込み:
   - {app}/urls.py
   - {app}/views.py
   - {app}/serializers.py
```

## 5. 依存関係に基づく読み込み順序

### 5.1 モデル依存関係

```
1. masters (マスターデータ) - 基盤
   ↓
2. bases (拠点) - mastersに依存
   ↓
3. staffs (スタッフ) - basesに依存
   ↓
4. seko (施行) - bases, staffsに依存
   ↓
5. orders (注文) - seko, itemsに依存
   ↓
6. henrei (返礼品) - ordersに依存
```

### 5.2 utils モジュール

```python
# utils/ - 共通ユーティリティ（最初に読み込まれる）
utils/
├── __init__.py
├── authentication.py    # JWT認証クラス
├── permissions.py       # 権限クラス
├── serializers.py       # 共通シリアライザー
├── view_mixins.py       # ビューミックスイン
├── filters.py           # フィルタークラス
└── jwt_keys.py          # JWT鍵管理
```

## 6. 具体的なリクエスト処理例

### 例1: スタッフログイン (`POST /staffs/login/`)

```
1. foc/urls.py → path('staffs/', include('staffs.urls'))
2. staffs/urls.py → path('login/', views.StaffTokenObtainPair.as_view())
3. staffs/views.py → StaffTokenObtainPair クラス
4. staffs/serializers.py → StaffTokenObtainPairSerializer
5. utils/authentication.py → ClassableRefreshToken.for_user()
6. staffs/models.py → Staff モデル
7. レスポンス生成・返却
```

### 例2: 施行一覧取得 (`GET /seko/`)

```
1. foc/urls.py → path('seko/', include('seko.urls'))
2. seko/urls.py → path('', views.SekoList.as_view())
3. utils/authentication.py → ClassableJWTAuthentication
4. utils/permissions.py → IsStaff 権限チェック
5. seko/views.py → SekoList クラス
6. seko/serializers.py → SekoSerializer
7. seko/models.py → Seko モデル
8. データベースクエリ実行
9. レスポンス生成・返却
```

### 例3: 施行詳細更新 (`PUT /seko/{id}/`)

```
1. URL解析・ルーティング
2. JWT認証・権限チェック
3. seko/views.py → SekoDetail.put()
4. seko/serializers.py → バリデーション
5. seko/models.py → データベース更新
6. レスポンス生成
```

## 7. データベース関連の読み込み

### 7.1 マイグレーション実行時

```
1. manage.py migrate
2. foc/settings.py (設定読み込み)
3. INSTALLED_APPS 順序でマイグレーション実行:
   - masters/migrations/
   - bases/migrations/
   - staffs/migrations/
   - seko/migrations/
   - ... (依存関係順)
```

### 7.2 モデル読み込み順序

```python
# Django起動時のモデル読み込み
1. masters.models (基盤マスター)
2. bases.models (拠点モデル)
3. staffs.models (スタッフモデル)
4. seko.models (施行モデル)
# ... 依存関係に基づく順序
```

## 8. 設定ファイル読み込み順序

### 8.1 環境設定

```
1. .env ファイル (環境変数)
2. foc/settings.py (Django設定)
3. utils/jwt_keys.py (JWT設定)
4. 各アプリの apps.py (アプリ固有設定)
```

### 8.2 URL設定読み込み

```
1. foc/urls.py (ルートURL)
2. 各アプリのurls.py (アプリ別URL):
   - masters/urls.py
   - bases/urls.py
   - staffs/urls.py
   - seko/urls.py
   - ... (INSTALLED_APPS順)
```

## 9. パフォーマンス最適化のポイント

### 9.1 現在の特徴
- **モジュラー設計**: アプリ別の明確な分離
- **依存関係管理**: 適切なアプリ読み込み順序
- **JWT認証**: ステートレスな認証システム

### 9.2 最適化提案
1. **データベースクエリ最適化**: select_related, prefetch_related の活用
2. **キャッシュ戦略**: Redis等を使用したクエリキャッシュ
3. **非同期処理**: Celery等を使用したバックグラウンド処理

この読み込み順序を理解することで、デバッグ時の問題特定や、新機能開発時の適切な配置場所の決定に役立てることができます。
