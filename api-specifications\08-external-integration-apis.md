# 外部連携API仕様書

## 概要

FOCシステムの外部連携API（FDN連携、SMS送信、決済システム連携等）の詳細仕様です。外部システムとの安全で効率的なデータ連携機能を提供します。

## 1. FDN連携API

### 1.1 FDN施行作成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /api/seko/fdn/` |
| **機能** | FDNシステムから施行情報を作成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `FdnSekoCreater` |
| **シリアライザー** | `FdnSekoCreateSerializer` |

**HTTPメソッド:** `POST`

**会社コード判定:**
```python
company: Base = Base.objects.filter(
    Q(del_flg=False),
    Q(base_type=Base.OrgType.COMPANY),
    Q(company_code=self.request.user.company_code),
).first()
```

**リクエストボディ:**
```json
{
  "fdn_seko_code": "FDN202406001",
  "soke_name": "田中家",
  "seko_style": 1,
  "death_date": "2024-06-20",
  "seko_date": "2024-06-25T10:00:00+09:00",
  "seko_place": "自宅",
  "seko_bumon_code": "BU001",
  "jyuchu_tantou_code": "ST001",
  "seko_tantou_code": "ST002",
  "af_tantou_code": "ST003",
  "shikijo_code": "VN001",
  "moshu": {
    "name": "田中 太郎",
    "kana": "タナカ タロウ",
    "kojin_moshu_relationship": 1,
    "zip_code": "1000001",
    "prefecture": "東京都",
    "address_1": "千代田区千代田",
    "address_2": "1-1-1",
    "tel": "03-1234-5678",
    "mail_address": "<EMAIL>"
  },
  "kojin": [
    {
      "kojin_num": 1,
      "name": "田中 一郎",
      "kana": "タナカ イチロウ",
      "moshu_kojin_relationship": 1,
      "age_kbn": 1,
      "age": 75,
      "birth_date": "1949-01-01",
      "death_date": "2024-06-20"
    }
  ],
  "schedules": [
    {
      "schedule_name": "通夜",
      "scheduled_datetime": "2024-06-24T18:00:00+09:00",
      "venue_name": "自宅"
    },
    {
      "schedule_name": "告別式",
      "scheduled_datetime": "2024-06-25T10:00:00+09:00",
      "venue_name": "自宅"
    }
  ]
}
```

**バリデーション:**
| フィールド | 制約 | エラーメッセージ |
|---|---|---|
| `fdn_seko_code` | 必須、重複不可 | この施行情報(施行番号=XXX)はすでに連携済みです |
| `seko_bumon_code` | 存在確認 | 施行部門コード(XXX)がFOCシステムに存在しません |
| `jyuchu_tantou_code` | 存在確認 | 受注担当者コード(XXX)がFOCシステムに存在しません |
| `seko_tantou_code` | 存在確認 | 施行担当者コード(XXX)がFOCシステムに存在しません |
| `af_tantou_code` | 存在確認 | AF担当者コード(XXX)がFOCシステムに存在しません |
| `shikijo_code` | 存在確認 | 式場コード(XXX)がFOCシステムに存在しません |

**レスポンス例:**
```json
{
  "id": 456,
  "fdn_code": "FDN202406001",
  "soke_name": "田中家",
  "seko_style": {
    "id": 1,
    "name": "家族葬"
  },
  "death_date": "2024-06-20",
  "seko_date": "2024-06-25T10:00:00+09:00",
  "moshu": {
    "id": 789,
    "name": "田中 太郎",
    "mail_address": "<EMAIL>"
  },
  "kojin": [
    {
      "id": 101,
      "kojin_num": 1,
      "name": "田中 一郎",
      "death_date": "2024-06-20"
    }
  ],
  "schedules": [
    {
      "id": 201,
      "schedule_name": "通夜",
      "scheduled_datetime": "2024-06-24T18:00:00+09:00"
    },
    {
      "id": 202,
      "schedule_name": "告別式",
      "scheduled_datetime": "2024-06-25T10:00:00+09:00"
    }
  ],
  "created_at": "2024-06-23T10:00:00+09:00"
}
```

### 1.2 FDN施行更新API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `PUT/PATCH /api/seko/fdn/{id}/` |
| **機能** | FDNシステムからの施行データ更新 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `FdnSekoUpdate` |
| **シリアライザー** | `FdnSekoUpdateSerializer` |

**HTTPメソッド:** `PUT/PATCH`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 施行ID |

**リクエストボディ:**
```json
{
  "fdn_code": "FDN202406001_UPDATED"
}
```

**レスポンス例:**
```json
{
  "id": 456,
  "fdn_code": "FDN202406001_UPDATED",
  "updated_at": "2024-06-26T10:00:00+09:00"
}
```

### 1.3 FDN注文一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /api/orders/fdn/` |
| **機能** | FDN連携用の注文データ一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `FdnOrderList` |
| **シリアライザー** | `FdnOrderListSerializer` |

**HTTPメソッド:** `GET`

**クエリパラメータ:**
| パラメータ | 型 | 必須 | 説明 | 使用例 |
|---|---|---|---|---|
| `company` | integer | ○ | 会社ID | `?company=1` |
| `seko_id` | integer | ○ | 施行ID | `?seko_id=123` |
| `entry_id` | integer | ○ | 注文ID | `?entry_id=456` |
| `service_id` | integer | ○ | サービスID | `?service_id=1` |
| `order_from` | date | ○ | 注文日（開始） | `?order_from=2024-06-01` |
| `order_to` | date | ○ | 注文日（終了） | `?order_to=2024-06-30` |

**レスポンス例:**
```json
[
  {
    "seko_id": 456,
    "soke_name": "田中家",
    "entry_id": 123,
    "entry_detail_id": 789,
    "service_id": 1,
    "service_name": "弔文作成",
    "quantity": 1,
    "unit_price": 5000,
    "total_amount": 5000,
    "order_type": "chobun",
    "order_status": 1,
    "order_ts": null,
    "entry_name": "田中 太郎",
    "entry_name_kana": "タナカ タロウ",
    "entry_tel": "03-1234-5678",
    "entry_mail_address": "<EMAIL>",
    "created_at": "2024-06-25T14:30:00+09:00"
  }
]
```

## 2. SMS送信API

### 2.1 SMS送信登録API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | 内部関数 `register_send_sms` |
| **機能** | SMS送信登録 |
| **外部API** | AOS SMS API |
| **実装ファイル** | `utils/sms.py` |
| **設定URL** | `SMS_REQUEST_URL` |

**関数シグネチャ:**
```python
def register_send_sms(
    token: str,
    client_id: str,
    sms_code: str,
    message: str,
    phone_number: str
) -> SmsResponseData
```

**パラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `token` | string | ✓ | SMS認証トークン |
| `client_id` | string | ✓ | クライアントID |
| `sms_code` | string | ✓ | SMS送信者コード |
| `message` | string | ✓ | 送信メッセージ（最大60文字） |
| `phone_number` | string | ✓ | 送信先電話番号 |

**リクエストデータ:**
```python
@dataclass
class SmsRequestData:
    token: str
    client_id: str
    sms_code: str
    message: str
    phone_number: str
    
    def as_post_data(self) -> Dict[str, str]:
        return {
            'token': self.token,
            'clientId': self.client_id,
            'smsCode': self.sms_code,
            'message': self.message,
            'phoneNumber': self.phone_number,
        }
```

**レスポンスデータ:**
```python
@dataclass
class SmsResponseData:
    response_code: int
    response_message: str
    phone_number: str
    sms_message: str
```

**レスポンス例（成功）:**
```json
{
  "responseCode": 0,
  "responseMessage": "Success.",
  "phoneNumber": "+************",
  "smsMessage": "FOCからのお知らせです。"
}
```

**レスポンス例（失敗）:**
```json
{
  "responseCode": 120,
  "responseMessage": "Message too long.",
  "phoneNumber": "+************",
  "smsMessage": "メッセージが長すぎます..."
}
```

### 2.2 SMS設定管理API

#### 基本情報
| 項目 | 値 |
|---|---|
| **モデル** | `SmsAccount` |
| **関連** | `Base`（会社）との1対1関係 |
| **テーブル** | `m_sms_account` |

**モデル構造:**
```python
class SmsAccount(models.Model):
    company = models.OneToOneField(Base, models.CASCADE, primary_key=True)
    token = models.TextField()
    client_id = models.TextField()
    sms_code = models.CharField(max_length=5, blank=True, null=True)
```

**フィールド:**
| フィールド | 型 | 説明 |
|---|---|---|
| `company` | OneToOneField | 会社（主キー） |
| `token` | TextField | SMS認証トークン |
| `client_id` | TextField | クライアントID |
| `sms_code` | CharField | SMS送信者コード（5文字） |

## 3. Epsilon決済システム連携API

### 3.1 決済登録API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | 内部関数 `register_payment` |
| **機能** | Epsilon決済登録 |
| **外部API** | Epsilon決済システム |
| **実装ファイル** | `orders/epsilon.py` |
| **設定URL** | `EPSILON_REGISTER_PAYMENT_URL` |

**関数シグネチャ:**
```python
def register_payment(
    contract_code: str,
    token: str,
    user_name: str,
    user_mail_address: str,
    price: int,
    user_agent: str,
    gmo_test_flg: bool = False,
    secure_chk_flg: bool = False,
) -> ResponsePayment
```

**パラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `contract_code` | string | ✓ | 契約コード |
| `token` | string | ✓ | 決済トークン |
| `user_name` | string | ✓ | ユーザー名 |
| `user_mail_address` | string | ✓ | ユーザーメールアドレス |
| `price` | integer | ✓ | 決済金額 |
| `user_agent` | string | ✓ | ユーザーエージェント |
| `gmo_test_flg` | boolean | ○ | テストモードフラグ |
| `secure_chk_flg` | boolean | ○ | セキュリティチェックフラグ |

**リクエストデータ:**
```python
@dataclass
class RequestPayment:
    contract_code: str
    token: str
    user_name: str
    user_mail_add: str
    item_price: int
    user_agent: str
    user_id: str = 'user0000'
    item_code: str = 'FOC'
    item_name: str = 'FOCサービス利用'
    st_code: str = '11000-0000-00000'
    mission_code: str = '1'
    process_code: str = '1'
    order_number: Optional[int] = None

**レスポンスデータ:**
```python
@dataclass
class ResponsePayment:
    result: str
    trans_code: str
    err_code: str
    err_detail: str
    acsurl: str
    pareq: str
    kari_flag: str
```

**レスポンス例（成功）:**
```xml
<?xml version="1.0" encoding="x-sjis-cp932"?>
<Epsilon_result>
    <result acsurl="" />
    <result err_code="" />
    <result err_detail="" />
    <result kari_flag="0" />
    <result pareq="" />
    <result result="1" />
    <result trans_code="1589043" />
</Epsilon_result>
```

### 3.2 決済戻り処理API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /orders/epsilon/return/` |
| **機能** | Epsilon決済システムからの戻り処理 |
| **認証** | 不要 |
| **権限** | なし |
| **実装クラス** | `EpsilonReturn` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "PaRes": "eJxVUstuwjAQ/BXLd+PEJCHhVqhAVaGqUqjUW+TYm2Cx7a29Dqj/XpuHVLXSHmZnZ2Z3V...",
  "MD": "dummy_contract,987654,false"
}
```

**処理フロー:**
1. `MD`パラメータから契約コード、注文番号、テストフラグを分離
2. `register_payment2`関数で決済完了処理
3. 決済結果をデータベースに保存
4. 注文ステータス更新

**レスポンス例:**
```json
{
  "status": "success",
  "message": "決済結果を正常に処理しました。",
  "entry_id": 123,
  "payment_status": "SUCCESS",
  "trans_code": "1589043"
}
```

### 3.3 決済結果管理API

#### 基本情報
| 項目 | 値 |
|---|---|
| **モデル** | `PaymentResult` |
| **関連** | `Entry`（注文）との関係 |
| **実装クラス** | `PaymentResultListView` |

**モデル構造:**
```python
class PaymentResult(models.Model):
    entry = models.ForeignKey(Entry, on_delete=models.CASCADE)
    payment_method = models.CharField(max_length=20)
    payment_amount = models.IntegerField()
    payment_status = models.CharField(max_length=20)
    transaction_id = models.CharField(max_length=50)
    epsilon_order_number = models.IntegerField()
    payment_ts = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
```

## 4. 外部システム認証・認可

### 4.1 FDN連携認証

#### 認証方式
| 項目 | 値 |
|---|---|
| **認証方式** | JWT認証 |
| **認証クラス** | `ClassableJWTAuthentication` |
| **権限クラス** | `IsStaff` |
| **会社コード判定** | `request.user.company_code` |

**認証フロー:**
1. JWTトークンによるユーザー認証
2. ユーザーの会社コード取得
3. 会社コードによるデータアクセス制御
4. スタッフ権限の確認

### 4.2 SMS送信認証

#### 認証方式
| 項目 | 値 |
|---|---|
| **認証方式** | トークンベース認証 |
| **認証情報** | `SmsAccount`モデル |
| **必要情報** | `token`, `client_id`, `sms_code` |

**認証情報管理:**
```python
# 会社ごとのSMS設定取得
sms_account = SmsAccount.objects.get(company=company)
token = sms_account.token
client_id = sms_account.client_id
sms_code = sms_account.sms_code
```

### 4.3 Epsilon決済認証

#### 認証方式
| 項目 | 値 |
|---|---|
| **認証方式** | 契約コードベース認証 |
| **認証情報** | `contract_code` |
| **セキュリティ** | HTTPS通信、XML署名検証 |

## 5. エラーコード一覧

### 5.1 FDN連携エラー

| コード | メッセージ | 発生条件 |
|---|---|---|
| E001 | この施行情報(施行番号=XXX)はすでに連携済みです | FDN施行番号の重複 |
| E002 | 施行部門コード(XXX)がFOCシステムに存在しません | 部門コードが見つからない |
| E003 | 受注担当者コード(XXX)がFOCシステムに存在しません | 受注担当者コードが見つからない |
| E004 | 施行担当者コード(XXX)がFOCシステムに存在しません | 施行担当者コードが見つからない |
| E005 | AF担当者コード(XXX)がFOCシステムに存在しません | AF担当者コードが見つからない |
| E006 | 式場コード(XXX)がFOCシステムに存在しません | 式場コードが見つからない |
| E007 | Base not found: company_code=XXX | 会社コードが見つからない |

### 5.2 SMS送信エラー

| レスポンスコード | メッセージ | 発生条件 |
|---|---|---|
| 0 | Success | 送信成功 |
| 120 | Message too long | メッセージが長すぎる |
| 121 | Invalid phone number | 無効な電話番号 |
| 122 | Authentication failed | 認証失敗 |
| 123 | Insufficient balance | 残高不足 |

### 5.3 Epsilon決済エラー

| 結果コード | 説明 | 対応 |
|---|---|---|
| 1 | 決済成功 | 正常処理 |
| 2 | 決済失敗 | エラー詳細確認 |
| 3 | 3Dセキュア認証必要 | 追加認証処理 |
| 9 | システムエラー | リトライまたは問い合わせ |

## 6. セキュリティ考慮事項

### 6.1 FDN連携セキュリティ

- **データ分離**: 会社コードによる完全なデータ分離
- **認証必須**: 全てのAPIでJWT認証が必要
- **権限チェック**: スタッフ権限の確認
- **入力検証**: FDNコードの重複チェックと存在確認
- **トランザクション**: @transaction.atomicによる整合性保証

### 6.2 SMS送信セキュリティ

- **認証情報保護**: トークンとクライアントIDの安全な管理
- **送信制限**: メッセージ長制限（60文字）
- **電話番号検証**: 国際電話番号形式の検証
- **送信頻度制限**: スパム防止のための制限

### 6.3 決済システムセキュリティ

- **HTTPS通信**: 全ての決済通信でHTTPS必須
- **トークン管理**: 決済トークンの適切な管理
- **PCI DSS準拠**: クレジットカード情報の非保持
- **3Dセキュア**: 追加認証による不正利用防止
- **ログ管理**: 決済ログの適切な記録と保護

## 7. パフォーマンス最適化

### 7.1 FDN連携最適化

- **バッチ処理**: 大量データの効率的な処理
- **select_related**: 関連オブジェクトの効率的な取得
- **インデックス**: FDNコードへの適切なインデックス
- **キャッシュ**: マスターデータのキャッシュ活用

### 7.2 外部API最適化

- **接続プール**: HTTP接続の再利用
- **タイムアウト設定**: 適切なタイムアウト値の設定
- **リトライ機構**: 一時的な障害への対応
- **非同期処理**: 重い処理の非同期化

## 8. 使用例

### 8.1 FDN施行作成完全フロー
```javascript
// FDN施行データ作成
const createFdnSeko = async (fdnData) => {
  const response = await fetch('/api/seko/fdn/', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${staffToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      fdn_seko_code: fdnData.sekoCode,
      soke_name: fdnData.sokeName,
      seko_style: fdnData.sekoStyle,
      death_date: fdnData.deathDate,
      seko_date: fdnData.sekoDate,
      seko_place: fdnData.sekoPlace,
      seko_bumon_code: fdnData.bumonCode,
      jyuchu_tantou_code: fdnData.jyuchuTantouCode,
      seko_tantou_code: fdnData.sekoTantouCode,
      af_tantou_code: fdnData.afTantouCode,
      shikijo_code: fdnData.shikijoCode,
      moshu: {
        name: fdnData.moshu.name,
        kana: fdnData.moshu.kana,
        kojin_moshu_relationship: fdnData.moshu.relationship,
        zip_code: fdnData.moshu.zipCode,
        prefecture: fdnData.moshu.prefecture,
        address_1: fdnData.moshu.address1,
        address_2: fdnData.moshu.address2,
        tel: fdnData.moshu.tel,
        mail_address: fdnData.moshu.email
      },
      kojin: fdnData.kojin.map(k => ({
        kojin_num: k.num,
        name: k.name,
        kana: k.kana,
        moshu_kojin_relationship: k.relationship,
        age_kbn: k.ageKbn,
        age: k.age,
        birth_date: k.birthDate,
        death_date: k.deathDate
      })),
      schedules: fdnData.schedules.map(s => ({
        schedule_name: s.name,
        scheduled_datetime: s.datetime,
        venue_name: s.venue
      }))
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'FDN施行作成に失敗しました');
  }

  return response.json();
};

// FDN注文データ取得
const getFdnOrders = async (filters) => {
  const params = new URLSearchParams();
  if (filters.company) params.append('company', filters.company);
  if (filters.sekoId) params.append('seko_id', filters.sekoId);
  if (filters.entryId) params.append('entry_id', filters.entryId);
  if (filters.serviceId) params.append('service_id', filters.serviceId);
  if (filters.orderFrom) params.append('order_from', filters.orderFrom);
  if (filters.orderTo) params.append('order_to', filters.orderTo);

  const response = await fetch(`/api/orders/fdn/?${params}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });

  return response.json();
};
```

### 8.2 SMS送信処理
```python
# SMS送信処理の実装例
from utils.sms import register_send_sms
from bases.models import SmsAccount

def send_notification_sms(company_id, phone_number, message):
    try:
        # SMS設定取得
        sms_account = SmsAccount.objects.get(company_id=company_id)

        # SMS送信
        result = register_send_sms(
            token=sms_account.token,
            client_id=sms_account.client_id,
            sms_code=sms_account.sms_code,
            message=message,
            phone_number=phone_number
        )

        if result.response_code == 0:
            return {'status': 'success', 'message': 'SMS送信成功'}
        else:
            return {
                'status': 'error',
                'code': result.response_code,
                'message': result.response_message
            }

    except SmsAccount.DoesNotExist:
        return {'status': 'error', 'message': 'SMS設定が見つかりません'}
    except Exception as e:
        return {'status': 'error', 'message': str(e)}
```

### 8.3 Epsilon決済処理
```python
# Epsilon決済処理の実装例
from orders.epsilon import register_payment, register_payment2

def process_payment(order_data):
    try:
        # 決済登録
        result = register_payment(
            contract_code=order_data['contract_code'],
            token=order_data['epsilon_token'],
            user_name=order_data['user_name'],
            user_mail_address=order_data['user_email'],
            price=order_data['amount'],
            user_agent=order_data['user_agent']
        )

        if result.result == '1':
            # 決済成功
            return {
                'status': 'success',
                'trans_code': result.trans_code,
                'order_number': result.order_number
            }
        elif result.result == '3':
            # 3Dセキュア認証必要
            return {
                'status': '3d_secure',
                'acsurl': result.acsurl,
                'pareq': result.pareq
            }
        else:
            # 決済失敗
            return {
                'status': 'error',
                'err_code': result.err_code,
                'err_detail': result.err_detail
            }

    except Exception as e:
        return {'status': 'error', 'message': str(e)}

def complete_3d_secure_payment(contract_code, pares, order_number):
    try:
        # 3Dセキュア完了処理
        result = register_payment2(
            contract_code=contract_code,
            tds_pares=pares,
            order_number=order_number
        )

        return {
            'status': 'success' if result.result == '1' else 'error',
            'trans_code': result.trans_code,
            'err_code': result.err_code,
            'err_detail': result.err_detail
        }

    except Exception as e:
        return {'status': 'error', 'message': str(e)}
```

### 8.4 外部連携エラーハンドリング
```javascript
// 外部連携エラーハンドリングの実装例
class ExternalApiClient {
  async callWithRetry(apiCall, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        console.error(`API呼び出し失敗 (試行 ${attempt}/${maxRetries}):`, error);

        if (attempt === maxRetries) {
          throw error;
        }

        // 指数バックオフでリトライ
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  async sendSms(phoneNumber, message) {
    return this.callWithRetry(async () => {
      const response = await fetch('/api/sms/send/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phone_number: phoneNumber, message })
      });

      if (!response.ok) {
        throw new Error(`SMS送信失敗: ${response.status}`);
      }

      return response.json();
    });
  }

  async processPayment(paymentData) {
    return this.callWithRetry(async () => {
      const response = await fetch('/api/payment/process/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(paymentData)
      });

      if (!response.ok) {
        throw new Error(`決済処理失敗: ${response.status}`);
      }

      return response.json();
    });
  }
}
```
```
