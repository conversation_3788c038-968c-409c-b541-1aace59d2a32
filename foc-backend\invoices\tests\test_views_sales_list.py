from typing import Dict, List

import faker
from dateutil.relativedelta import relativedelta
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from invoices.tests.factories import (
    SalesCompanyFactory,
    SalesDetailFactory,
    SalesIndexFactory,
    SalesSekoDepartmentFactory,
)
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

fake_provider = faker.Faker('ja_JP')
jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class SalesListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.sales_index_1 = SalesIndexFactory(sum_ts=timezone.localtime())
        self.sales_index_2 = SalesIndexFactory(
            sum_ts=timezone.localtime() + relativedelta(months=-1)
        )

        sales_company_1 = SalesCompanyFactory(sales_index=self.sales_index_1, company_id=1)
        sales_company_2 = SalesCompanyFactory(sales_index=self.sales_index_1, company_id=2)
        sales_company_3 = SalesCompanyFactory(sales_index=self.sales_index_1, company_id=3)
        sales_company_4 = SalesCompanyFactory(sales_index=self.sales_index_2, company_id=1)
        sales_company_5 = SalesCompanyFactory(sales_index=self.sales_index_2, company_id=2)

        SalesDetailFactory(
            sales_seko_department=SalesSekoDepartmentFactory(sales_company=sales_company_1)
        )
        SalesDetailFactory(
            sales_seko_department=SalesSekoDepartmentFactory(sales_company=sales_company_2)
        )
        SalesDetailFactory(
            sales_seko_department=SalesSekoDepartmentFactory(sales_company=sales_company_3)
        )
        SalesDetailFactory(
            sales_seko_department=SalesSekoDepartmentFactory(sales_company=sales_company_4)
        )
        SalesDetailFactory(
            sales_seko_department=SalesSekoDepartmentFactory(sales_company=sales_company_5)
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_sales_list_succeed(self) -> None:
        """売上集計一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('invoices:sales_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

    def test_sales_list_filter_by_sales_yymm(self) -> None:
        """売上集計一覧APIで集計月度を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'sales_yymm': self.sales_index_2.sales_yymm}
        response = self.api_client.get(reverse('invoices:sales_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_sales_list_filter_by_fiscal_year(self) -> None:
        """売上集計一覧APIで集計年度を検索する"""
        self.sales_index_2.fiscal_year = self.sales_index_1.fiscal_year - 1
        self.sales_index_2.save()

        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'fiscal_year': self.sales_index_1.fiscal_year}
        response = self.api_client.get(reverse('invoices:sales_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_sales_list_filter_by_company_id(self) -> None:
        """売上集計一覧APIで葬儀社IDを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'company': 1}
        response = self.api_client.get(reverse('invoices:sales_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)
