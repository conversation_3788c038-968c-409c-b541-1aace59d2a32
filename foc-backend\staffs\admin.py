from django import forms
from django.contrib import admin
from django.contrib.auth.models import Group
from django.utils.translation import gettext_lazy as _

from bases.models import Base
from staffs.models import Staff


class StaffForm(forms.ModelForm):
    """スタッフ追加フォーム"""

    base = forms.ModelChoiceField(label=_('base'), queryset=Base.objects)
    name = forms.CharField(label=_('staffname'), widget=forms.TextInput)
    login_id = forms.CharField(label=_('login_id'), widget=forms.TextInput)
    password1 = forms.CharField(label=_('password'), widget=forms.PasswordInput)
    password2 = forms.CharField(label=_('password confirmation'), widget=forms.PasswordInput)

    class Meta:
        model = Staff
        fields = [
            'base',
            'name',
            'login_id',
            'password1',
            'password2',
            'mail_address',
            'retired_flg',
        ]

    def clean_password2(self) -> str:
        """パスワード入力確認Validation

        Raises:
            forms.ValidationError: パスワードが不一致の場合発生します

        Returns:
            str: password2の値
        """
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError(_("Passwords don't match"))
        return password2

    def save(self, commit: bool = True) -> Staff:
        """パスワードをハッシュ化し、スタッフを保存します

        Args:
            commit (bool, optional): Defaults to True. データベースにcommitするか否か

        Returns:
            Staff: 保存したスタッフ
        """
        staff = super().save(commit=False)
        if self.cleaned_data['password1']:
            staff.set_password(self.cleaned_data['password1'])
        if commit:
            staff.save()
        return staff


@admin.register(Staff)
class StaffAdmin(admin.ModelAdmin):
    """スタッフの管理ページ"""

    form = StaffForm

    readonly_fields = ['created_at', 'updated_at']
    list_display = ['base', 'name', 'login_id', 'created_at', 'updated_at']
    list_display_links = ['name', 'login_id']
    search_fields = ['name', 'login_id', 'mail_address']
    ordering = ['base', 'name', 'login_id']
    filter_horizontal = []


admin.site.unregister(Group)
