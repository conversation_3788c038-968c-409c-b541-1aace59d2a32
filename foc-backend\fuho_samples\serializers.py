from rest_framework import serializers

from fuho_samples.models import FuhoSample
from utils.serializer_mixins import AddUserIdMixin


class FuhoSampleSerializer(AddUserIdMixin, serializers.ModelSerializer):
    create_user_id = serializers.IntegerField(required=False)
    update_user_id = serializers.IntegerField(required=False)

    class Meta:
        model = FuhoSample
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']
