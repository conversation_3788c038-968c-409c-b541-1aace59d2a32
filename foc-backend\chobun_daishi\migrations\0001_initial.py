# Generated by Django 3.1.2 on 2020-10-29 06:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bases', '0008_delete_fuhosample'),
        ('masters', '0006_chobundaishimaster'),
    ]

    operations = [
        migrations.CreateModel(
            name='<PERSON>bun<PERSON><PERSON><PERSON>',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('del_flg', models.BooleanField(default=False, verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('create_user_id', models.IntegerField(verbose_name='created by')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('update_user_id', models.IntegerField(verbose_name='updated by')),
                (
                    'company',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='bases.base',
                        verbose_name='company',
                    ),
                ),
                (
                    'daishi',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='masters.chobundaishimaster',
                        verbose_name='chobun daishi',
                    ),
                ),
            ],
            options={
                'db_table': 'm_chobun_daishi',
            },
        ),
    ]
