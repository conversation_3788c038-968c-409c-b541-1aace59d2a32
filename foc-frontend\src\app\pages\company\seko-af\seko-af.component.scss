@import "src/assets/scss/company/setting";

.container .inner {
  .contents >.table_fixed.body {
    max-height: calc(100% - 230px);
  }
}
.contents {
  min-width: 1012px!important;
  .menu_title {
    .icon.big {
      margin-right: 20px;
    }
    .icon.combo {
      position: absolute;
      left: 45px;
    }
  }
  .ui.toggle.checkbox {
    padding: 4px 10px 0;
  }
  tr {
    .id {
      width: 6%;
    }
    .mourner_family {
      width: 6%;
    }
    .mourner_name {
      width: 8%;
    }
    .exec_date {
      width: 8%;
    }
    .af_employee {
      width: 8%;
    }
    .depart {
      width: 17%;
    }
    .af_wish {
      width: 8%;
    }
    .af_wish_answer {
      width: 8%;
    }
    .af_inquiry {
      width: 8%;
    }
    .operation {
      width: 60px;
    }
  }
  .ui.modal.seko {
    top: 20px!important;
    .content {
      padding-top: 10px;
    }
    .content .table-area {
      min-height: 150px;
      height: calc(100vh - 600px);
    }
    .content .table_fixed.body {
      max-height: calc(100% - 30px);
    }
    .title {
      font-size: smaller;
      margin-left: 20px;
      border: none;
      padding-bottom: 0;
      .data {
        margin-left: 10px;
        font-weight: normal;
      }
    }
    .sub_title {
      font-size: larger;
      font-weight: bold;
    }
    .input_area {
      margin: 20px 0;
    }
    .af_group {
      font-weight: bold;
      margin-left: 10px;
      margin-bottom: 5px;
    }
    .af-check {
      display: flex;
      margin-bottom: 10px;
      flex-wrap: wrap;
      margin-top: 0;
      margin-left: 10px;
      box-shadow: none;
      padding-bottom: 0;
      .ui.checkbox {
        margin-left: 20px;
        margin-bottom: 10px;
        input[disabled]~label {
          opacity: 1;
          color: inherit;
        }
      }

    }
    .table_fixed tr {
      cursor: default;
      &:hover {
        background: $table-row-light;
      }
      .updated_at {
        width: 150px;
      }
      .query, .answer {
        white-space: pre-wrap;
      }
      .operation {
        width: 50px;
        i {
          cursor: pointer;
        }
      }
      .staff_name {
        width: 100px;
      }
      .group {
        border-top: 0;
      }
    }
  }
}
