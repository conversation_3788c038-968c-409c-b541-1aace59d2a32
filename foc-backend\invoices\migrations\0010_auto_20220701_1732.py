# Generated by Django 3.1.14 on 2022-07-01 08:32

from pathlib import Path

from django.db import migrations


def is_postgres(schema_editor) -> bool:
    return schema_editor.connection.vendor.startswith('postgres')


def forwards(apps, schema_editor):
    if not is_postgres(schema_editor):
        return

    with open(Path('invoices/migrations/salescalc_v7.sql'), encoding='utf-8') as f:
        ddl: str = f.read()
    schema_editor.execute(ddl.replace('%', '%%'), params=[])


def backwards(apps, schema_editor):
    if not is_postgres(schema_editor):
        return

    with open(Path('invoices/migrations/salescalc_v6.sql'), encoding='utf-8') as f:
        ddl: str = f.read()
    schema_editor.execute(ddl.replace('%', '%%'), params=[])


class Migration(migrations.Migration):

    dependencies = [
        ('invoices', '0009_auto_20210507_1058'),
    ]

    operations = [migrations.RunPython(forwards, backwards, atomic=True)]
