from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base, FocFee
from bases.tests.factories import BaseFactory, FocFeeFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class FocFeeCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()
        self.focfee_data: FocFee = FocFeeFactory.build(company=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'monthly_fee': self.focfee_data.monthly_fee,
            'chobun_fee': self.focfee_data.chobun_fee,
            'chobun_fee_unit': self.focfee_data.chobun_fee_unit,
            'kumotsu_fee': self.focfee_data.kumotsu_fee,
            'kumotsu_fee_unit': self.focfee_data.kumotsu_fee_unit,
            'koden_fee': self.focfee_data.koden_fee,
            'koden_fee_unit': self.focfee_data.koden_fee_unit,
            'henreihin_fee': self.focfee_data.henreihin_fee,
            'henreihin_fee_unit': self.focfee_data.henreihin_fee_unit,
            'gmo_code': self.focfee_data.gmo_code,
            'gmo_code_koden': self.focfee_data.gmo_code_koden,
        }

    def test_focfee_create_succeed(self) -> None:
        """FOC利用料を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('bases:focfee', kwargs={'base_id': self.base.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record.get('monthly_fee'), self.focfee_data.monthly_fee)
        self.assertEqual(record.get('chobun_fee'), self.focfee_data.chobun_fee)
        self.assertEqual(record.get('chobun_fee_unit'), self.focfee_data.chobun_fee_unit)
        self.assertEqual(record.get('kumotsu_fee'), self.focfee_data.kumotsu_fee)
        self.assertEqual(record.get('kumotsu_fee_unit'), self.focfee_data.kumotsu_fee_unit)
        self.assertEqual(record.get('koden_fee'), self.focfee_data.koden_fee)
        self.assertEqual(record.get('koden_fee_unit'), self.focfee_data.koden_fee_unit)
        self.assertEqual(record.get('henreihin_fee'), self.focfee_data.henreihin_fee)
        self.assertEqual(record.get('henreihin_fee_unit'), self.focfee_data.henreihin_fee_unit)
        self.assertEqual(record.get('gmo_code'), self.focfee_data.gmo_code)
        self.assertEqual(record.get('gmo_code_koden'), self.focfee_data.gmo_code_koden)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.staff.id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

        new_focfee = FocFee.objects.get(pk=self.base.id)
        self.assertEqual(new_focfee.company, self.base)

    def test_focfee_create_failed_by_notfound(self) -> None:
        """FOC利用料追加APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.post(
            reverse('bases:focfee', kwargs={'base_id': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_focfee_create_failed_by_multiple_focfee(self) -> None:
        """FOC利用料追加APIが既にFOC利用料レコードを持っている拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        FocFeeFactory(company=self.base)
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('bases:focfee', kwargs={'base_id': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_focfee_create_failed_without_auth(self) -> None:
        """FOC利用料追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('bases:focfee', kwargs={'base_id': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class FocFeeDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.focfee = FocFeeFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_focfee_detail_succeed(self) -> None:
        """FOC利用料詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:focfee', kwargs={'base_id': self.focfee.company.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('monthly_fee'), self.focfee.monthly_fee)
        self.assertEqual(record.get('chobun_fee'), self.focfee.chobun_fee)
        self.assertEqual(record.get('chobun_fee_unit'), self.focfee.chobun_fee_unit)
        self.assertEqual(record.get('kumotsu_fee'), self.focfee.kumotsu_fee)
        self.assertEqual(record.get('kumotsu_fee_unit'), self.focfee.kumotsu_fee_unit)
        self.assertEqual(record.get('koden_fee'), self.focfee.koden_fee)
        self.assertEqual(record.get('koden_fee_unit'), self.focfee.koden_fee_unit)
        self.assertEqual(record.get('henreihin_fee'), self.focfee.henreihin_fee)
        self.assertEqual(record.get('henreihin_fee_unit'), self.focfee.henreihin_fee_unit)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.focfee.create_user_id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.focfee.update_user_id)

    def test_focfee_detail_failed_by_notfound_base(self) -> None:
        """FOC利用料詳細APIが存在しない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:focfee', kwargs={'base_id': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_focfee_detail_failed_by_base_missing_focfee(self) -> None:
        """FOC利用料詳細APIがFOC利用料レコードのない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_child_base: Base = BaseFactory()
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:focfee', kwargs={'base_id': non_child_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_focfee_detail_failed_without_auth(self) -> None:
        """施行アルバム詳細取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:focfee', kwargs={'base_id': self.focfee.company.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class FocFeeUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.focfee = FocFeeFactory()
        self.new_focfee_data = FocFeeFactory.build(company=self.focfee.company)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'monthly_fee': self.new_focfee_data.monthly_fee,
            'chobun_fee': self.new_focfee_data.chobun_fee,
            'chobun_fee_unit': self.new_focfee_data.chobun_fee_unit,
            'kumotsu_fee': self.new_focfee_data.kumotsu_fee,
            'kumotsu_fee_unit': self.new_focfee_data.kumotsu_fee_unit,
            'koden_fee': self.new_focfee_data.koden_fee,
            'koden_fee_unit': self.new_focfee_data.koden_fee_unit,
            'henreihin_fee': self.new_focfee_data.henreihin_fee,
            'henreihin_fee_unit': self.new_focfee_data.henreihin_fee_unit,
            'gmo_code': self.new_focfee_data.gmo_code,
            'gmo_code_koden': self.new_focfee_data.gmo_code_koden,
        }

    def test_focfee_update_succeed(self) -> None:
        """施行アルバムを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('bases:focfee', kwargs={'base_id': self.focfee.company.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('monthly_fee'), self.new_focfee_data.monthly_fee)
        self.assertEqual(record.get('chobun_fee'), self.new_focfee_data.chobun_fee)
        self.assertEqual(record.get('chobun_fee_unit'), self.new_focfee_data.chobun_fee_unit)
        self.assertEqual(record.get('kumotsu_fee'), self.new_focfee_data.kumotsu_fee)
        self.assertEqual(record.get('kumotsu_fee_unit'), self.new_focfee_data.kumotsu_fee_unit)
        self.assertEqual(record.get('koden_fee'), self.new_focfee_data.koden_fee)
        self.assertEqual(record.get('koden_fee_unit'), self.new_focfee_data.koden_fee_unit)
        self.assertEqual(record.get('henreihin_fee'), self.new_focfee_data.henreihin_fee)
        self.assertEqual(record.get('henreihin_fee_unit'), self.new_focfee_data.henreihin_fee_unit)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.focfee.create_user_id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_focfee_update_failed_by_notfound_base(self) -> None:
        """FOC利用料更新APIが存在しない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:focfee', kwargs={'base_id': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_focfee_update_failed_by_illegal_base(self) -> None:
        """FOC利用料更新APIがFOC利用料レコードのない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_child_base: Base = BaseFactory()
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:focfee', kwargs={'base_id': non_child_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_focfee_update_failed_without_auth(self) -> None:
        """FOC利用料更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:focfee', kwargs={'base_id': self.focfee.company.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
