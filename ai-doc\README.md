# FOCプロジェクト 開発ドキュメント

## 概要

このディレクトリには、FOC（Funeral Online system Connect）プロジェクトにおける新規機能開発のためのガイドドキュメントが含まれています。

## 技術スタック概要

- **フロントエンド**: Angular 10 + TypeScript + Fomantic UI
- **バックエンド**: Django 3.1 + Django REST Framework + JWT認証
- **データベース**: PostgreSQL/SQLite
- **CI/CD**: GitHub Actions
- **コンテナ**: Docker

## ドキュメント構成

### 📋 [development-flow-guide.md](./development-flow-guide.md)
**新規機能開発フローの全体像**

新規機能（例：ユーザープロフィール編集機能）を追加する際の一般的な開発フローを説明します。

**内容:**
- フロントエンド側の実装フロー
  - 新しい画面コンポーネントやルーティングの追加方法
  - フォーム入力や状態管理（SessionService）
  - APIとの通信部分（HttpClientService）
- バックエンド側の実装フロー
  - ルーティングの追加（Django URL設定）
  - ビジネスロジックの処理箇所（ビュー、シリアライザー）
  - DBモデル・マイグレーションの変更（Django ORM）
- フロント↔バックエンド連携の仕様
  - APIのエンドポイント設計方針
  - バリデーション・エラーハンドリング
  - リクエスト・レスポンスの形式
- 認証認可の影響
  - ログイン済ユーザーしかアクセスできないAPI保護の実装方法
- テスト・レビュー・デプロイの流れ
  - テストケースの追加方法（フロント/バック）
  - CIのトリガーと自動テストの実行フロー
  - 本番デプロイまでのステップ

### 💻 [implementation-examples.md](./implementation-examples.md)
**具体的な実装例とコードサンプル**

実際のコードを交えた詳細な実装例を提供します。

**内容:**
- フロントエンド実装例
  - Angularコンポーネントの完全な実装例
  - TypeScriptでの型定義とバリデーション
  - HTMLテンプレートとSCSSスタイル
  - HttpClientServiceの拡張方法
  - ルーティング設定の追加
- バックエンド実装例
  - Djangoシリアライザーの実装
  - APIビューの実装（RetrieveUpdateAPIView）
  - URL設定の追加
  - モデルの拡張とマイグレーション
- テスト実装例
  - フロントエンドのユニットテスト（Jasmine + Karma）
  - バックエンドのユニットテスト（Django Test Framework）
  - E2Eテスト（Protractor）
  - ファクトリーパターンの活用

### 🏗️ [project-specific-guide.md](./project-specific-guide.md)
**FOCプロジェクト固有の設定とベストプラクティス**

プロジェクト特有の設計パターンや慣習について説明します。

**内容:**
- プロジェクト構造の理解
  - フロントエンド・バックエンドのディレクトリ構成
  - 各層の責務と役割
- 認証・認可システム
  - カスタマイズされたJWT認証の仕組み
  - スタッフ・葬家の認証分離
  - 権限ガードの使い分け
- API設計パターン
  - RESTful API設計方針
  - シリアライザーの継承パターン
  - ビューの継承パターン
- フロントエンド設計パターン
  - コンポーネント設計方針
  - フレームコンポーネントパターン
  - HTTP通信パターン
- データベース設計パターン
  - 共通フィールドパターン
  - マスターデータ管理パターン
- テスト戦略
  - ファクトリーパターンの活用
  - 認証テストパターン
- 環境設定とデプロイ
  - 環境変数管理
  - Docker設定

## 開発フロー概要

### 1. 事前準備
1. 要件定義と設計
2. 既存コードベースの理解
3. 必要な権限・認証要件の確認

### 2. 実装
1. **バックエンド先行開発**
   - モデル設計・マイグレーション
   - シリアライザー実装
   - APIビュー実装
   - ユニットテスト作成

2. **フロントエンド開発**
   - コンポーネント実装
   - ルーティング設定
   - API通信実装
   - ユニットテスト作成

### 3. テスト・統合
1. 統合テスト実行
2. E2Eテスト実行
3. 手動テスト・検証

### 4. レビュー・デプロイ
1. コードレビュー
2. CI/CDパイプライン実行
3. ステージング環境デプロイ
4. 本番環境デプロイ

## 開発環境セットアップ

### フロントエンド
```bash
cd foc-frontend
npm install
npm start  # 開発サーバー起動（http://localhost:4200）
```

### バックエンド
```bash
cd foc-backend
poetry install
poetry run python manage.py migrate
poetry run python manage.py runserver  # 開発サーバー起動（http://localhost:8000）
```

### テスト実行
```bash
# フロントエンド
cd foc-frontend
npm test

# バックエンド
cd foc-backend
poetry run python manage.py test
```

## 重要な注意点

### セキュリティ
- JWT トークンの適切な管理
- 認証・認可の実装
- バリデーションの実装（フロント・バック両方）
- CORS設定の確認

### パフォーマンス
- データベースクエリの最適化
- フロントエンドでのLazy Loading
- 適切なキャッシュ戦略

### 保守性
- 一貫したコーディングスタイル
- 適切なテストカバレッジ
- ドキュメントの更新
- コードレビューの実施

## 参考リンク

- [Angular 公式ドキュメント](https://angular.io/docs)
- [Django 公式ドキュメント](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Fomantic UI](https://fomantic-ui.com/)

## 更新履歴

- 2024-12-26: 初版作成
  - 開発フローガイド作成
  - 実装例集作成
  - プロジェクト固有ガイド作成

---

このドキュメントは、FOCプロジェクトでの効率的な開発を支援するために作成されました。不明な点や改善提案があれば、開発チームまでお知らせください。
