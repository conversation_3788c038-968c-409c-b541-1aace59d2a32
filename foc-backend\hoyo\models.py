from datetime import date
from typing import Dict, List

from dateutil.relativedelta import relativedelta
from django.db import models
from django.db.models import Q
from django.utils.translation import gettext_lazy as _

from bases.models import Base
from masters.models import HoyoStyle, TermUnitType
from seko.models import Seko
from staffs.models import Staff


class Hoyo(models.Model):
    company = models.ForeignKey(
        Base, models.PROTECT, verbose_name=_('company'), related_name='hoyo'
    )
    style = models.ForeignKey(
        HoyoStyle, models.PROTECT, verbose_name=_('hoyo style'), related_name='hoyo'
    )
    name = models.TextField(_('name'))
    elapsed_time = models.IntegerField(_('elapsed time'), blank=True, null=True)
    unit = models.IntegerField(_('unit'), choices=TermUnitType.choices, blank=True, null=True)
    display_num = models.IntegerField(_('display order'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_hoyo'

    def disable(self) -> None:
        """法要を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()

    @property
    def elapsed_timedelta(self) -> relativedelta:
        timedeltas: Dict = {
            TermUnitType.DAYS: relativedelta(days=self.elapsed_time),
            TermUnitType.MONTHS: relativedelta(months=self.elapsed_time),
            TermUnitType.YEARS: relativedelta(years=self.elapsed_time),
        }
        return timedeltas[self.unit]


class HoyoSample(models.Model):
    company = models.ForeignKey(
        Base, models.PROTECT, verbose_name=_('company'), related_name='hoyo_samples'
    )
    sentence = models.TextField(_('sentence'))
    display_num = models.IntegerField(_('display order'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_hoyo_sample'

    def disable(self) -> None:
        """法要サンプルを即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()


class HoyoMailTemplate(models.Model):
    company = models.ForeignKey(
        Base, models.PROTECT, verbose_name=_('company'), related_name='hoyo_mail_templates'
    )
    sentence = models.TextField(_('sentence'))
    display_num = models.IntegerField(_('display order'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_hoyo_mail_template'

    def disable(self) -> None:
        """法要メールテンプレートを即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()


class HoyoSeko(models.Model):
    seko = models.ForeignKey(
        Seko, models.PROTECT, verbose_name=_('seko'), related_name='hoyo_seko'
    )
    hoyo = models.ForeignKey(
        Hoyo,
        models.PROTECT,
        verbose_name=_('hoyo'),
        related_name='hoyo',
        blank=True,
        null=True,
    )
    hoyo_name = models.TextField(_('hoyo name'), blank=True, null=True)
    hoyo_planned_date = models.DateField(_('planned on'), blank=True, null=True)
    hoyo_activity_date = models.DateField(_('held on'), blank=True, null=True)
    begin_time = models.TimeField(_('begins at'), blank=True, null=True)
    end_time = models.TimeField(_('ends at'), blank=True, null=True)
    hall = models.ForeignKey(Base, models.PROTECT, verbose_name=_('hall'), blank=True, null=True)
    dine_flg = models.BooleanField(_('dine flag'))
    seshu_name = models.TextField(_('seshu name'))
    kojin_seshu_relationship = models.TextField(_('kojin seshu relationship'))
    zip_code = models.CharField(_('zipcode'), max_length=7, blank=True, null=True)
    prefecture = models.TextField(_('prefecture'), blank=True, null=True)
    address_1 = models.TextField(_('address1'), blank=True, null=True)
    address_2 = models.TextField(_('address2'), blank=True, null=True)
    address_3 = models.TextField(_('address3'), blank=True, null=True)
    tel = models.CharField(_('tel no'), max_length=15, blank=True, null=True)
    hoyo_sentence = models.TextField(_('hoyo sentence'), blank=True, null=True)
    shishiki_name = models.TextField(_('shishiki name'), blank=True, null=True)
    reply_limit_date = models.DateField(_('reply limited on'))
    hoyo_site_end_date = models.DateField(_('hoyo site closed on'))
    note = models.TextField(_('note'), blank=True, null=True)
    staff = models.ForeignKey(
        Staff, models.PROTECT, verbose_name=_('staff'), blank=True, null=True
    )
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_hoyo'

    @classmethod
    def generate_from_seko(cls, seko: Seko) -> List['HoyoSeko']:
        hoyo_qs = Hoyo.objects.filter(
            Q(company=seko.seko_company) & Q(style=seko.seko_style.hoyo_style) & Q(del_flg=False)
        ).all()
        if not hoyo_qs:
            raise ValueError(
                _(
                    f"Seko company doesn't have Hoyo style: {seko.seko_style.hoyo_style.pk}"
                    f'{seko.seko_style.hoyo_style.name}'
                )
            )

        hoyoseko_list: List = []
        for hoyo in hoyo_qs:
            instance = cls(
                seko=seko,
                hoyo=hoyo,
                hoyo_name=hoyo.name,
                hoyo_planned_date=seko.death_date + hoyo.elapsed_timedelta,
                hoyo_activity_date=None,
                begin_time=None,
                end_time=None,
                hall=None,
                dine_flg=True,
                seshu_name=seko.moshu.name,
                kojin_seshu_relationship=seko.moshu.kojin_moshu_relationship,
                zip_code=seko.moshu.zip_code,
                prefecture=seko.moshu.prefecture,
                address_1=seko.moshu.address_1,
                address_2=seko.moshu.address_2,
                address_3=seko.moshu.address_3,
                tel=seko.moshu.tel,
                hoyo_sentence=None,
                shishiki_name=None,
                reply_limit_date=date(2999, 12, 31),
                hoyo_site_end_date=date(2999, 12, 31),
                note=None,
                staff=None,
                del_flg=False,
            )
            instance.save()
            hoyoseko_list.append(instance)

        return hoyoseko_list

    def disable(self) -> None:
        """法要施行を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()

    def update_schedules(self, schedules_data: List[Dict]) -> None:
        self.schedules.all().delete()
        new_schedules: List[HoyoSchedule] = []
        for schedule_dict in schedules_data:
            new_schedules.append(HoyoSchedule(**schedule_dict))
        self.schedules.set(new_schedules, bulk=False)


class HoyoSchedule(models.Model):
    hoyo_seko = models.ForeignKey(
        HoyoSeko,
        models.CASCADE,
        db_column='hoyo_id',
        verbose_name=_('hoyoseko'),
        related_name='schedules',
    )
    schedule_name = models.TextField(_('name'))
    hall = models.ForeignKey(Base, models.PROTECT, verbose_name=_('hall'), blank=True, null=True)
    hall_name = models.TextField(_('hall name'))
    schedule_date = models.DateField(_('scheduled on'), blank=True, null=True)
    begin_time = models.TimeField(_('begins at'), blank=True, null=True)
    end_time = models.TimeField(_('ends at'), blank=True, null=True)
    display_num = models.IntegerField(_('display order'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_hoyo_schedule'
        ordering = ['display_num']


class HoyoMail(models.Model):
    hoyo = models.ForeignKey(
        Hoyo, models.PROTECT, verbose_name=_('hoyo'), related_name='mails', blank=True, null=True
    )
    select_ts = models.DateTimeField(_('selected at'))
    send_ts = models.DateTimeField(_('sent at'), blank=True, null=True)
    content = models.TextField(_('content'))
    staff = models.ForeignKey(Staff, models.PROTECT, verbose_name=_('staff'))
    note = models.TextField(_('note'), blank=True, null=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_hoyo_mail'


class HoyoMailTarget(models.Model):
    hoyo_mail = models.ForeignKey(
        HoyoMail, models.CASCADE, verbose_name=_('hoyo mail'), related_name='targets'
    )
    seko = models.ForeignKey(
        Seko, models.PROTECT, verbose_name=_('seko'), related_name='hoyo_mail_targets'
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_hoyo_mail_target'
