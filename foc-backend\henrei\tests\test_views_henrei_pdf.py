from typing import Dict

from django.core import mail
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory, TokushoFactory
from henrei.tests.factories import (
    HenreihinKodenFactory,
    HenreihinKumotsuFactory,
    OrderHenreihinFactory,
)
from masters.tests.factories import ScheduleFactory
from orders.tests.factories import (
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryFactory,
)
from seko.tests.factories import (
    HenreihinKakegamiFactory,
    MoshuFactory,
    SekoFactory,
    SekoScheduleFactory,
)
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class HenreiPDFViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory()
        supplier = SupplierFactory(company=base)

        seko = SekoFactory(seko_company=base)
        MoshuFactory(seko=seko)
        schedule_1 = ScheduleFactory(id=1)
        schedule_2 = ScheduleFactory(id=2)
        SekoScheduleFactory(seko=seko, schedule=schedule_1)
        SekoScheduleFactory(seko=seko, schedule=schedule_2)
        HenreihinKakegamiFactory(seko=seko)
        entry = EntryFactory(seko=seko)
        self.henrei_order = OrderHenreihinFactory(seko=seko, supplier=supplier)

        entry_detail_1 = EntryDetailFactory(entry=entry)
        entry_detail_2 = EntryDetailFactory(entry=entry)
        entry_detail_3 = EntryDetailFactory(entry=entry)

        kumotsu_1 = EntryDetailKumotsuFactory(entry_detail=entry_detail_1)
        kumotsu_2 = EntryDetailKumotsuFactory(entry_detail=entry_detail_3)
        kumotsu_3 = EntryDetailKumotsuFactory(entry_detail=entry_detail_2)

        HenreihinKumotsuFactory(detail_kumotsu=kumotsu_1, henrei_order=self.henrei_order)
        HenreihinKumotsuFactory(detail_kumotsu=kumotsu_2, henrei_order=self.henrei_order)
        HenreihinKumotsuFactory(detail_kumotsu=kumotsu_3, henrei_order=self.henrei_order)

        koden_3 = EntryDetailKodenFactory(entry_detail=entry_detail_1)
        koden_4 = EntryDetailKodenFactory(entry_detail=entry_detail_3)
        koden_5 = EntryDetailKodenFactory(entry_detail=entry_detail_2)

        HenreihinKodenFactory(detail_koden=koden_3, henrei_order=self.henrei_order)
        HenreihinKodenFactory(detail_koden=koden_4, henrei_order=self.henrei_order)
        HenreihinKodenFactory(detail_koden=koden_5, henrei_order=self.henrei_order)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_henrei_pdf_succeed(self) -> None:
        """返礼品発注書PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('henrei:henrei_pdf', kwargs={'pk': self.henrei_order.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'返礼品発注書-{self.henrei_order.pk}.pdf')

    def test_henrei_pdf_succeed_without_auth(self) -> None:
        """返礼品発注書PDF取得APIはAuthorizationヘッダがなくても成功する"""

        params: Dict = {}
        response = self.api_client.get(
            reverse('henrei:henrei_pdf', kwargs={'pk': self.henrei_order.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_henrei_pdf_failed_by_order_henrei_notfound(self) -> None:
        """存在しない返礼品発注IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_order_henrei = OrderHenreihinFactory.build()
        response = self.api_client.get(
            reverse('henrei:henrei_pdf', kwargs={'pk': not_exist_order_henrei.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class HenreiPDFFaxViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory()
        self.tokusho = TokushoFactory(company=base)
        self.supplier = SupplierFactory(company=base)

        seko = SekoFactory(seko_company=base)
        MoshuFactory(seko=seko)
        schedule_1 = ScheduleFactory(id=1)
        schedule_2 = ScheduleFactory(id=2)
        SekoScheduleFactory(seko=seko, schedule=schedule_1)
        SekoScheduleFactory(seko=seko, schedule=schedule_2)
        HenreihinKakegamiFactory(seko=seko)
        entry = EntryFactory(seko=seko)
        self.henrei_order = OrderHenreihinFactory(seko=seko, supplier=self.supplier)

        entry_detail_1 = EntryDetailFactory(entry=entry)
        entry_detail_2 = EntryDetailFactory(entry=entry)
        entry_detail_3 = EntryDetailFactory(entry=entry)

        kumotsu_1 = EntryDetailKumotsuFactory(entry_detail=entry_detail_1)
        kumotsu_2 = EntryDetailKumotsuFactory(entry_detail=entry_detail_3)
        kumotsu_3 = EntryDetailKumotsuFactory(entry_detail=entry_detail_2)

        HenreihinKumotsuFactory(detail_kumotsu=kumotsu_1, henrei_order=self.henrei_order)
        HenreihinKumotsuFactory(detail_kumotsu=kumotsu_2, henrei_order=self.henrei_order)
        HenreihinKumotsuFactory(detail_kumotsu=kumotsu_3, henrei_order=self.henrei_order)

        koden_3 = EntryDetailKodenFactory(entry_detail=entry_detail_1)
        koden_4 = EntryDetailKodenFactory(entry_detail=entry_detail_3)
        koden_5 = EntryDetailKodenFactory(entry_detail=entry_detail_2)

        HenreihinKodenFactory(detail_koden=koden_3, henrei_order=self.henrei_order)
        HenreihinKodenFactory(detail_koden=koden_4, henrei_order=self.henrei_order)
        HenreihinKodenFactory(detail_koden=koden_5, henrei_order=self.henrei_order)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_henrei_pdf_fax_succeed(self) -> None:
        """返礼品発注書PDFをFaxで送信する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('henrei:henrei_fax', kwargs={'pk': self.henrei_order.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.from_email, self.tokusho.from_name)
        self.assertEqual(sent_mail.to, [f'{self.supplier.fax_digits()}@cl1.faximo.jp'])
        self.assertEqual(sent_mail.subject, '===<<<')
        self.assertEqual(sent_mail.body, '')
        self.assertEqual(len(sent_mail.attachments), 1)

        # 返礼品発注の発注ステータスなどや返礼品香典/供花供物の発注ステータスも更新される
        self.henrei_order.refresh_from_db()
        self.assertEqual(self.henrei_order.order_status, 2)
        self.assertIsNotNone(self.henrei_order.order_ts)
        self.assertEqual(self.henrei_order.order_staff, self.staff)
        for koden in self.henrei_order.henrei_koden.all():
            self.assertEqual(koden.order_status, 2)
        for kumotsu in self.henrei_order.henrei_kumotsu.all():
            self.assertEqual(kumotsu.order_status, 2)

    def test_henrei_pdf_fax_fail_without_auth(self) -> None:
        """返礼品発注書PDF Fax送信APIがAuthorizationヘッダがなくて失敗する"""

        params: Dict = {}
        response = self.api_client.post(
            reverse('henrei:henrei_fax', kwargs={'pk': self.henrei_order.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_henrei_pdf_fax_failed_by_order_henrei_notfound(self) -> None:
        """返礼品発注書PDF Fax送信APIが存在しない返礼品発注IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_order_henrei = OrderHenreihinFactory.build()
        response = self.api_client.post(
            reverse('henrei:henrei_fax', kwargs={'pk': not_exist_order_henrei.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
