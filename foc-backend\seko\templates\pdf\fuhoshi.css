body {
  font-family: "Noto Serif CJK JP", serif;
}
.frame {
  padding: 3px;
  border: solid 1px #000000;
  margin-left: -10px;
  width: 650px;
}
.content {
  height: 640px;
  border: solid 5px #000000;
  text-align: center;
}
.header {
  font-size: 30px;
  text-align: center;
  white-space: pre;
}
.kojin_area {
  height: 120px;
  margin-left: 50px;
  text-align: left;
}
.kojin_area .kojin {
  font-size: 20px;
  line-height: 1.5;
  overflow: hidden;
  display: flex;
}
.kojin_area .kojin .label {
  width: 40px;
}
.kojin_area .kojin .relation {
  width: 100px;
}
.kojin_area .kojin .name {
  width: 250px;
}
.kojin_area .kojin .label2 {
  width:70px;
}
.kojin_area .kojin .age_kbn {
  width:50px;
}
.kojin_area .death {
  margin-left: 90px;
  font-size: 16px;
  line-height: 1.5;
  white-space: pre;
}
.sentence {
  height: 120px;
  display: inline-block;
  margin: 0 auto;
  font-size: 16px;
  white-space: pre-wrap;
  line-height: 1.5;
  width: 100%;
}

.schedule_area {
  height: 140px;
  margin-left: 30px;
  font-size: 20px;
  line-height: 1.5;
  text-align: left;
}
.schedule_area .schedule {
  display: flex;
}
.schedule_area .schedule .name {
  width: 100px;
}
.schedule_area .schedule .date {
  width: 500px;
  display: flex;
}
.schedule_area .schedule .date .month {
  width: 50px;
}
.schedule_area .schedule .date .day {
  width: 50px;
  text-align: right;
}
.schedule_area .schedule .date .week {
  width: 60px;
}
.schedule_area .schedule .date .time {
  width: 100px;
  text-align: right;
}
.schedule_area .schedule .date .nami {
  width: 30px;
  text-align: right;
}

.hall_area {
  height: 80px;
  margin-left: 30px;
  display: flex;
  text-align: left;
}
.hall_area .label {
  width: 100px;
  font-size: 20px;
  line-height: 1.5;
}
.hall_area .hall_info {
  font-size: 16px;
}
.hall_area .hall_info .line1 {
  display: flex;
}
.hall_area .hall_info .line1 .name {
  width: 320px;
}
.hall_area .hall_info .address {
  width: 500px;
  line-height: 1.3;
}
.style_area {
  height: 50px;
  margin-left: 30px;
  font-size: 20px;
  display: flex;
  text-align: left;
}
.style_area .style {
  width: 300px;
  line-height: 1.2;
  white-space: pre;
}
.style_area .moshu {
  width: 300px;
  line-height: 1.2;
  white-space: pre;
}
.contact_area {
  height: 20px;
  margin-left: 30px;
  font-size: 20px;
  line-height: 1.2;
  display: flex;
  text-align: left;
}
.contact_area .label {
  width: 100px;
}
.contact_area .name {
  width: 320px;
}
.contact_area .tel {
  font-size: 16px;
  width: 150px;
  white-space: pre;
}

.footer {
  height: 280px;
  border: solid 5px #000000;
  margin-top: 10px;
}
.footer .title {
  margin-top: 20px;
  height: 50px;
  text-align: center;
  font-size: 20px;
}
.smaho_area {
  height: 140px;
  display: flex;
  margin-left: 50px;
}
.smaho_area .label_area {
  width: 400px;
}
.smaho_area .label_area .label {
  font-size: 20px;
}
.smaho_area .label_area .label img {
  height: 20px;
}
.smaho_area .label_area .desc {
  font-size: 14px;
  margin-left: 30px;
}
.smaho_area .qrcode img {
  width: 140px;
}
.pc_area {
  height: 70px;
  display: flex;
  margin-left: 50px;
}
.pc_area .label {
  width: 250px;
  font-size: 20px;
}
.pc_area .label img {
  height: 20px;
}
.pc_area .address_area {
  width: 350px;
}
.pc_area .address_area .address {
  font-size:14px;
}
.pc_area .address_area .fuho-num {
  display: flex;
  margin-top: 5px;
}
.pc_area .address_area .borderL {
  border: solid 1px black;
  padding: 3px 10px;
}
.pc_area .address_area .borderR {
  border: solid 1px black;
  border-left-width: 0;
  padding: 3px 10px;
  min-width: 100px;
}
