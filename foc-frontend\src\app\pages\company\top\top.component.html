
<div class="container">
  <div class="inner">
    <div class="contents">
      <div class="ui attached tabular menu">
        <div class="active item" data-tab="main-menu" (click)="selectTab('main-menu')">
          <i class="home icon"></i>
          メインメニュー
        </div>
        <div class="item" data-tab="service-menu" *ngIf="login_info?.staff?.base?.base_type!==Const.BASE_TYPE_ADMIN" (click)="selectTab('service-menu')">
          <i class="hand holding medical icon"></i>
          AFメニュー
        </div>
        <div class="item" data-tab="mainte-menu" (click)="selectTab('mainte-menu')" *ngIf="login_info?.staff?.base?.base_type===Const.BASE_TYPE_ADMIN || login_info?.staff?.base?.base_type===Const.BASE_TYPE_COMPANY">
          <i class="database icon"></i>
          管理メニュー
        </div>
      </div>
      <div class="ui attached active tab segment" data-tab="main-menu">
        <div class="main content">
          <button class="ui circular icon button reload" title="最新情報更新" (click)="getBase(login_base_id?login_base_id:login_company.id)">
            <i class="icon redo alternate"></i>
          </button>
          <div class="input_area" *ngIf="login_info?.staff?.base?.base_type!==Const.BASE_TYPE_ADMIN">
            <div class="line">
              <label>施行拠点</label>
              <com-dropdown [settings]="sekoBaseCombo" [(selectedValue)]="login_base_id" (selectedItemChange)="sekoBaseChange($event)"></com-dropdown>
            </div>
          </div>
          <div class="menu">
            <i class="id badge icon big"></i>
            <div class="subtitle">訃報登録</div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/seko/edit" [class.disabled]="login_info?.staff?.base?.base_type===Const.BASE_TYPE_ADMIN">
                <i class="add icon button"></i> 新規登録
              </div>
            </div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/seko">
                <i class="edit icon button"></i> 変更
              </div>
            </div>
          </div>
          <div class="menu">
            <i class="map icon big"></i>
            <div class="subtitle">弔文受付</div>
            <div class="info">
              <div class="ui right labeled button mini" [class.loading]="is_loading">
                <div class="ui button mini" routerLink="/foc/chobun">
                  <i class="list ul icon button"></i> 受付一覧
                </div>
                <a class="ui left pointing label">
                  <span class="important" *ngIf="!is_loading">未印刷：{{unprinted_chobun_count}}件</span>
                </a>
              </div>
            </div>
          </div>
          <div class="menu">
            <i class="spa icon big"></i>
            <div class="subtitle">供花・供物受付</div>
            <div class="info">
              <div class="ui right labeled button mini" [class.loading]="is_loading">
                <div class="ui button mini" routerLink="/foc/kumotsu">
                  <i class="list ul icon button"></i> 受付一覧
                </div>
                <a class="ui left pointing label">
                  <span class="important" *ngIf="!is_loading">未発注：{{kumotsu_count.unordered}}件、</span>
                  <span class="important" *ngIf="!is_loading">未確認：{{kumotsu_count.unconfirmed}}件</span>
                </a>
              </div>
            </div>
          </div>
          <div class="menu">
            <i class="award icon big"></i>
            <div class="subtitle">香典受付</div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/koden">
                <i class="list ul icon button"></i>受付一覧
              </div>
            </div>
          </div>
          <div class="menu">
            <i class="comment dots icon big"></i>
            <div class="subtitle">追悼メッセージ受付</div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/message">
                <i class="list ul icon button"></i>受付一覧
              </div>
            </div>
          </div>
          <div class="menu">
            <i class="gift icon big"></i>
            <div class="subtitle">返礼品受付</div>
            <div class="info">
              <div class="ui right labeled button mini" [class.loading]="is_loading">
                <div class="ui button mini" routerLink="/foc/henreihin">
                  <i class="list ul icon button"></i> 受付一覧
                </div>
                <a class="ui left pointing label">
                  <span class="important" *ngIf="!is_loading">未発注：{{henreihin_count.unordered}}件、</span>
                  <span class="important" *ngIf="!is_loading">未確認：{{henreihin_count.unconfirmed}}件</span>
                </a>
              </div>
            </div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/henreihin/order">
                <i class="list ul icon button"></i>発注一覧
              </div>
            </div>
          </div>
          <div class="menu" *ngIf="login_info?.staff?.base?.base_type!==Const.BASE_TYPE_ADMIN">
            <i class="hand holding medical icon big"></i>
            <i class="comment outline icon mini combo"></i>
            <div class="subtitle">問合せ</div>
            <div class="info">
              <div class="ui right labeled button mini" [class.loading]="is_loading">
                <div class="ui button mini" routerLink="/foc/seko/af">
                  <i class="list ul icon button"></i> 問合せ回答
                </div>
                <a class="ui left pointing label">
                  <span class="important" *ngIf="!is_loading">フリー未回答：{{seko_inquiries_count.unanswerd_inquiry}}件、</span>
                  <span class="important" *ngIf="!is_loading">AF希望未回答：{{seko_inquiries_count.unanswerd_af}}件</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="ui attached tab segment" data-tab="service-menu">
        <div class="main content">
          <div class="menu">
            <i class="id monument icon big"></i>
            <div class="subtitle mini">法要</div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/hoyo/guidance">
                <i class="list ul icon button"></i> 法要案内履歴
              </div>
            </div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/hoyo/filter/ki">
                <i class="filter icon button"></i> 法要案内抽出（忌法要）
              </div>
            </div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/hoyo/filter/bon">
                <i class="filter icon button"></i> 法要案内抽出（盆）
              </div>
            </div>
          </div>
          <div class="menu">
            <i class="hand holding medical icon big"></i>
            <div class="subtitle mini">AF</div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/seko/af">
                <i class="list ul icon button"></i> AF活動状況一覧
              </div>
            </div>
          </div>
          <div class="menu">
            <i class="calendar alternate icon big"></i>
            <div class="subtitle mini">イベント</div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/event/guidance">
                <i class="list ul icon button"></i> イベント案内履歴
              </div>
            </div>
            <div class="info">
              <div class="ui button mini" routerLink="/foc/event/filter">
                <i class="filter icon button"></i> イベント案内抽出
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="ui attached tab segment" data-tab="mainte-menu">
        <div class="title">
          マスタ管理
        </div>
        <div class="content">
          <div class="menu selectable" routerLink="/foc/base">
            <i class="building icon big"></i>
            <div class="subtitle">拠点</div>
          </div>
          <div class="menu selectable" routerLink="/foc/staff">
            <i class="user tie icon big"></i>
            <div class="subtitle">担当者</div>
          </div>
          <div class="menu selectable" routerLink="/foc/supplier">
            <i class="hands helping icon big"></i>
            <div class="subtitle">発注先</div>
          </div>
          <div class="menu selectable" routerLink="/foc/fuho-sample">
            <i class="money check icon big"></i>
            <div class="subtitle">訃報文</div>
          </div>
          <div class="menu selectable" routerLink="/foc/chobun-daishi">
            <i class="chess board icon big"></i>
            <div class="subtitle">弔文台紙</div>
          </div>
          <div class="menu selectable" routerLink="/foc/item-service">
            <i class="gifts icon big"></i>
            <div class="subtitle">サービス・商品</div>
          </div>
          <div class="menu selectable" routerLink="/foc/af/group">
            <i class="hand holding medical icon big"></i>
            <i class="stream icon mini combo"></i>
            <div class="subtitle">AF項目グループ</div>
          </div>
          <div class="menu selectable" routerLink="/foc/af/contact">
            <i class="hand holding medical icon big"></i>
            <i class="linkify icon mini combo"></i>
            <div class="subtitle">AF連絡方法</div>
          </div>
          <div class="menu selectable" routerLink="/foc/hoyo/master">
            <i class="monument icon big"></i>
            <div class="subtitle">法要マスタ</div>
          </div>
          <div class="menu selectable" routerLink="/foc/hoyo/sample">
            <i class="book open icon big"></i>
            <div class="subtitle">法要案内文</div>
          </div>
          <div class="menu selectable" routerLink="/foc/hoyo/mail-template">
            <i class="envelope open text icon big"></i>
            <div class="subtitle">法要メールテンプレート</div>
          </div>
          <div class="menu selectable" routerLink="/foc/faq">
            <i class="question icon big"></i>
            <div class="subtitle">FAQ登録</div>
          </div>
          <div class="menu selectable" routerLink="/foc/ad">
            <i class="ad icon big"></i>
            <div class="subtitle">広告</div>
          </div>
          <div class="menu selectable" routerLink="/foc/soke-menu">
            <i class="bars icon big"></i>
            <div class="subtitle">葬家専用ページメニュー</div>
          </div>
          <!--
          <div class="menu selectable">
            <i class="bell icon big"></i>
            <div class="subtitle">お知らせ</div>
          </div>
          -->
        </div>
        <div class="title">
          請求書・売上集計表
        </div>
        <div class="content">
          <ng-container *ngIf="login_info?.staff?.base?.base_type===Const.BASE_TYPE_COMPANY">
            <div class="menu selectable" routerLink="/foc/invoice/">
              <i class="file invoice dollar icon big"></i>
              <div class="subtitle">請求書出力</div>
            </div>
          </ng-container>
          <div class="menu selectable" routerLink="/foc/sales/summary">
            <i class="file invoice icon big"></i>
            <div class="subtitle">売上集計表出力</div>
          </div>
          <div class="menu selectable" routerLink="/foc/koden/summary">
            <i class="award invoice icon big"></i>
            <i class="calculator icon mini combo"></i>
            <div class="subtitle">香典集計</div>
          </div>
        </div>

        <ng-container *ngIf="login_info?.staff?.base?.base_type===Const.BASE_TYPE_ADMIN">
          <div class="title">
            月次集計
          </div>
          <div class=" content">
            <div class="menu selectable" routerLink="/foc/monthly/summary">
              <i class="list alternate icon big"></i>
              <div class="subtitle">月次集計</div>
            </div>
            <div class="menu selectable" routerLink="/foc/monthly/confirm">
              <i class="tasks icon big"></i>
              <div class="subtitle">月次確定</div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</div>
