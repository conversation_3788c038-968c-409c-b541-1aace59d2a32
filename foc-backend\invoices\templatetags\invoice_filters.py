from datetime import date

from django import template

register = template.Library()


@register.filter
def first_day(date: date) -> date:
    return date.replace(day=1)


@register.filter
def is_first_of_seko(details, count: int) -> bool:
    if count == 1:
        return True
    return details[count - 2].seko_id != details[count - 1].seko_id


@register.filter
def is_first_of_service(details, count: int) -> bool:
    if count == 1:
        return True
    return (
        details[count - 2].seko_id != details[count - 1].seko_id
        or details[count - 2].service_id != details[count - 1].service_id
    )


@register.filter
def divide(a: int, b: int) -> int:
    return int(a / b)


@register.filter
def multiple(a: int, b: int) -> int:
    return int(a * b)
