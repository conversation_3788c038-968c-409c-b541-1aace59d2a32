from typing import Dict
from unittest.mock import patch

from django.core import mail
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from seko.models import SekoInquiry
from seko.tests.factories import MoshuFactory, SekoAnswerFactory, SekoInquiryFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class SekoAnswerCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko_inquiry = SekoInquiryFactory()
        MoshuFactory(seko=self.seko_inquiry.seko)
        self.seko_answer_data = SekoAnswerFactory.build(inquiry=self.seko_inquiry)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {'answer': self.seko_answer_data.answer}

    @patch('seko.views.request_origin')
    def test_seko_answer_create_succeed(self, mock_origin) -> None:
        """施行回答を追加する"""
        mock_origin.return_value = 'http://frontend:9999'

        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('seko:answer_list', kwargs={'inquiry_id': self.seko_inquiry.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertEqual(record['inquiry'], self.seko_answer_data.inquiry.pk)
        self.assertEqual(record['answer'], self.seko_answer_data.answer)
        self.assertEqual(record['staff']['id'], self.staff.pk)
        self.assertEqual(record['staff']['name'], self.staff.name)
        self.assertFalse(record['del_flg'])
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, '【てれ葬儀こころ】お問合せのご回答')
        self.assertEqual(sent_mail.to, [self.seko_inquiry.seko.moshu.mail_address])

    def test_seko_answer_create_failed_by_seko_notfound(self) -> None:
        """施行回答追加APIが存在しない施行問合せIDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko_inquiry: SekoInquiry = SekoInquiryFactory.build()

        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:answer_list', kwargs={'inquiry_id': non_saved_seko_inquiry.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_answer_create_failed_without_auth(self) -> None:
        """施行回答追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:answer_list', kwargs={'inquiry_id': self.seko_inquiry.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_seko_answer_create_deny_from_staff(self) -> None:
        """施行回答追加APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:answer_list', kwargs={'inquiry_id': self.seko_inquiry.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
