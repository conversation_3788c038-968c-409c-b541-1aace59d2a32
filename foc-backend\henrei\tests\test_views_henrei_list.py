from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from henrei.tests.factories import HenreihinKodenFactory, HenreihinKumotsuFactory
from masters.tests.factories import ScheduleFactory
from orders.tests.factories import (
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryFactory,
)
from seko.tests.factories import KojinFactory, MoshuFactory, SekoFactory, SekoScheduleFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class HenreiListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_1 = BaseFactory()
        self.base_2 = BaseFactory()

        self.seko_1 = SekoFactory(seko_company=self.base_1)
        self.seko_2 = SekoFactory(seko_company=self.base_2)
        self.kojin_1 = KojinFactory(kojin_num=1, seko=self.seko_1)
        KojinFactory(kojin_num=2, seko=self.seko_1)
        self.kojin_2 = KojinFactory(kojin_num=1, seko=self.seko_2)
        self.moshu_1 = MoshuFactory(seko=self.seko_1)
        self.moshu_2 = MoshuFactory(seko=self.seko_2)
        soshiki_schedule = ScheduleFactory(id=2)
        self.schedule_1 = SekoScheduleFactory(seko=self.seko_1, schedule=soshiki_schedule)
        self.schedule_2 = SekoScheduleFactory(seko=self.seko_2, schedule=soshiki_schedule)
        self.entry_1 = EntryFactory(seko=self.seko_1)
        self.entry_2 = EntryFactory(seko=self.seko_2)

        entry_detail_21 = EntryDetailFactory(entry=self.entry_2)
        entry_detail_22 = EntryDetailFactory(entry=self.entry_2)
        entry_detail_11 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_12 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_13 = EntryDetailFactory(entry=self.entry_1)

        self.kumotsu_1 = EntryDetailKumotsuFactory(entry_detail=entry_detail_21)
        self.kumotsu_2 = EntryDetailKumotsuFactory(entry_detail=entry_detail_22)
        self.kumotsu_3 = EntryDetailKumotsuFactory(entry_detail=entry_detail_11)
        self.kumotsu_4 = EntryDetailKumotsuFactory(entry_detail=entry_detail_13)
        self.kumotsu_5 = EntryDetailKumotsuFactory(entry_detail=entry_detail_12)

        self.henrei_kumotsu_1 = HenreihinKumotsuFactory(
            detail_kumotsu=self.kumotsu_1, select_status=2
        )
        self.henrei_kumotsu_2 = HenreihinKumotsuFactory(
            detail_kumotsu=self.kumotsu_2, select_status=2
        )
        self.henrei_kumotsu_3 = HenreihinKumotsuFactory(
            detail_kumotsu=self.kumotsu_3, select_status=2
        )
        self.henrei_kumotsu_4 = HenreihinKumotsuFactory(
            detail_kumotsu=self.kumotsu_4, select_status=2
        )
        self.henrei_kumotsu_5 = HenreihinKumotsuFactory(
            detail_kumotsu=self.kumotsu_5, select_status=2
        )

        self.koden_1 = EntryDetailKodenFactory(entry_detail=entry_detail_21)
        self.koden_2 = EntryDetailKodenFactory(entry_detail=entry_detail_22)
        self.koden_3 = EntryDetailKodenFactory(entry_detail=entry_detail_11)
        self.koden_4 = EntryDetailKodenFactory(entry_detail=entry_detail_13)
        self.koden_5 = EntryDetailKodenFactory(entry_detail=entry_detail_12)

        self.henrei_koden_1 = HenreihinKodenFactory(detail_koden=self.koden_1, select_status=2)
        self.henrei_koden_2 = HenreihinKodenFactory(detail_koden=self.koden_2, select_status=2)
        self.henrei_koden_3 = HenreihinKodenFactory(detail_koden=self.koden_3, select_status=2)
        self.henrei_koden_4 = HenreihinKodenFactory(detail_koden=self.koden_4, select_status=2)
        self.henrei_koden_5 = HenreihinKodenFactory(detail_koden=self.koden_5, select_status=2)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_henrei_list_succeed(self) -> None:
        """返礼品一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # records[0]になるため、''に返礼品名を修正
        self.henrei_kumotsu_5.henreihin_name = ''
        self.henrei_kumotsu_5.save()

        params: Dict = {}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 10)

        # records[0]はself.henrei_kumotsu_5です。
        response_henrei_kumotsu_5 = records[0]
        self.assertEqual(
            response_henrei_kumotsu_5['henreihin_id'], self.henrei_kumotsu_5.henreihin_id
        )
        self.assertEqual(response_henrei_kumotsu_5['henreihin_type'], 2)
        self.assertEqual(
            response_henrei_kumotsu_5['detail_id'],
            self.henrei_kumotsu_5.detail_kumotsu.entry_detail.id,
        )
        self.assertEqual(
            response_henrei_kumotsu_5['seko_id'],
            self.henrei_kumotsu_5.detail_kumotsu.entry_detail.entry.seko.id,
        )
        self.assertEqual(
            response_henrei_kumotsu_5['entry_id'],
            self.henrei_kumotsu_5.detail_kumotsu.entry_detail.entry.id,
        )
        self.assertEqual(
            response_henrei_kumotsu_5['seko_date'],
            self.henrei_kumotsu_5.detail_kumotsu.entry_detail.entry.seko.seko_date.strftime(
                '%Y-%m-%d'
            ),
        )
        self.assertEqual(
            response_henrei_kumotsu_5['hall_name'],
            self.henrei_kumotsu_5.detail_kumotsu.entry_detail.entry.seko.schedules.get().hall_name,
        )
        self.assertEqual(
            response_henrei_kumotsu_5['soke_name'],
            self.henrei_kumotsu_5.detail_kumotsu.entry_detail.entry.seko.soke_name,
        )
        self.assertEqual(
            response_henrei_kumotsu_5['kojin_name'],
            self.henrei_kumotsu_5.detail_kumotsu.entry_detail.entry.seko.kojin.get(
                kojin_num=1
            ).name,
        )
        self.assertEqual(
            response_henrei_kumotsu_5['moshu_name'],
            self.henrei_kumotsu_5.detail_kumotsu.entry_detail.entry.seko.moshu.name,
        )
        self.assertEqual(
            response_henrei_kumotsu_5['henreihin_name'], self.henrei_kumotsu_5.henreihin_name
        )
        self.assertEqual(
            response_henrei_kumotsu_5['henreihin_price'], self.henrei_kumotsu_5.henreihin_price
        )
        self.assertEqual(
            response_henrei_kumotsu_5['supplier_id'], self.henrei_kumotsu_5.supplier.id
        )
        self.assertEqual(
            response_henrei_kumotsu_5['supplier_name'], self.henrei_kumotsu_5.supplier.name
        )
        self.assertEqual(
            response_henrei_kumotsu_5['order_status'], self.henrei_kumotsu_5.order_status
        )

        # 申込ID,返礼品名の昇順になっているか
        entry_ids = [record['entry_id'] for record in records]
        self.assertEqual(entry_ids, sorted(entry_ids))
        henreihin_name_1 = [
            record['henreihin_name'] for record in records if self.entry_1.id == record['entry_id']
        ]
        self.assertEqual(henreihin_name_1, sorted(henreihin_name_1))
        henreihin_name_2 = [
            record['henreihin_name'] for record in records if self.entry_2.id == record['entry_id']
        ]
        self.assertEqual(henreihin_name_2, sorted(henreihin_name_2))

    def test_henrei_list_ignores_deleted_seko(self) -> None:
        """削除した施行の返礼品は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko_1.disable()

        params: Dict = {}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

    def test_henrei_list_ignores_when_kojin_num_is_not_equal_1(self) -> None:
        """故人番号が1ではない返礼品は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.kojin_2.kojin_num = 2
        self.kojin_2.save()

        params: Dict = {}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 6)

    def test_henrei_list_ignores_when_select_status_not_equal_2(self) -> None:
        """選択ステータスが2ではない返礼品は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.henrei_kumotsu_2.select_status = 1
        self.henrei_kumotsu_2.save()

        params: Dict = {}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 9)

        self.henrei_koden_2.select_status = 1
        self.henrei_koden_2.save()

        params: Dict = {}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 8)

    def test_henrei_list_filter_by_company_id(self) -> None:
        """返礼品一覧で葬儀社IDを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'company_id': self.base_1.id}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 6)

    def test_henrei_list_filter_by_seko_id(self) -> None:
        """返礼品一覧で施行IDを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'seko_id': self.seko_2.id}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

    def test_henrei_list_filter_by_order_status(self) -> None:
        """返礼品一覧で発注状況を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.henrei_kumotsu_1.order_status = 0
        self.henrei_kumotsu_2.order_status = 1
        self.henrei_kumotsu_3.order_status = 2
        self.henrei_kumotsu_4.order_status = 0
        self.henrei_kumotsu_5.order_status = 1
        self.henrei_kumotsu_1.save()
        self.henrei_kumotsu_2.save()
        self.henrei_kumotsu_3.save()
        self.henrei_kumotsu_4.save()
        self.henrei_kumotsu_5.save()

        self.henrei_koden_1.order_status = 0
        self.henrei_koden_2.order_status = 0
        self.henrei_koden_3.order_status = 1
        self.henrei_koden_4.order_status = 2
        self.henrei_koden_5.order_status = 2
        self.henrei_koden_1.save()
        self.henrei_koden_2.save()
        self.henrei_koden_3.save()
        self.henrei_koden_4.save()
        self.henrei_koden_5.save()

        params: Dict = {'order_status': '0,2'}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 7)

        params: Dict = {'order_status': ''}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 10)

    def test_henrei_list_filter_by_hall_id(self) -> None:
        """返礼品一覧で式場IDを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'hall_id': self.schedule_1.hall_id}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 6)

    def test_henrei_list_filter_by_seko_date(self) -> None:
        """返礼品一覧で施行日を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')
        self.seko_1.seko_date = timezone.now() - timezone.timedelta(20)
        self.seko_1.save()
        self.seko_2.seko_date = timezone.now() - timezone.timedelta(30)
        self.seko_2.save()

        params: Dict = {'seko_date': self.seko_2.seko_date.strftime('%Y-%m-%d')}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

    def test_henrei_list_filter_by_supplier_id(self) -> None:
        """返礼品一覧で発注先IDを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'supplier_id': self.henrei_kumotsu_1.supplier_id}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_henrei_list_filter_by_soke_name(self) -> None:
        """返礼品一覧で葬家名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko_1.soke_name = '1一'
        self.seko_2.soke_name = '1一2二'
        self.seko_1.save()
        self.seko_2.save()

        params: Dict = {'soke_name': '一2'}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

    def test_henrei_list_filter_by_kojin_name(self) -> None:
        """返礼品一覧で故人名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.kojin_1.name = '1一'
        self.kojin_2.name = '1一2二'
        self.kojin_1.save()
        self.kojin_2.save()

        params: Dict = {'kojin_name': '一2'}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

    def test_henrei_list_filter_by_moshu_name(self) -> None:
        """返礼品一覧で喪主名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.moshu_1.name = '1一'
        self.moshu_2.name = '2二1一'
        self.moshu_1.save()
        self.moshu_2.save()

        params: Dict = {'moshu_name': '二1'}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

    def test_henrei_list_filter_by_is_cancel(self) -> None:
        """返礼品一覧でキャンセル状況を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.entry_1.cancel_ts = timezone.now() - timezone.timedelta(30)
        self.entry_1.save()

        params: Dict = {'is_cancel': True}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 6)

        params: Dict = {'is_cancel': False}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

    def test_henrei_list_failed_without_auth(self) -> None:
        """返礼品一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('henrei:henrei_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
