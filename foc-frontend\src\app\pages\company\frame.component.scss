@import "src/assets/scss/company/setting";
.main-header {
  position: fixed;
  width: 100%;
  height: 40px;
  top: 0;
  left: 0;
  background-color: $background-dark;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  --wekit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  z-index: 99;
  .logo {
    position: absolute;
    width: 150px;
    height: 40px;
    top: 0;
    left: 0;
    background-image: url(../../../assets/img/company/logo.png);
    background-repeat: no-repeat;
    background-position: 12px center;
    background-size: 100px auto;
    text-indent: -9999px;
    cursor: pointer;
    &:focus {
      outline: 0;
    }
    &:hover {
      opacity: .8;
    }
  }
  .menu-area {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    &.hide {
      display: none;
    }
    >.top-menu {
      padding: 12px 5px;
      >i {
        font-size: 1.14em;
      }
    }
  }
}
.messageArea {
  position: fixed;
  z-index: 10;
  left: 5px;
  right: 5px;
  top: -30px;
  display: flex;
  align-items: center;
  opacity: .9;
  .ui.message {
    width: 100%;
    margin: auto;
    transition: all 0.2s ease-in-out;
    &.hidden{
      display: block!important;
      top: -50px;
    }
    &.visible{
      top: 70px;
    }
    .segment {
      padding: 0;
    }
  }
}
.routerArea{
  height: 100%;
  overflow: auto;
}
