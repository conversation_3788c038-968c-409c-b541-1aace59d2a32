import secrets
from typing import Dict, List, Optional

from django.contrib.auth.base_user import AbstractBaseUser
from django.contrib.auth.hashers import check_password, make_password
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from items.models import Item
from masters.models import Schedule, SekoStyle, Service
from staffs import hash
from staffs.models import Base, Staff
from utils.strings import extract_digits


class Seko(models.Model):
    order_staff = models.ForeignKey(
        Staff, models.DO_NOTHING, verbose_name=_('order staff'), related_name='ordered_seko'
    )
    seko_staff = models.ForeignKey(
        Staff,
        models.DO_NOTHING,
        verbose_name=_('seko staff'),
        related_name='staffed_seko',
        blank=True,
        null=True,
    )
    af_staff = models.ForeignKey(
        Staff,
        models.DO_NOTHING,
        verbose_name=_('af_staff'),
        related_name='af_seko',
        blank=True,
        null=True,
    )
    seko_company = models.ForeignKey(
        Base, models.DO_NOTHING, verbose_name=_('seko company'), related_name='seko_by_company'
    )
    seko_department = models.ForeignKey(
        Base,
        models.DO_NOTHING,
        verbose_name=_('seko department'),
        related_name='seko',
        blank=True,
        null=True,
    )
    death_date = models.DateField(_('date of death'))
    seko_date = models.DateField(_('date of seko'), db_index=True)
    soke_name = models.TextField(_('soke name'))
    soke_kana = models.TextField(_('soke kana'))
    seko_style = models.ForeignKey(
        SekoStyle, models.DO_NOTHING, verbose_name=_('seko style'), related_name='seko'
    )
    seko_style_name = models.TextField(_('seko style display name'))
    fuho_site_admission_ts = models.DateTimeField(blank=True, null=True)
    fuho_site_end_date = models.DateField(_('fuho site open up to'))
    fuho_sentence = models.TextField(_('fuho sentence'), blank=True, null=True)
    fuho_contact_name = models.TextField(_('fuho contact name'), blank=True, null=True)
    fuho_contact_tel = models.CharField(
        _('fuho contact tel'), max_length=15, blank=True, null=True
    )
    henreihin_rate = models.IntegerField(_('henreihin rate'))
    invoice_file_name = models.FileField(
        _('invoice file'), upload_to='invoices', blank=True, null=True
    )
    note = models.TextField(_('note'), blank=True, null=True)
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    fdn_code = models.TextField(_('fdn code'), blank=True, null=True)
    free1_label = models.TextField(_('free1 label'), blank=True, null=True)
    free1_data = models.TextField(_('free1 data'), blank=True, null=True)
    free2_label = models.TextField(_('free2 label'), blank=True, null=True)
    free2_data = models.TextField(_('free2 data'), blank=True, null=True)
    free3_label = models.TextField(_('free3 label'), blank=True, null=True)
    free3_data = models.TextField(_('free3 data'), blank=True, null=True)
    free4_label = models.TextField(_('free4 label'), blank=True, null=True)
    free4_data = models.TextField(_('free4 data'), blank=True, null=True)
    moshu_display_place = models.IntegerField(_('moshu display place'), default=0)
    seireki_display_flg = models.BooleanField(_('deleted'), default=False)
    attached_file1_name = models.TextField(_('attached file1 name'), blank=True, null=True)
    attached_file1 = models.FileField(
        _('attached file1'), upload_to='invoices', blank=True, null=True
    )
    attached_file2_name = models.TextField(_('attached file2 name'), blank=True, null=True)
    attached_file2 = models.FileField(
        _('attached file2'), upload_to='invoices', blank=True, null=True
    )
    attached_file3_name = models.TextField(_('attached file3 name'), blank=True, null=True)
    attached_file3 = models.FileField(
        _('attached file3'), upload_to='invoices', blank=True, null=True
    )

    class Meta:
        db_table = 'tr_seko'

    def disable(self) -> None:
        """施行を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()

    def update_moshu(self, moshu_data: Dict) -> None:
        if hasattr(self, 'moshu'):
            moshu: Moshu = self.moshu
            for key, val in moshu_data.items():
                setattr(moshu, key, val)
            moshu.save()
        else:
            Moshu.objects.create(seko=self, **moshu_data)

    def update_kojin(self, kojin_data: List[Dict]) -> None:
        self.kojin.all().delete()
        new_kojin: List[Kojin] = []
        for kojin_dict in kojin_data:
            new_kojin.append(Kojin(**kojin_dict))
        self.kojin.set(new_kojin, bulk=False)

    def update_schedules(self, schedules_data: List[Dict]) -> None:
        self.schedules.all().delete()
        new_schedules: List[SekoSchedule] = []
        for schedule_dict in schedules_data:
            new_schedules.append(SekoSchedule(**schedule_dict))
        self.schedules.set(new_schedules, bulk=False)

    def update_videos(self, videos_data: List[Dict]) -> None:
        self.videos.all().delete()
        new_videos: List[SekoVideo] = []
        for video_dict in videos_data:
            new_videos.append(SekoVideo(**video_dict))
        self.videos.set(new_videos, bulk=False)

    def update_items(self, items_data: List[Dict]) -> None:
        self.items.all().delete()
        new_items: List[SekoItem] = []
        for item_dict in items_data:
            new_items.append(SekoItem(**item_dict))
        self.items.set(new_items, bulk=False)

    def update_services(self, services_data: List[Dict]) -> None:
        self.services.all().delete()
        new_services: List[SekoService] = []
        for service_dict in services_data:
            new_services.append(SekoService(**service_dict))
        self.services.set(new_services, bulk=False)

    def update_kakegami(self, kakegami_data: Dict) -> None:
        if hasattr(self, 'henrei_kakegami'):
            kakegami: HenreihinKakegami = self.henrei_kakegami
            for key, val in kakegami_data.items():
                setattr(kakegami, key, val)
            kakegami.save()
        else:
            HenreihinKakegami.objects.create(seko=self, **kakegami_data)

    def update_seko_contact(self, seko_contact_data: Dict) -> None:
        if hasattr(self, 'seko_contact'):
            seko_contact: SekoContact = self.seko_contact
            if seko_contact_data:
                for key, val in seko_contact_data.items():
                    setattr(seko_contact, key, val)
                seko_contact.save()
            else:
                seko_contact.delete()
        else:
            if seko_contact_data:
                SekoContact.objects.create(seko=self, **seko_contact_data)


class Moshu(AbstractBaseUser):
    seko = models.OneToOneField(
        Seko,
        models.DO_NOTHING,
        verbose_name=_('seko'),
        related_name='moshu',
        db_column='id',
        primary_key=True,
    )
    name = models.TextField(_('name'))
    kana = models.TextField(_('kana'))
    kojin_moshu_relationship = models.TextField(
        _('kojin moshu relationship'), blank=True, null=True
    )
    zip_code = models.CharField(_('zipcode'), max_length=7)
    prefecture = models.TextField(_('prefecture'))
    address_1 = models.TextField(_('address1'))
    address_2 = models.TextField(_('address2'), blank=True, null=True)
    address_3 = models.TextField(_('address3'), blank=True, null=True)
    tel = models.CharField(_('tel no'), max_length=15, blank=True, null=True)
    mobile_num = models.CharField(_('mobile no'), max_length=15, blank=True, null=True)
    mail_address = models.TextField(_('mail address'), blank=True, null=True)
    password = models.TextField(_('password'))
    salt = models.CharField(_('salt'), max_length=128)
    agree_ts = models.DateTimeField(_('agreed at'), blank=True, null=True)
    soke_site_del_request_flg = models.BooleanField(_('soke site delete requested'), default=False)
    soke_site_del_flg = models.BooleanField(_('soke site deleted'), default=False)
    mail_flg = models.BooleanField(_('email accepted'), default=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_moshu'

    def set_password(self, raw_password: Optional[str]) -> None:
        """アカウントのパスワードを更新します

        親クラスと同様、インスタンスのパスワードを更新しますが自動的にcommitは
        行いません

        アカウントのSaltが設定されていなければ先に自動生成し、それを用いて
        パスワードをハッシュ化します

        Args:
            raw_password (str): 設定するパスワード(平文)
        """
        if raw_password is None:
            self.password = make_password(raw_password)
            self._password = raw_password
            return

        if not self.salt:
            self.salt = hash.generate_salt()
        self.password = hash.make_password(raw_password, self.salt)
        self._password = raw_password

    def check_password(self, raw_password: Optional[str]) -> bool:
        """アカウントのパスワードを照合します

        Args:
            raw_password (Optional[str]): パスワード(平文)

        Raises:
            ValueError: アカウントにSaltが設定されていないなどの状態異常

        Returns:
            bool: パスワード照合の成否
        """
        if raw_password is None:
            return check_password(raw_password, self.salt)
        if not self.salt:
            raise ValueError('Salt must be set before checking password')

        return self.password == hash.make_password(raw_password, self.salt)

    @classmethod
    def generate_raw_password(cls) -> str:
        """初期パスワードを生成します

        Returns:
            str: 初期パスワード
        """

        return secrets.token_urlsafe(16)

    def reset_password(self) -> str:
        """パスワードをリセットします

        リセットの場合はSaltもパスワードもシステム側で自動生成します

        Returns:
            str: 自動生成した平文パスワード
        """

        self.salt = hash.generate_salt()
        _raw_password = self.generate_raw_password()
        self.password = hash.make_password(_raw_password, self.salt)
        self.save()
        self._password = _raw_password
        return _raw_password

    @property
    def is_active(self):
        return True

    def mobile_num_digits(self) -> str:
        """携帯電話番号の文字列から数値以外を除去した文字列を返します"""
        return extract_digits(self.mobile_num)

    def update_last_login(self) -> None:
        """ログイン日時を更新します"""
        self.last_login = timezone.localtime()
        self.save(update_fields=['last_login'])


class Kojin(models.Model):
    seko = models.ForeignKey(Seko, models.DO_NOTHING, verbose_name=_('seko'), related_name='kojin')
    kojin_num = models.IntegerField(_('numbering'))
    name = models.TextField(_('kojin name'))
    kana = models.TextField(_('kojin kana'))
    kaimyo = models.TextField(_('kaimyo'), blank=True, null=True)
    iei_file_name = models.ImageField(
        _('portrait of deceased'), upload_to='portraits', blank=True, null=True
    )
    moshu_kojin_relationship = models.TextField(
        _('moshu kojin relationship'), blank=True, null=True
    )
    birth_date = models.DateField(_('birthday'))
    death_date = models.DateField(_('date of death'))
    age_kbn = models.TextField(_('age type'))
    age = models.IntegerField(_('age'))
    note = models.TextField(_('note'), blank=True, null=True)
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_kojin'


class SekoSchedule(models.Model):
    seko = models.ForeignKey(
        Seko, models.DO_NOTHING, verbose_name=_('seko'), related_name='schedules'
    )
    schedule = models.ForeignKey(
        Schedule,
        models.DO_NOTHING,
        verbose_name=_('schedule'),
        related_name='seko_schedules',
        blank=True,
        null=True,
    )
    schedule_name = models.TextField(_('schedule name'))
    hall = models.ForeignKey(
        Base,
        models.DO_NOTHING,
        verbose_name=_('hall'),
        related_name='seko_schedules',
        blank=True,
        null=True,
    )
    hall_name = models.TextField(_('hall name'))
    hall_zip_code = models.CharField(_('hall zipcode'), max_length=7, blank=True, null=True)
    hall_address = models.TextField(_('hall address'), blank=True, null=True)
    hall_tel = models.CharField(_('hall tel no'), max_length=15, blank=True, null=True)
    schedule_date = models.DateField(_('schedule date'), blank=True, null=True)
    begin_time = models.TimeField(_('begins at'), blank=True, null=True)
    end_time = models.TimeField(_('ends at'), blank=True, null=True)
    display_num = models.IntegerField(_('display order'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_seko_schedule'


class SekoVideo(models.Model):
    seko = models.ForeignKey(
        Seko, models.DO_NOTHING, verbose_name=_('seko'), related_name='videos'
    )
    title = models.TextField(_('title'))
    live_begin_ts = models.DateTimeField(_('live begins at'), blank=True, null=True)
    live_end_ts = models.DateTimeField(_('live ends at'), blank=True, null=True)
    delivery_end_ts = models.DateTimeField(_('delivery ends at'))
    youtube_code = models.TextField(_('YouTube code'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_video'


class SekoAlbum(models.Model):
    seko = models.ForeignKey(
        Seko, models.DO_NOTHING, verbose_name=_('seko'), related_name='albums'
    )
    file_name = models.ImageField(_('file name'), upload_to='seko_album')
    display_num = models.IntegerField(_('display order'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_album'


class SekoShareImage(models.Model):
    seko = models.ForeignKey(
        Seko, models.DO_NOTHING, verbose_name=_('seko'), related_name='share_images'
    )
    file_name = models.ImageField(_('file name'), upload_to='seko_share_image')
    mode = models.IntegerField(_('mode'))
    display_num = models.IntegerField(_('display order'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_share_image'


class SekoItem(models.Model):
    seko = models.ForeignKey(Seko, models.CASCADE, verbose_name=_('seko'), related_name='items')
    item = models.ForeignKey(Item, models.PROTECT, verbose_name=_('item'))
    seko_before_flg = models.BooleanField(_('seko before flag'), default=True)
    seko_after_flg = models.BooleanField(_('seko after flag'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_seko_item'


class SekoService(models.Model):
    seko = models.ForeignKey(Seko, models.CASCADE, verbose_name=_('seko'), related_name='services')
    hall_service = models.ForeignKey(Service, models.PROTECT, verbose_name=_('hall service'))
    limit_ts = models.DateTimeField(_('reception ends at'), blank=True, null=True)
    henreihin_select_flg = models.BooleanField(_('selectable as henreihin'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_seko_service'


class HenreihinKakegami(models.Model):
    seko = models.OneToOneField(
        Seko,
        models.CASCADE,
        db_column='id',
        primary_key=True,
        verbose_name=_('seko'),
        related_name='henrei_kakegami',
    )
    omotegaki = models.TextField(_('omotegaki'), blank=True, null=True)
    mizuhiki = models.TextField(_('mizuhiki'), blank=True, null=True)
    package_type_name = models.TextField(_('package type'), blank=True, null=True)
    okurinushi_name = models.TextField(_('sender name'), blank=True, null=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_henreihin_kakegami'


class SekoInquiry(models.Model):
    seko = models.ForeignKey(
        Seko, models.CASCADE, verbose_name=_('seko'), related_name='inquiries'
    )
    query_group_id = models.IntegerField(_('query group id'))
    query = models.TextField(_('query content'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_seko_query'


class SekoAnswer(models.Model):
    inquiry = models.ForeignKey(
        SekoInquiry,
        models.CASCADE,
        db_column='query_id',
        verbose_name=_('seko inquiry'),
        related_name='answers',
    )
    answer = models.TextField(_('answer content'))
    staff = models.ForeignKey(Staff, models.PROTECT)
    del_flg = models.BooleanField(_('deleted'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_seko_answer'

    def disable(self) -> None:
        """施行回答を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()


class SekoContact(models.Model):
    seko = models.OneToOneField(
        Seko,
        models.DO_NOTHING,
        verbose_name=_('seko'),
        related_name='seko_contact',
        db_column='id',
        primary_key=True,
    )
    name = models.TextField(_('name'), blank=True, null=True)
    zip_code = models.CharField(_('zipcode'), max_length=7, blank=True, null=True)
    prefecture = models.TextField(_('prefecture'), blank=True, null=True)
    address_1 = models.TextField(_('address1'), blank=True, null=True)
    address_2 = models.TextField(_('address2'), blank=True, null=True)
    address_3 = models.TextField(_('address3'), blank=True, null=True)
    kojin_relationship = models.TextField(_('kojin relationship'), blank=True, null=True)
    tel = models.CharField(_('tel no'), max_length=15, blank=True, null=True)
    mobile_num = models.CharField(_('mobile no'), max_length=15, blank=True, null=True)
    mail_address = models.TextField(_('mail address'), blank=True, null=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_seko_contact'

    def mobile_num_digits(self) -> str:
        """携帯電話番号の文字列から数値以外を除去した文字列を返します"""
        return extract_digits(self.mobile_num)
