from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.tests.factories import (
    AfContactWayFactory,
    AfterFollowFactory,
    AfWishFactory,
    SekoAfFactory,
)
from seko.tests.factories import SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class SekoAfCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko_af_data = SekoAfFactory.build(
            seko=SekoFactory(), contact_way=AfContactWayFactory()
        )
        self.af_wish_data_1 = AfWishFactory.build(
            seko_af=self.seko_af_data, af_type=AfterFollowFactory()
        )
        self.af_wish_data_2 = AfWishFactory.build(
            seko_af=self.seko_af_data, af_type=AfterFollowFactory()
        )
        self.af_wish_data_3 = AfWishFactory.build(
            seko_af=self.seko_af_data, af_type=AfterFollowFactory()
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        af_wishes: List[Dict] = []
        for af_wish_data in [self.af_wish_data_1, self.af_wish_data_2, self.af_wish_data_3]:
            af_wishes.append(
                {
                    'af_type': af_wish_data.af_type.pk,
                    'answered_flg': af_wish_data.answered_flg,
                    'proposal_status': af_wish_data.proposal_status,
                    'order_status': af_wish_data.order_status,
                    'order_date': af_wish_data.order_date.isoformat(),
                    'order_chance': af_wish_data.order_chance,
                }
            )

        return {
            'seko': self.seko_af_data.seko.pk,
            'contact_way': self.seko_af_data.contact_way.pk,
            'note': self.seko_af_data.note,
            'wishes': af_wishes,
        }

    def test_seko_af_create_succeed(self) -> None:
        """施行AFを追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('after_follow:seko_af_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertEqual(record['seko'], self.seko_af_data.seko.pk)
        self.assertEqual(
            record['contact_way']['contact_way'], self.seko_af_data.contact_way.contact_way
        )
        self.assertEqual(
            record['contact_way']['display_num'], self.seko_af_data.contact_way.display_num
        )
        self.assertEqual(record['note'], self.seko_af_data.note)

        self.assertEqual(len(record['wishes']), 3)

    def test_seko_af_create_failed_without_auth(self) -> None:
        """施行AF追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('after_follow:seko_af_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
