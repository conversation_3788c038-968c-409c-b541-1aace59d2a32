@import "src/assets/scss/company/setting";
.container {
  .inner {
    .contents {
      .table_fixed.main tr{
        cursor: default;
      }
      .table_fixed.body {
        max-height: calc(100% - 200px);
      }
      tr {
        .base_type_name {
          width: 25%;
        }
        .base_name {
          width: 25%;
        }
        .operation {
          i {
            cursor: pointer;
          }
          width: 105px;
				}

      }
      .ui.modal {
        &.item.list{
          top: 10px !important;
          .content {
            height: calc(100vh - 230px);
          }
          .table_fixed.body {
            max-height: calc(100% - 100px);
          }
        }
        &.item.supplier {
          top: 50px !important;
          .content {
            height: calc(100vh - 300px);
          }
          .table_fixed.body {
            max-height: calc(100% - 50px);
          }
        }
        .ui.tab.segment {
          height: calc(100% - 20px);
          background-color: $tab-background;
        }
        .title {
          font-size: smaller;
          margin-left: 20px;
          border: none;
          padding-bottom: 0;
          .data {
            margin-left: 10px;
            font-weight: normal;
          }
        }
        &.service .content {
          display: flex;
          -webkit-flex-wrap: wrap;
          -ms-flex-wrap: wrap;
          flex-wrap: wrap;
          padding: 20px 20px;
          justify-content: center;
          .item {
            width: 120px;
            height: 120px;
            padding: 10px 0;
            margin: 10px 20px;
            background-color: $label-background-light;
            cursor: pointer;
            text-align: center;
            opacity: .7;
            .subtitle {
              -webkit-box-sizing: border-box;
              -moz-box-sizing: border-box;
              box-sizing: border-box;
              margin-top: 5px;
              font-size: 1.077em;
              font-weight: 700;
            }
            &:hover {
              box-shadow: 0 5px 20px rgba(0,0,0,.3);
              --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
              -webkit-transform: translateY(-1px);
              transform: translateY(-1px);
            }
            &.selected {
              opacity: 1;
              box-shadow: 0 5px 20px rgba(0,0,0,.3);
              --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
              -webkit-transform: translateY(-1px);
              transform: translateY(-1px);
            }
            .ui.checkbox {
              margin-top: 10px;
            }
            // &:focus {
            //   outline: none !important;
            //   border: 0;
            // }
          }
        }
        &.service-limit {
          .content {
            padding-left: 50px;
          }
          .input_area .line {
            .tiny {
              width: 70px;
              min-width: 70px;
            }
            label {
              min-width: 120px;
              &.plane {
                min-width: 50px;
              }
              &.schedule {
                margin-left: 120px;
                width: 174px;
              }
              &.limit_hour {
                margin-left: 1px;
                width: 70px;
              }
              &.unit {
                margin-left: 1px;
                width: 118px;
              }
              &.limit_time {
                margin-left: 1px;
                width: 120px;
              }
            }
          }
        }
        &.item.list tr {
          .hinban {
            width: 9%;
          }
          .item_price {
            width: 9%;
          }
          .tax_pct {
            width: 60px;
          }
          .begin_date {
            width: 10%;
          }
          .end_date {
            width: 10%;
          }
          .fdn_code {
            width: 10%;
          }
          .display_num {
            width: 60px;
          }
          .image {
            width: 70px;
            &.data {
              padding: 0;
            }
            >div {
              display: flex;
              width: 100%;
              height: 42px;
              >img {
                margin: auto;
                width: auto;
                height: auto;
                max-width: 100%;
                max-height: 42px;
                display: none;
                &.enabled {
                  display: block;
                }
              }
            }
          }
          .supplier {
            width: 60px;
          }
          .operation {
            width: 60px;
          }
          .operation2 {
            width: 80px;
          }

        }
        &.item.supplier tr {
          .select {
            width: 70px
          }
          .default {
            width: 90px
          }
          .supplier_name {
            width: 25%;
          }
          .ui.radio.checkbox {
            padding: 0;
          }
        }
      }
      .ui.button.remark {
        padding-top: 6px;
        padding-bottom: 2px;
        vertical-align: top!important;
        .under {
          font-size: 0.6em;
        }
      }
    }
  }
}
