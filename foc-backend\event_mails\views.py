from django.db.models import Prefetch
from django_filters import rest_framework as filters
from rest_framework import generics

from event_mails.models import EventMail, EventMailTarget
from event_mails.serializers import EventMailSerializer
from masters.models import ChobunDaishiMaster, Service
from seko.models import <PERSON><PERSON>, <PERSON>shu
from service_reception_terms.models import ServiceReceptionTerm
from utils.permissions import IsStaff
from utils.view_mixins import AddContextMixin


class EventMailListFilter(filters.FilterSet):
    send_date = filters.DateFromToRangeFilter(field_name='send_ts')
    company = filters.NumberFilter(field_name='targets__seko__seko_company', distinct=True)


class EventMailList(AddContextMixin, generics.ListCreateAPIView):

    queryset = (
        EventMail.objects.select_related('staff', 'staff__base')
        .prefetch_related(
            Prefetch(
                'staff__base__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
            ),
            Prefetch('staff__base__services', queryset=Service.objects.all()),
            Prefetch(
                'staff__base__service_reception_terms', queryset=ServiceReceptionTerm.objects.all()
            ),
            Prefetch(
                'targets', queryset=EventMailTarget.objects.select_related('seko').order_by('seko')
            ),
            Prefetch('targets__seko__moshu', queryset=Moshu.objects.all()),
            Prefetch('targets__seko__kojin', queryset=Kojin.objects.order_by('kojin_num')),
        )
        .order_by('select_ts')
        .all()
    )
    serializer_class = EventMailSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = EventMailListFilter
    permission_classes = [IsStaff]
