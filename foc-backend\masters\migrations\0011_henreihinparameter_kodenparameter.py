# Generated by Django 3.1.4 on 2020-12-17 05:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('masters', '0010_paymenttype'),
    ]

    operations = [
        migrations.CreateModel(
            name='HenreihinParameter',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('henreihin_omote_default', models.TextField()),
                ('henreihin_mizuhiki_default', models.TextField()),
                ('henreihin_hoso_default', models.TextField()),
            ],
            options={
                'db_table': 'mm_henreihin_parameter',
            },
        ),
        migrations.CreateModel(
            name='KodenParameter',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('koden_commission_tax_pct', models.IntegerField()),
                ('koden_upper_limit', models.IntegerField()),
            ],
            options={
                'db_table': 'mm_koden_parameter',
            },
        ),
    ]
