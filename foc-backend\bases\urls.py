from django.urls import path

from bases import views

app_name = 'bases'
urlpatterns = [
    path('', views.BaseList.as_view(), name='base_list'),
    path('<int:pk>/', views.BaseDetail.as_view(), name='base_detail'),
    path('<int:base_id>/focfee/', views.FocFeeDetail.as_view(), name='focfee'),
    path('<int:base_id>/tokusho/', views.TokushoDetail.as_view(), name='tokusho'),
    path('<int:base_id>/sms/', views.SmsAccountDetail.as_view(), name='sms_account'),
    path('<int:pk>/inquiry/', views.InquiryMail.as_view(), name='inquiry'),
    path('all/', views.BaseFullList.as_view(), name='list_all_bases'),
]
