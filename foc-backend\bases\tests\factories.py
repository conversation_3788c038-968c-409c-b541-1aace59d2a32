import factory
import factory.fuzzy as fuzzy
import faker
from django.utils import timezone
from factory.django import DjangoModelFactory, ImageField

from bases.models import Base, BaseService, FocFee, SmsAccount, Tokusho
from masters.tests.factories import ServiceFactory

fake_provider = faker.Faker('ja_<PERSON>')
tz = timezone.get_current_timezone()


class BaseFactory(DjangoModelFactory):
    class Meta:
        model = Base

    id = factory.Sequence(lambda n: n)
    base_type = fuzzy.FuzzyChoice(Base.OrgType)
    company_code = fuzzy.FuzzyText(length=20)
    base_name = factory.Faker('company', locale='ja_JP')
    zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    prefecture = factory.Faker('prefecture', locale='ja_JP')
    address_1 = factory.Faker('city', locale='ja_JP')
    address_2 = factory.Faker('town', locale='ja_JP')
    address_3 = factory.Faker('chome', locale='ja_JP')
    tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    fax = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    company_logo_file = ImageField()
    company_map_file = ImageField()
    calc_type = fuzzy.FuzzyInteger(1, 3)
    display_num = factory.Faker('pyint', min_value=0, max_value=1000)
    created_at = factory.Faker(
        'past_datetime', start_date='-30d', tzinfo=timezone.get_current_timezone()
    )
    create_user_id = 0
    updated_at = factory.Faker(
        'past_datetime', start_date='-30d', tzinfo=timezone.get_current_timezone()
    )
    update_user_id = 0


class TokushoFactory(DjangoModelFactory):
    class Meta:
        model = Tokusho

    company = factory.SubFactory(BaseFactory)
    responsible_name = factory.Faker('name', locale='ja_JP')
    mail_address = factory.Faker('ascii_free_email', locale='ja_JP')
    from_name = factory.Sequence(
        lambda n: f"{factory.Faker('ascii_free_email', locale='ja_JP')} {n}"
    )
    url = factory.Faker('uri')


class FocFeeFactory(DjangoModelFactory):
    class Meta:
        model = FocFee

    company = factory.SubFactory(BaseFactory)
    monthly_fee = factory.Faker('pyint', min_value=0, max_value=2000)
    chobun_fee = factory.Faker('pyint', min_value=0, max_value=2000)
    chobun_fee_unit = fuzzy.FuzzyChoice(FocFee.UnitType)
    kumotsu_fee = factory.Faker('pyint', min_value=0, max_value=2000)
    kumotsu_fee_unit = fuzzy.FuzzyChoice(FocFee.UnitType)
    koden_fee = factory.Faker('pyint', min_value=0, max_value=2000)
    koden_fee_unit = fuzzy.FuzzyChoice(FocFee.UnitType)
    henreihin_fee = factory.Faker('pyint', min_value=0, max_value=2000)
    henreihin_fee_unit = fuzzy.FuzzyChoice(FocFee.UnitType)
    gmo_code = factory.Faker('pystr_format', string_format='########')
    gmo_code_koden = factory.Faker('pystr_format', string_format='########')
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0


class SmsAccountFactory(DjangoModelFactory):
    class Meta:
        model = SmsAccount

    company = factory.SubFactory(BaseFactory)
    token = factory.Faker('pystr', min_chars=20, max_chars=20)
    client_id = factory.Faker('pystr', min_chars=20, max_chars=20)
    sms_code = factory.Faker('pystr', min_chars=5, max_chars=5)


class BaseServiceFactory(DjangoModelFactory):
    class Meta:
        model = BaseService

    id = factory.Sequence(lambda n: n)
    base = factory.SubFactory(BaseFactory)
    service = factory.SubFactory(ServiceFactory)
