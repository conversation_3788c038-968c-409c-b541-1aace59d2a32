# Generated by Django 3.1.2 on 2020-10-20 07:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bases', '0003_auto_20201020_1647'),
    ]

    operations = [
        migrations.CreateModel(
            name='FocFee',
            fields=[
                (
                    'company',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='focfee',
                        serialize=False,
                        to='bases.base',
                        verbose_name='foc fee',
                    ),
                ),
                ('monthly_fee', models.IntegerField(verbose_name='monthly fee')),
                ('chobun_fee', models.IntegerField(verbose_name='chobun fee')),
                ('chobun_fee_unit', models.IntegerField(verbose_name='chobun fee unit')),
                ('kumotsu_fee', models.IntegerField(verbose_name='kumotsu fee')),
                ('kumotsu_fee_unit', models.IntegerField(verbose_name='kumotsu fee unit')),
                ('koden_fee', models.IntegerField(verbose_name='koden fee')),
                ('koden_fee_unit', models.IntegerField(verbose_name='koden fee unit')),
                ('henreihin_fee', models.IntegerField(verbose_name='henreihin fee')),
                ('henreihin_fee_unit', models.IntegerField(verbose_name='henreihin fee unit')),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, null=True, verbose_name='created at'),
                ),
                (
                    'create_user_id',
                    models.IntegerField(blank=True, null=True, verbose_name='created by'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, null=True, verbose_name='updated at'),
                ),
                (
                    'update_user_id',
                    models.IntegerField(blank=True, null=True, verbose_name='created by'),
                ),
            ],
            options={
                'db_table': 'm_foc_fee',
            },
        ),
        migrations.CreateModel(
            name='Tokusho',
            fields=[
                (
                    'company',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='tokusho',
                        serialize=False,
                        to='bases.base',
                        verbose_name='company',
                    ),
                ),
                ('responsible_name', models.TextField(verbose_name='responsible name')),
                ('mail_address', models.EmailField(max_length=254, verbose_name='mail address')),
                ('from_name', models.TextField(unique=True, verbose_name='from name')),
                ('url', models.TextField(verbose_name='url')),
            ],
            options={
                'db_table': 'm_tokusho',
            },
        ),
    ]
