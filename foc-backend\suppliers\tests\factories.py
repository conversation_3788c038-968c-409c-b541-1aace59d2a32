import factory
import faker
from django.utils import timezone
from factory.django import DjangoModelFactory
from factory.fuzzy import FuzzyInteger

from bases.tests.factories import BaseFactory
from suppliers.models import Supplier

tz = timezone.get_current_timezone()


fake_provider = faker.Faker('ja_JP')


class SupplierFactory(DjangoModelFactory):
    class Meta:
        model = Supplier

    id = factory.Sequence(lambda n: n)
    company = factory.SubFactory(BaseFactory)
    name = factory.Faker('name', locale='ja_JP')
    tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    fax = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    address = factory.Faker('address', locale='ja_JP')
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = FuzzyInteger(1)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = FuzzyInteger(1)
