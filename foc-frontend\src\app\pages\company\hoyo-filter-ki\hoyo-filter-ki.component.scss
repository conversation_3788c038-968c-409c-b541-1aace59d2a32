
@import "src/assets/scss/company/setting";
.container .inner .contents {
  .menu_title {
    .icon.big {
      margin-right: 20px;
    }
    .icon.combo {
      position: absolute;
      left: 45px;
    }
  }
  min-height: 400px;
  .search_area .line {
    .ui.input.small {
      max-width: 150px;
    }
  }
  .input_area {
    position: absolute;
    bottom: 30px;
    width: 90%;
    left: 10px;
    .description {
      margin-left: 20px;
    }
  }
  .ui.toggle.checkbox {
    padding: 4px 10px 0;
  }
  .table_fixed.body {
    max-height: calc(100% - 320px);
    &.no-page-nav {
      max-height: calc(100% - 290px);
    }
  }
  >.table_fixed tr {
    line-height: 1;
    td {
      >div:not(.checkbox) {
        min-height: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &.center {
          text-align: center;
        }
        &:not(:last-child) {
          min-height: 19px;
          border-bottom: dotted 1px $background-light1;
          margin: 0 -10px 5px;
          padding-left: 10px;
          padding-bottom: 5px;
        }
      }
    }
    .check {
      width: 50px;
      padding-left: 15px;
    }
    .seko_id {
      width: 7%;
    }
    .soke_name {
      width: 9%;
    }
    .kojin_name {
      width: 10%;
    }
    .moshu_name {
      width: 10%;
    }
    .contact_name {
      width: 10%;
    }
    .death_date {
      width: 8%;
    }
    .seko_date {
      width: 8%;
    }
    .hoyo_planned_date {
      width: 8%;
    }
    .filtered {
      width: 7%;
    }
  }
  .ui.modal {
    .ui.segment {
      cursor: pointer;
      font-size: 1.2em;
      &:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,.3);
        --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
        -webkit-transform: translateY(-2px);
        transform: translateY(-2px);
      }
      pre {
        white-space: pre-wrap;
      }
    }
  }
}
