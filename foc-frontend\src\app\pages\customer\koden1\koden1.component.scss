@import "src/assets/scss/customer/setting";
.container .inner .contents {
  &.with-background-image {
    background-image: url(../../../../assets/img/customer/koden.jpg);
  }
  &.description >div {
    max-width: 750px;
  }
  .remark {
    font-size: 1rem;
    line-height: 1.5;
    .important {
      color: $text-red;
    }
  }
  .notice {
    font-size: 1rem;
    line-height: 1.5;
    .important {
      color: $text-red;
    }
    margin-top: -10px;
  }
  .seko_company {
    font-size: 1rem;
    line-height: 1.5;
  }
  .input-area {
    padding-top: 20px;
    .line {
      margin-top: 10px;
      .input {
        display: flex;
        justify-content: flex-start;
        >div {
          font-size: 1rem;
          padding: 0 5px;
        }
        @media screen and (max-width: 560px) {
          &.price >div {
            padding: 0;
            margin-left: -4px;
          }
        }
        input.small {
          max-width: 80px;
        }
        com-dropdown {
          &.small, &.small .ui.dropdown {
            max-width: 80px;
            @media screen and (max-width: 560px) {
              max-width: 90px;
            }
          }
        }
        input {
          text-align: right;
        }
      }
      .description {
        padding: 5px 0;

      }
    }
    .commission {
      margin-top: 10px;
      margin-left: 130px;
      display: flex;
      .koden_commission {
        padding-left: 10px;
        font-size: 0.9rem;
      }
      @media screen and (max-width: 560px) {
        margin-left: 10px;
      }
    }
    .total {
      padding: 15px 5px 3px;
      display: inline;
      font-size: 1.1rem;
      font-weight: bold;
      margin-left: 130px;
      border-bottom: solid 2px black;
    }
  }
}
