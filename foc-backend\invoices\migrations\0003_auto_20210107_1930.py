# Generated by Django 3.1.5 on 2021-01-07 10:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('invoices', '0002_auto_20210106_1502'),
    ]

    operations = [
        migrations.AlterField(
            model_name='salescompany',
            name='sales_index',
            field=models.ForeignKey(
                db_column='sales_admin_id',
                on_delete=django.db.models.deletion.CASCADE,
                related_name='sales_companies',
                to='invoices.salesindex',
            ),
        ),
        migrations.AlterField(
            model_name='salesdetail',
            name='sales_seko_department',
            field=models.ForeignKey(
                blank=True,
                db_column='sales_seko_dept_id',
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='sales_details',
                to='invoices.salessekodepartment',
            ),
        ),
        migrations.AlterField(
            model_name='salessekodepartment',
            name='sales_company',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='sales_seko_departments',
                to='invoices.salescompany',
            ),
        ),
    ]
