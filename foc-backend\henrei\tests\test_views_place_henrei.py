from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from henrei.models import HenreihinKoden, HenreihinKumotsu
from henrei.tests.factories import HenreihinKodenFactory, HenreihinKumotsuFactory
from orders.models import Entry
from orders.tests.factories import (
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryFactory,
)
from seko.models import Moshu, Seko
from seko.tests.factories import MoshuFactory, SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

tz = timezone.get_current_timezone()
jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake_provider = Faker(locale='ja_JP')


class PlaceHenreiOrderViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.entry: Entry = EntryFactory()
        self.moshu = MoshuFactory(seko=self.entry.seko)
        self.henrei_koden_1 = HenreihinKodenFactory(
            detail_koden=EntryDetailKodenFactory(
                entry_detail=EntryDetailFactory(entry=self.entry)
            ),
            select_status=1,
        )
        self.henrei_koden_2 = HenreihinKodenFactory(
            detail_koden=EntryDetailKodenFactory(
                entry_detail=EntryDetailFactory(entry=self.entry)
            ),
            select_status=2,
        )
        self.henrei_koden_3 = HenreihinKodenFactory(
            detail_koden=EntryDetailKodenFactory(
                entry_detail=EntryDetailFactory(entry=self.entry)
            ),
            select_status=0,
        )
        self.henrei_koden_4 = HenreihinKodenFactory(
            detail_koden=EntryDetailKodenFactory(
                entry_detail=EntryDetailFactory(entry=self.entry)
            ),
            select_status=1,
        )
        self.henrei_kumotsu_1 = HenreihinKumotsuFactory(
            detail_kumotsu=EntryDetailKumotsuFactory(
                entry_detail=EntryDetailFactory(entry=self.entry)
            ),
            select_status=1,
        )
        self.henrei_kumotsu_2 = HenreihinKumotsuFactory(
            detail_kumotsu=EntryDetailKumotsuFactory(
                entry_detail=EntryDetailFactory(entry=self.entry)
            ),
            select_status=2,
        )
        self.henrei_kumotsu_3 = HenreihinKumotsuFactory(
            detail_kumotsu=EntryDetailKumotsuFactory(
                entry_detail=EntryDetailFactory(entry=self.entry)
            ),
            select_status=0,
        )
        self.henrei_kumotsu_4 = HenreihinKumotsuFactory(
            detail_kumotsu=EntryDetailKumotsuFactory(
                entry_detail=EntryDetailFactory(entry=self.entry)
            ),
            select_status=1,
        )

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {'seko': self.entry.seko.pk}

    def test_place_henrei_order_succeed(self) -> None:
        """返礼品供花供物/香典の選択ステータスが1のものを2に更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:place_henrei_order'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(len(record['henrei_koden']), 2)
        affected_koden: List[HenreihinKoden] = [self.henrei_koden_1, self.henrei_koden_4]
        for record_koden, self_koden in zip(record['henrei_koden'], affected_koden):
            self.assertEqual(record_koden['detail_koden'], self_koden.pk)

        self.assertEqual(len(record['henrei_kumotsu']), 2)
        affected_kumotsu: List[HenreihinKumotsu] = [self.henrei_kumotsu_1, self.henrei_kumotsu_4]
        for record_kumotsu, self_kumotsu in zip(record['henrei_kumotsu'], affected_kumotsu):
            self.assertEqual(record_kumotsu['detail_kumotsu'], self_kumotsu.pk)

    def test_place_henrei_order_failed_by_illegal_seko(self) -> None:
        """返礼品選択ステータス更新APIが不正な施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = self.basic_params()
        params['seko'] = non_saved_seko.pk

        response = self.api_client.post(
            reverse('henrei:place_henrei_order'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('seko'))

    def test_place_henrei_order_fail_by_seko_disabled(self) -> None:
        """返礼品選択ステータス更新APIが所属する施行が無効になっているため失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.entry.seko.disable()

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:place_henrei_order'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_place_henrei_order_deny_from_another_moshu(self) -> None:
        """返礼品選択ステータス更新APIが他の喪主からの呼び出しを拒否する"""
        another_moshu: Moshu = MoshuFactory()
        refresh = RefreshToken.for_user(another_moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:place_henrei_order'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_place_henrei_order_deny_from_staff(self) -> None:
        """返礼品選択ステータス更新APIが担当者からの呼び出しを拒否する"""
        staff = StaffFactory()
        refresh = RefreshToken.for_user(staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:place_henrei_order'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_place_henrei_order_failed_without_auth(self) -> None:
        """返礼品選択ステータス更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:place_henrei_order'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
