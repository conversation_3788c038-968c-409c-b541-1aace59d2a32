from typing import Dict
from urllib.parse import urljoin

from django.conf import settings
from django.core.mail import EmailMessage
from django.template.loader import get_template
from rest_framework import status, views
from rest_framework.response import Response

from inquiry.serializers import InquiryMailSerializer
from utils.requests import request_origin


class InquiryMail(views.APIView):
    def post(self, request, format=None):
        fuho_num = request.data.pop('fuho_num', None)
        fuho_url = None
        if fuho_num:
            fuho_url: str = urljoin(request_origin(self.request), f'fuho/{fuho_num}')
        serializer = InquiryMailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        template_context: Dict = dict(
            [
                (key, val)
                for key, val in serializer.validated_data.items()
                if key
                in ['sender_name', 'sender_kana', 'sender_tel', 'sender_address', 'mail_body']
            ]
        )
        template_context['fuho_url'] = fuho_url
        mail_body = (
            get_template('mail/inquiry_mail.txt').render(template_context).replace('  ', '　')
        )

        message = EmailMessage(
            subject='FOCお問合せ',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=[serializer.validated_data['recipient_address']],
            bcc=settings.SUPPORT_EMAIL_BCC,
        )
        message.send(fail_silently=False)
        return Response(data={}, status=status.HTTP_200_OK)
