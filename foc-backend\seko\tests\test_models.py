from datetime import datetime

from django.contrib.auth.hashers import UNUSABLE_PASSWORD_PREFIX
from django.test import TestCase

from seko.models import Moshu, Seko, SekoAnswer
from seko.tests.factories import MoshuFactory, SekoAnswerFactory, SekoFactory


class SekoModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko: Seko = SekoFactory(del_flg=False)

    def test_seko_disable(self) -> None:
        """施行を無効化(論理削除)する"""
        self.seko.disable()
        self.assertTrue(self.seko.del_flg)


class MoshuModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.moshu: Moshu = MoshuFactory()

    def test_set_and_check_password(self) -> None:
        """喪主のパスワードが再設定、確認できるか"""
        NEW_DUMMY_PASSWORD = 'ThisIsNewDummyPassword'
        self.moshu.salt = 'RdyAC+0XH84KTqTdJ/lh9f4bxhPcqXOJeAo8CQug90E='
        self.moshu.set_password(NEW_DUMMY_PASSWORD)
        self.moshu.save()
        self.assertEqual(
            self.moshu.password,
            (
                '2qru4fe59qGqxJQ9ztFEoWQ/P3jJs3GlnV0W3cyi5Q9NPxS2LrHMFnrnI0mCHRoWzqoTAH0FOu9yQ/BEg'
                'EOayg=='
            ),
        )
        self.assertFalse(self.moshu.check_password('ThisIsIncollectPassword'))
        self.assertTrue(self.moshu.check_password(NEW_DUMMY_PASSWORD))
        self.assertFalse(self.moshu.check_password(None))

        self.moshu.salt = None
        with self.assertRaises(ValueError):
            self.moshu.check_password(NEW_DUMMY_PASSWORD)

    def test_set_unusable_password(self) -> None:
        """喪主にNoneのパスワードをセットすると無効なパスワードになる"""
        self.moshu.set_password(None)
        self.assertTrue(self.moshu.password.startswith(UNUSABLE_PASSWORD_PREFIX))

    def test_generate_raw_password_succeed(self):
        """喪主の初期パスワードがURLSafeな文字で生成される"""
        for _ in range(1000):
            new_password = Moshu.generate_raw_password()
            self.assertRegex(new_password, '[a-zA-Z0-9-_=]{17,}')

    def test_reset_password_succeed(self):
        """喪主のパスワードをリセットする"""
        old_password = self.moshu.password  # 中身はHash化されている
        old_salt = self.moshu.salt

        # リセットの場合はSaltも変える
        self.moshu.reset_password()
        self.assertNotEqual(old_password, self.moshu.password)
        self.assertNotEqual(old_salt, self.moshu.salt)
        self.assertTrue(self.moshu.check_password(self.moshu._password))

    def test_mobile_num_digits(self) -> None:
        """喪主の携帯電話番号から数字以外を除去した文字列を返す"""
        self.moshu.mobile_num = ' 0/1２34-5　6 '
        self.assertEqual(self.moshu.mobile_num_digits(), '013456')

    def test_update_last_login(self) -> None:
        """喪主の最終ログイン日時を更新する"""
        self.assertIsNone(self.moshu.last_login)
        prev_updated_at: datetime = self.moshu.updated_at

        self.moshu.update_last_login()
        self.assertIsNotNone(self.moshu.last_login)
        # 最終ログイン日時は更新されるが更新日時は変わらない
        # self.assertNotEqual(self.moshu.last_login, self.moshu.updated_at)
        self.assertEqual(prev_updated_at, self.moshu.updated_at)


class SekoAnswerModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko_answer: SekoAnswer = SekoAnswerFactory(del_flg=False)

    def test_seko_answer_disable(self) -> None:
        """施行回答を無効化(論理削除)する"""
        self.seko_answer.disable()
        self.assertTrue(self.seko_answer.del_flg)
