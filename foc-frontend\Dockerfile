FROM node:12.18.3 as build-stage

ARG BUILD_ENVIRONMENT

WORKDIR /app

COPY package*.json /app/
RUN npm install
COPY ./ /app/
RUN npm run build -- $BUILD_ENVIRONMENT --output-path=./dist/out

FROM nginx

ENV \
  DEBUG_LOG=true \
  API_URL=/api \
  CARD_TOKEN_URL=https://beta.epsilon.jp/js/token.js \
  INQUIRY_ADDRESS=<EMAIL>

COPY --from=build-stage /app/dist/out/ /usr/share/nginx/html
COPY ./support/docker/default.conf.template /etc/nginx/templates/default.conf.template
COPY ./support/docker/40-foc-envsubst.sh /docker-entrypoint.d/40-foc-envsubst.sh
RUN chmod 775 /docker-entrypoint.d/40-foc-envsubst.sh
