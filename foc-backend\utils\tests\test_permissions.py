from django.contrib.auth.models import AnonymousUser
from django.test import RequestFactory, TestCase

from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.permissions import IsMoshu, IsStaff, ReadNeedAuth


class IsStaffTest(TestCase):
    def setUp(self):
        super().setUp()

        self.permission = IsStaff()
        self.request = RequestFactory().get('/nonexistent', data={}, secure=False)

    def test_has_permission(self) -> None:
        """スタッフのみ許可する"""
        self.request.user = StaffFactory()
        self.assertTrue(self.permission.has_permission(self.request, None))

        self.request.user = MoshuFactory()
        self.assertFalse(self.permission.has_permission(self.request, None))


class IsMoshuTest(TestCase):
    def setUp(self):
        super().setUp()

        self.permission = IsMoshu()
        self.request = RequestFactory().get('/nonexistent', data={}, secure=False)

    def test_has_permission(self) -> None:
        """喪主(葬家)のみ許可する"""
        self.request.user = StaffFactory()
        self.assertFalse(self.permission.has_permission(self.request, None))

        self.request.user = MoshuFactory()
        self.assertTrue(self.permission.has_permission(self.request, None))


class ReadNeedAuthTest(TestCase):
    def setUp(self):
        super().setUp()

        self.permission = ReadNeedAuth()
        self.staff = StaffFactory()
        self.moshu = MoshuFactory()

    def test_has_permission(self) -> None:
        """スタッフでも喪主でも取得系は許可する"""

        # 取得系はゲストのみ不可
        get_request = RequestFactory().get('/nonexistent', data={}, secure=False)
        get_request.user = AnonymousUser()
        self.assertFalse(self.permission.has_permission(get_request, None))
        get_request.user = self.staff
        self.assertTrue(self.permission.has_permission(get_request, None))
        get_request.user = self.moshu
        self.assertTrue(self.permission.has_permission(get_request, None))

        # 追加/更新/削除はすべて不可(ReadNeedAuthの担当外)
        post_request = RequestFactory().post('/nonexistent', data={}, secure=False)
        post_request.user = AnonymousUser()
        self.assertFalse(self.permission.has_permission(post_request, None))
        post_request.user = StaffFactory()
        self.assertFalse(self.permission.has_permission(post_request, None))
        post_request.user = MoshuFactory()
        self.assertFalse(self.permission.has_permission(post_request, None))

        put_request = RequestFactory().put('/nonexistent', data={}, secure=False)
        put_request.user = AnonymousUser()
        self.assertFalse(self.permission.has_permission(put_request, None))
        put_request.user = StaffFactory()
        self.assertFalse(self.permission.has_permission(put_request, None))
        put_request.user = MoshuFactory()
        self.assertFalse(self.permission.has_permission(put_request, None))

        patch_request = RequestFactory().patch('/nonexistent', data={}, secure=False)
        patch_request.user = AnonymousUser()
        self.assertFalse(self.permission.has_permission(patch_request, None))
        patch_request.user = StaffFactory()
        self.assertFalse(self.permission.has_permission(patch_request, None))
        patch_request.user = MoshuFactory()
        self.assertFalse(self.permission.has_permission(patch_request, None))

        delete_request = RequestFactory().delete('/nonexistent', data={}, secure=False)
        delete_request.user = AnonymousUser()
        self.assertFalse(self.permission.has_permission(delete_request, None))
        delete_request.user = StaffFactory()
        self.assertFalse(self.permission.has_permission(delete_request, None))
        delete_request.user = MoshuFactory()
        self.assertFalse(self.permission.has_permission(delete_request, None))
