@import "src/assets/scss/customer/setting";
.container .inner .contents {
  >div:not(.message-area){
    max-width: 500px;
  }
  .card-images {
    max-width: 480px;
  }
  .notice {
    font-size: 0.8rem;
    line-height: 1.5;
    .important {
      color: $text-red;
    }
    font-weight: bold;
    margin-top: -20px;
  }
  .input-area .line .input.limit {
    display: flex;
    justify-content: flex-start;
    >div {
      font-size: 1rem;
      padding: 0 5px;
    }
  }
  .input-area .line .input.security_code {
    display: flex;
    justify-content: flex-start;
    >a {
      cursor: pointer;
      width: auto;
      height: auto;
      display: inline;
      text-decoration: underline;
      color: $text-red;
      font-size: 0.8rem;
      padding-left: 20px;
    }
  }
  @media screen and (min-width: 560px) {
    &:not(.header) {
      padding-bottom: 80px;
    }
  }
  .input-area .line.security_code {
    position: relative;
    .about-security {
      position: absolute;
      padding: 15px;
      border: 1px solid $border-grey;
      border-radius: 5px;
      background-color: #ffffff;
      font-size: 0.75rem;
      line-height: 1.6;
      width: 233px;
      height: 127px;
      right: -20px;
      top: 30px;
      @media screen and (max-width: 560px) {
        height: 115px;
        font-size: 0.75rem;
        line-height: 1.5;
        right: 0px;
        top: 60px;
      }
      .close {
        cursor: pointer;
        font-size: 12px;
        position: absolute;
        right: 0;
        top: 0;
        width: 15px;
        height: 20px;
        &:hover {
          color: $text-red;
        }
      }
    }
  }
}
.button-area {
    margin-top: 30px;
}
