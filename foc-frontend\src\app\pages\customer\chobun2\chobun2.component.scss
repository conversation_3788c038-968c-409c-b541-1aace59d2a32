@import "src/assets/scss/customer/setting";
.container .inner .contents {
  .input-area {
    max-width: 600px;
    .line .label {
      min-width: 168px;
    }
    .line .button-area {
      width: 100%;
    }
    .line .button {
      margin-left: 10px;
      @media screen and (max-width: 560px) {
        margin-top: 10px;
      }
    }
    .line .letter {
      float: right;
      line-height: 1.5;
      font-size: 0.8rem;
      @media screen and (max-width: 560px) {
        margin-right: 5%;
      }
    }
    .line .desc {
      float: left;
      line-height: 1.5;
      font-size: 0.8rem;
      @media screen and (max-width: 560px) {
        margin-left: 5%;
      }
    }
    .daishi {
      display: flex;
      flex-wrap: wrap;
      width: 70%;
      .item-box {
        margin: 0 10px;
        font-size: 0.9rem;
        padding-top: 10px;
        .image-box {
          position: relative;
          width: 100px;
          height: 141px;
          background: #fff;
          border-radius: 5px;
          margin: 5px;
          cursor: pointer;
          opacity: .8;
          box-shadow: 0 5px 20px rgba(0,0,0,.3);
          --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
          &:hover {
            -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
            opacity: 1;
          }
          &.selected {
            box-shadow: 0 5px 20px rgba(0,0,0,.3);
            --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
            -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
            opacity: 1;
          }
          img {
            width: 98px;
            height: 138px;
            border-radius: 5px;
          }
        }
        .name {
          text-align: center;
        }
      }
      &::before {
        position: absolute;
        content: attr(err-msg);
        color: $text-red;
        bottom: 53px;
        font-size: 0.7rem;
      }
      &.error {
        border: solid 1px $border-red;
      }
      @media screen and (max-width: 560px) {
        max-width: 300px;
        width: 90%;
        margin-left: 15px;
        .item-box {
          margin: 0 5px;
        }
      }
      @media screen and (max-width: 360px) {
        .item-box {
          .image-box {
            width: 90px;
            height: 128px;
            img {
              width: 88px;
              height: 126px;
            }
          }
        }
      }
    }
  }
}
.ui.modal.daishi {
  position: fixed !important;
  width: 400px !important;
  min-width: 200px;
  height: 564px;
  top: calc((100vh - 564px) / 2 - 10px) !important;
  >.content {
    padding: 0 !important;
    font-size: 1.4rem;
    >img{
      width: 400px;
      height: 564px;
    }
    >.contents {
      position: absolute;
      writing-mode: vertical-rl;
      text-orientation: upright;
      width: auto;
      background: rgba(0,0,0,0%);
      font-weight: normal;
      font-size: 0.6rem;
      margin: 0;
      padding: 0;
      text-align: start;
      transform: scale(0.75, 1);
      letter-spacing: 3px;
    }
    >.atena{
      letter-spacing: 6px;
      top: 11%;
      right: 11%;
      font-size: 1.2rem;
    }
    >.honbun{
      top: 16%;
      right: 13%;
      font-size: 1rem;
      white-space: pre;
      line-height: 1.35;
      width: 250px;
      letter-spacing: 4px;
    }
    >.date {
      top: 20%;
      left: 31%;
    }
    >.address1 {
      top: 25%;
      left: 27.5%;
    }
    >.address2 {
      top: 30%;
      left: 24%;
    }
    >.okurinushi_company{
      top: 20%;
      left: 20.5%;
    }
    >.okurinushi_title{
      top: 40%;
      left: 17%;
    }
    >.okurinushi_name{
      top: 60%;
      left: 13.5%;
    }
    >.renmei1{
      top: 60%;
      left: 10%;
    }
    >.renmei2{
      top: 60%;
      left: 6.5%;
    }
  }
  @media screen and (max-width: 560px) {
    width: 300px !important;
    height: 423px;
    top: calc((100vh - 423px) / 2 - 10px) !important;
    >.content {
      >img{
        width: 300px;
        height: 423px;
      }
      >.contents {
        letter-spacing: 0px;
        transform: scale(0.7, 1);
      }
      >.atena{
        letter-spacing: 6px;
      }
      >.honbun{
        letter-spacing: 4px;
        right: 13%;
        width: 190px;
      }
    }
  }
  @media screen and (max-width: 380px) {
    >.content {
      >.contents {
      }
      >.atena{
        font-size: 1.3rem;
      }
      >.honbun{
        font-size: 1.15rem;
      }
    }
  }
}

.ui.modal.item {
  position: fixed !important;
  width: 90% !important;
  max-width: 810px;
  max-height: 60vh;
  min-height: 200px;
  top: calc((100vh - 564px) / 2 - 10px) !important;
  >.content {
    padding: 15px !important;
    font-size: 1.4rem;
    height: 100%;
    width: 100%;
    max-height: 60vh;
    display: flex;
    flex-wrap: wrap;
    .item-box {
      padding: 10px;
      width: 380px;
      height: 170px;
      margin: 2px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      border-bottom: solid 2px $border-grey;;
      &:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,.3);
        --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
        -webkit-transform: translateY(-1px);
        transform: translateY(-1px);
      }
      .image-box {
        width: 150px;
        height: 150px;
        background-color: #fff;
        display: flex;
        justify-content: center;
        &.no-image {
          background-color: #e9e9e9;
        }
        img {
          height: auto;
          width: auto;
          max-height: 100%;
          max-width: 100%;
          margin: auto;
          min-width: 1px;
        }
        .no-image {
          margin-top: 10px;
          opacity: .5;
          font-size: 1.2rem;
        }
      }
      .title-box {
        line-height: 1.8;
        .name {
          margin-top: 5px;
          font-size: 1.1rem;
          font-weight: bold;
        }
        .price {
          font-size: 0.9rem;
        }
      }
    }
  }
  @media screen and (max-width: 560px) {
    max-width: 405px;
    top: 50px !important;
    max-height: 70vh;
    >.content {
      max-height: 70vh;
      .item-box {
        height: 150px;
        .image-box {
          width: 130px;
          height: 130px;
        }
      }
    }
  }
  @media screen and (max-width: 380px) {
    >.content {
      padding: 10px !important;
      .item-box {
        height: 130px;
        .image-box {
          width: 110px;
          height: 110px;
        }
      }
    }
  }
}

.container .inner {
  >.button-area .button.grey {
    width: 200px;
    @media screen and (max-width: 560px) {
      width: 180px;
    }
    @media screen and (max-width: 380px) {
      width: 150px;
    }
  }
}
