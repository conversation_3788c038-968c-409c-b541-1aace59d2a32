from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.models import AfWish
from after_follow.tests.factories import AfActivityDetailFactory, AfWishFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class AfActivityDetailCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.af_activity_detail_data = AfActivityDetailFactory.build(af_wish=AfWishFactory())

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'af_wish': self.af_activity_detail_data.af_wish.pk,
            'activity_ts': self.af_activity_detail_data.activity_ts,
            'activity': self.af_activity_detail_data.activity,
        }

    def test_af_activity_detail_create_succeed(self) -> None:
        """AF活動内容を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('after_follow:activity_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertEqual(record['af_wish'], self.af_activity_detail_data.af_wish.pk)
        self.assertEqual(
            record['activity_ts'],
            self.af_activity_detail_data.activity_ts.astimezone(tz).isoformat(),
        )
        self.assertEqual(record['activity'], self.af_activity_detail_data.activity)

    def test_af_activity_detail_create_fail_by_notfound_af_wish(self) -> None:
        """AF活動内容追加APIが存在しないAF希望項目を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_af_wish: AfWish = AfWishFactory.build()

        params: Dict = self.basic_params()
        params['af_wish'] = non_saved_af_wish.pk
        response = self.api_client.post(
            reverse('after_follow:activity_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['af_wish'])

    def test_af_activity_detail_create_failed_without_auth(self) -> None:
        """AF活動内容追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('after_follow:activity_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_af_activity_detail_create_deny_call_from_moshu(self) -> None:
        """AF活動内容追加APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('after_follow:activity_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
