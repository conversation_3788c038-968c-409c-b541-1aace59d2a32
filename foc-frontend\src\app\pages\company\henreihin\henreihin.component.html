
<div class="container">
  <div class="inner with_footer">
    <div class="contents">
      <div class="menu_title">
        <i class="gift icon big"></i>
        返礼品受付一覧
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(sekoBaseComboEm, supplierComboEm, hallComboEm, sekoDateEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchHenreihin()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="form_data.company_id" (selectedItemChange)="companyChange($event, sekoBaseComboEm, hallComboEm)"></com-dropdown>
          <label>施行拠点</label>
          <com-dropdown #sekoBaseComboEm [settings]="sekoBaseCombo" [(selectedValue)]="form_data.seko_department"></com-dropdown>
          <label>式場</label>
          <com-dropdown #hallComboEm [settings]="hallCombo" [(selectedValue)]="form_data.hall_id"></com-dropdown>
          <label class="large">発注状況</label>
          <div class="ui checkbox" (click)="checkClick('none', form_data.unorder, form_data.ordered, form_data.confirmed)">
            <input type="checkbox" name="none" [(ngModel)]="form_data.none" [checked]="form_data.none">
            <label>未作成</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('unorder', form_data.none, form_data.ordered, form_data.confirmed)">
            <input type="checkbox" name="unorder" [(ngModel)]="form_data.unorder" [checked]="form_data.unorder">
            <label>未発注</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('ordered', form_data.none, form_data.unorder, form_data.confirmed)">
            <input type="checkbox" name="ordered" [(ngModel)]="form_data.ordered" [checked]="form_data.ordered">
            <label>発注済</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('confirmed', form_data.none, form_data.unorder, form_data.ordered)">
            <input type="checkbox" name="confirmed" [(ngModel)]="form_data.confirmed" [checked]="form_data.confirmed">
            <label>確認済</label>
          </div>
        </div>
        <div class="line">
          <label>施行番号</label>
          <div class="ui input">
            <input type="tel" [(ngModel)]="form_data.seko_id">
          </div>
          <label>葬家名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.soke_name">
          </div>
          <label>施行日</label>
          <com-calendar #sekoDateEm [settings]="calendarOptionDate" id="seko_date" [(value)]="form_data.seko_date"></com-calendar>
          <label class="large">キャンセル状況</label>
          <div class="ui checkbox" (click)="checkClick('not_canceled', form_data.canceled)">
            <input type="checkbox" name="not_canceled" [(ngModel)]="form_data.not_canceled">
            <label>未</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('canceled', form_data.not_canceled)">
            <input type="checkbox" name="canceled" [(ngModel)]="form_data.canceled">
            <label>済</label>
          </div>
        </div>
        <div class="line">
          <label>発注先</label>
          <com-dropdown #supplierComboEm [settings]="supplierCombo" [(selectedValue)]="form_data.supplier_id"></com-dropdown>
          <label>故人名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.kojin_name">
          </div>
          <label>喪主名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.moshu_name">
          </div>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="henreihin_list?.length">
          全{{henreihin_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?henreihin_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="check">
                <div class="ui checkbox all_check" [class.disabled]="!henreihin_list?.length">
                  <input type="checkbox" [(ngModel)]="all_check" (change)="checkAllItem()">
                  <label></label>
                </div>
              </th>
              <th class="seko_id">施行番号</th>
              <th class="seko_date">施行日</th>
              <th class="department_name">施行拠点</th>
              <th class="hall_name">式場</th>
              <th class="soke_name">葬家名</th>
              <th class="kojin_name">故人名</th>
              <th class="moshu_name">喪主名</th>
              <th class="henreihin_name">商品名</th>
              <th class="henreihin_price">価格</th>
              <th class="supplier">発注先</th>
              <th class="order_status">発注状況</th>
              <th class="is_cancel">キャンセル</th>
              <th class="operation" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let henreihin of henreihin_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" (click)="checkItem($event, henreihin)">
              <td class="center aligned check">
                <div class="ui checkbox">
                  <input type="checkbox" [(ngModel)]="henreihin.selected">
                  <label></label>
                </div>
              </td>
              <td class="center aligned seko_id" title="{{henreihin.seko_id}}">{{henreihin.seko_id}}</td>
              <td class="center aligned seko_date" title="{{henreihin.seko_date | date: 'yyyy/MM/dd'}}">
                {{henreihin.seko_date | date: 'yyyy/MM/dd'}}</td>
              <td class="department_name" title="{{henreihin.seko_department_name}}">{{henreihin.seko_department_name}}</td>
              <td class="hall_name" title="{{henreihin.hall_name}}">{{henreihin.hall_name}}</td>
              <td class="soke_name" title="{{henreihin.soke_name}}">{{henreihin.soke_name}}</td>
              <td class="kojin_name" title="{{henreihin.kojin_name}}">{{henreihin.kojin_name}}</td>
              <td class="moshu_name" title="{{henreihin.moshu_name}}">{{henreihin.moshu_name}}</td>
              <td class="henreihin_name" title="{{henreihin.henreihin_name}}">{{henreihin.henreihin_name}}</td>
              <td class="right aligned henreihin_price" title="{{henreihin.henreihin_price | number}}">
                {{henreihin.henreihin_price | number}}</td>
              <td class="supplier" title="{{henreihin.supplier_name}}">
                {{henreihin.supplier_name}}
              </td>
              <td class="center aligned order_status" title="{{Utils.getOrderStatusName(henreihin.order_status)}}">
                {{Utils.getOrderStatusName(henreihin.order_status)}}
              </td>
              <td class="center aligned is_cancel" title="{{henreihin.cancel_ts?'キャンセル済':''}}">
                {{henreihin.cancel_ts?'キャンセル済':''}}
              </td>

              <td class="center aligned button operation" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN">
                <i class="large hands helping icon" title="発注先" *ngIf="canChangeSupplier(henreihin)" (click)="showSupplier(henreihin)"></i>
              </td>
            </tr>
            </ng-container>
            <tr *ngIf="!henreihin_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="13">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal supplier" id="supplier">
        <div class="header ui"><i class="hands helping icon large"></i>発注先</div>
        <div class="content">
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!supplier_list?.length">
                  <th class="select">選択</th>
                  <th class="supplier_name">発注先名</th>
                  <th class="supplier_address">住所</th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngFor="let supplier of supplier_list">
                  <tr (click)="selectSupplier(supplier)">
                    <td class="center aligned select">
                      <div class="ui radio checkbox">
                        <input type="radio" name="supplier_default" [checked]="supplier.selected">
                        <label></label>
                      </div>
                    </td>
                    <td class="supplier_name" title="{{supplier.name}}">{{supplier.name}}</td>
                    <td class="supplier_address" title="{{supplier.address}}">{{supplier.address}}</td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveHenreihinSupplier()" [disabled]="!supplierCanSave()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeDialog()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

		</div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group">
    <button class="ui labeled icon button mini light" (click)="exportCsv()">
      <i class="download icon"></i>CSV出力
    </button>
    <button class="ui labeled icon button mini light" [class.disabled]="buttonDisabled(Const.ORDER_STATUS_ID_NONE)" (click)="saveOrder()">
      <i class="edit icon"></i>発注書作成
    </button>
    <button class="ui labeled icon button mini light" routerLink="/foc/henreihin/order">
      <i class="list icon"></i>発注一覧
    </button>
  </div>
</div>
