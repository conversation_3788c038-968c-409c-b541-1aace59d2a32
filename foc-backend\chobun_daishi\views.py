from django_filters import rest_framework as filters
from rest_framework import generics
from rest_framework.permissions import IsAuthenticatedOrReadOnly

from chobun_daishi.models import ChobunDaishi
from chobun_daishi.serializers import ChobunDaishiSerializer


class ChobunDaishiList(generics.ListCreateAPIView):

    queryset = ChobunDaishi.objects.select_related('daishi').all()
    serializer_class = ChobunDaishiSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['company']
    permission_classes = [IsAuthenticatedOrReadOnly]
