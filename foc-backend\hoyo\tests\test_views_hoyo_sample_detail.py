from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from hoyo.models import Base, HoyoSample
from hoyo.tests.factories import HoyoSampleFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class HoyoSampleDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.hoyo_sample = HoyoSampleFactory(company=base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_sample_detail_succeed(self) -> None:
        """法要案内文サンプル詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': self.hoyo_sample.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.hoyo_sample.pk)
        self.assertEqual(record['company'], self.hoyo_sample.company.pk)
        self.assertEqual(record['sentence'], self.hoyo_sample.sentence)
        self.assertEqual(record['display_num'], self.hoyo_sample.display_num)
        self.assertEqual(record['del_flg'], self.hoyo_sample.del_flg)
        self.assertEqual(
            record['created_at'], self.hoyo_sample.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.hoyo_sample.create_user_id)
        self.assertEqual(
            record['updated_at'], self.hoyo_sample.updated_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['update_user_id'], self.hoyo_sample.update_user_id)

    def test_hoyo_sample_detail_failed_by_notfound(self) -> None:
        """法要案内文サンプル詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_hoyo_sample: HoyoSample = HoyoSampleFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': non_saved_hoyo_sample.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_sample_detail_ignores_deleted(self) -> None:
        """法要案内文サンプル詳細APIは無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_sample.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': self.hoyo_sample.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_sample_detail_failed_without_auth(self) -> None:
        """法要案内文サンプル詳細APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': self.hoyo_sample.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class HoyoSampleUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()
        self.hoyo_sample = HoyoSampleFactory(company=self.base)
        self.new_hoyo_data = HoyoSampleFactory.build(company=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.new_hoyo_data.company.pk,
            'sentence': self.new_hoyo_data.sentence,
            'display_num': self.new_hoyo_data.display_num,
        }

    def test_hoyo_sample_update_succeed(self) -> None:
        """法要案内文サンプルを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': self.hoyo_sample.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.hoyo_sample.pk)
        self.assertEqual(record['company'], self.new_hoyo_data.company.pk)
        self.assertEqual(record['sentence'], self.new_hoyo_data.sentence)
        self.assertEqual(record['display_num'], self.new_hoyo_data.display_num)
        self.assertEqual(record['del_flg'], self.new_hoyo_data.del_flg)
        self.assertEqual(
            record['created_at'], self.hoyo_sample.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.hoyo_sample.create_user_id)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_hoyo_sample_update_failed_by_notfound(self) -> None:
        """法要案内文サンプル更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_hoyo_sample: HoyoSample = HoyoSampleFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': non_saved_hoyo_sample.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_sample_update_ignores_deleted(self) -> None:
        """法要案内文サンプル更新APIは無効化された法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_sample.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': self.hoyo_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_sample_update_failed_without_auth(self) -> None:
        """法要案内文サンプル更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': self.hoyo_sample.company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class HoyoSampleDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.hoyo_sample: HoyoSample = HoyoSampleFactory(company=self.company)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.company)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_sample_delete_succeed(self) -> None:
        """法要案内文サンプルを論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': self.hoyo_sample.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 法要案内文サンプルのdel_flgがTrueになるだけ
        db_hoyo: HoyoSample = HoyoSample.objects.get(pk=self.hoyo_sample.pk)
        self.assertTrue(db_hoyo.del_flg)
        self.assertEqual(db_hoyo.create_user_id, self.hoyo_sample.create_user_id)
        self.assertEqual(db_hoyo.update_user_id, self.staff.pk)

    def test_hoyo_sample_delete_failed_by_notfound(self) -> None:
        """法要案内文サンプル削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_hoyo: HoyoSample = HoyoSampleFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': non_saved_hoyo.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_sample_delete_failed_by_already_deleted(self) -> None:
        """法要案内文サンプル削除APIが論理削除済みの法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_sample.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': self.hoyo_sample.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_sample_delete_failed_without_auth(self) -> None:
        """法要案内文サンプル削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_sample_detail', kwargs={'pk': self.hoyo_sample.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
