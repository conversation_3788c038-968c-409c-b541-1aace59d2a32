from typing import Dict

from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base, Tokusho
from bases.tests.factories import BaseFactory, TokushoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class TokushoCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()
        self.tokusho_data: Tokusho = TokushoFactory.build(company=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_tokusho_create_succeed(self) -> None:
        """特商法を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.tokusho_data)
        response = self.api_client.post(
            reverse('bases:tokusho', kwargs={'base_id': self.base.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()

        for attr, value in record.items():
            self.assertEqual(value, params[attr])

        new_tokusho = Tokusho.objects.get(pk=self.base.id)
        self.assertEqual(new_tokusho.company, self.base)

    def test_tokusho_create_failed_by_notfound(self) -> None:
        """特商法追加APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.post(
            reverse('bases:tokusho', kwargs={'base_id': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_tokusho_create_failed_by_multiple_tokusho(self) -> None:
        """特商法追加APIが既に特商法レコードを持っている拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        TokushoFactory(company=self.base)
        params: Dict = model_to_dict(self.tokusho_data)
        response = self.api_client.post(
            reverse('bases:tokusho', kwargs={'base_id': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_tokusho_create_failed_without_auth(self) -> None:
        """特商法追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = model_to_dict(self.tokusho_data)
        response = self.api_client.post(
            reverse('bases:tokusho', kwargs={'base_id': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class TokushoDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.tokusho = TokushoFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_tokusho_detail_succeed(self) -> None:
        """特商法詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:tokusho', kwargs={'base_id': self.tokusho.company.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        saved_tokusho = model_to_dict(self.tokusho)
        for attr, value in record.items():
            self.assertEqual(value, saved_tokusho[attr])

    def test_tokusho_detail_succeed_without_auth(self) -> None:
        """特商法詳細取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:tokusho', kwargs={'base_id': self.tokusho.company.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        saved_tokusho = model_to_dict(self.tokusho)
        for attr, value in record.items():
            self.assertEqual(value, saved_tokusho[attr])

    def test_tokusho_detail_failed_by_notfound_base(self) -> None:
        """特商法詳細APIが存在しない拠点IDを指定して失敗する"""
        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:tokusho', kwargs={'base_id': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_tokusho_detail_failed_by_base_missing_tokusho(self) -> None:
        """特商法詳細APIが特商法レコードのない拠点IDを指定して失敗する"""
        non_child_base: Base = BaseFactory()
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:tokusho', kwargs={'base_id': non_child_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class TokushoUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.tokusho = TokushoFactory()
        self.new_tokusho_data = TokushoFactory.build(company=self.tokusho.company)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_tokusho_update_succeed(self) -> None:
        """施行アルバムを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.new_tokusho_data)
        response = self.api_client.put(
            reverse('bases:tokusho', kwargs={'base_id': self.tokusho.company.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        del params['company']
        for attr, value in params.items():
            self.assertEqual(value, record[attr])

    def test_tokusho_update_failed_by_notfound_base(self) -> None:
        """特商法更新APIが存在しない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:tokusho', kwargs={'base_id': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_tokusho_update_failed_by_illegal_base(self) -> None:
        """特商法更新APIが特商法レコードのない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_child_base: Base = BaseFactory()
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:tokusho', kwargs={'base_id': non_child_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_tokusho_update_failed_without_auth(self) -> None:
        """特商法更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:tokusho', kwargs={'base_id': self.tokusho.company.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
