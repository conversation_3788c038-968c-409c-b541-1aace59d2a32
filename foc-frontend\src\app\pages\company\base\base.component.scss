@import "src/assets/scss/company/setting";
.container {
  .inner {
    .contents {
      .ui.tab {
        height: calc(100% - 80px);
        .search_area {
          margin-top: 0;
        }
        .table_fixed.body {
          max-height: calc(100% - 160px);
        }
        tr {
          .base_name {
            width: 11%;
          }
          .zip_code {
            width: 6%;
          }
          .prefecture {
            width: 6%;
          }
          .address_1 {
            width: 10%;
          }
          .address_2 {
            width: 10%;
          }
          .company_code {
            width: 7%;
          }
          .tel {
            width: 7%;
          }
          .fax {
            width: 7%;
          }
          .fdn_code {
            width: 7%;
          }
          .logo {
            width: 8%;
            padding: 0;
            text-align: center;
          }
          .calc_type {
            width: 6%;
          }
          .operation {
            width: 150px;
          }
        }
        &.depart tr, &.hall tr {
          .display_num {
            width: 7%;

          }
          .base_name {
            width: 16%;
          }
          .address_1 {
            width: 13%;
          }
          .address_2 {
            width: 13%;
          }
          .operation {
            width: 60px;
          }
          .map {
            width: 8%;
            padding: 0;
            text-align: center;
          }
        }
        .logo, .map {
          >.image {
            display: flex;
            width: 100%;
            height: 42px;
            >img {
              margin: auto;
              width: auto;
              height: auto;
              max-width: 100%;
              max-height: 42px;
            }
          }
        }
      }

      .ui.modal.service .content {
        display: flex;
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        padding: 20px 20px;
        justify-content: center;
        .item {
          width: 120px;
          height: 120px;
          padding: 10px 0;
          margin: 10px 20px;
          background-color: $label-background-light;
          cursor: pointer;
          text-align: center;
          opacity: .7;
          .subtitle {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            margin-top: 5px;
            font-size: 1.077em;
            font-weight: 700;
          }
          &:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,.3);
            --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
            -webkit-transform: translateY(-1px);
            transform: translateY(-1px);
          }
          &.selected {
            opacity: 1;
            box-shadow: 0 5px 20px rgba(0,0,0,.3);
            --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
            -webkit-transform: translateY(-1px);
            transform: translateY(-1px);
          }
          .ui.checkbox {
            margin-top: 10px;
          }
          // &:focus {
          //   outline: none !important;
          //   border: 0;
          // }
        }
      }
      .ui.modal.law .input_area .line >label {
        min-width: 180px;
      }
      .ui.modal .input_area  {
        .hide {
          display: none;
        }
        .ui.input.monthly {
          border-left: 3px solid $label-border-required;
        }
        label.plane {
          width: 33px;
        }
        label.divided {
          margin-left: 30px;
        }
        .ui.input input[type="number"] {
          text-align: right;
        }
        .ui.toggle.checkbox {
          margin-top: 5px;
        }
      }
      .ui.card .image img {
        max-height: 200px;
        max-width: 100%;
        width: auto;
        margin: auto;
      }
    }
  }
}
