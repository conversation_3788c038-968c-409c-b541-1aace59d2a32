# Generated by Django 3.1.5 on 2021-01-26 08:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('seko', '0012_moshu_last_login'),
        ('after_follow', '0004_auto_20210119_1357'),
    ]

    operations = [
        migrations.CreateModel(
            name='SekoAf',
            fields=[
                (
                    'seko',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='seko_af',
                        serialize=False,
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
                ('note', models.TextField(blank=True, null=True, verbose_name='note')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'contact_way',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to='after_follow.afcontactway',
                        verbose_name='contact way',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_af',
            },
        ),
        migrations.CreateModel(
            name='AfWish',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('answered_flg', models.BooleanField(verbose_name='answered')),
                (
                    'proposal_status',
                    models.IntegerField(
                        blank=True,
                        choices=[(0, 'not proposed'), (1, 'proposed')],
                        null=True,
                        verbose_name='proposal status',
                    ),
                ),
                (
                    'order_status',
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (1, 'in negotiating'),
                            (2, 'received order'),
                            (3, 'failured to receiv order'),
                        ],
                        null=True,
                        verbose_name='order status',
                    ),
                ),
                ('order_date', models.DateField(blank=True, null=True, verbose_name='ordered on')),
                (
                    'order_chance',
                    models.IntegerField(
                        blank=True,
                        choices=[(0, 'no prospect'), (1, 'prospective')],
                        null=True,
                        verbose_name='order chance',
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'af_type',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='after_follow.afterfollow',
                        verbose_name='after follow type',
                    ),
                ),
                (
                    'seko_af',
                    models.ForeignKey(
                        db_column='af_id',
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='wishes',
                        to='after_follow.sekoaf',
                        verbose_name='after follow',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_af_wish',
            },
        ),
        migrations.CreateModel(
            name='AfActivityDetail',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('activity_ts', models.DateTimeField(auto_now_add=True, verbose_name='act at')),
                ('activity', models.TextField(verbose_name='activity')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'af_wish',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='activities',
                        to='after_follow.afwish',
                        verbose_name='wish',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_af_activity_detail',
            },
        ),
    ]
