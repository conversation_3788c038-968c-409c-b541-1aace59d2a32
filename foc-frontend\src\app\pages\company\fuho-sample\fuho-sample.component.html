
<div class="container">
  <div class="inner with_footer">
    <div class="contents mini">
      <div class="menu_title">
        <i class="money check icon big"></i>
        訃報文
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini" (click)="showFuhoSample()">
          <i class="list icon"></i>サンプル表示
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #CompanyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" (selectedItemChange)="companyChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="item_list?.length">
          全{{item_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?item_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination, pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(pagination, page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination, pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!item_list?.length">
              <th class="display_num">表示順</th>
              <th class="sentence">訃報文</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let item of item_list; index as i">
              <tr (click)="showData($event, item)" *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
                <td class="display_num">{{item.display_num}}</td>
                <td class="sentence" title="{{item.tel}}">{{item.sentence}}</td>
                <td class="center aligned button operation">
                  <i class="large trash alternate icon" title="削除" (click)="deleteData(item.id)"></i>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <tr title="新規追加" (click)="showData($event)">
              <td colspan="6" class="center aligned"><i class="large add icon"></i></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal small" id="item-edit">
        <div class="header ui"><i class="money check icon large"></i>
          訃報文{{edit_type===1?'登録':'編集'}}
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <label class="required">表示順</label>
              <div class="ui input tiny">
                <input type="number" min="1" [class.error]="errField.get('display_num')" autocomplete="off" id="display_num" [(ngModel)]="item_edit.display_num">
              </div>
            </div>
            <div class="line row5">
              <label class="required">訃報文</label>
              <div class="ui icon input textarea">
                <textarea [class.error]="errField.get('sentence')" id="sentence" rows="4" cols="35" [(ngModel)]="item_edit.sentence"></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal small fuho-sample" id="fuho-sample">
        <div class="header ui"><i class="list icon large"></i>訃報文サンプル</div>
        <div class="scrolling content" *ngIf="fuho_sample_list?.length">
          <div class="ui segment" *ngFor="let fuho_sample of fuho_sample_list" [class.selected]="fuho_sample.selected" (click)="selectChangeFuhoSample(fuho_sample)">
            <div class="ui checkbox">
              <input type="checkbox" [(ngModel)]="fuho_sample.selected">
              <label></label>
            </div>
            <pre>{{fuho_sample.sentence}}</pre>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="addFuhoSample()" [disabled]="!fuhoSampleSelected()">
            <i class="add icon"></i>追加
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>

