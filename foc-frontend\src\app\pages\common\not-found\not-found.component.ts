import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

declare var $;
@Component({
  selector: 'app-not-found',
  templateUrl: './not-found.component.html',
  styleUrls: ['./not-found.component.scss']
})
export class NotFoundComponent implements OnInit {

  constructor(
    private router: Router
  ) { }

  ngOnInit(): void {
    if (window.location.pathname.includes('/foc/')) {
      this.router.navigate(['/foc/top']);
      return;
    }
    $('#focFavicon').attr('href', 'favicon_cus.ico');
  }

}
