# FOCプロジェクト 全機能一覧

## 概要

FOC（Funeral Online system Connect）プロジェクトの全機能を体系的に整理したドキュメントです。

## 1. 企業向け機能（CompanyFrameComponent）

### 1.1 認証・ユーザー管理
- **ログイン機能** (`/foc/login`)
  - スタッフ認証（JWT）
  - 自動ログイン機能
  - パスワード変更
- **ユーザー情報管理** (`/foc/me`)
  - プロフィール表示・編集
  - 権限情報表示

### 1.2 施行管理
- **施行一覧・検索** (`/foc/seko`)
  - 施行データ一覧表示
  - 検索・フィルタリング機能
  - ページネーション
- **施行詳細・編集** (`/foc/seko/edit/:id`, `/foc/seko/edit`)
  - 施行情報の詳細表示
  - 施行情報の新規作成・編集
  - 関連情報（個人情報、アルバム等）管理
- **施行関連機能**
  - 訃報情報管理
  - 個人情報管理
  - アルバム管理
  - 共有画像管理
  - お問い合わせ管理

### 1.3 拠点管理（管理者権限）
- **拠点管理** (`/foc/base`)
  - 葬儀社・部門・式場の階層管理
  - 拠点情報の表示・編集
  - FOC手数料設定
  - 特商法設定
  - SMS設定

### 1.4 仕入先管理（管理者権限）
- **仕入先管理** (`/foc/supplier`)
  - 仕入先一覧・検索
  - 仕入先情報の新規作成・編集・削除

### 1.5 商品管理（管理者権限）
- **商品管理** (`/foc/item`)
  - 商品一覧・検索
  - 商品情報の新規作成・編集・削除
  - カテゴリ管理

### 1.6 注文管理
- **注文管理** (`/foc/order`)
  - 注文一覧・検索
  - 注文詳細表示
  - 注文ステータス管理

### 1.7 返礼品管理
- **返礼品管理** (`/foc/henrei`)
  - 返礼品一覧・検索
  - 返礼品注文管理
  - 配送管理

### 1.8 弔文管理
- **弔文管理** (`/foc/chobun`)
  - 弔文一覧・検索
  - 弔文作成・編集
  - 印刷管理

### 1.9 供物管理
- **供物管理** (`/foc/kumotsu`)
  - 供物一覧・検索
  - 供物注文管理
  - 配送管理

### 1.10 香典管理
- **香典管理** (`/foc/koden`)
  - 香典一覧・検索
  - 香典集計機能
- **香典サマリー** (`/foc/koden/summary`) - 管理者権限
  - 香典集計レポート
  - 統計情報表示

### 1.11 請求書管理（管理者権限）
- **請求書管理** (`/foc/invoice`)
  - 請求書一覧・検索
  - 請求書作成・編集
  - 支払い管理

### 1.12 FAQ管理（管理者権限）
- **FAQ管理** (`/foc/faq`)
  - FAQ一覧・検索
  - FAQ作成・編集・削除

### 1.13 広告管理（管理者権限）
- **広告管理** (`/foc/ad`)
  - 広告一覧・検索
  - 広告作成・編集・削除

### 1.14 葬家サイトメニュー管理（管理者権限）
- **葬家サイトメニュー** (`/foc/soke-menu`)
  - メニュー項目管理
  - 表示順設定

### 1.15 アフターフォロー
- **アフターフォロー** (`/foc/af-contact`)
  - アフターフォロー一覧
  - 連絡履歴管理

### 1.16 法要管理
- **法要管理** (`/foc/hoyo`)
  - 法要一覧・検索
  - 法要情報管理

### 1.17 お問い合わせ管理
- **お問い合わせ管理** (`/foc/inquiry`)
  - お問い合わせ一覧
  - 回答管理

## 2. 顧客向け機能（CustomerFrameComponent）

### 2.1 訃報機能
- **訃報表示** (`/fuho`, `/fuho/:id`)
  - 訃報情報の表示
  - 詳細情報表示
- **訃報承認** (`/fuho-approve/:id`)
  - 訃報内容の確認・承認

### 2.2 法要機能
- **法要案内** (`/hoyo`, `/hoyo/:id`)
  - 法要案内の表示
  - 法要詳細情報

### 2.3 注文機能
- **弔文注文** (`/chobun/entry1`, `/chobun/entry2`)
  - 弔文注文フォーム（ステップ1・2）
  - 注文内容確認・送信
- **供物注文** (`/kumotsu/entry`)
  - 供物注文フォーム
  - 商品選択・注文
- **香典注文** (`/koden/entry1`, `/koden/entry2`)
  - 香典注文フォーム（ステップ1・2）
  - 香典情報入力・確認

### 2.4 返礼品機能
- **返礼品注文** (`/henrei/entry1`, `/henrei/entry2`, `/henrei/entry3`)
  - 返礼品注文フォーム（3ステップ）
  - 商品選択・配送先設定・確認

### 2.5 お問い合わせ機能
- **一般お問い合わせ** (`/inquiry`)
  - お問い合わせフォーム
- **葬儀お問い合わせ** (`/inquiry/funeral`)
  - 葬儀に関する専用お問い合わせ

### 2.6 情報ページ
- **利用規約** (`/userpolicy`)
- **サイトポリシー** (`/sitepolicy`)
- **プライバシーポリシー** (`/privacypolicy`)
- **特定商取引法** (`/tokushoho`)
- **共有方法説明** (`/howtoshare`)

## 3. 家族向け機能（FamilyFrameComponent）

### 3.1 認証機能
- **葬家ログイン** (`/soke/login`)
  - 葬家専用認証
  - セッション管理

### 3.2 葬家専用機能
- **葬家専用ページ** (`/soke/private`)
  - 葬家限定情報表示
  - 家族向けコンテンツ

## 4. 共通機能

### 4.1 認証・セキュリティ
- **JWT認証システム**
  - アクセストークン・リフレッシュトークン
  - 自動トークン更新
- **権限管理**
  - ロールベースアクセス制御
  - 画面・機能レベルでの権限制御

### 4.2 セッション管理
- **SessionService**
  - ログイン情報管理
  - 一時データ保存
  - メッセージ管理

### 4.3 ローディング管理
- **LoaderService**
  - 非同期処理中の表示制御
  - ユーザビリティ向上

### 4.4 キャッシュ管理
- **CacheService**
  - マスターデータキャッシュ
  - パフォーマンス向上

### 4.5 エラーハンドリング
- **統一エラー処理**
  - API エラーレスポンス処理
  - ユーザーフレンドリーなエラーメッセージ
  - ログ出力

## 5. 管理機能

### 5.1 マスターデータ管理
- **日程マスタ**
- **施行形式マスタ**
- **郵便番号マスタ**
- **和暦マスタ**
- **訃報サンプルマスタ**
- **弔文台紙マスタ**
- **弔文サンプルマスタ**
- **サービスマスタ**
- **税率マスタ**
- **関係性マスタ**
- **返礼品パラメータ**
- **香典パラメータ**
- **法要スタイルマスタ**
- **法要初期値マスタ**
- **法要サンプルマスタ**
- **葬家サイトメニューマスタ**

### 5.2 システム管理
- **ログ管理**
- **バックアップ管理**
- **パフォーマンス監視**

## 6. 外部連携機能

### 6.1 FDN連携
- **FDN施行データ連携**
  - 外部システムとのデータ同期
  - 施行情報の自動取得・更新

### 6.2 SMS連携
- **SMS送信機能**
  - 通知メッセージ送信
  - 認証コード送信

### 6.3 メール機能
- **自動メール送信**
  - 注文確認メール
  - 通知メール
  - リマインダーメール

## 7. レポート・分析機能

### 7.1 集計レポート
- **香典集計レポート**
- **注文集計レポート**
- **売上レポート**

### 7.2 PDF出力
- **各種帳票PDF出力**
- **QRコード生成**
- **印刷用レイアウト**

この機能一覧は、FOCプロジェクトの全体像を把握し、新規機能開発時の影響範囲を検討する際の参考資料として活用してください。
