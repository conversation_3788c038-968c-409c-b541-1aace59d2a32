
@import "src/assets/scss/company/setting";

.container .inner {
  .contents >.table_fixed.body {
    max-height: calc(100% - 215px);
  }
}
.contents {
  min-width: 1380px!important;
  .search_area .ui.checkbox {
    padding: 8px 10px 0;
    label {
      cursor: pointer;
    }
  }
  tr {
    line-height: 1;
    td {
      cursor: default;
      >div {
        min-height: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:not(:last-child) {
          min-height: 19px;
          border-bottom: dotted 1px $background-light1;
          margin: 0 -10px 5px;
          padding-left: 10px;
          padding-bottom: 5px;
        }
      }
    }
    .entry_id {
      width: 8%;
    }
    .entry_name {
      width: 10%;
    }
    .entry_tel {
      width: 10%;
    }
    .seko_date {
      width: 9%;
    }
    .department_name {
      width: 12%;
    }
    .soke_name {
      width: 10%;
    }
    .kojin_name {
      width: 10%;
    }
    .moshu_name {
      width: 10%;
    }
    .relationship {
      width: 12%;
    }
    .entry_ts {
      width: 10%;
    }
    .release_status {
      width: 10%;
    }
  }
}