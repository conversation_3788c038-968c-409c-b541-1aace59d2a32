
<div class="container">
  <div class="inner">
    <div class="contents header">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="navigation">
      <div class="item"><span>送り主</span><span>情報登録</span></div>
      <div class="arrow"></div>
      <div class="item current"><span>本文</span><span>登録</span></div>
      <div class="arrow"></div>
      <div class="item"><span>カート</span><span>内容確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>注文</span><span>手続き</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込み</span><span>完了</span></div>
    </div>
    <div class="contents">
      <h2>本文登録</h2>
      <div class="input-area">
        <div class="line">
          <div class="label required">弔文セット選択</div>
          <div class="input item_id" #itemIdEm [class.error]="isErrorField(itemIdEm)">
            <com-dropdown #itemComboEm [settings]="itemCombo" [(selectedValue)]="chobun_edit.item" (selectedItemChange)="itemChange($event)"></com-dropdown>
          </div>
          <a class="button grey" (click)="showItemList()">商品画像</a>
        </div>
        <div class="line">
          <div class="label required">宛名</div>
          <div class="input atena suffix" #atenaEm [class.error]="isErrorField(atenaEm)" suffix="様">
            <input type="text" [(ngModel)]="chobun_edit.chobun.atena">
          </div>
        </div>
        <div class="line">
          <div class="label">本文サンプル</div>
          <div class="input">
            <com-dropdown #chobunComboEm [settings]="chobunCombo" [(selectedValue)]="chobun_edit.chobun.chobun_sample" (selectedItemChange)="chobunChange($event)"></com-dropdown>
          </div>
        </div>
        <div class="line">
          <div class="label required">本文</div>
          <div class="input honbun" #honbunEm [class.error]="isErrorField(honbunEm)">
            <textarea rows="10" [maxlength]="honbun_max_length" [(ngModel)]="chobun_edit.chobun.honbun"></textarea>
            <div class="desc">
              ※1行21文字、10行以内
            </div>
            <div class="letter">
              {{chobun_edit.chobun.honbun?chobun_edit.chobun.honbun.length:0}}/{{ honbun_max_length }}文字
            </div>
          </div>
        </div>
        <div class="line">
          <div class="label required">台紙</div>
          <div class="daishi" #daishiEm [class.error]="isErrorField(daishiEm)">
            <div *ngFor="let daishi of seko_info.seko_company.chobun_daishi_masters">
              <div class="item-box">
                <div class="ui radio checkbox">
                  <input type="radio" name="chobun_daishi" [value]="daishi.id" [(ngModel)]="chobun_edit.chobun.daishi" [checked]="daishi.id===chobun_edit.chobun.daishi">
                  <label>{{daishi.name}}</label>
                </div>
                <div class="image-box" title="この台紙でプレビュー" (click)="selectDaishi(daishi.id)">
                  <img [src]="daishi.file_name" *ngIf="daishi.file_name">

                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="line">
          <div class="label"></div>
          <div class="button-area">
            <a class="button red" [class.disabled]="!chobun_edit?.chobun?.daishi" (click)="showDaishiPreview()">プレビューを見る</a>
          </div>
        </div>
      </div>
    </div>

    <div class="ui modal daishi" id="daishi-preview">
      <div class="content">
        <img [src]="preview_daishi_file" *ngIf="preview_daishi_file">
        <div class="contents atena" *ngIf="chobun_edit.chobun.atena">{{chobun_edit.chobun.atena}}様</div>
        <div class="contents honbun" *ngIf="chobun_edit.chobun.honbun">{{chobun_edit.chobun.honbun}}</div>
        <div class="contents date" *ngIf="seko_info?.seko_date">{{getWarekiYear(seko_info.seko_date)}}{{getMonthKanji(seko_info.seko_date)}}{{getDateKanji(seko_info.seko_date)}}</div>
        <div class="contents address1" *ngIf="chobun_edit.chobun">{{chobun_edit.chobun.okurinushi_prefecture}}{{addressConvert(chobun_edit.chobun.okurinushi_address_1)}}{{addressConvert(chobun_edit.chobun.okurinushi_address_2)}}</div>
        <div class="contents address2" *ngIf="chobun_edit.chobun">{{addressConvert(chobun_edit.chobun.okurinushi_address_3)}}</div>
        <div class="contents okurinushi_company" *ngIf="chobun_edit.chobun.okurinushi_company">{{chobun_edit.chobun.okurinushi_company}}</div>
        <div class="contents okurinushi_title" *ngIf="chobun_edit.chobun.okurinushi_title">{{chobun_edit.chobun.okurinushi_title}}</div>
        <div class="contents okurinushi_name" *ngIf="chobun_edit.chobun.okurinushi_name">{{chobun_edit.chobun.okurinushi_name}}</div>
        <div class="contents renmei1" *ngIf="chobun_edit.chobun.renmei1">{{chobun_edit.chobun.renmei1}}</div>
        <div class="contents renmei2" *ngIf="chobun_edit.chobun.renmei2">{{chobun_edit.chobun.renmei2}}</div>
      </div>
    </div>

    <div class="ui modal item" id="item-list">
      <div class="content scrolling">
        <ng-container *ngFor="let item of item_list">
          <div class="item-box" (click)="selectItem(item)">
            <div class="title-box">
              <div class="name">{{item.name}}</div>
              <div class="price">{{item.item_price | number}}円(税込)</div>
            </div>
            <div class="image-box" [class.selected]="item.selected" [class.no-image]="!item.image_file">
              <img [src]="item.image_file" *ngIf="item.image_file" (error)="imageLoadError(item)">
              <ng-container *ngIf="!item.image_file">
                <div class="no-image">
                  <i class="image icon huge"></i>
                  <div class="noimage">No image</div>
                </div>
              </ng-container>

            </div>
          </div>
        </ng-container>
      </div>
    </div>
    <div class="button-area">
      <a class="button" (click)="back()">< 戻る</a>
      <a class="button grey" (click)="saveData()">{{chobun_edit?.index>=0?'カート内容を変更する':'カートに入れる'}}  ></a>
    </div>
  </div>
</div>

