import { Component, OnInit, Inject, LOCALE_ID } from '@angular/core';
import { SessionService } from 'src/app/service/session.service';
import { HttpClientService } from 'src/app/service/http-client.service';
import { LoaderService } from 'src/app/service/loader.service';
import { MessageGetRequest, MessageGetResponse } from 'src/app/interfaces/order';
import { Utils } from 'src/app/const/func-util';
import { CommonConstants } from 'src/app/const/const';
declare var $;

@Component({
  selector: 'app-com-message',
  templateUrl: './message.component.html',
  styleUrls: ['./message.component.scss']
})
export class MessageComponent implements OnInit {
  Const = CommonConstants;
  Utils = Utils;

  host_url = window.location.origin;
  login_info = this.sessionSvc.get('staff_login_info');
  company_list = Utils.getCompanyList();
  login_company = Utils.getLoginCompany();
  companyCombo = { values: [], clearable: false, showOnFocus: false, disableSearch: true };
  sekoBaseCombo = { values: [], clearable: true };
  form_data = {
    company_id: null,
    seko_department: null,
    entry_id: null,
    entry_name: null,
    entry_tel: null,
    soke_name: null,
    kojin_name: null,
    moshu_name: null,
    is_ok: true,
    is_ng: false,
  };
  searched = false;
  msg_list: Array<MessageGetResponse> = null;

  page_per_count = 20;
  pagination = {
    pages: new Array(),
    current: 0
  };
  receipt_url = null;
  constructor(
    private httpClientService: HttpClientService,
    private sessionSvc: SessionService,
    private loaderSvc: LoaderService,
    @Inject(LOCALE_ID) private locale: string,
  ) { }


  ngOnInit() {
    this.initControl();
  }

  initControl() {
    if (this.company_list) {
      this.companyCombo.values = this.company_list.map(value => ({ name: value.base_name, value: value.id, data: value }));
      if (this.login_company) {
        if (this.login_company.base_type === CommonConstants.BASE_TYPE_COMPANY) {
          this.form_data.company_id = this.login_company.id;
          this.sekoBaseCombo.values = Utils.getSekoBaseList(this.login_company);
          if (this.login_info.staff.base.base_type !== CommonConstants.BASE_TYPE_COMPANY) {
            this.form_data.seko_department = this.login_info.staff.base.id;
          }
        } else {
          this.companyCombo.disableSearch = false;
          this.form_data.company_id = this.company_list[0].id;
          this.sekoBaseCombo.values = Utils.getSekoBaseList(this.company_list[0]);
        }
      }
    }
  }
  companyChange(event, sekoBaseComboEm) {
    this.sekoBaseCombo.values = [];
    this.form_data.seko_department = null;
    if (event && event.data) {
      this.sekoBaseCombo.values = Utils.getSekoBaseList(event.data);
    }
    sekoBaseComboEm.clear();
    sekoBaseComboEm.initComponent();
  }

  checkClick(flg_name, ...params: any[]) {

    for (const param of params) {
      if (param) {
        this.form_data[flg_name] = !this.form_data[flg_name];
        return;
      }
    }
  }

  searchMsg() {
    this.sessionSvc.clear('message');
    const request = new MessageGetRequest();
    request.company_id = this.form_data.company_id;
    request.seko_department = this.form_data.seko_department;
    request.entry_id = this.form_data.entry_id;
    request.entry_name = this.form_data.entry_name;
    request.entry_tel = this.form_data.entry_tel;
    request.soke_name = this.form_data.soke_name;
    request.kojin_name = this.form_data.kojin_name;
    request.moshu_name = this.form_data.moshu_name;
    request.release_status = new Array();
    if (this.form_data.is_ok) {
      request.release_status.push(this.Const.RELEASE_STATUS_ID_OK);
    }
    if (this.form_data.is_ng) {
      request.release_status.push(this.Const.RELEASE_STATUS_ID_NG);
    }
    this.loaderSvc.call();
    this.pagination.pages = new Array();
    this.pagination.current = 0;
    this.httpClientService.getMsgList(request).then((response) => {
      Utils.log(response);
      this.searched = true;
      this.msg_list = response;

      if (this.msg_list && this.msg_list.length) {
        for (const msg of this.msg_list) {
          if (msg.entry_detail.entry.entry_ts) {
            msg.entry_detail.entry.entry_ts = new Date(msg.entry_detail.entry.entry_ts);
          }
        }
      }
      this.calcPagination();
      this.loaderSvc.dismiss();

    }).catch(error => {
      this.loaderSvc.dismiss();
    });
  }
  calcPagination() {
    if (!this.msg_list || !this.msg_list.length) {
      return;
    }
    const count = Math.ceil(this.msg_list.length / this.page_per_count);
    for (let i = 1; i <= count; i++) {
      this.pagination.pages.push(i);
    }
    this.pagination.current = 1;
  }
  pageTo(page_num) {
    this.pagination.current = page_num;
  }
  clearForm(companyComboEm, sekoBaseComboEm) {

    this.form_data = {
      company_id: null,
      seko_department: null,
      entry_id: null,
      entry_name: null,
      entry_tel: null,
      soke_name: null,
      kojin_name: null,
      moshu_name: null,
      is_ok: true,
      is_ng: true,
    };
    if (this.login_company) {
      if (this.login_company.base_type === CommonConstants.BASE_TYPE_COMPANY) {
        this.form_data.company_id = this.login_company.id;
        if (this.login_info.staff.base.base_type === CommonConstants.BASE_TYPE_COMPANY) {
          sekoBaseComboEm.clear();
        } else {
          this.form_data.seko_department = this.login_info.staff.base.id;
          sekoBaseComboEm.setValue(this.form_data.seko_department);
        }
      } else {
        this.form_data.company_id = this.company_list[0].id;
        this.sekoBaseCombo.values = Utils.getSekoBaseList(this.company_list[0]);
        sekoBaseComboEm.clear();
        sekoBaseComboEm.initComponent();
      }
      companyComboEm.setValue(this.form_data.company_id);
    }

  }
}
