@import "src/assets/scss/customer/setting";
.container .inner .contents {
  >div {
    max-width: 700px;
  }
  padding: 15px;
  @media screen and (max-width: 360px) {
    padding: 10px;
  }
  .service-label {
    font-size: 1rem;
    line-height: 1;
    padding-bottom: 10px;
    @media screen and (max-width: 560px) {
      padding: 10px;
    }
    @media screen and (max-width: 360px) {
      padding: 10px;
    }
  }
  .image {
    width: 15px;
    height: 15px;
    margin: 0 7px 0 -4px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 15px auto;
    &.chobun{
      background-image: url(../../../../assets/img/family/icon_01.png);
    }
    &.kumotsu {
      background-image: url(../../../../assets/img/family/icon_02.png);
    }
    &.koden {
      background-image: url(../../../../assets/img/family/icon_03.png);
    }
    &.msg {
      background-image: url(../../../../assets/img/family/icon_04.png);
    }
  }
  .service-check {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding-bottom: 10px;
    >div {
      margin-left: 30px;
      @media screen and (max-width: 560px) {
        margin-left: 20px;
        margin-right: 20px;
      }
      @media screen and (max-width: 360px) {
        margin-left: 10px;
        margin-right: 10px;
      }
      font-size: 1rem;
      line-height: 1;
      font-weight: bold;
      display: flex;
      padding-bottom: 10px;
      input {
        margin: auto 0;
      }
      label {
        display: flex;
        font-weight: normal;
      }
    }
  }
  .title_area, .data_area .ui.accordion {
    padding: 10px;
    line-height: 1.2;
    table {
      width: 100%;
      margin: auto;
      tr td {
        &.entry_name {
          width: 20%;
        }
        &.service {
          width: 20%;
          >div {
            @media screen and (max-width: 560px) {
              .name {
                display: none;
              }
            }
            display: flex;
            .image{
              margin: auto 5px;
              @media screen and (max-width: 560px) {
                margin: auto;
              }
            }
          }
        }
        &.detail {
          padding-left: 0;
          text-align: center;
          width: 60px;
          @media screen and (max-width: 560px) {
            width: 40px;
          }
        }
      }
    }
  }
  .title_area table tr td {
    padding-left: 10px;
  }
  .data_area {
    .ui.accordion {
      padding: 0 !important;      
    }
    padding: 0 10px;
    font-size: 0.9rem;
    border-bottom: solid 1px $border-grey;
    &:nth-last-child(4) {
      border-bottom-width: 2px;
    }
    table {
      margin: auto;
      tr td {
        line-height: 1.5;
        &.label {
          text-align: right;
          padding-right: 20px;
          width: 20%;
          min-width: 80px;
          vertical-align: top;
        }
        .honbun {
          white-space: pre;
        }
      }
    }
    .henreihin_button {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 10px;
      .button {
        font-size: 0.8rem;
        margin-top: 5px;
        width: 110px;
        border-radius: 5px;
        height: 23px;
        line-height: 18px;
        color: white;
        &.pink {
          background-color: $background-red;
          border-color: $border-red;
        }
        &.grey {
          background-color: #aaa89b;
          border-color: #aaa89b;
          cursor: default;
          &:hover {
            opacity: 1;
          }
        }
        &.blue {
          background-color: #219599;
          border-color: #219599;
        }
        @media screen and (max-width: 560px) {
          width: 100px;
        }
        @media screen and (max-width: 380px) {
          width: 90px;
        }
      }
    }
  }
  .title_area {
    font-size: 0.9rem;
    @media screen and (max-width: 560px) {
      font-size: 0.8rem;
    }
    border-bottom: solid 2px $border-grey;
  }
  .total_area {
    text-align: right;
    display: flex;
    justify-content: flex-end;
    padding-bottom: 0;
    line-height: 1.5;
    .price {
      min-width: 70px;
    }
  }
  .ui.accordion {
    padding: 5px !important;
    .title {
      .chevron.up.icon {
        display: none;
      }
      &.active {
        .chevron.down.icon {
          display: none;
        }
        .chevron.up.icon {
          display: inline;
        }
      }
    }
    .content {
      padding-top: 0px !important;
      padding-bottom: 10px;
      .ui.radio.checkbox {
        margin-right: 10px;
      }
      .button {
        font-size: 0.8rem;
        margin-top: 5px;
        width: 110px;
        border-radius: 5px;
        height: 23px;
        line-height: 18px;
        @media screen and (max-width: 560px) {
          width: 100px;
        }
        @media screen and (max-width: 380px) {
          width: 90px;
        }
      }
    }
  }
}

.inner >.button-area {
  flex-wrap: wrap;
  .button {
    width: 180px;
    border-radius: 5px;
    margin: 5px !important;
    &.pink {
      background-color: $background-red;
      border-color: $border-red;
      color: white;
    }
    @media screen and (max-width: 560px) {
      width: 160px;
    }
    @media screen and (max-width: 380px) {
      width: 130px;
    }
  }
}

.ui.modal.item {
  position: fixed !important;
  width: 90% !important;
  max-width: 810px;
  max-height: 60vh;
  min-height: 200px;
  top: calc(40vh / 2 - 10px) !important;
  .header >div {
    display: flex;
    line-height: 1.5;
    font-size: 1rem;
    .label {
      font-size: 0.8rem;
      width: 80px;
      margin-top: 2px;
    }
    .data {
      font-size: 1rem;
    }
    &.price_filter {
      margin-top: 10px;
      font-size: 0.8rem;
      @media screen and (max-width: 560px) {
        display: block;
      }
      .data {
        font-size: 0.8rem;
        display: flex;
        div {
          margin: auto;
        }
        input {
          width: 70px;
          height: 25px;
          margin: 0 10px;
        }
        .button {
          width: 60px;
          border-radius: 5px;
          height: 25px;
          line-height: 18px;
        }
      }
    }
  }
  >.content {
    padding: 15px !important;
    font-size: 1.4rem;
    height: 100%;
    width: 100%;
    max-height: 45vh;
    display: flex;
    flex-wrap: wrap;
    .item-box {
      position: relative;
      padding: 10px;
      width: 380px;
      height: 170px;
      margin: 2px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      border-bottom: solid 2px $border-grey;;
      &:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,.3);
        --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
        -webkit-transform: translateY(-1px);
        transform: translateY(-1px);
      }
      &.selected {
        border: solid 2px #219599;
        &::before {
          content: '選択中';
          position: absolute;
          left: 0;
          top: 0;
          font-size: 0.8rem;
          padding: 2px 5px;
          background-color: #219599;
          color: #ffffff;
        }
      }
      .image-box {
        width: 150px;
        height: 150px;
        background-color: #fff;
        display: flex;
        justify-content: center;
        &.no-image {
          background-color: #e9e9e9;
        }
        img {
          height: auto;
          width: auto;
          max-height: 100%;
          max-width: 100%;
          margin: auto;
          min-width: 1px;
        }
        .no-image {
          margin-top: 10px;
          opacity: .5;
          font-size: 1.2rem;
        }
      }
      .title-box {
        line-height: 1.8;
        .name {
          margin-top: 5px;
          font-size: 1.1rem;
          font-weight: bold;
        }
        .price {
          font-size: 1rem;
        }
        .hinban {
          font-size: 0.9rem;
        }
      }
    }
    .no-data {
      font-size: 1.1rem;
      margin: auto;
    }
  }
  @media screen and (max-width: 560px) {
    max-width: 405px;
    top: calc(20vh / 2 - 10px) !important;
    max-height: 80vh;
    >.content {
      max-height: 65vh;
      .item-box {
        height: 150px;
        .image-box {
          width: 130px;
          height: 130px;
        }
      }
    }
  }
  @media screen and (max-width: 380px) {
    >.content {
      padding: 10px !important;
      .item-box {
        height: 130px;
        .image-box {
          width: 110px;
          height: 110px;
        }
      }
    }
  }
}

.ui.modal.confirm-list {
  position: fixed !important;
  width: 90% !important;
  max-width: 810px;
  max-height: 60vh;
  min-height: 200px;
  top: calc(40vh / 2 - 10px) !important;
  .header {
    font-size: 1.1rem;
  }
  >.content {
    padding: 15px !important;
    font-size: 1.4rem;
    height: 100%;
    width: 100%;
    max-height: 40vh;
    display: flex;
    flex-wrap: wrap;
    .entry-box {
      .entry-detail {
        padding: 10px 10px 0 10px;
        display: flex;
        line-height: 1.5;
        font-size: 1rem;
        .label {
          font-size: 0.8rem;
          width: 80px;
          margin-top: 2px;
        }
        .data {
          font-size: 1rem;
        }
      }
      .item-box {
        position: relative;
        padding: 10px;
        width: 380px;
        height: 170px;
        margin: 2px;
        display: flex;
        justify-content: space-between;
        border-bottom: solid 2px $border-grey;;
        .image-box {
          width: 150px;
          height: 150px;
          background-color: #fff;
          display: flex;
          justify-content: center;
          &.no-image {
            background-color: #e9e9e9;
          }
          img {
            height: auto;
            width: auto;
            max-height: 100%;
            max-width: 100%;
            margin: auto;
            min-width: 1px;
          }
          .no-image {
            margin-top: 10px;
            opacity: .5;
            font-size: 1.2rem;
          }
        }
        .title-box {
          line-height: 1.8;
          .name {
            margin-top: 5px;
            font-size: 1.1rem;
            font-weight: bold;
          }
          .price {
            font-size: 1rem;
          }
          .hinban {
            font-size: 0.9rem;
          }
        }
      }
    }
    .no-data {
      font-size: 1.1rem;
      margin: auto;
    }
  }
  @media screen and (max-width: 560px) {
    max-width: 405px;
    top: calc(20vh / 2 - 10px) !important;
    max-height: 80vh;
    min-height: 100px;
    >.content {
      max-height: 60vh;
      .entry-box {
        width: 100%;
        .item-box {
          width: 100%;
          height: 150px;
          .image-box {
            width: 130px;
            height: 130px;
          }
      }
      }
    }
  }
  @media screen and (max-width: 380px) {
    >.content {
      padding: 10px !important;
      .entry-box .item-box {
        height: 130px;
        .image-box {
          width: 110px;
          height: 110px;
        }
      }
    }
  }
  >.price-area {
    font-size: 1rem;
    padding: 10px 20px;
    text-align: right;
  }
  >.button-area {
    flex-wrap: wrap;
    .button {
      width: 120px;
      border-radius: 5px;
      margin: 5px !important;
      &.pink {
        background-color: $background-red;
        border-color: $border-red;
        color: white;
      }
      @media screen and (max-width: 560px) {
        width: 110px;
      }
      @media screen and (max-width: 380px) {
        width: 100px;
      }
    }
  }
}
.ui.modal.confirm {
  .content.red {
    color: $text-red;
  }
}