@import "src/assets/scss/customer/setting";
.container .inner .contents {
  padding-bottom: 50px;
  > .item-box {
    padding: 15px !important;
    font-size: 1.4rem;
    height: 100%;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .image-frame{
      display:block;
    }
    .image-box {
      width: 150px;
      height: 150px;
      background-color: #fff;
      display: flex;
      justify-content: center;
      border: $border-grey 2px solid;
      cursor: pointer;
      margin: 5px;
      &:hover {
        opacity: .8;
      }
      img {
        height: auto;
        width: auto;
        max-height: 100%;
        max-width: 100%;
        margin: auto;
        min-width: 1px;
      }
    }
    .clear {
      display: flex;
      justify-content: center;
      cursor: pointer;
      .button {
        margin: 0;
        font-size: 1rem;
        &:hover {
          opacity: .8;
        }
      }
    }
    @media screen and (max-width: 560px) {
      justify-content: center;
    }
  }
  >.button-area {
    flex-wrap: wrap;
    justify-content: left;
    padding-bottom: 0;
    .button {
      width: 180px;
      border-radius: 5px;
      margin: 5px !important;
      &.pink {
        background-color: $background-red;
        border-color: $border-red;
        color: white;
      }
      @media screen and (max-width: 560px) {
        width: 160px;
      }
      @media screen and (max-width: 380px) {
        width: 130px;
      }
    }
    &.save {
      justify-content: center;
      margin-top: 50px;
      margin-bottom: -20px;
    }
  }
  >.disclaimer {
    font-size: 0.8rem;
    padding-bottom: 0;
    line-height: 20px;
  }
}

.ui.modal.pop {
  position: fixed !important;
  width: 90% !important;
  max-width: 63vw;
  max-height: 63vh;
  min-height: 200px;
  top: calc(40vh / 2 - 10px) !important;
  >.pop-content {
    padding: 15px !important;
    font-size: 1.4rem;
    height: 100%;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .image-box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      justify-content: center;
      img {
        height: auto;
        width: auto;
        max-height: 60vh;
        max-width: 60vw;
        margin: auto;
        min-width: 1px;
      }
    }
  }
  @media screen and (max-width: 560px) {
    max-width: 405px;
    top: calc(20vh / 2 - 10px) !important;
    max-height: 82vh;
    >.pop-content {
      max-height: 82vh;
    }
  }
  @media screen and (max-width: 380px) {
    >.pop-content {
      padding: 10px !important;
    }
  }
}