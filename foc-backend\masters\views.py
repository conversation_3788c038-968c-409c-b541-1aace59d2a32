from rest_framework import exceptions, filters, generics
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly

from masters.models import (
    ChobunDaishiMaster,
    ChobunSample,
    FuhoSampleMaster,
    HenreihinParameter,
    HoyoDefaultMaster,
    HoyoSampleMaster,
    HoyoStyle,
    KodenParameter,
    Relationship,
    Schedule,
    SekoStyle,
    Service,
    SokeMenu,
    Tax,
    Wareki,
    ZipCode,
)
from masters.serializers import (
    ChobunDaishiMasterSerializer,
    ChobunSampleSerializer,
    FuhoSampleMasterSerializer,
    HenreihinParameterSerializer,
    HoyoDefaultMasterSerializer,
    HoyoSampleMasterSerializer,
    HoyoStyleSerializer,
    KodenParameterSerializer,
    RelationshipSerializer,
    ScheduleSerializer,
    SekoStyleSerializer,
    ServiceSerializer,
    SokeMenuSerializer,
    TaxSerializer,
    WarekiSerializer,
    ZipCodeSerializer,
)


class ScheduleFullListView(generics.ListAPIView):
    """
    get: 日程項目一覧を取得します
    """

    queryset = Schedule.objects.order_by('id')
    serializer_class = ScheduleSerializer
    permission_classes = [IsAuthenticated]


class SekoStyleFullListView(generics.ListAPIView):
    """
    get: 施行形式一覧を取得します
    """

    queryset = SekoStyle.objects.select_related('hoyo_style').order_by('hoyo_style__pk', 'id')
    serializer_class = SekoStyleSerializer
    permission_classes = [IsAuthenticated]


class ZipCodeQueryView(generics.ListAPIView):
    """
    get: 郵便番号を部分一致で検索します
    """

    queryset = ZipCode.objects.order_by('id')
    filter_backends = [filters.SearchFilter]
    search_fields = ['zip_code']
    serializer_class = ZipCodeSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]


class WarekiListView(generics.ListAPIView):
    """
    get: 和暦一覧を取得します
    """

    queryset = Wareki.objects.order_by('-begin_date')
    serializer_class = WarekiSerializer


class FuhoSampleMasterListView(generics.ListAPIView):
    """
    get: 訃報サンプル一覧を取得します
    """

    queryset = FuhoSampleMaster.objects.order_by('id')
    serializer_class = FuhoSampleMasterSerializer


class ChobunDaishiMasterListView(generics.ListAPIView):
    """
    get: 弔文台紙一覧を取得します
    """

    queryset = ChobunDaishiMaster.objects.order_by('id')
    serializer_class = ChobunDaishiMasterSerializer


class ChobunSampleListView(generics.ListAPIView):
    """
    get: 弔文サンプル一覧を取得します
    """

    queryset = ChobunSample.objects.order_by('id')
    serializer_class = ChobunSampleSerializer


class ServiceListView(generics.ListAPIView):
    """
    get: サービス一覧を取得します
    """

    queryset = Service.objects.order_by('id')
    serializer_class = ServiceSerializer


class TaxListView(generics.ListAPIView):
    """
    get: 消費税率一覧を取得します
    """

    queryset = Tax.objects.order_by('id')
    serializer_class = TaxSerializer


class RelationshipListView(generics.ListAPIView):
    """
    get: 間柄一覧を取得します
    """

    queryset = Relationship.objects.order_by('id')
    serializer_class = RelationshipSerializer


class HenreihinParameterDetail(generics.RetrieveAPIView):
    """
    get: 返礼品パラメータを取得します
    """

    queryset = HenreihinParameter.objects.all()
    serializer_class = HenreihinParameterSerializer

    def get_object(self) -> HenreihinParameter:
        instance: HenreihinParameter = self.get_queryset().first()
        if not instance:
            raise exceptions.NotFound(
                f'No {HenreihinParameter._meta.object_name} matches the given query.'
            )

        self.check_object_permissions(self.request, instance)

        return instance


class KodenParameterDetail(generics.RetrieveAPIView):
    """
    get: 香典パラメータを取得します
    """

    queryset = KodenParameter.objects.all()
    serializer_class = KodenParameterSerializer

    def get_object(self) -> KodenParameter:
        instance: KodenParameter = self.get_queryset().first()
        if not instance:
            raise exceptions.NotFound(
                f'No {KodenParameter._meta.object_name} matches the given query.'
            )

        self.check_object_permissions(self.request, instance)

        return instance


class HoyoStyleList(generics.ListAPIView):
    """
    get: 法要形式一覧を取得します
    """

    queryset = HoyoStyle.objects.order_by('pk')
    serializer_class = HoyoStyleSerializer


class HoyoDefaultMasterList(generics.ListAPIView):
    """
    get: 法要初期値マスタ一覧を取得します
    """

    queryset = HoyoDefaultMaster.objects.select_related('hoyo_style').order_by('hoyo_style', 'pk')
    serializer_class = HoyoDefaultMasterSerializer


class HoyoSampleMasterList(generics.ListAPIView):
    """
    get: 法要サンプルマスタ一覧を取得します
    """

    queryset = HoyoSampleMaster.objects.order_by('id')
    serializer_class = HoyoSampleMasterSerializer


class SokeMenuListView(generics.ListAPIView):
    """
    get: 葬家サイトメニュー一覧を取得します
    """

    queryset = SokeMenu.objects.order_by('disp_no', 'id')
    serializer_class = SokeMenuSerializer
