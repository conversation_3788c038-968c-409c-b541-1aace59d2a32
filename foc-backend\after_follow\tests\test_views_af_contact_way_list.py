from typing import Dict, List

from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.models import AfContactWay
from after_follow.tests.factories import AfContactWayFactory
from bases.models import Base
from bases.tests.factories import BaseFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class AfContactWayListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.af_contact_way_3 = AfContactWayFactory(
            company=BaseFactory(base_type=Base.OrgType.COMPANY), display_num=2
        )
        self.af_contact_way_1 = AfContactWayFactory(company=self.base_1, display_num=3)
        self.af_contact_way_2 = AfContactWayFactory(company=self.base_1, display_num=1)
        self.af_contact_way_4 = AfContactWayFactory(company=self.base_1, display_num=1)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_af_contact_way_list_succeed(self) -> None:
        """AF連絡方法一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)
        # 順番確認 (2 -> 4 -> 1 -> 3)
        self.assertEqual(records[0]['id'], self.af_contact_way_2.pk)
        self.assertEqual(records[1]['id'], self.af_contact_way_4.pk)
        self.assertEqual(records[2]['id'], self.af_contact_way_1.pk)
        self.assertEqual(records[3]['id'], self.af_contact_way_3.pk)

    def test_af_contact_way_list_succeed_by_company(self) -> None:
        """拠点で絞り込みしたAF連絡方法一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company': self.base_1.pk,
        }
        response = self.api_client.get(
            reverse('after_follow:contact_way_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        for record in records:
            self.assertEqual(record['company'], self.base_1.pk)

    def test_af_contact_way_list_succeed_by_company_2(self) -> None:
        """拠点で絞り込みしたAF連絡方法一覧を返す(項目確認)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company': self.af_contact_way_3.company.pk,
        }
        response = self.api_client.get(
            reverse('after_follow:contact_way_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)
        record = records[0]
        self.assertEqual(record['id'], self.af_contact_way_3.pk)
        self.assertEqual(record['company'], self.af_contact_way_3.company.pk)
        self.assertEqual(record['contact_way'], self.af_contact_way_3.contact_way)
        self.assertEqual(record['display_num'], self.af_contact_way_3.display_num)
        self.assertEqual(record['del_flg'], self.af_contact_way_3.del_flg)
        self.assertEqual(
            record['created_at'], self.af_contact_way_3.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.af_contact_way_3.create_user_id)
        self.assertEqual(
            record['updated_at'], self.af_contact_way_3.updated_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['update_user_id'], self.af_contact_way_3.update_user_id)

    def test_af_contact_way_list_ignores_deleted(self) -> None:
        """AF連絡方法一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.af_contact_way_1.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_af_contact_way_list_allow_from_moshu(self) -> None:
        """AF連絡方法一覧APIが喪主による呼び出しを許可する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_af_contact_way_list_failed_without_auth(self) -> None:
        """AF連絡方法一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class AfContactWayCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.af_contact_way_data: AfContactWay = AfContactWayFactory.build(company=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.base.pk,
            'contact_way': self.af_contact_way_data.contact_way,
            'display_num': self.af_contact_way_data.display_num,
        }

    def test_af_contact_way_create_succeed(self) -> None:
        """AF連絡方法を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('after_follow:contact_way_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['id'])
        self.assertEqual(record['company'], self.base.pk)
        self.assertEqual(record['contact_way'], self.af_contact_way_data.contact_way)
        self.assertEqual(record['display_num'], self.af_contact_way_data.display_num)
        self.assertEqual(record['del_flg'], self.af_contact_way_data.del_flg)
        self.assertIsNotNone(record['created_at'])
        self.assertEqual(record['create_user_id'], self.staff.pk)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_af_contact_way_create_deny_from_moshu(self) -> None:
        """AF連絡方法追加APIが喪主による呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('after_follow:contact_way_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_af_contact_way_create_failed_without_auth(self) -> None:
        """AF連絡方法追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = model_to_dict(self.af_contact_way_data)
        response = self.api_client.post(
            reverse('after_follow:contact_way_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
