# FOC API詳細仕様書

## 概要

このフォルダには、FOC（Funeral Online system Connect）プロジェクトの全APIエンドポイントの詳細仕様書が格納されています。

## フォルダ構成

```
api-specifications/
├── README.md                           # このファイル
├── 01-authentication-apis.md           # 認証API仕様書
├── 02-staff-management-apis.md         # スタッフ管理API仕様書
├── 03-base-management-apis.md          # 拠点管理API仕様書
├── 04-seko-management-apis.md          # 施行管理API仕様書
├── 05-master-data-apis.md              # マスターデータAPI仕様書
├── 06-business-apis.md                 # 業務系API仕様書
├── 07-utility-apis.md                  # ユーティリティAPI仕様書
├── 08-external-integration-apis.md     # 外部連携API仕様書
└── common/
    ├── authentication.md               # 認証共通仕様
    ├── error-codes.md                  # エラーコード一覧
    └── response-formats.md             # レスポンス形式共通仕様
```

## API仕様書の構成

各API仕様書には以下の情報が含まれています：

### 1. 基本情報
- エンドポイント
- HTTPメソッド
- 機能説明
- 認証要件
- 権限要件

### 2. リクエスト仕様
- パスパラメータ
- クエリパラメータ
- リクエストボディ
- 必須/任意の区分
- データ型・桁数制限
- バリデーション内容

### 3. レスポンス仕様
- 成功時レスポンス
- エラー時レスポンス
- レスポンスサンプル
- ステータスコード

### 4. 補足情報
- 業務ルール
- 注意事項
- 関連API
- 使用例

## 共通仕様

### 認証方式
- **JWT認証**: `Authorization: Bearer {access_token}`
- **リフレッシュトークン**: アクセストークンの更新に使用

### 権限クラス
- **IsAuthenticated**: 認証済みユーザー
- **IsStaff**: スタッフユーザー
- **IsMoshu**: 葬家ユーザー
- **ReadNeedAuth**: 読み取り専用認証

### データ形式
- **リクエスト**: JSON形式
- **レスポンス**: JSON形式
- **文字エンコーディング**: UTF-8
- **日時形式**: ISO 8601形式（例：2024-01-01T12:00:00+09:00）

### エラーハンドリング
- **400 Bad Request**: リクエストパラメータエラー
- **401 Unauthorized**: 認証エラー
- **403 Forbidden**: 権限エラー
- **404 Not Found**: リソースが見つからない
- **500 Internal Server Error**: サーバー内部エラー

### ページネーション
リスト取得APIでは以下のページネーション仕様を使用：

```json
{
  "count": 100,
  "next": "http://api.example.com/resource/?page=3",
  "previous": "http://api.example.com/resource/?page=1",
  "results": [...]
}
```

### フィルタリング
- **検索**: `search`パラメータによる全文検索
- **フィルタ**: 各フィールドによる条件絞り込み
- **ソート**: `ordering`パラメータによる並び順指定

## 使用方法

1. 実装したいAPIの仕様書を参照
2. リクエスト仕様に従ってAPIを呼び出し
3. レスポンス仕様に従って結果を処理
4. エラー時は共通エラーコードを参照

## 更新履歴

| 日付 | バージョン | 更新内容 |
|---|---|---|
| 2024-06-27 | 1.0.0 | 初版作成 |

## 注意事項

- 本仕様書は開発時点での情報です
- 実装時は最新のコードベースを確認してください
- 不明な点があれば開発チームにお問い合わせください
