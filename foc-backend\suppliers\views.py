from django.db.models import Q
from django_filters import rest_framework as filters
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from suppliers.models import Supplier
from suppliers.serializers import SupplierSerializer
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class SupplierList(AddContextMixin, generics.ListCreateAPIView):
    queryset = Supplier.objects.all().filter(Q(del_flg=False)).order_by('id')
    serializer_class = SupplierSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['company']
    permission_classes = [IsAuthenticated]


class SupplierDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 発注先マスターを取得します
    """

    queryset = Supplier.objects.filter(Q(del_flg=False)).order_by('company_id').all()
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated]
