# Generated by Django 3.1.7 on 2021-11-09 00:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('seko', '0021_auto_20211021_1013'),
    ]

    operations = [
        migrations.CreateModel(
            name='SekoContact',
            fields=[
                (
                    'seko',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        primary_key=True,
                        related_name='seko_contact',
                        serialize=False,
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
                ('name', models.TextField(blank=True, null=True, verbose_name='name')),
                (
                    'zip_code',
                    models.CharField(blank=True, max_length=7, null=True, verbose_name='zipcode'),
                ),
                ('prefecture', models.TextField(blank=True, null=True, verbose_name='prefecture')),
                ('address_1', models.TextField(blank=True, null=True, verbose_name='address1')),
                ('address_2', models.TextField(blank=True, null=True, verbose_name='address2')),
                ('address_3', models.TextField(blank=True, null=True, verbose_name='address3')),
                (
                    'mobile_num',
                    models.CharField(
                        blank=True, max_length=15, null=True, verbose_name='mobile no'
                    ),
                ),
                (
                    'kojin_relationship',
                    models.TextField(blank=True, null=True, verbose_name='kojin relationship'),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'db_table': 'tr_seko_contact',
            },
        ),
    ]
