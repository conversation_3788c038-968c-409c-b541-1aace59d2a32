# FOCシステム バックエンドのみセキュリティ改修案

## 概要

JWTペイロードを変更せず、バックエンドのみでセキュリティ対策を実施する改修案です。
アクセストークンのユーザーIDから最新のcompany_codeとbase_typeを取得してチェックし、
base_listの権限フィルタリングも実装します。

## 1. 改修対象と効果

### 1.1 改修対象ファイル（バックエンドのみ）

1. **`utils/authentication.py`** - JWT認証時のユーザー情報検証強化
2. **`bases/views.py`** - BaseFullListの権限フィルタリング追加
3. **`utils/security_mixins.py`** - 共通セキュリティミックスイン（新規作成）

### 1.2 対策効果の検証

#### ✅ **会社コード改ざん対策**
- **効果**: 完全に対策可能
- **理由**: バックエンドでJWTのuser_idから最新のcompany_codeを取得し、リクエストパラメータを無視

#### ✅ **ユーザーコード改ざん対策**
- **効果**: 完全に対策可能
- **理由**: JWTのuser_idでユーザー認証し、セッションのuser_idは無視

#### ✅ **ユーザー権限改ざん対策**
- **効果**: 完全に対策可能
- **理由**: データベースから最新のbase_typeを取得し、セッションの権限情報は無視

#### ✅ **テナント会社ログイン時の他社情報漏洩対策**
- **効果**: 完全に対策可能
- **理由**: base_listを権限に応じてフィルタリング

## 2. 実装詳細

### 2.1 JWT認証強化（utils/authentication.py）

```python
# utils/authentication.py

from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import AuthenticationFailed, InvalidToken
from rest_framework_simplejwt.settings import api_settings
from rest_framework_simplejwt.state import User
from rest_framework_simplejwt.tokens import RefreshToken

from seko.models import Moshu
from bases.models import Base


class ClassableRefreshToken(RefreshToken):
    @classmethod
    def for_user(cls, user):
        if isinstance(user, Moshu):
            setattr(user, 'id', user.seko.pk)
        token = super().for_user(user)
        token['user_class'] = user._meta.object_name
        if isinstance(user, Moshu):
            delattr(user, 'id')
        return token


class ClassableJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        try:
            user_id = validated_token[api_settings.USER_ID_CLAIM]
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user identification'))

        try:
            user_class: str = validated_token['user_class']
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user class'))

        try:
            if user_class == 'Moshu':
                user = Moshu.objects.get(Q(pk=user_id) & Q(seko__del_flg=False))
            else:
                user = User.objects.get(**{api_settings.USER_ID_FIELD: user_id})
        except (Moshu.DoesNotExist, User.DoesNotExist):
            raise AuthenticationFailed(_('User not found'), code='user_not_found')

        if not user.is_active:
            raise AuthenticationFailed(_('User is inactive'), code='user_inactive')

        # 【新規追加】ユーザー情報の整合性チェック
        self.validate_user_integrity(user)

        return user
    
    def validate_user_integrity(self, user):
        """
        データベースから最新のユーザー情報を取得して整合性をチェック
        セッション改ざん攻撃を防ぐため、常に最新の情報を使用
        """
        try:
            if isinstance(user, Moshu):
                # 喪主の場合：施行情報と拠点情報を再取得
                fresh_moshu = Moshu.objects.select_related('seko__base').get(
                    Q(pk=user.pk) & Q(seko__del_flg=False)
                )
                if not fresh_moshu.seko or not fresh_moshu.seko.base:
                    raise AuthenticationFailed(
                        'Moshu seko or base not found', 
                        code='moshu_data_integrity_error'
                    )
                
                # 最新の会社コードと拠点タイプを設定
                user._fresh_company_code = fresh_moshu.seko.base.company_code
                user._fresh_base_type = fresh_moshu.seko.base.base_type
                
            else:  # Staff
                # スタッフの場合：拠点情報を再取得
                fresh_staff = User.objects.select_related('base').get(pk=user.pk)
                if not fresh_staff.base:
                    raise AuthenticationFailed(
                        'Staff base not found', 
                        code='staff_data_integrity_error'
                    )
                
                # 最新の会社コードと拠点タイプを設定
                user._fresh_company_code = fresh_staff.company_code
                user._fresh_base_type = fresh_staff.base.base_type
                
        except (Moshu.DoesNotExist, User.DoesNotExist):
            raise AuthenticationFailed(
                'User data integrity check failed', 
                code='user_integrity_error'
            )
    
    def get_fresh_company_code(self, user):
        """セキュアな会社コード取得"""
        return getattr(user, '_fresh_company_code', None)
    
    def get_fresh_base_type(self, user):
        """セキュアな拠点タイプ取得"""
        return getattr(user, '_fresh_base_type', None)
```

### 2.2 共通セキュリティミックスイン（新規作成）

```python
# utils/security_mixins.py

from django.db.models import Q
from rest_framework.exceptions import PermissionDenied
from bases.models import Base
from seko.models import Moshu


class SecureCompanyFilterMixin:
    """
    会社コードによる安全なデータフィルタリング
    JWTのuser_idから最新の会社情報を取得してフィルタリング
    """
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return self.apply_secure_company_filter(queryset)
    
    def apply_secure_company_filter(self, queryset):
        """
        セキュアな会社コードフィルタリング
        セッションやリクエストパラメータは無視し、JWTのuser_idから取得
        """
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return queryset.none()
            
        user = self.request.user
        
        # 最新の会社コードを取得（JWT認証時に設定済み）
        company_code = getattr(user, '_fresh_company_code', None)
        base_type = getattr(user, '_fresh_base_type', None)
        
        if not company_code or base_type is None:
            return queryset.none()
        
        # システム管理会社の場合
        if base_type == Base.OrgType.ADMIN:
            # リクエストパラメータで指定された会社のデータのみ
            requested_company = self.get_requested_company_code()
            if requested_company:
                return self.filter_by_company_code(queryset, requested_company)
            return queryset.none()  # 会社指定なしの場合は空
            
        # テナント会社の場合は自社データのみ
        return self.filter_by_company_code(queryset, company_code)
    
    def get_requested_company_code(self):
        """リクエストパラメータから会社コードを取得"""
        company_params = ['seko_company', 'company_id', 'company']
        for param in company_params:
            value = self.request.GET.get(param)
            if value:
                return value
        return None
    
    def filter_by_company_code(self, queryset, company_code):
        """
        会社コードでクエリセットをフィルタリング
        各APIで適切にオーバーライドする
        """
        # デフォルト実装（各APIで適切にオーバーライド）
        if hasattr(queryset.model, 'seko_company'):
            return queryset.filter(seko_company__company_code=company_code)
        elif hasattr(queryset.model, 'company_code'):
            return queryset.filter(company_code=company_code)
        return queryset
    
    def validate_company_access(self):
        """会社コードアクセス権限の検証"""
        requested_company = self.get_requested_company_code()
        
        if not requested_company:
            return True
            
        user = self.request.user
        company_code = getattr(user, '_fresh_company_code', None)
        base_type = getattr(user, '_fresh_base_type', None)
        
        # システム管理会社は全テナントアクセス可能
        if base_type == Base.OrgType.ADMIN:
            return True
            
        # テナント会社は自社のみ
        if str(requested_company) != str(company_code):
            raise PermissionDenied(
                f'Access denied: requested company {requested_company}, '
                f'user company {company_code}'
            )
        return True


class SecureUserValidationMixin:
    """
    ユーザー権限の安全な検証
    セッション改ざんを防ぐため、常にデータベースから最新情報を取得
    """
    
    def dispatch(self, request, *args, **kwargs):
        # リクエスト毎にユーザー権限を再検証
        if hasattr(request, 'user') and request.user.is_authenticated:
            self.validate_user_permissions(request.user)
        return super().dispatch(request, *args, **kwargs)
    
    def validate_user_permissions(self, user):
        """ユーザー権限の整合性チェック"""
        company_code = getattr(user, '_fresh_company_code', None)
        base_type = getattr(user, '_fresh_base_type', None)
        
        if not company_code or base_type is None:
            raise PermissionDenied('User permission validation failed')
    
    def get_secure_user_company_code(self):
        """セキュアな会社コード取得"""
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return None
        return getattr(self.request.user, '_fresh_company_code', None)
    
    def get_secure_user_base_type(self):
        """セキュアな拠点タイプ取得"""
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return None
        return getattr(self.request.user, '_fresh_base_type', None)
    
    def is_secure_system_admin(self):
        """セキュアなシステム管理者判定"""
        base_type = self.get_secure_user_base_type()
        return base_type == Base.OrgType.ADMIN
```

### 2.3 BaseFullListの権限フィルタリング

```python
# bases/views.py

from django.db.models import Q, Prefetch
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from bases.models import Base
from bases.serializers import BaseDownwardSerializer, BaseSerializer
from masters.models import Service
from utils.requests import request_origin
from utils.view_mixins import AddContextMixin, SoftDestroyMixin
from utils.security_mixins import SecureUserValidationMixin  # 追加


class BaseFullList(SecureUserValidationMixin, generics.ListAPIView):
    """
    get: 全拠点の一覧を階層構造で取得します
    権限に応じてフィルタリングされます
    """

    serializer_class = BaseDownwardSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self, queryset=None):
        # 【改修】権限に応じたフィルタリングを追加
        base_queryset = Base.objects.filter(Q(del_flg=False))
        
        # ユーザーの権限を取得
        user_company_code = self.get_secure_user_company_code()
        user_base_type = self.get_secure_user_base_type()
        
        if not user_company_code or user_base_type is None:
            return base_queryset.none()
        
        # システム管理会社の場合：全ての会社情報を返す
        if user_base_type == Base.OrgType.ADMIN:
            filtered_queryset = base_queryset
        else:
            # テナント会社の場合：自社の情報のみを返す
            filtered_queryset = base_queryset.filter(
                company_code=user_company_code
            )
        
        return (
            filtered_queryset
            .prefetch_related(
                'tokusho',
                'focfee',
                'sms_account',
                Prefetch('services', queryset=Service.objects.order_by('id')),
            )
            .get_cached_trees()
        )


# 既存のクラスは変更なし
class BaseList(AddContextMixin, generics.CreateAPIView):
    serializer_class = BaseSerializer
    permission_classes = [IsAuthenticated]


class BaseDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 拠点情報を取得します
    put: 拠点情報を更新します
    patch: 拠点情報を更新します
    delete: 拠点情報を(論理)削除します
    """

    queryset = Base.objects.filter(Q(del_flg=False)).prefetch_related(
        'tokusho', 'focfee', 'sms_account'
    )
    serializer_class = BaseDownwardSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return BaseSerializer
        return self.serializer_class
```
