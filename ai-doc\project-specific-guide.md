# FOCプロジェクト固有設定・ベストプラクティスガイド

## 概要

このドキュメントでは、FOCプロジェクト特有の設定、慣習、ベストプラクティスについて説明します。

## 1. プロジェクト構造の理解

### 1.1 フロントエンド構造（foc-frontend）

```
foc-frontend/
├── src/
│   ├── app/
│   │   ├── guard/                    # ルートガード
│   │   │   ├── authF.guard.ts       # スタッフ認証ガード
│   │   │   ├── authS.guard.ts       # 葬家認証ガード
│   │   │   ├── admin.guard.ts       # 管理者権限ガード
│   │   │   ├── order.guard.ts       # 注文権限ガード
│   │   │   └── staff.guard.ts       # スタッフ専用ガード
│   │   ├── model/                   # データモデル定義
│   │   ├── pages/                   # ページコンポーネント
│   │   │   ├── company/             # 企業向け画面
│   │   │   ├── customer/            # 顧客向け画面
│   │   │   ├── family/              # 家族向け画面
│   │   │   └── common/              # 共通画面
│   │   ├── service/                 # サービス層
│   │   │   ├── http-client.service.ts  # API通信
│   │   │   ├── session.service.ts      # セッション管理
│   │   │   ├── loader.service.ts       # ローディング管理
│   │   │   └── cache.service.ts        # キャッシュ管理
│   │   └── component/               # 共通コンポーネント
│   ├── assets/                      # 静的リソース
│   └── environments/                # 環境設定
└── support/                         # Docker設定等
```

### 1.2 バックエンド構造（foc-backend）

```
foc-backend/
├── foc/                            # プロジェクト設定
│   ├── settings.py                 # Django設定
│   ├── urls.py                     # ルートURL設定
│   └── wsgi.py / asgi.py          # WSGI/ASGI設定
├── utils/                          # 共通ユーティリティ
│   ├── authentication.py          # JWT認証拡張
│   ├── permissions.py              # 権限クラス
│   ├── serializer_mixins.py        # シリアライザーミックスイン
│   └── view_mixins.py              # ビューミックスイン
├── masters/                        # マスターデータ管理
├── bases/                          # 拠点管理
├── staffs/                         # スタッフ管理
├── seko/                           # 世話管理
├── orders/                         # 注文管理
├── items/                          # 商品管理
└── [その他のアプリ]/
```

## 2. 認証・認可システム

### 2.1 JWT認証の仕組み

FOCプロジェクトでは、カスタマイズされたJWT認証を使用：

```python
# utils/authentication.py
class ClassableJWTAuthentication(JWTAuthentication):
    """
    スタッフと葬家で異なる認証を可能にするJWT認証クラス
    """
    def get_user(self, validated_token):
        # トークンのクラス情報に基づいてユーザーを取得
        user_class = validated_token.get('user_class')
        if user_class == 'staff':
            return Staff.objects.get(id=validated_token['user_id'])
        elif user_class == 'moshu':
            return Moshu.objects.get(id=validated_token['user_id'])
```

### 2.2 フロントエンドでの認証管理

```typescript
// session.service.ts での認証情報管理
export class SessionService {
  // スタッフログイン情報
  getStaffLoginInfo() {
    return this.get('staff_login_info');
  }

  // 葬家ログイン情報
  getSokeLoginInfo() {
    return this.get('soke_login_info');
  }

  // 認証情報クリア
  clearAll(): void {
    this.clearData();
    sessionStorage.removeItem('staff_login_info');
    sessionStorage.removeItem('soke_login_info');
  }
}
```

### 2.3 権限ガードの使い分け

```typescript
// ルーティングでの権限制御例
const routes: Routes = [
  { 
    path: 'foc/admin-only', 
    component: CompanyFrameComponent, 
    canActivate: [AuthFGuard, AdminGuard]  // スタッフ認証 + 管理者権限
  },
  { 
    path: 'foc/staff-only', 
    component: CompanyFrameComponent, 
    canActivate: [AuthFGuard, StaffOnlyGuard]  // スタッフ認証 + スタッフ専用
  },
  { 
    path: 'soke/private', 
    component: FamilyFrameComponent, 
    canActivate: [AuthSGuard]  // 葬家認証
  }
];
```

## 3. API設計パターン

### 3.1 RESTful API設計方針

FOCプロジェクトでは以下のURL設計パターンを採用：

```
/api/{app_name}/{resource}/
/api/{app_name}/{resource}/{id}/
/api/{app_name}/{resource}/{id}/{action}/
```

例：
- `GET /api/staffs/` - スタッフ一覧取得
- `POST /api/staffs/` - スタッフ作成
- `GET /api/staffs/123/` - 特定スタッフ取得
- `PUT /api/staffs/123/` - 特定スタッフ更新
- `POST /api/staffs/login/` - スタッフログイン
- `GET /api/staffs/me/` - 自分の情報取得

### 3.2 シリアライザーの継承パターン

```python
# 基底シリアライザー
class BaseSerializer(serializers.ModelSerializer):
    class Meta:
        abstract = True
        read_only_fields = ['created_at', 'updated_at']

# ミックスインの活用
class AddUserIdMixin:
    """作成者・更新者IDを自動設定するミックスイン"""
    def create(self, validated_data):
        validated_data['create_user_id'] = self.context['request'].user.id
        return super().create(validated_data)

# 実際のシリアライザー
class StaffSerializer(AddUserIdMixin, BaseSerializer):
    class Meta:
        model = Staff
        fields = '__all__'
```

### 3.3 ビューの継承パターン

```python
# 共通ミックスイン
class AddContextMixin:
    """リクエストコンテキストを追加するミックスイン"""
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context.update({'request': self.request})
        return context

class SoftDestroyMixin:
    """論理削除を行うミックスイン"""
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.del_flg = True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)

# 実際のビュー
class StaffList(AddContextMixin, generics.ListCreateAPIView):
    queryset = Staff.objects.filter(del_flg=False)
    serializer_class = StaffSerializer
    permission_classes = [IsAuthenticated]
```

## 4. フロントエンド設計パターン

### 4.1 コンポーネント設計方針

```typescript
// 基底コンポーネント
export abstract class BaseComponent implements OnInit {
  isLoading = false;
  errors: { [key: string]: string } = {};

  constructor(
    protected httpClientService: HttpClientService,
    protected sessionSvc: SessionService,
    protected loaderSvc: LoaderService,
    protected router: Router
  ) {}

  abstract ngOnInit(): void;

  protected showSuccessMessage(message: string) {
    const messageData = { type: 'success', title: message };
    this.sessionSvc.save('message', messageData);
  }

  protected showErrorMessage(message: string) {
    const messageData = { type: 'error', title: message };
    this.sessionSvc.save('message', messageData);
  }
}

// 実際のコンポーネント
export class UserProfileEditComponent extends BaseComponent {
  userProfile: UserProfileEditRequest = { /* ... */ };

  async ngOnInit() {
    await this.loadUserProfile();
  }

  // 具体的な実装...
}
```

### 4.2 フレームコンポーネントパターン

FOCプロジェクトでは、3つのフレームコンポーネントで画面を管理：

```typescript
// CompanyFrameComponent - 企業向け画面
export class CompanyFrameComponent {
  get isLogin(): boolean { return this.router.url.includes('/foc/login'); }
  get isTop(): boolean { return this.router.url.includes('/foc/top'); }
  get isUserProfileEdit(): boolean { return this.router.url.includes('/foc/user-profile/edit'); }
  // その他の画面判定...
}

// CustomerFrameComponent - 顧客向け画面
export class CustomerFrameComponent {
  get isFuho(): boolean { return this.router.url.includes('/fuho'); }
  get isHoyo(): boolean { return this.router.url.includes('/hoyo'); }
  // その他の画面判定...
}

// FamilyFrameComponent - 家族向け画面
export class FamilyFrameComponent {
  get isSokeLogin(): boolean { return this.router.url.includes('/soke/login'); }
  // その他の画面判定...
}
```

### 4.3 HTTP通信パターン

```typescript
// http-client.service.ts での統一的なAPI呼び出し
export class HttpClientService {
  // 認証付きGET
  private async getWithToken<T>(method: string, params: HttpParams): Promise<T> {
    await this.refreshTokenIfNeeded();
    const headers = this.getAuthHeaders();
    return this.http.get<T>(this.host + method, { headers, params }).toPromise();
  }

  // 認証付きPOST
  private async postWithToken<TRequest, TResponse>(method: string, data: TRequest): Promise<TResponse> {
    await this.refreshTokenIfNeeded();
    const headers = this.getAuthHeaders();
    return this.http.post<TResponse>(this.host + method, data, { headers }).toPromise();
  }

  // エラーハンドリング
  private checkErrorType(err: any) {
    if (err.status === 401) {
      this.sessionSvc.clearAll();
      this.router.navigate(['foc/login']);
    }
    // その他のエラー処理...
  }
}
```

## 5. データベース設計パターン

### 5.1 共通フィールドパターン

```python
# 基底モデル
class BaseModel(models.Model):
    del_flg = models.BooleanField('削除フラグ', default=False)
    created_at = models.DateTimeField('作成日時', auto_now_add=True)
    create_user_id = models.IntegerField('作成者ID')
    updated_at = models.DateTimeField('更新日時', auto_now=True)
    update_user_id = models.IntegerField('更新者ID')

    class Meta:
        abstract = True

# 実際のモデル
class Staff(BaseModel):
    company_code = models.CharField('会社コード', max_length=10)
    login_id = models.CharField('ログインID', max_length=50, unique=True)
    name = models.CharField('名前', max_length=100)
    # その他のフィールド...

    class Meta:
        db_table = 'staffs_staff'
```

### 5.2 マスターデータ管理パターン

```python
# マスターデータの基底クラス
class MasterModel(BaseModel):
    name = models.CharField('名前', max_length=100)
    display_order = models.IntegerField('表示順', default=0)
    active_flg = models.BooleanField('有効フラグ', default=True)

    class Meta:
        abstract = True
        ordering = ['display_order', 'id']

# 具体的なマスターデータ
class Schedule(MasterModel):
    required_flg = models.BooleanField('必須フラグ')

    class Meta:
        db_table = 'mm_schedule'
```

## 6. テスト戦略

### 6.1 ファクトリーパターンの活用

```python
# tests/factories.py
class StaffFactory(DjangoModelFactory):
    class Meta:
        model = Staff

    company_code = factory.Sequence(lambda n: f'COMP{n:03d}')
    login_id = factory.Sequence(lambda n: f'user{n}')
    name = factory.Faker('name', locale='ja_JP')
    mail_address = factory.Faker('email')
    base = factory.SubFactory(BaseFactory)

# テストでの使用
class StaffTestCase(TestCase):
    def setUp(self):
        self.staff = StaffFactory()
        self.admin_staff = StaffFactory(base__base_type=BaseType.ADMIN)
```

### 6.2 認証テストパターン

```python
class AuthenticatedTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.staff = StaffFactory()

    def authenticate(self, user=None):
        user = user or self.staff
        refresh = RefreshToken.for_user(user)
        self.client.credentials(
            HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}'
        )

    def test_authenticated_access(self):
        self.authenticate()
        response = self.client.get('/api/staffs/me/')
        self.assertEqual(response.status_code, 200)
```

## 7. 環境設定とデプロイ

### 7.1 環境変数管理

```python
# settings.py での環境変数使用例
import environ

env = environ.Env()

# データベース設定
DATABASES = {
    'default': dict(
        env.db(default='sqlite:///db.sqlite3'), 
        ATOMIC_REQUEST=True
    )
}

# JWT設定
JWT_EXPIRATION_MINUTES = env.int('JWT_EXPIRATION_MINUTES', default=5)
JWT_REFRESH_EXPIRATION_DAYS = env.int('JWT_REFRESH_EXPIRATION_DAYS', default=1)

# メール設定
EMAIL_HOST = env.str('EMAIL_HOST', default='localhost')
EMAIL_PORT = env.int('EMAIL_PORT', default=25)
```

### 7.2 Docker設定

```dockerfile
# foc-backend/Dockerfile
FROM python:3.8-buster

# 環境変数設定
ENV DEBUG=False \
    DJANGO_SETTINGS_MODULE=foc.settings \
    DATABASE_URL=sqlite:///db.sqlite3

# Poetry インストール
RUN pip install "poetry==1.1.4"

# 依存関係インストール
COPY pyproject.toml poetry.lock ./
RUN poetry export -f requirements.txt > requirements.txt && \
    pip install --no-cache-dir -r requirements.txt

# アプリケーションコピー
COPY . .

# 静的ファイル収集
RUN python manage.py collectstatic --no-input

# サーバー起動
CMD ["gunicorn", "foc.asgi:application", "-k", "foc.workers.CustomUvicornWorker"]
```

このガイドに従うことで、FOCプロジェクトの設計思想に沿った一貫性のある開発が可能になります。
