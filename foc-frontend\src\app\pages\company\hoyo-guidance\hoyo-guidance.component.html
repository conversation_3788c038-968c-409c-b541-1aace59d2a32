
<div class="container">
  <div class="inner with_footer">
    <div class="contents">
      <div class="menu_title">
        <i class="monument icon big"></i>
        <i class="list icon combo"></i>
        法要案内履歴
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(hoyoMasterComboEm, dateFromEm, dateToEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchHoyoMail()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>法要種別</label>
          <com-dropdown #hoyoMasterComboEm [settings]="hoyoMasterCombo" [(selectedValue)]="form_data.hoyo_id"></com-dropdown>
          <label>送信日</label>
          <com-calendar #dateFromEm [settings]="calendarOptionDate" id="send_date_from" [(value)]="form_data.send_date_from"></com-calendar>
          <label class="plane">～</label>
          <com-calendar #dateToEm [settings]="calendarOptionDate" id="send_date_to" [(value)]="form_data.send_date_to"></com-calendar>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="hoyo_mail_list?.length">
          全{{hoyo_mail_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?hoyo_mail_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="hoyo_name">法要種別</th>
              <th class="select_date">抽出日</th>
              <th class="send_date">送信日</th>
              <th class="mail_content">メール本文</th>
              <th class="note">備考</th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body" [class.no-page-nav]="pagination.pages.length===1">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let hoyo_mail of hoyo_mail_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" (click)="showItem($event, hoyo_mail)">
              <td class="hoyo_name" title="{{hoyo_mail.hoyo?.name}}">{{hoyo_mail.hoyo?.name}}</td>
              <td class="center aligned select_date" title="{{hoyo_mail.select_ts | date: 'yyyy/MM/dd'}}">
                {{hoyo_mail.select_ts | date: 'yyyy/MM/dd'}}</td>
              <td class="center aligned send_date" title="{{hoyo_mail.send_ts | date: 'yyyy/MM/dd'}}">
                {{hoyo_mail.send_ts | date: 'yyyy/MM/dd'}}</td>
              <td class="mail_content" title="{{hoyo_mail.content}}">{{hoyo_mail.content}}</td>
              <td class="note" title="{{hoyo_mail.note}}">{{hoyo_mail.note}}</td>
            </tr>
            </ng-container>
            <tr *ngIf="!hoyo_mail_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="10">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal big" id="hoyo-mail">
        <div class="header ui"><i class="mail bulk icon large"></i>法要案内送信済一覧</div>
        <div class="content" *ngIf="selected_hoyo_mail">          
          <div class="input_area">
            <div class="line">
              <label class="large">法要種別</label>
              <label class="data" >{{selected_hoyo_mail.hoyo?.name}}</label>
            </div>
            <div class="line">
              <label class="large">送信日時</label>
              <label class="data" >{{selected_hoyo_mail.send_ts | date: 'yyyy/MM/dd H:mm'}}</label>
            </div>
            <div class="line">
              <label class="large mail_content">メール本文</label>
              <label class="data mail_content" >{{selected_hoyo_mail.content}}</label>
            </div>
            <div class="line">
              <label class="large">備考</label>
              <label class="data note">{{selected_hoyo_mail.note}}</label>
            </div>
          </div>
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!selected_hoyo_mail?.inquiries?.length">
                  <th class="seko_id">施行ID</th>
                  <th class="soke_name">葬家名</th>
                  <th class="kojin_name">故人名</th>
                  <th class="moshu_name">喪主名</th>
                  <th class="death_date">逝去日</th>
                  <th class="seko_date">施行日</th>
                  <th class="hall_name">式場</th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body" *ngIf="selected_hoyo_mail.targets">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngFor="let target of selected_hoyo_mail.targets; index as i">
                <tr>
                  <td class="center aligned seko_id" title="{{target.seko.id}}">{{target.seko.id}}</td>
                  <td class="soke_name" title="{{target.seko.soke_name}}">{{target.seko.soke_name}}</td>
                  <td class="kojin_name" title="{{target.seko.kojin[0].name}}">{{target.seko.kojin[0].name}}</td>
                  <td class="moshu_name" title="{{target.seko.moshu.name}}">{{target.seko.moshu.name}}</td>
                  <td class="center aligned death_date" title="{{target.seko.death_date | date: 'yyyy/MM/dd'}}">
                    {{target.seko.death_date | date: 'yyyy/MM/dd'}}</td>
                  <td class="center aligned seko_date" title="{{target.seko.seko_date | date: 'yyyy/MM/dd'}}">
                    {{target.seko.seko_date | date: 'yyyy/MM/dd'}}</td>
                  <td class="hall_name" title="{{getPlace(target.seko)}}">{{getPlace(target.seko)}}</td>
                </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini light cancel" (click)="closeItem()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
		</div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group">
    <div class="ui labeled icon button mini light" routerLink="/foc/hoyo/filter/ki">
      <i class="filter icon"></i> 法要案内抽出（忌法要）
    </div>
    <div class="ui labeled icon button mini light" routerLink="/foc/hoyo/filter/bon">
      <i class="filter icon"></i> 法要案内抽出（盆）
    </div>
  </div>
</div>
