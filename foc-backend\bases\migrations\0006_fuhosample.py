# Generated by Django 3.1.2 on 2020-10-27 02:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bases', '0005_auto_20201022_1623'),
    ]

    operations = [
        migrations.SeparateDatabaseAndState(
            state_operations=[
                migrations.CreateModel(
                    name='FuhoSample',
                    fields=[
                        (
                            'id',
                            models.AutoField(
                                auto_created=True,
                                primary_key=True,
                                serialize=False,
                                verbose_name='ID',
                            ),
                        ),
                        ('sentence', models.TextField(verbose_name='sentence')),
                        ('display_num', models.IntegerField(verbose_name='display order')),
                        (
                            'created_at',
                            models.DateTimeField(auto_now_add=True, verbose_name='created at'),
                        ),
                        ('create_user_id', models.IntegerField(verbose_name='created by')),
                        (
                            'updated_at',
                            models.DateTimeField(auto_now=True, verbose_name='updated at'),
                        ),
                        ('update_user_id', models.IntegerField(verbose_name='updated by')),
                        (
                            'company',
                            models.ForeignKey(
                                on_delete=django.db.models.deletion.DO_NOTHING,
                                related_name='fuho_samples',
                                to='bases.base',
                                verbose_name='company',
                            ),
                        ),
                    ],
                    options={
                        'db_table': 'm_fuho_sample',
                    },
                ),
            ],
            database_operations=[],
        ),
    ]
