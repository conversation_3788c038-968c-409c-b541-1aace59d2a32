from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from henrei.tests.factories import HenreihinKodenFactory, HenreihinKumotsuFactory
from orders.tests.factories import (
    EntryDetailChobunFactory,
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryDetailMsgFactory,
    EntryFactory,
)
from seko.tests.factories import KojinFactory, SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class HomeichoChobunPDFViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        entry_1 = EntryFactory(seko=self.seko)
        KojinFactory(kojin_num=1, seko=self.seko)
        entry_2 = EntryFactory(seko=self.seko)

        entry_detail_21 = EntryDetailFactory(entry=entry_2)
        entry_detail_22 = EntryDetailFactory(entry=entry_2)
        entry_detail_11 = EntryDetailFactory(entry=entry_1)
        entry_detail_12 = EntryDetailFactory(entry=entry_1)
        entry_detail_13 = EntryDetailFactory(entry=entry_1)

        EntryDetailChobunFactory(entry_detail=entry_detail_21)
        EntryDetailChobunFactory(entry_detail=entry_detail_22)
        EntryDetailChobunFactory(entry_detail=entry_detail_11)
        EntryDetailChobunFactory(entry_detail=entry_detail_13)
        EntryDetailChobunFactory(entry_detail=entry_detail_12)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_homeicho_chobun_pdf_succeed(self) -> None:
        """芳名帳（弔文）PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_chobun_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'芳名帳（弔文）-{self.seko.pk}.pdf')

    def test_homeicho_chobun_pdf_succeed_without_auth(self) -> None:
        """芳名帳（弔文）PDF取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_chobun_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_homeicho_chobun_pdf_failed_by_seko_notfound(self) -> None:
        """存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_seko = SekoFactory.build()
        response = self.api_client.get(
            reverse('seko:homeicho_chobun_pdf', kwargs={'pk': not_exist_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class HomeichoKumotsuPDFViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        entry_1 = EntryFactory(seko=self.seko)
        KojinFactory(kojin_num=1, seko=self.seko)
        entry_2 = EntryFactory(seko=self.seko)

        entry_detail_21 = EntryDetailFactory(entry=entry_2)
        entry_detail_22 = EntryDetailFactory(entry=entry_2)
        entry_detail_11 = EntryDetailFactory(entry=entry_1)
        entry_detail_12 = EntryDetailFactory(entry=entry_1)
        entry_detail_13 = EntryDetailFactory(entry=entry_1)

        kumotsu_1 = EntryDetailKumotsuFactory(entry_detail=entry_detail_21)
        kumotsu_2 = EntryDetailKumotsuFactory(entry_detail=entry_detail_22)
        kumotsu_3 = EntryDetailKumotsuFactory(entry_detail=entry_detail_11)
        kumotsu_4 = EntryDetailKumotsuFactory(entry_detail=entry_detail_13)
        kumotsu_5 = EntryDetailKumotsuFactory(entry_detail=entry_detail_12)

        self.henrei_1 = HenreihinKumotsuFactory(detail_kumotsu=kumotsu_1)
        self.henrei_2 = HenreihinKumotsuFactory(detail_kumotsu=kumotsu_2)
        self.henrei_3 = HenreihinKumotsuFactory(detail_kumotsu=kumotsu_3)
        self.henrei_4 = HenreihinKumotsuFactory(detail_kumotsu=kumotsu_4)
        self.henrei_5 = HenreihinKumotsuFactory(detail_kumotsu=kumotsu_5)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_homeicho_kumotsu_pdf_succeed(self) -> None:
        """芳名帳（供花供物）PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_kumotsu_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'芳名帳（供花供物）-{self.seko.pk}.pdf')

    def test_homeicho_kumotsu_pdf_succeed_without_auth(self) -> None:
        """芳名帳（供花供物）PDF取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_kumotsu_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_homeicho_kumotsu_pdf_succeed_without_henrei(self) -> None:
        """芳名帳（供花供物）PDF取得APIは返礼品がなくても成功する"""

        self.henrei_1.delete()
        self.henrei_2.delete()
        self.henrei_3.delete()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_kumotsu_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_homeicho_kumotsu_pdf_failed_by_seko_notfound(self) -> None:
        """存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_seko = SekoFactory.build()
        response = self.api_client.get(
            reverse('seko:homeicho_kumotsu_pdf', kwargs={'pk': not_exist_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class HomeichoKodenPDFViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        entry_1 = EntryFactory(seko=self.seko)
        KojinFactory(kojin_num=1, seko=self.seko)
        entry_2 = EntryFactory(seko=self.seko)

        entry_detail_21 = EntryDetailFactory(entry=entry_2)
        entry_detail_22 = EntryDetailFactory(entry=entry_2)
        entry_detail_11 = EntryDetailFactory(entry=entry_1)
        entry_detail_12 = EntryDetailFactory(entry=entry_1)
        entry_detail_13 = EntryDetailFactory(entry=entry_1)

        koden_1 = EntryDetailKodenFactory(entry_detail=entry_detail_21)
        koden_2 = EntryDetailKodenFactory(entry_detail=entry_detail_22)
        koden_3 = EntryDetailKodenFactory(entry_detail=entry_detail_11)
        koden_4 = EntryDetailKodenFactory(entry_detail=entry_detail_13)
        koden_5 = EntryDetailKodenFactory(entry_detail=entry_detail_12)

        self.henrei_1 = HenreihinKodenFactory(detail_koden=koden_1)
        self.henrei_2 = HenreihinKodenFactory(detail_koden=koden_2)
        self.henrei_3 = HenreihinKodenFactory(detail_koden=koden_3)
        self.henrei_4 = HenreihinKodenFactory(detail_koden=koden_4)
        self.henrei_5 = HenreihinKodenFactory(detail_koden=koden_5)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_homeicho_koden_pdf_succeed(self) -> None:
        """芳名帳（香典）PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_koden_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'芳名帳（香典）-{self.seko.pk}.pdf')

    def test_homeicho_koden_pdf_succeed_without_auth(self) -> None:
        """芳名帳（香典）PDF取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_koden_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_homeicho_koden_pdf_succeed_without_henrei(self) -> None:
        """芳名帳（香典）PDF取得APIは返礼品がなくても成功する"""

        self.henrei_4.delete()
        self.henrei_5.delete()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_koden_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_homeicho_koden_pdf_failed_by_seko_notfound(self) -> None:
        """存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_seko = SekoFactory.build()
        response = self.api_client.get(
            reverse('seko:homeicho_koden_pdf', kwargs={'pk': not_exist_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class HomeichoMsgPDFViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        entry_1 = EntryFactory(seko=self.seko)
        KojinFactory(kojin_num=1, seko=self.seko)
        entry_2 = EntryFactory(seko=self.seko)

        entry_detail_21 = EntryDetailFactory(entry=entry_2)
        entry_detail_22 = EntryDetailFactory(entry=entry_2)
        entry_detail_11 = EntryDetailFactory(entry=entry_1)
        entry_detail_12 = EntryDetailFactory(entry=entry_1)
        entry_detail_13 = EntryDetailFactory(entry=entry_1)

        EntryDetailMsgFactory(entry_detail=entry_detail_21)
        EntryDetailMsgFactory(entry_detail=entry_detail_22)
        EntryDetailMsgFactory(entry_detail=entry_detail_11)
        EntryDetailMsgFactory(entry_detail=entry_detail_13)
        EntryDetailMsgFactory(entry_detail=entry_detail_12)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_homeicho_msg_pdf_succeed(self) -> None:
        """芳名帳（追悼MSG）PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_msg_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'芳名帳（追悼MSG）-{self.seko.pk}.pdf')

    def test_homeicho_msg_pdf_succeed_without_auth(self) -> None:
        """芳名帳（追悼MSG）PDF取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:homeicho_msg_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_homeicho_msg_pdf_failed_by_seko_notfound(self) -> None:
        """存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_seko = SekoFactory.build()
        response = self.api_client.get(
            reverse('seko:homeicho_msg_pdf', kwargs={'pk': not_exist_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
