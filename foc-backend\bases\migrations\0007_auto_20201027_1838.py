# Generated by Django 3.1.2 on 2020-10-27 09:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bases', '0006_fuhosample'),
        ('seko', '0008_delete_fuhosample'),
    ]

    operations = [
        migrations.AddField(
            model_name='fuhosample',
            name='del_flg',
            field=models.BooleanField(default=False, verbose_name='deleted'),
        ),
        migrations.AlterField(
            model_name='focfee',
            name='company',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                primary_key=True,
                related_name='focfee',
                serialize=False,
                to='bases.base',
                verbose_name='company',
            ),
        ),
    ]
