from django.db import models
from django.utils import timezone

from staffs.models import Staff


class SalesIndex(models.Model):
    sales_yymm = models.CharField(unique=True, max_length=6)
    fiscal_year = models.IntegerField()
    sum_ts = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'sum_sales_admin'


class SalesCompany(models.Model):
    sales_index = models.ForeignKey(
        SalesIndex, models.CASCADE, db_column='sales_admin_id', related_name='sales_companies'
    )
    company_id = models.IntegerField(blank=True, null=True)
    company_name = models.TextField(blank=True, null=True)
    zip_code = models.CharField(max_length=7, blank=True, null=True)
    address_1 = models.TextField(blank=True, null=True)
    address_2 = models.TextField(blank=True, null=True)
    tel = models.CharField(max_length=15, blank=True, null=True)
    invoice_date = models.DateField(blank=True, null=True)
    pay_date = models.DateField(blank=True, null=True)
    monthly_fee = models.IntegerField(blank=True, null=True)
    chobun_fee = models.IntegerField(blank=True, null=True)
    chobun_fee_unit = models.TextField(blank=True, null=True)
    chobun_qty = models.IntegerField(blank=True, null=True)
    chobun_order_price = models.IntegerField(blank=True, null=True)
    chobun_tax = models.IntegerField(blank=True, null=True)
    chobun_order_fee = models.IntegerField(blank=True, null=True)
    kumotsu_fee = models.IntegerField(blank=True, null=True)
    kumotsu_fee_unit = models.TextField(blank=True, null=True)
    kumotsu_qty = models.IntegerField(blank=True, null=True)
    kumotsu_order_price = models.IntegerField(blank=True, null=True)
    kumotsu_tax = models.IntegerField(blank=True, null=True)
    kumotsu_order_fee = models.IntegerField(blank=True, null=True)
    henreihin_fee = models.IntegerField(blank=True, null=True)
    henreihin_fee_unit = models.TextField(blank=True, null=True)
    henreihin_qty = models.IntegerField(blank=True, null=True)
    henreihin_order_price = models.IntegerField(blank=True, null=True)
    henreihin_tax = models.IntegerField(blank=True, null=True)
    henreihin_order_fee = models.IntegerField(blank=True, null=True)
    koden_qty = models.IntegerField(blank=True, null=True)
    koden_order_price = models.IntegerField(blank=True, null=True)
    koden_order_fee = models.IntegerField(blank=True, null=True)
    koden_fee_commission = models.IntegerField(blank=True, null=True)
    koden_fee_commission_tax = models.IntegerField(blank=True, null=True)
    koden_commission_pct = models.DecimalField(
        max_digits=3, decimal_places=2, blank=True, null=True
    )
    sales_price = models.IntegerField(blank=True, null=True)
    invoice_tax_pct = models.IntegerField(blank=True, null=True)
    invoice_tax = models.IntegerField(blank=True, null=True)
    confirm_ts = models.DateTimeField(blank=True, null=True)
    confirm_staff_id = models.IntegerField(blank=True, null=True)
    confirm_staff_name = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'sum_sales_company'

    def fix_invoice(self, staff: Staff) -> None:
        if self.confirm_ts:
            raise ValueError('Invoice is already confirmed')
        self.confirm_ts = timezone.localtime()
        self.confirm_staff_id = staff.pk
        self.confirm_staff_name = staff.name
        self.save()

    def cancel_fix_invoice(self) -> None:
        self.confirm_ts = None
        self.confirm_staff_id = None
        self.confirm_staff_name = None
        self.save()


class SalesSekoDepartment(models.Model):
    sales_company = models.ForeignKey(
        SalesCompany, models.CASCADE, related_name='sales_seko_departments'
    )
    seko_department_id = models.IntegerField(blank=True, null=True)
    base_name = models.TextField(blank=True, null=True)
    chobun_qty = models.IntegerField(blank=True, null=True)
    chobun_order_price = models.IntegerField(blank=True, null=True)
    chobun_tax = models.IntegerField(blank=True, null=True)
    kumotsu_qty = models.IntegerField(blank=True, null=True)
    kumotsu_order_price = models.IntegerField(blank=True, null=True)
    kumotsu_tax = models.IntegerField(blank=True, null=True)
    henreihin_qty = models.IntegerField(blank=True, null=True)
    henreihin_order_price = models.IntegerField(blank=True, null=True)
    henreihin_tax = models.IntegerField(blank=True, null=True)
    koden_qty = models.IntegerField(blank=True, null=True)
    koden_order_price = models.IntegerField(blank=True, null=True)
    koden_tax = models.IntegerField(blank=True, null=True)
    koden_order_fee = models.IntegerField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'sum_sales_seko_department'


class SalesDetail(models.Model):
    sales_seko_department = models.ForeignKey(
        SalesSekoDepartment,
        models.CASCADE,
        blank=True,
        null=True,
        db_column='sales_seko_dept_id',
        related_name='sales_details',
    )
    seko_id = models.IntegerField(blank=True, null=True)
    seko_date = models.DateField(blank=True, null=True)
    soke_name = models.TextField(blank=True, null=True)
    kojin_name = models.TextField(blank=True, null=True)
    moshu_name = models.TextField(blank=True, null=True)
    entry_id = models.IntegerField(blank=True, null=True)
    entry_name = models.TextField(blank=True, null=True)
    entry_ts = models.DateTimeField(blank=True, null=True)
    entry_detail_id = models.IntegerField(blank=True, null=True)
    service_id = models.IntegerField(blank=True, null=True)
    service_name = models.TextField(blank=True, null=True)
    item_id = models.IntegerField(blank=True, null=True)
    item_name = models.TextField(blank=True, null=True)
    quantity = models.IntegerField(blank=True, null=True)
    item_price = models.IntegerField(blank=True, null=True)
    item_tax = models.IntegerField(blank=True, null=True)
    item_tax_pct = models.IntegerField(blank=True, null=True)
    item_taxed_price = models.IntegerField(blank=True, null=True)
    keigen_flg = models.BooleanField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'sum_sales_detail'
