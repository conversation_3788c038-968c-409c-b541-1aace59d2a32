from typing import Optional

from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend
from django.db.models import Q

UserModel = get_user_model()


class CombinationModelBackend(ModelBackend):
    """
    Authenticates against settings.AUTH_USER_MODEL.
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        company_code: Optional[str] = kwargs.get('company_code')
        if username is None:
            username = kwargs.get(UserModel.USERNAME_FIELD)
        if company_code is None or username is None or password is None:
            return
        try:
            user = UserModel.objects.get(
                Q(company_code=company_code)
                & Q(login_id=username)
                & Q(del_flg=False)
                & Q(retired_flg=False)
            )
        except UserModel.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user (#20760).
            UserModel().set_password(password)
        else:
            if user.check_password(password) and self.user_can_authenticate(user):
                user.update_last_login()
                return user
