# DATABASE_URL=postgresql://db_user:db_password@localhost:6543/db_name
# DATABASE_URL=sqlite:///db.sqlite3
DATABASE_URL=sqlite:///db.sqlite3

SECRET_KEY=fgki*1m46g=%%51prir@m9rm128=*(aeqo5&21or(yh5hw(^n-

DEBUG=True
ALLOWED_HOSTS=www.foc.jpn.com,foc.jpn.com

# Use Silk to inspect Django requests and queries etc.
# ENABLE_SILK=False

CORS_ORIGIN_ALLOW_ALL=True
FRONTEND_HOSTNAME=http://localhost:4200/

# JWT settings
JWT_EXPIRATION_MINUTES=5
JWT_REFRESH_EXPIRATION_DAYS=1
JWT_ISSUER=Focus inc.
JWT_AUDIENCE=FOC dev team
JWT_PRIVATE_KEY_FILE=jwt-rsa

STATIC_ROOT=.static
MEDIA_URL=media/
MEDIA_ROOT=.media

USE_TLS=False

EMAIL_ON_CONSOLE=True
EMAIL_VIA_SENDGRID=False
SENDGRID_SANDBOX_MODE_IN_DEBUG=True
SENDGRID_TRACK_EMAIL_OPENS=False
SENDGRID_TRACK_CLICKS_HTML=False
SENDGRID_TRACK_CLICKS_PLAIN=False
EMAIL_FROM=<EMAIL>
ORDER_EMAIL_BCC=<EMAIL>;<EMAIL>
SUPPORT_EMAIL_BCC=<EMAIL>;<EMAIL>
INQUIRY_EMAIL_BCC=<EMAIL>;<EMAIL>
INVOICE_EMAIL_BCC=<EMAIL>;<EMAIL>

ENABLE_EPSILON_PAYMENT=False
EPSILON_REGISTER_PAYMENT_URL=https://beta.epsilon.jp/cgi-bin/order/direct_card_payment.cgi
EPSILON_REGISTER_PAYMENT_URL_TEST=https://beta.epsilon.jp/cgi-bin/order/direct_card_payment.cgi

#SMS API SETTING
SMS_REQUEST_URL=https://qpd-api.aossms.com/p5/api/mt.json
