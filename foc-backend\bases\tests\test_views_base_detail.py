from base64 import b64encode
from typing import Dict

import factory
from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory, BaseServiceFactory
from chobun_daishi.tests.factories import ChobunDaishiFactory
from henrei.tests.factories import HenreihinKodenFactory, HenreihinKumotsuFactory
from masters.tests.factories import ChobunDaishiMasterFactory, ScheduleFactory, ServiceFactory
from orders.tests.factories import (
    EntryDetailChobunFactory,
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryFactory,
)
from seko.tests.factories import SekoAnswerFactory, SekoFactory, SekoInquiryFactory
from service_reception_terms.tests.factories import ServiceReceptionTermFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class BaseDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()
        self.child_base_1 = BaseFactory(parent=self.base)
        BaseFactory(parent=self.base)
        BaseFactory(parent=self.child_base_1)

        self.service_reception_term_1 = ServiceReceptionTermFactory(department=self.base)
        self.service_reception_term_2 = ServiceReceptionTermFactory(department=self.base)

        # 件数データ
        self.seko = SekoFactory(seko_company=self.base)
        self.entry = EntryFactory(seko=self.seko)
        entry_detail_1 = EntryDetailFactory(entry=self.entry)
        entry_detail_2 = EntryDetailFactory(entry=self.entry)
        entry_detail_3 = EntryDetailFactory(entry=self.entry)

        EntryDetailChobunFactory(entry_detail=entry_detail_1, printed_flg=False)
        EntryDetailChobunFactory(entry_detail=entry_detail_2, printed_flg=True)
        EntryDetailChobunFactory(entry_detail=entry_detail_3, printed_flg=False)

        detail_kumotsu_1 = EntryDetailKumotsuFactory(entry_detail=entry_detail_1, order_status=1)
        detail_kumotsu_2 = EntryDetailKumotsuFactory(entry_detail=entry_detail_2, order_status=2)
        detail_kumotsu_3 = EntryDetailKumotsuFactory(entry_detail=entry_detail_3, order_status=2)
        HenreihinKumotsuFactory(detail_kumotsu=detail_kumotsu_1, order_status=0)
        HenreihinKumotsuFactory(detail_kumotsu=detail_kumotsu_2, order_status=2)
        HenreihinKumotsuFactory(detail_kumotsu=detail_kumotsu_3, order_status=1)

        detail_koden_1 = EntryDetailKodenFactory(entry_detail=entry_detail_1)
        detail_koden_2 = EntryDetailKodenFactory(entry_detail=entry_detail_2)
        detail_koden_3 = EntryDetailKodenFactory(entry_detail=entry_detail_3)
        HenreihinKodenFactory(detail_koden=detail_koden_1, order_status=1)
        HenreihinKodenFactory(detail_koden=detail_koden_2, order_status=2)
        HenreihinKodenFactory(detail_koden=detail_koden_3, order_status=2)

        # 施行問合せ件数データ
        SekoInquiryFactory(seko=self.seko)
        inquiry_1 = SekoInquiryFactory(seko=self.seko)
        SekoAnswerFactory(inquiry=inquiry_1)
        SekoAnswerFactory(inquiry=inquiry_1)
        SekoAnswerFactory(inquiry=SekoInquiryFactory(seko=self.seko))

        seko_2 = SekoFactory(seko_company=self.base)
        inquiry_2 = SekoInquiryFactory(seko=seko_2)
        SekoAnswerFactory(inquiry=inquiry_2)
        SekoAnswerFactory(inquiry=inquiry_2)
        inquiry_3 = SekoInquiryFactory(seko=seko_2)
        SekoAnswerFactory(inquiry=inquiry_3)
        SekoInquiryFactory(seko=seko_2)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_base_detail_succeed(self) -> None:
        """拠点詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:base_detail', kwargs={'pk': self.base.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('id'), self.base.id)
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), self.base.base_type)
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), self.base.company_code)
        self.assertEqual(record.get('base_name'), self.base.base_name)
        self.assertEqual(record.get('zip_code'), self.base.zip_code)
        self.assertEqual(record.get('prefecture'), self.base.prefecture)
        self.assertEqual(record.get('address_1'), self.base.address_1)
        self.assertEqual(record.get('address_2'), self.base.address_2)
        self.assertEqual(record.get('address_3'), self.base.address_3)
        self.assertEqual(record.get('tel'), self.base.tel)
        self.assertEqual(record.get('fax'), self.base.fax)
        self.assertIsNotNone(record.get('company_logo_file'))
        self.assertIsNotNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), self.base.calc_type)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.base.create_user_id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.base.update_user_id)
        # 子拠点も階層構造で返す
        self.assertEqual(len(record.get('children', [])), 2)

        # self.assertEqual(record.get('unprinted_chobun_count'), 2)
        # self.assertEqual(record.get('kumotsu_count'), {'unordered': 1, 'unconfirmed': 2})
        # self.assertEqual(record.get('henrei_kumotsu_count'), {'unordered': 2, 'unconfirmed': 1})
        # self.assertEqual(record.get('henrei_koden_count'), {'unordered': 1, 'unconfirmed': 2})
        # self.assertEqual(
        #     record.get('seko_inquiries_count'), {'unanswerd_inquiry': 2, 'unanswerd_af': 0}
        # )

    def test_base_detail_succeed_delete_seko(self) -> None:
        """削除した施行で拠点詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:base_detail', kwargs={'pk': self.base.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        self.assertEqual(record.get('unprinted_chobun_count'), 0)
        self.assertEqual(record.get('kumotsu_count'), {'unordered': 0, 'unconfirmed': 0})
        self.assertEqual(record.get('henrei_kumotsu_count'), {'unordered': 0, 'unconfirmed': 0})
        self.assertEqual(record.get('henrei_koden_count'), {'unordered': 0, 'unconfirmed': 0})
        self.assertEqual(
            record.get('seko_inquiries_count'), {'unanswerd_inquiry': 0, 'unanswerd_af': 0}
        )

    def test_base_detail_succeed_canceled_entry(self) -> None:
        """キャンセルした申込で拠点詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.entry.cancel_ts = timezone.now() - timezone.timedelta(30)
        self.entry.save()

        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:base_detail', kwargs={'pk': self.base.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        self.assertEqual(record.get('unprinted_chobun_count'), 0)
        self.assertEqual(record.get('kumotsu_count'), {'unordered': 0, 'unconfirmed': 0})
        self.assertEqual(record.get('henrei_kumotsu_count'), {'unordered': 0, 'unconfirmed': 0})
        self.assertEqual(record.get('henrei_koden_count'), {'unordered': 0, 'unconfirmed': 0})
        self.assertEqual(
            record.get('seko_inquiries_count'), {'unanswerd_inquiry': 0, 'unanswerd_af': 0}
        )

    def test_base_detail_exclude_deleted_children(self) -> None:
        """拠点詳細APIは論理削除された子拠点を除外する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.child_base_1.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:base_detail', kwargs={'pk': self.base.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('id'), self.base.id)
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), self.base.base_type)
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), self.base.company_code)
        self.assertEqual(record.get('base_name'), self.base.base_name)
        self.assertEqual(record.get('zip_code'), self.base.zip_code)
        self.assertEqual(record.get('prefecture'), self.base.prefecture)
        self.assertEqual(record.get('address_1'), self.base.address_1)
        self.assertEqual(record.get('address_2'), self.base.address_2)
        self.assertEqual(record.get('address_3'), self.base.address_3)
        self.assertEqual(record.get('tel'), self.base.tel)
        self.assertEqual(record.get('fax'), self.base.fax)
        self.assertIsNotNone(record.get('company_logo_file'))
        self.assertIsNotNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), self.base.calc_type)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.base.create_user_id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.base.update_user_id)
        # 子拠点は2つのうち1つが論理削除されている
        self.assertEqual(len(record.get('children', [])), 1)

    def test_base_detail_failed_by_notfound(self) -> None:
        """拠点詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:base_detail', kwargs={'pk': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_base_detail_failed_with_notfound(self) -> None:
        """拠点詳細APIは無効になった施行を返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.base.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:base_detail', kwargs={'pk': self.base.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_base_detail_failed_without_auth(self) -> None:
        """拠点詳細APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class BaseUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()

        self.chobun_daishi_master_1 = ChobunDaishiMasterFactory()
        self.chobun_daishi_master_2 = ChobunDaishiMasterFactory()
        ChobunDaishiFactory(company=self.base, daishi=self.chobun_daishi_master_1)
        ChobunDaishiFactory(company=self.base, daishi=self.chobun_daishi_master_2)

        self.service_1 = ServiceFactory()
        self.service_2 = ServiceFactory()
        self.base_service_1 = BaseServiceFactory(base=self.base, service=self.service_1)
        self.base_service_2 = BaseServiceFactory(base=self.base, service=self.service_2)

        self.service_reception_term_1 = ServiceReceptionTermFactory(department=self.base)
        self.service_reception_term_2 = ServiceReceptionTermFactory(department=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        new_base_data: Dict = factory.build(dict, FACTORY_CLASS=BaseFactory)
        new_base_data.pop('id')
        new_base_data['company_logo_file'] = b64encode(
            new_base_data['company_logo_file'].file.read()
        )
        new_base_data['company_map_file'] = b64encode(
            new_base_data['company_map_file'].file.read()
        )
        new_base_data.pop('create_user_id')
        new_base_data.pop('update_user_id')

        new_chobun_daishi_master = ChobunDaishiMasterFactory()
        new_base_data['chobun_daishi_master_ids'] = [
            self.chobun_daishi_master_1.pk,
            new_chobun_daishi_master.pk,
        ]

        self.new_service = ServiceFactory()
        new_base_data['service_ids'] = [
            self.service_1.pk,
            self.new_service.pk,
        ]

        self.new_service_reception_term = ServiceReceptionTermFactory.build(
            department=self.base,
            service=ServiceFactory(),
            schedule=ScheduleFactory(),
        )
        new_service_reception_term_model = model_to_dict(self.new_service_reception_term)
        del new_service_reception_term_model['id']
        new_base_data['service_reception_terms'] = [
            new_service_reception_term_model,
            model_to_dict(self.service_reception_term_1),
        ]

        return new_base_data

    def test_base_update_succeed(self) -> None:
        """拠点を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), params['base_type'])
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), params['company_code'])
        self.assertEqual(record.get('base_name'), params['base_name'])
        self.assertEqual(record.get('zip_code'), params['zip_code'])
        self.assertEqual(record.get('prefecture'), params['prefecture'])
        self.assertEqual(record.get('address_1'), params['address_1'])
        self.assertEqual(record.get('address_2'), params['address_2'])
        self.assertEqual(record.get('address_3'), params['address_3'])
        self.assertEqual(record.get('tel'), params['tel'])
        self.assertEqual(record.get('fax'), params['fax'])
        self.assertIsNotNone(record.get('company_logo_file'))
        self.assertIsNotNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), params['calc_type'])
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.base.create_user_id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)
        self.assertSetEqual(
            set(record.get('chobun_daishi_master_ids')), set(params['chobun_daishi_master_ids'])
        )
        self.assertSetEqual(set(record.get('service_ids')), set(params['service_ids']))

        record_service_reception_term_ids = [
            data['id'] for data in record.get('service_reception_terms')
        ]
        params_service_reception_term_ids = [
            self.new_service_reception_term.id,
            self.service_reception_term_1.id,
        ]
        self.assertSetEqual(
            set(record_service_reception_term_ids), set(params_service_reception_term_ids)
        )

    def test_base_update_succeed_with_some_empty_field(self) -> None:
        """拠点更新API(PUT)はロゴファイル、地図ファイルが空文字の場合はファイルを削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['company_logo_file'] = ''
        params['company_map_file'] = ''
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), params['base_type'])
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), params['company_code'])
        self.assertEqual(record.get('base_name'), params['base_name'])
        self.assertEqual(record.get('zip_code'), params['zip_code'])
        self.assertEqual(record.get('prefecture'), params['prefecture'])
        self.assertEqual(record.get('address_1'), params['address_1'])
        self.assertEqual(record.get('address_2'), params['address_2'])
        self.assertEqual(record.get('address_3'), params['address_3'])
        self.assertEqual(record.get('tel'), params['tel'])
        self.assertEqual(record.get('fax'), params['fax'])
        self.assertIsNone(record.get('company_logo_file'))
        self.assertIsNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), params['calc_type'])
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.base.create_user_id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_base_update_succeed_with_related_field_empty(self) -> None:
        """拠点を更新する(空の関連フィールド)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['chobun_daishi_master_ids'] = []
        params['service_ids'] = []
        params['service_reception_terms'] = []
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('chobun_daishi_master_ids'), [])
        self.assertEqual(record.get('service_ids'), [])
        self.assertEqual(record.get('service_reception_terms'), [])

    def test_base_update_succeed_with_none_on_file_fields(self) -> None:
        """拠点更新API(PUT)はロゴファイル、地図ファイルがNoneの場合はファイルを削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['company_logo_file'] = None
        params['company_map_file'] = None
        params['service_reception_terms'] = None
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), params['base_type'])
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), params['company_code'])
        self.assertEqual(record.get('base_name'), params['base_name'])
        self.assertEqual(record.get('zip_code'), params['zip_code'])
        self.assertEqual(record.get('prefecture'), params['prefecture'])
        self.assertEqual(record.get('address_1'), params['address_1'])
        self.assertEqual(record.get('address_2'), params['address_2'])
        self.assertEqual(record.get('address_3'), params['address_3'])
        self.assertEqual(record.get('tel'), params['tel'])
        self.assertEqual(record.get('fax'), params['fax'])
        self.assertIsNone(record.get('company_logo_file'))
        self.assertIsNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), params['calc_type'])
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.base.create_user_id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

        self.assertEqual(record.get('service_reception_terms'), [])

    def test_base_update_succeed_without_file_fields(self) -> None:
        """拠点更新API(PUT)はロゴファイル、地図ファイルの項目がない場合はファイルを削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params.pop('company_logo_file')
        params.pop('company_map_file')
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), params['base_type'])
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), params['company_code'])
        self.assertEqual(record.get('base_name'), params['base_name'])
        self.assertEqual(record.get('zip_code'), params['zip_code'])
        self.assertEqual(record.get('prefecture'), params['prefecture'])
        self.assertEqual(record.get('address_1'), params['address_1'])
        self.assertEqual(record.get('address_2'), params['address_2'])
        self.assertEqual(record.get('address_3'), params['address_3'])
        self.assertEqual(record.get('tel'), params['tel'])
        self.assertEqual(record.get('fax'), params['fax'])
        self.assertIsNone(record.get('company_logo_file'))
        self.assertIsNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), params['calc_type'])
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.base.create_user_id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_base_update_failed_with_not_exist_chobun_daishi_master(self) -> None:
        """拠点更新APIは存在しない弔文台紙が指定された場合失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['chobun_daishi_master_ids'] = [ChobunDaishiMasterFactory.build().pk]
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('chobun_daishi_master_ids'))

    def test_base_update_failed_with_not_exist_service(self) -> None:
        """拠点更新APIは存在しないサービスが指定された場合失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['service_ids'] = [ServiceFactory.build().pk]
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('service_ids'))

    def test_base_partial_update_can_remove_file_fields(self) -> None:
        """拠点更新API(PATCH)はロゴファイル、地図ファイルが空文字の場合はファイルを削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company_logo_file': '',
            'company_map_file': '',
        }
        response = self.api_client.patch(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('base_type'), self.base.base_type)
        self.assertIsNone(record.get('parent'))
        self.assertEqual(record.get('company_code'), self.base.company_code)
        self.assertEqual(record.get('base_name'), self.base.base_name)
        self.assertEqual(record.get('zip_code'), self.base.zip_code)
        self.assertEqual(record.get('prefecture'), self.base.prefecture)
        self.assertEqual(record.get('address_1'), self.base.address_1)
        self.assertEqual(record.get('address_2'), self.base.address_2)
        self.assertEqual(record.get('address_3'), self.base.address_3)
        self.assertEqual(record.get('tel'), self.base.tel)
        self.assertEqual(record.get('fax'), self.base.fax)
        self.assertIsNone(record.get('company_logo_file'))
        self.assertIsNone(record.get('company_map_file'))
        self.assertEqual(record.get('calc_type'), self.base.calc_type)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('create_user_id'), self.base.create_user_id)
        self.assertIsNotNone(record.get('updated_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_base_update_failed_without_auth(self) -> None:
        """拠点更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

        response = self.api_client.patch(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_base_update_failed_by_notfound(self) -> None:
        """拠点更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        response = self.api_client.patch(
            reverse('bases:base_detail', kwargs={'pk': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_base_update_failed_by_already_deleted(self) -> None:
        """拠点更新APIが論理削除済みの施行を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.base.disable()

        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        response = self.api_client.patch(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class BaseDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_base_delete_succeed(self) -> None:
        """拠点を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 施行のdel_flgがTrueになる
        db_base: Base = Base.objects.get(pk=self.base.id)
        self.assertTrue(db_base.del_flg)
        self.assertEqual(db_base.create_user_id, self.base.create_user_id)
        self.assertEqual(db_base.update_user_id, self.staff.id)

    def test_base_delete_failed_by_notfound(self) -> None:
        """拠点削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('bases:base_detail', kwargs={'pk': non_saved_base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_base_delete_failed_by_already_deleted(self) -> None:
        """拠点削除APIが論理削除済みの施行を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.base.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_base_delete_failed_without_auth(self) -> None:
        """拠点削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('bases:base_detail', kwargs={'pk': self.base.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
