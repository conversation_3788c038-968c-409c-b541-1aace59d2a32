# Generated by Django 3.1.5 on 2021-02-10 07:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('seko', '0013_sekoanswer_sekoinquiry'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventMail',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('event_name', models.TextField(verbose_name='event name')),
                ('select_ts', models.DateTimeField(verbose_name='selected at')),
                ('send_ts', models.DateTimeField(blank=True, null=True, verbose_name='sent at')),
                ('content', models.TextField(verbose_name='content')),
                ('note', models.TextField(blank=True, null=True, verbose_name='note')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'staff',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='staff',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_event_mail',
            },
        ),
        migrations.CreateModel(
            name='EventMailTarget',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'event_mail',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='targets',
                        to='event_mails.eventmail',
                        verbose_name='event mail',
                    ),
                ),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='event_mail_targets',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_event_mail_target',
            },
        ),
    ]
