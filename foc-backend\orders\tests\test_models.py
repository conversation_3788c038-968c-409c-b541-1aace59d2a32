from django.test import TestCase
from django.utils import timezone

from items.tests.factories import ItemFactory
from masters.tests.factories import ServiceFactory
from orders.models import Entry, EntryDetailKumotsu
from orders.tests.factories import (
    EntryDetailChobunFactory,
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryFactory,
)
from staffs.models import Staff
from staffs.tests.factories import StaffFactory


class EntryModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.entry: Entry = EntryFactory()
        service_10 = ServiceFactory(id=10)
        service_20 = ServiceFactory(id=20)
        service_30 = ServiceFactory(id=30)
        item = ItemFactory(service=ServiceFactory(id=40))
        item_10 = ItemFactory(service=service_10)
        item_20 = ItemFactory(service=service_20)
        item_30 = ItemFactory(service=service_30)

        entry_detail = EntryDetailFactory(entry=self.entry, item=item, item_tax_pct=8)
        self.entry_detail_10 = EntryDetailFactory(entry=self.entry, item=item_10, item_tax_pct=10)
        self.entry_detail_20 = EntryDetailFactory(entry=self.entry, item=item_20, item_tax_pct=8)
        self.entry_detail_20_2 = EntryDetailFactory(
            entry=self.entry, item=item_20, item_tax_pct=10
        )
        self.entry_detail_30 = EntryDetailFactory(entry=self.entry, item=item_30, item_tax_pct=8)

        EntryDetailKodenFactory(entry_detail=entry_detail)
        EntryDetailKodenFactory(entry_detail=self.entry_detail_10)
        EntryDetailKodenFactory(entry_detail=self.entry_detail_20)
        self.entry_koden_30 = EntryDetailKodenFactory(entry_detail=self.entry_detail_30)

    def test_increase_receipt_count(self):
        old_receipt_count = self.entry.receipt_count
        increased_receipt_count = self.entry.increase_receipt_count()
        self.assertEqual(old_receipt_count + 1, increased_receipt_count)

    def test_get_receipt_data(self):
        receipt_data = Entry.get_receipt_data(self.entry.pk)

        price_8 = self.entry_detail_20.item_unit_price * self.entry_detail_20.quantity
        tax_8 = self.entry_detail_20.item_tax + self.entry_detail_20.tax_adjust
        price_10 = (self.entry_detail_10.item_unit_price * self.entry_detail_10.quantity) + (
            self.entry_detail_20_2.item_unit_price * self.entry_detail_20_2.quantity
        )
        tax_10 = (self.entry_detail_10.item_tax + self.entry_detail_10.tax_adjust) + (
            self.entry_detail_20_2.item_tax + self.entry_detail_20_2.tax_adjust
        )
        price_koden = self.entry_koden_30.koden_commission
        tax_koden = self.entry_koden_30.koden_commission_tax + self.entry_koden_30.tax_adjust

        self.assertEqual(receipt_data['price_8'], price_8)
        self.assertEqual(receipt_data['tax_8'], tax_8)
        self.assertEqual(receipt_data['price_10'], price_10)
        self.assertEqual(receipt_data['tax_10'], tax_10)
        self.assertEqual(receipt_data['price_koden'], price_koden)
        self.assertEqual(receipt_data['tax_koden'], tax_koden)


class EntryDetailChobunModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.entry_detail_chobun: Entry = EntryDetailChobunFactory(printed_flg=False)

    def test_set_printed(self):
        self.entry_detail_chobun.set_printed()
        self.assertTrue(self.entry_detail_chobun.printed_flg)


class EntryDetailKumotsuModelTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

        self.detail_kumotsu: EntryDetailKumotsu = EntryDetailKumotsuFactory(
            order_status=0, order_ts=None, order_staff=None
        )

    def test_set_order_status(self) -> None:
        """発注ステータスを発注済みに更新する際には発注日時なども更新する"""
        staff: Staff = StaffFactory()

        self.detail_kumotsu.set_order_status(1, staff)
        self.detail_kumotsu.refresh_from_db()
        self.assertEqual(self.detail_kumotsu.order_status, 1)
        self.assertIsNone(self.detail_kumotsu.order_ts)
        self.assertIsNone(self.detail_kumotsu.order_staff, staff)

        # 発注済みにするとorder_ts、order_staffも更新
        self.detail_kumotsu.set_order_status(2, staff)
        self.detail_kumotsu.refresh_from_db()
        self.assertEqual(self.detail_kumotsu.order_status, 2)
        self.assertIsNotNone(self.detail_kumotsu.order_ts)
        self.assertEqual(self.detail_kumotsu.order_staff, staff)

        # 発注済みにしてもorder_tsが既に入っている場合は更新しない
        order_ts: timezone.datetime = self.detail_kumotsu.order_ts
        order_staff: Staff = self.detail_kumotsu.order_staff
        self.detail_kumotsu.order_status = 3
        self.detail_kumotsu.save()

        another_staff: Staff = StaffFactory()
        self.detail_kumotsu.set_order_status(2, another_staff)
        self.detail_kumotsu.refresh_from_db()
        self.assertEqual(self.detail_kumotsu.order_status, 2)
        self.assertEqual(self.detail_kumotsu.order_ts, order_ts)
        self.assertEqual(self.detail_kumotsu.order_staff, order_staff)
