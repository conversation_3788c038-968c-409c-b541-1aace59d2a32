
<div id="epsilonScript"></div>
<form name="downloadForm" action="{{tds2_url}}" method="POST">
  <input type="hidden" name="PaReq" value="{{pareq}}">
  <input type="hidden" name="TermUrl" value="{{api_url}}/orders/epsilon/return/">
  <input type="hidden" name="MD" value="{{MD}}">
</form>
<div class="container">
  <div class="inner">
    <div class="contents header">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="navigation">
      <div class="item"><span>カート</span><span>内容確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込者</span><span>情報登録</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込内容</span><span>確認</span></div>
      <div class="arrow"></div>
      <div class="item current"><span>お支払い</span><span>手続き</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込み</span><span>完了</span></div>
    </div>
    <div class="contents">
      <h2>お支払い手続き</h2>
      <div class="title">ご利用できるクレジットカード</div>
      <div class="card-images">
        <div class="visa" *ngIf="utils.canUseCredit(seko_info, 1)"></div>
        <div class="master" *ngIf="utils.canUseCredit(seko_info, 2)"></div>
        <div class="jcb" *ngIf="utils.canUseCredit(seko_info, 3) && !koden_only"></div>
        <div class="amex" *ngIf="utils.canUseCredit(seko_info, 4) && !koden_only"></div>
        <div class="diners" *ngIf="utils.canUseCredit(seko_info, 5) && !koden_only"></div>
      </div>
      <div class="notice">
        <span class="important">【重要なお知らせ】3Dセキュア（本人認証サービス）対応のお願い<br>
このたび、クレジットカード決済の安全性向上を目的として、2025年4月1日より「3Dセキュア（本人認証サービス）」の登録が義務化されました。<br>
これにより、当サイトにてクレジットカードをご利用いただく際には、3Dセキュアに対応したカード、および本人認証の登録が必須となります。<br>
※全てのご注文で本人認証が求められるわけではなく、カード会社の判断によりワンタイムパスワード等の追加認証が行われます。<br>
【ご注意】<br>
※「3Dセキュア2.0」非対応のクレジットカードはご利用になれません。対応状況についてはカード発行会社にご確認ください。<br>
※本人認証の方法や、表示される本人認証画面の使い方は、カード会社により異なります。<br>
　詳しくはカード発行会社にご確認ください。<br>
何卒、ご理解とご協力を賜りますようお願い申し上げます。</span>
      </div>
      <div class="input-area">
        <div class="line">
          <div class="label required large">カード番号</div>
          <div class="input no" #noEm [class.error]="isErrorField(noEm)">
            <input type="tel" id="cardno"  autocomplete="off" placeholder="1234567890123456" maxlength="16" [(ngModel)]="card_info.no">
          </div>
        </div>
        <div class="line">
          <div class="label required large">カード有効期限</div>
          <div class="input limit" #limitEm [class.error]="isErrorField(limitEm)">
            <com-dropdown class="mini" [settings]="monthCombo" [(selectedValue)]="card_info.month"></com-dropdown>
            <input type="hidden" id="expire_month" [(ngModel)]="card_info.month">
            <div>月/</div>
            <com-dropdown class="tiny" [settings]="yearCombo" [(selectedValue)]="card_info.year"></com-dropdown>
            <input type="hidden" id="expire_year" [(ngModel)]="card_info.year">
            <div>年</div>
          </div>
        </div>
        <div class="line">
          <div class="label required large">カード名義人</div>
          <div class="input name" #nameEm [class.error]="isErrorField(nameEm)">
            <input type="text" id="holdername"  autocomplete="off" placeholder="TARO TANAKA" [(ngModel)]="card_info.name">
          </div>
        </div>
        <div class="line security_code">
          <div class="label required large">セキュリティコード</div>
          <div class="input security_code" #security_codeEm [class.error]="isErrorField(security_codeEm)">
            <input class="mini" type="tel"  autocomplete="off" id="securitycode" placeholder="1234" maxlength="4" [(ngModel)]="card_info.security_code">
            <a (click)="showSecurityView()">セキュリティコードとは?</a>
          </div>
          <div class="about-security" *ngIf="security_view">
            <div class="close" (click)="closeSecurityView()">×</div>
            <div class="text">
              セキュリティコードは、カード裏面のサインパネル右上に印字されている7桁の数字のうち、下3桁です。
            </div>
            <div class="image"><img src="/assets/img/customer/security_code.png" alt=""></div>
          </div>
        </div>
      </div>
      <div class="message-area" *ngIf="message">
        {{message}}
        <br>
        {{message_sub}}
      </div>
    </div>
    <div class="button-area">
      <a class="button" (click)="back()">< 戻る</a>
      <a class="button grey" (click)="saveData()">お申込み完了 ></a>
    </div>
    <input type="text" id="token" #tokenEm style="display: none;">
    <input type="text" id="token_error" #tokenErrorEm style="display: none;">
    <input type='button' id="get_token" onclick="postForm('object')" style="display: none;"/>
    <input type='button' id="post_secure" onclick="postSecure()" style="display: none;"/>
    <input type='button' id="save_entry" (click)="saveEntry(tokenEm, tokenErrorEm)" style="display: none;"/>
  </div>
</div>
