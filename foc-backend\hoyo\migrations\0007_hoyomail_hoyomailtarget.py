# Generated by Django 3.1.5 on 2021-02-03 07:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('seko', '0013_sekoanswer_sekoinquiry'),
        ('hoyo', '0006_auto_20210127_1616'),
    ]

    operations = [
        migrations.CreateModel(
            name='HoyoMail',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('select_ts', models.DateTimeField(verbose_name='selected at')),
                ('send_ts', models.DateTimeField(blank=True, null=True, verbose_name='sent at')),
                ('content', models.TextField(verbose_name='content')),
                ('note', models.TextField(blank=True, null=True, verbose_name='note')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'hoyo',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='mails',
                        to='hoyo.hoyo',
                        verbose_name='hoyo',
                    ),
                ),
                (
                    'staff',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='staff',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_hoyo_mail',
            },
        ),
        migrations.CreateModel(
            name='HoyoMailTarget',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'hoyo_mail',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='targets',
                        to='hoyo.hoyomail',
                        verbose_name='hoyo mail',
                    ),
                ),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_hoyo_mail_target',
            },
        ),
    ]
