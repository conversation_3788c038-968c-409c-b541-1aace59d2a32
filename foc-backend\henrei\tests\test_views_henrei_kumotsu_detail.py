from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from henrei.models import HenreihinKumotsu
from henrei.tests.factories import HenreihinKumotsuFactory
from items.tests.factories import ItemFactory
from orders.tests.factories import EntryDetailKumotsuFactory
from seko.models import Moshu
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class HenreiKumotsuDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.henrei_kumotsu = HenreihinKumotsuFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_henrei_kumotsu_detail_succeed(self) -> None:
        """返礼品供花供物詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['detail_kumotsu'], self.henrei_kumotsu.pk)
        self.assertEqual(record['henreihin'], self.henrei_kumotsu.henreihin.pk)
        self.assertEqual(record['henreihin_hinban'], self.henrei_kumotsu.henreihin_hinban)
        self.assertEqual(record['henreihin_name'], self.henrei_kumotsu.henreihin_name)
        self.assertEqual(record['henreihin_price'], self.henrei_kumotsu.henreihin_price)
        self.assertEqual(record['henreihin_tax'], self.henrei_kumotsu.henreihin_tax)
        self.assertEqual(record['henreihin_tax_pct'], self.henrei_kumotsu.henreihin_tax_pct)
        self.assertEqual(record['keigen_flg'], self.henrei_kumotsu.keigen_flg)
        self.assertEqual(record['select_status'], self.henrei_kumotsu.select_status)
        self.assertIsNone(record['henrei_order'])
        self.assertEqual(record['supplier']['id'], self.henrei_kumotsu.supplier.pk)
        self.assertEqual(record['order_status'], self.henrei_kumotsu.order_status)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

    def test_henrei_kumotsu_detail_failed_notfound(self) -> None:
        """返礼品供花供物詳細APIは存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_henrei_kumotsu: HenreihinKumotsu = HenreihinKumotsuFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': non_saved_henrei_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_kumotsu_detail_failed_without_auth(self) -> None:
        """返礼品供花供物詳細APIはAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])


class HenreiKumotsuUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.henrei_kumotsu = HenreihinKumotsuFactory()
        self.new_henrei_kumotsu_data = HenreihinKumotsuFactory.build(
            henreihin=ItemFactory(), supplier=SupplierFactory()
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'henreihin': self.new_henrei_kumotsu_data.henreihin.pk,
            'henreihin_hinban': self.new_henrei_kumotsu_data.henreihin_hinban,
            'henreihin_name': self.new_henrei_kumotsu_data.henreihin_name,
            'henreihin_price': self.new_henrei_kumotsu_data.henreihin_price,
            'henreihin_tax': self.new_henrei_kumotsu_data.henreihin_tax,
            'henreihin_tax_pct': self.new_henrei_kumotsu_data.henreihin_tax_pct,
            'keigen_flg': self.new_henrei_kumotsu_data.keigen_flg,
            'select_status': self.new_henrei_kumotsu_data.select_status,
            'henrei_order': self.new_henrei_kumotsu_data.henrei_order,
            'supplier': self.new_henrei_kumotsu_data.supplier.pk,
            'order_status': self.new_henrei_kumotsu_data.order_status,
        }

    def test_henrei_kumotsu_update_succeed(self) -> None:
        """返礼品供花供物を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['henreihin'], self.new_henrei_kumotsu_data.henreihin.pk)
        self.assertEqual(record['henreihin_hinban'], self.new_henrei_kumotsu_data.henreihin_hinban)
        self.assertEqual(record['henreihin_name'], self.new_henrei_kumotsu_data.henreihin_name)
        self.assertEqual(record['henreihin_price'], self.new_henrei_kumotsu_data.henreihin_price)
        self.assertEqual(record['henreihin_tax'], self.new_henrei_kumotsu_data.henreihin_tax)
        self.assertEqual(
            record['henreihin_tax_pct'], self.new_henrei_kumotsu_data.henreihin_tax_pct
        )
        self.assertEqual(record['keigen_flg'], self.new_henrei_kumotsu_data.keigen_flg)
        self.assertEqual(record['select_status'], self.new_henrei_kumotsu_data.select_status)
        self.assertIsNone(record['henrei_order'], self.new_henrei_kumotsu_data.henrei_order)
        self.assertEqual(record['supplier']['id'], self.new_henrei_kumotsu_data.supplier.pk)
        self.assertEqual(record['order_status'], self.new_henrei_kumotsu_data.order_status)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

    def test_henrei_kumotsu_patch_supplier(self) -> None:
        """返礼品供花供物更新API(PATCH)で発注先を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = dict(
            [(key, val) for key, val in self.basic_params().items() if key in ['supplier']]
        )
        response = self.api_client.patch(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['henreihin'], self.henrei_kumotsu.henreihin.pk)
        self.assertEqual(record['henreihin_hinban'], self.henrei_kumotsu.henreihin_hinban)
        self.assertEqual(record['henreihin_name'], self.henrei_kumotsu.henreihin_name)
        self.assertEqual(record['henreihin_price'], self.henrei_kumotsu.henreihin_price)
        self.assertEqual(record['henreihin_tax'], self.henrei_kumotsu.henreihin_tax)
        self.assertEqual(record['henreihin_tax_pct'], self.henrei_kumotsu.henreihin_tax_pct)
        self.assertEqual(record['keigen_flg'], self.henrei_kumotsu.keigen_flg)
        self.assertEqual(record['select_status'], self.henrei_kumotsu.select_status)
        self.assertIsNone(record['henrei_order'], self.henrei_kumotsu.henrei_order)
        self.assertEqual(record['supplier']['id'], self.new_henrei_kumotsu_data.supplier.pk)
        self.assertEqual(record['order_status'], self.henrei_kumotsu.order_status)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

    def test_henrei_kumotsu_cannot_update_detail_kumotsu(self) -> None:
        """返礼品供花供物更新API(PATCH)はdetail_kumotsuを更新できない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'detail_kumotsu': EntryDetailKumotsuFactory().pk}
        response = self.api_client.patch(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['detail_kumotsu'], self.henrei_kumotsu.detail_kumotsu.pk)

    def test_henrei_kumotsu_update_failed_notfound(self) -> None:
        """返礼品供花供物更新APIは存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_henrei_kumotsu: HenreihinKumotsu = HenreihinKumotsuFactory.build()
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': non_saved_henrei_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_kumotsu_update_failed_without_auth(self) -> None:
        """返礼品供花供物APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])


class HenreiKumotsuDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.henrei_kumotsu = HenreihinKumotsuFactory(select_status=1)
        self.moshu = MoshuFactory(seko=self.henrei_kumotsu.detail_kumotsu.entry_detail.entry.seko)

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_henrei_kumotsu_delete_succeed(self) -> None:
        """返礼品供花供物を削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        with self.assertRaises(HenreihinKumotsu.DoesNotExist):
            self.henrei_kumotsu.refresh_from_db()

    def test_henrei_kumotsu_delete_failed_by_illegal_seko(self) -> None:
        """返礼品供花供物削除APIが不正な施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_henrei_kumotsu: HenreihinKumotsu = HenreihinKumotsuFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': non_saved_henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_kumotsu_delete_fail_by_already_placed(self) -> None:
        """返礼品供花供物削除APIが選択ステータスが確定済みになっているため失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.henrei_kumotsu.select_status = 2
        self.henrei_kumotsu.save()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_kumotsu_delete_fail_by_seko_disabled(self) -> None:
        """返礼品供花供物削除APIが所属する施行が無効になっているため失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.henrei_kumotsu.detail_kumotsu.entry_detail.entry.seko.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_kumotsu_delete_deny_from_another_moshu(self) -> None:
        """返礼品供花供物削除APIが他の喪主からの呼び出しを拒否する"""
        another_moshu: Moshu = MoshuFactory()
        refresh = RefreshToken.for_user(another_moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_kumotsu_delete_deny_from_staff(self) -> None:
        """返礼品供花供物削除APIが担当者からの呼び出しを拒否する"""
        staff = StaffFactory()
        refresh = RefreshToken.for_user(staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_kumotsu_delete_failed_without_auth(self) -> None:
        """返礼品供花供物削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('henrei:henrei_kumotsu_detail', kwargs={'pk': self.henrei_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
