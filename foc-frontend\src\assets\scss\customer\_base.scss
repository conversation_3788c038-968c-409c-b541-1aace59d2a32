
@import "./setting";
app-customer-frame, app-family-frame, app-not-found {
  display: block;
  width: 100%;
  height: 100%;
  font-size: 1.3rem;
  font-family: "Noto Serif JP", serif;
  background-color: $background-light;
  color: $text-dark;
  font-weight: 500;
  margin: 0 auto;
  position: relative;
  text-size-adjust: auto;

  .container {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: auto;
    background-color: $background-light;
    .inner {
      position: relative;
      width: 100%;
      background-color: $background-light;
      @media screen and (max-width: 560px) {
        margin-top: 10px;
      }
      .contents {
        background-color: #ffffff;
        width: 90%;
        max-width: 1000px;
        margin: 0px auto 20px;
        text-align: center;
        border-radius: 10px;
        padding-bottom: 30px;
        &.with-background-image {
          background-size: 40%;
          background-position: right bottom;
          background-repeat: no-repeat;
        }
        &.header {
          padding-top: 15px;
          padding-bottom: 15px;
          .label {
            margin: auto 0;
            font-size: 1rem;
            font-weight: 700;
          }
          .name {
            font-size: 1.4rem;
            font-weight: 700;
          }
          @media screen and (max-width: 560px) {
            padding-top: 10px;
            padding-bottom: 10px;
          }
        }
        h2 {
          display: inline-block;
          font-size: 1.4rem;
          font-weight: 700;
          padding: 0px 20px 5px;
          margin: 30px auto;
          text-align: center;
          border-bottom: solid 2px #413f36;
          @media screen and (max-width: 560px) {
            margin: 20px auto;
          }
        }
        >div {
          max-width: 90%;
          margin: 0 auto;
          padding: 0 20px 20px;
          text-align: left;
          line-height: 35px;
          font-size: 1.1rem;
          &:last-child {
            padding-bottom: 0;
          }
          &.pre-wrap {
            white-space: pre-wrap;
            line-height: 1.5;
          }
          &.data-item {
            display: flex;
            .label {
              margin-right: 20px;
            }
          }
          &.title {
            padding-bottom: 0;
          }
          &.card-images {
            display: flex;
            >div {
              width: 80px;
              height: 80px;
              background-size: contain;
              background-position: center center;
              background-repeat: no-repeat;
              margin-left: 5px;
              @media screen and (max-width: 560px) {
                width: 60px;
                height: 60px;
              }
              &:first-child {
                margin-left: 0;
              }
              &.visa {
                background-image: url(../../img/customer/card-visa.png);
              }
              &.master {
                background-image: url(../../img/customer/card-master.png);
              }
              &.jcb {
                background-image: url(../../img/customer/card-jcb.gif);
              }
              &.amex {
                background-image: url(../../img/customer/card-amex.gif);
              }
              &.diners {
                background-image: url(../../img/customer/card-diners.gif);
              }
            }

          }
        }
        .input-area {
          max-width: 500px;
          &.big {
            max-width: 600px;
          }
          font-size: 1rem;
          .line {
            width: 100%;
            display: flex;
            line-height: 30px;
            margin-top: 20px;
            &.single {
              display: block;
            }
            &:first-child{
              margin-top: 0;
            }
            .label {
              min-width: 130px;
              width: 30%;
              &.required::after {
                content: '*必須';
                color: $text-red;
                font-size: 0.7rem;
              }
              &.tiny {
                min-width: 60px;
              }
              &.small {
                min-width: 100px;
              }
              &.big {
                min-width: 160px;
              }
              &.large {
                min-width: 200px;
              }
              &.huge {
                min-width: 250px;
              }
            }
            .input {
              width: 70%;
              position: relative;
              >input:not([type="checkbox"]):not([type="radio"]), >textarea {
                width: 100%;
                max-width: 100%;
                border: solid 1px $border-dark;
                border-radius: 5px;
                padding: 5px;
                &:focus {
                  outline: none;
                  border-width: 2px;
                }
                &.small {
                  max-width: 150px;
                }
                &.tiny {
                  max-width: 70px;
                }
                &.mini {
                  max-width: 50px;
                }
              }
              >textarea {
                white-space: pre-wrap;
              }
              &.suffix {
                >input:not([type="checkbox"]):not([type="radio"]) {
                  width: 90%;
                }
              }
              >input:not([type="checkbox"]):not([type="radio"]) {
                height: 30px;
                line-height: 30px;
              }
              com-dropdown {
                width: 95%;
                height: 30px;
                .ui.dropdown {
                  width: 95%;
                  border: solid 1px $border-dark;
                  height: 30px;
                  min-height: 30px;
                  padding: 7px;
                  >input.search {
                    padding: 7px;
                  }
                  .dropdown.icon {
                    padding: 6px;
                    // background: $border-dark;
                    right: 11px;
                    border-radius: 0 2px 2px 0;
                    &::before {
                      // color: $text-light;
                    }
                  }
                  .menu {
                    border-color: $border-dark;
                    .item {
                      padding: 7px!important;
                      min-height: 30px;
                    }
                  }
                }
                &.small, &.small .ui.dropdown {
                  max-width: 150px;
                }
                &.tiny, &.tiny  .ui.dropdown {
                  max-width: 70px;
                }
                &.mini , &.mini .ui.dropdown {
                  max-width: 50px;
                }
                &.with-heaer {
                  .ui.dropdown .menu {
                    .header {
                      margin: 0;
                      background-color: $background-light;
                      padding: 5px 7px!important;
                      font-size: 0.9rem;
                      font-weight: bold;
                    }
                    .item{
                      padding: 7px 15px!important;
                    }
                  }
                }
                &.align.right .ui.dropdown {
                  padding-right: 20px;
                  text-align: right;
                  .menu {
                    .item {
                      padding-right: 20px!important;
                      text-align: right;
                    }
                  }
                }
              }
              &::before {
                position: absolute;
                content: attr(err-msg);
                color: $text-red;
                bottom: -21px;
                font-size: 0.7rem;
              }
              &::after {
                position: absolute;
                content: attr(suffix);
                left: 92%;
              }
              &.error {
                >input, >textarea, >com-dropdown .ui.dropdown, >com-dropdown .ui.dropdown .menu {
                  border-color: $border-red!important;
                }
              }
            }
          }
          .description {
            font-size: 0.8rem;
            line-height: 1.5;
            margin: 0 20px;
            padding-top: 15px;
          }
          @media screen and (max-width: 560px) {
            .line {
              display: block;
              .label {
                width: 100%;
              }
              .input {
                width: 100%;
                >input:not([type="checkbox"]):not([type="radio"]), >textarea, >com-dropdown {
                  margin-left: 5% !important;
                  width: 90%;
                  max-width: 90%;
                  .ui.dropdown {
                    display: inline-block !important;
                    width: 90% !important;
                  }
                }
                &.suffix {
                  >input:not([type="checkbox"]):not([type="radio"]) {
                    width: 80%;
                  }
                }
                &::before {
                  bottom: -25px;
                  left: 5%;
                }
                &::after {
                  left: 87%;
                }
              }
            }
          }
        }
        .message-area {
          font-size: 0.9rem;
          text-align: center;
          color: $text-red;
        }
      }
      .navigation {
        display: flex;
        width: 90%;
        justify-content: center;
        font-size: 1rem;
        height: 40px;
        margin-left: 20px;
        .item {
          margin-top: 4px;
          &.current {
            color: $text-red;
          }
        }
        .arrow {
          font-size: 3rem;
          transform: scale(0.2, 1);
          width: 30px;
          &::before {
            content: '>';
          }
        }
        @media screen and (max-width: 560px) {
          font-size: 0.9rem;
          height: 60px;
          line-height: 1.3rem;
          margin-bottom: -15px;
          .item {
            margin-top: 0;
            >span {
              text-align: center;
              display: block;
              white-space: nowrap;
            }
          }
          .arrow {
            font-size: 4rem;
            margin-top: 7px;
            width: 20px;
          }
        }
      }
      .button-area {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
        font-size: 1rem;
        text-align: center;
        :last-child {
          margin-right: 0 !important;

        }
        @media screen and (max-width: 560px) {
          font-size: 0.9rem;
        }
      }
      a.button {
        text-align: center;
        width: 150px;
        border: solid 2px $border-dark;
        border-radius: 50px;
        height: 30px;
        line-height: 26px;
        margin-right: 20px;
        cursor: pointer;
        &.lightgrey {
          background-color: $border-grey;
          border-color: $border-grey;
        }
        &.grey {
          background-color: $label-dark;
          color: $text-light;
        }
        &.red {
          background-color: $background-red;
          color: $text-light;
          border-color: $background-red;
        }
        &:hover {
          opacity: .8;
        }
        &.disabled {
          cursor: default;
          opacity: .5;
          &:hover {
            opacity: .5;
          }

        }
        @media screen and (max-width: 560px) {
          width: 120px;
          height: 25px;
          line-height: 21px;
        }
      }
      .service-menu {
        background-color: #ffffff;
        width: 90%;
        max-width: 1000px;
        margin: 0px auto;
        text-align: center;
        border-radius: 10px;
        padding-bottom: 50px;
        margin: 0px auto 30px;
        h2 {
          font-family: inherit;
          display: inline-block;
          font-size: 22px;
          font-weight: bold;
          padding: 0px 20px 5px;
          margin: 50px auto 40px;
          text-align: center;
          border-bottom: solid 2px #413f36;
          @media screen and (max-width: 560px) {
            margin: 40px auto 35px;
          }
        }
        .item {
          display: flex;
          flex-wrap: wrap;
          width: 95%;
          margin: auto;
          max-width: 700px;
          >div {
            width: 48%;
            max-width: 350px;
            height: 130px;
            margin: 5px 1%;
            border: solid 2px #413f36;
            border-radius: 10px;
            background-position: right bottom;
            background-size: 150px;
            background-repeat: no-repeat;
            text-align: left;
            line-height: 25px;
            cursor: pointer;
            @media screen and (max-width: 560px) {
              max-width: 280px;
              height: 100px;
            }
            &.chobun {
              background-image: url(../../img/customer/choden.jpg);
            }
            &.kumotsu {
              background-image: url(../../img/customer/hana.jpg);
            }
            &.koden {
              background-image: url(../../img/customer/koden.jpg);
            }
            &.message {
              background-image: url(../../img/customer/phone.jpg);
            }
            p:first-child {
              font-size: 20px;
              padding: 35px 0 5px 10%;
              font-weight: 700;
              margin: 0;
              @media screen and (max-width: 560px) {
                font-size: 18px;
              }
            }
            .p_small {
              font-size: 17px;
              padding-left: 10%;
              @media screen and (max-width: 560px) {
                font-size: 11px;
                padding-left: 5%;
              }
            }
            @media screen and (max-width: 560px) {
              &.chobun p:first-child, &.koden p:first-child {
                padding: 20px 0 0 5%;
              }
              &.kumotsu p:first-child, &.message p:first-child {
                width: 100px;
                padding: 10px 0 0 5%;
              }
            }
          }
        }
      }
    }
  }

  .ui.modal.confirm {
    padding: 0 10px;
    width: 92% !important;
    min-width: 200px;
    max-width: 400px;
    .content {
      padding: 30px !important;
      text-align: center;
      &.red {
          color: $text-red;
      }
    }
  }
  h1, h2, h3, h4, h5 {
    font: inherit;
  }
  p {
    margin: 0;
    line-height: inherit;
  }
  pre {
    font: inherit;
  }

  .button a {
    color: #ffff;
    font-size: 0.85rem;
    text-align: center;
    line-height: 30px;
    background-color: #413f36;
    border-radius: 20px;
    >pre {
      font: inherit;
    }
    &:hover {
      color: #ffff;
    }
  }

  ul {
    margin: 0;
    padding: 0;
    list-style: none;
    li {
      list-style: none;
    }
  }
  a {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
    color: inherit;
    &:hover {
      color: inherit;
      opacity: .8;
    }
  }
  input::placeholder, textarea::placeholder {
    color: #c7c7c7;
  }
}
app-family-frame {
  a.button {
    border-radius: 5px !important;
  }
  .ui.accordion .title:not(.ui) {
    font-family: inherit!important;
  }
}
:host ::ng-deep  com-dropdown .ui.input input {
  width: 100%;
  border: solid 1px $border-dark;
  border-radius: 5px;
  padding: 5px;
  &:focus {
    outline: none;
    border-width: 2px;
  }
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance:textfield;
}
:host ::ng-deep .flex-direction-nav a:before {
  font-size: 30px;
  padding-top: 10px;
}
