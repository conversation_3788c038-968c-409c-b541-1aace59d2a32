# Generated by Django 3.1.5 on 2021-01-21 06:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bases', '0013_smsaccount'),
        ('seko', '0012_moshu_last_login'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('hoyo', '0003_hoyomailtemplate'),
    ]

    operations = [
        migrations.CreateModel(
            name='HoyoSeko',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('hoyo_name', models.TextField(blank=True, null=True, verbose_name='hoyo name')),
                (
                    'hoyo_planned_date',
                    models.DateField(blank=True, null=True, verbose_name='planned on'),
                ),
                (
                    'hoyo_activity_date',
                    models.DateField(blank=True, null=True, verbose_name='held on'),
                ),
                ('begin_time', models.TimeField(blank=True, null=True, verbose_name='begins at')),
                ('end_time', models.TimeField(blank=True, null=True, verbose_name='ends at')),
                ('dine_flg', models.BooleanField(verbose_name='dine flag')),
                ('seshu_name', models.TextField(verbose_name='seshu name')),
                (
                    'kojin_seshu_relationship',
                    models.TextField(verbose_name='kojin seshu relationship'),
                ),
                (
                    'zip_code',
                    models.CharField(blank=True, max_length=7, null=True, verbose_name='zipcode'),
                ),
                ('prefecture', models.TextField(blank=True, null=True, verbose_name='prefecture')),
                ('address_1', models.TextField(blank=True, null=True, verbose_name='address1')),
                ('address_2', models.TextField(blank=True, null=True, verbose_name='address2')),
                ('address_3', models.TextField(blank=True, null=True, verbose_name='address3')),
                (
                    'tel',
                    models.CharField(blank=True, max_length=15, null=True, verbose_name='tel no'),
                ),
                (
                    'hoyo_sentence',
                    models.TextField(blank=True, null=True, verbose_name='hoyo sentence'),
                ),
                (
                    'shishiki_name',
                    models.TextField(blank=True, null=True, verbose_name='shishiki name'),
                ),
                ('reply_limit_date', models.DateField(verbose_name='reply limited on')),
                ('hoyo_site_end_date', models.DateField(verbose_name='hoyo site closed on')),
                ('note', models.TextField(blank=True, null=True, verbose_name='note')),
                ('del_flg', models.BooleanField(default=False, verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'hall',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to='bases.base',
                        verbose_name='hall',
                    ),
                ),
                (
                    'hoyo',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='hoyo',
                        to='hoyo.hoyo',
                        verbose_name='hoyo',
                    ),
                ),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='hoyo_seko',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
                (
                    'staff',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='staff',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_hoyo',
            },
        ),
        migrations.CreateModel(
            name='HoyoSchedule',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('schedule_name', models.TextField(verbose_name='name')),
                ('hall_name', models.TextField(verbose_name='hall name')),
                (
                    'hall_zip_code',
                    models.CharField(
                        blank=True, max_length=7, null=True, verbose_name='hall zipcode'
                    ),
                ),
                (
                    'hall_address',
                    models.TextField(blank=True, null=True, verbose_name='hall address'),
                ),
                (
                    'hall_tel',
                    models.CharField(
                        blank=True, max_length=15, null=True, verbose_name='hall tel no'
                    ),
                ),
                (
                    'schedule_date',
                    models.DateField(blank=True, null=True, verbose_name='scheduled on'),
                ),
                ('begin_time', models.TimeField(blank=True, null=True, verbose_name='begins at')),
                ('end_time', models.TimeField(blank=True, null=True, verbose_name='ends at')),
                ('display_num', models.IntegerField(verbose_name='display order')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'hall',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to='bases.base',
                        verbose_name='hall',
                    ),
                ),
                (
                    'hoyo_seko',
                    models.ForeignKey(
                        db_column='hoyo_id',
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='schedules',
                        to='hoyo.hoyoseko',
                        verbose_name='hoyoseko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_hoyo_schedule',
            },
        ),
    ]
