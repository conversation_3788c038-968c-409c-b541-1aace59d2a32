# Generated by Django 3.1.1 on 2020-09-24 03:04

import django.db.models.deletion
import mptt.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='Base',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'base_type',
                    models.IntegerField(
                        choices=[
                            (None, '(Unknown)'),
                            (0, 'Administrator'),
                            (1, 'Company'),
                            (2, 'Department'),
                            (3, 'Hall'),
                            (9, 'Other hall'),
                        ],
                        verbose_name='base type',
                    ),
                ),
                (
                    'company_code',
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        unique=True,
                        verbose_name='company code',
                    ),
                ),
                ('base_name', models.TextField(verbose_name='base name')),
                ('zip_code', models.Char<PERSON>ield(max_length=7, verbose_name='zipcode')),
                ('prefecture', models.TextField(verbose_name='prefecture')),
                ('address_1', models.TextField(verbose_name='address1')),
                ('address_2', models.TextField(blank=True, null=True, verbose_name='address2')),
                ('address_3', models.TextField(blank=True, null=True, verbose_name='address3')),
                ('tel', models.CharField(max_length=15, verbose_name='tel no')),
                ('fax', models.CharField(max_length=15, verbose_name='fax no')),
                (
                    'company_logo_file',
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to='company_logo',
                        verbose_name='company logo file',
                    ),
                ),
                (
                    'company_map_file',
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to='company_map',
                        verbose_name='company map file',
                    ),
                ),
                ('del_flg', models.BooleanField(default=False, verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('create_user_id', models.IntegerField(verbose_name='created by')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('update_user_id', models.IntegerField(verbose_name='updated by')),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('level', models.PositiveIntegerField(editable=False)),
                (
                    'parent',
                    mptt.fields.TreeForeignKey(
                        blank=True,
                        db_column='parent_base_id',
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='children',
                        to='bases.base',
                        verbose_name='parent base company',
                    ),
                ),
            ],
            options={
                'db_table': 'm_base',
            },
        ),
    ]
