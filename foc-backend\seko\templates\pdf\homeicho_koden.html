<!DOCTYPE html>
{% load humanize %}
<html>
<title>香典</title>
<head>
<style type="text/css">
@page {
  @top-right {
    content: counter(page) "/" counter(pages);
  }
}
body {
  font-family: "Noto Serif CJK JP", serif;
  font-size: 12px;
  color: #333;
  margin: 0;
}
.content {
  position: relative;
}
.content table {
  border-collapse: collapse;
  width: 100%;
}
.content td {
  padding: 7px;
  border: 1px solid rgba(0,0,0,0.4);
  vertical-align: top;
}
.content table tr:nth-child(1) {
  background-color:rgba(0,0,0,0.05);
  text-align: center;
}
.content table tr td:nth-child(1) {
  width: 50px;
  text-align: center;
  vertical-align: middle;
}
.content table tr td:nth-child(2) {
  vertical-align: middle;
}

.money::after {
  content: '円';
}
.money:empty {
  display: none;
}
</style>
</head>
<body>
<section class="content" style="clear: both">
  <div>
    <table>
      <tr>
        <td>No</td>
        <td>ご芳名</td>
        <td>摘要</td>
      </tr>
      {% for entry in seko.entries.all %}
      {% for detail in entry.details.all %}
      <tr>
        <td>{{ counter }}</td>
        <td>
          {{ entry.entry_name }}<br>
          {{ entry.entry_name_kana }}
        </td>
        <td>
          <div class="money">{% widthratio detail.item_unit_price 1 detail.quantity as price %}{{ price|intcomma:False }}</div>
          <div>
            <span>{{ detail.koden.henrei_koden.henreihin_hinban|default_if_none:"" }}　{{ detail.koden.henrei_koden.henreihin_name|default_if_none:"" }}</span>
            <span class="money" style="float: right">{{ detail.koden.henrei_koden.henreihin_price|intcomma:False }}</span>
          </div>
        </td>
      </tr>
      {% endfor %}
      {% endfor %}
      <tr>
        <td colspan="3" style="text-align: right">
          香典金額合計：{{ total_koden|intcomma:False }}円
          {% if total_henrei > 0 %}
          返礼品合計：{{ total_henrei|intcomma:False }}円
          {% endif %}
        </td>
      </tr>
    </table>
  </div>
</section>
</body>
</html>
