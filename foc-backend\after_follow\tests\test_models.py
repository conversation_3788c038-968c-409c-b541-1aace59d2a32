from django.test import TestCase

from after_follow.models import AfContactWay, AfGroup, AfterFollow
from after_follow.tests.factories import AfContactWayFactory, AfGroupFactory, AfterFollowFactory


class AfGroupModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.af_group: AfGroup = AfGroupFactory()

    def test_af_group_disable(self) -> None:
        """AF項目グループを無効化(論理削除)する"""
        self.af_group.disable()
        self.assertTrue(self.af_group.del_flg)


class AfterFollowModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.after_follow: AfterFollow = AfterFollowFactory()

    def test_after_follow_disable(self) -> None:
        """AF項目を無効化(論理削除)する"""
        self.after_follow.disable()
        self.assertTrue(self.after_follow.del_flg)


class AfContactWayModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.af_contact_way: AfContactWay = AfContactWayFactory()

    def test_af_contact_way_disable(self) -> None:
        """AF連絡方法を無効化(論理削除)する"""
        self.af_contact_way.disable()
        self.assertTrue(self.af_contact_way.del_flg)
