from base64 import b64encode
from datetime import date
from typing import Dict, List, Optional

from dateutil.relativedelta import relativedelta
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from advertises.tests.factories import AdvertiseFactory
from after_follow.models import AfGroup, AfWish, SekoAf
from after_follow.tests.factories import (
    AfActivityDetailFactory,
    AfGroupFactory,
    AfterFollowFactory,
    AfWishFactory,
    SekoAfFactory,
)
from bases.models import Base
from bases.tests.factories import BaseFactory, FocFeeFactory, TokushoFactory
from chobun_daishi.tests.factories import ChobunDaishiFactory
from hoyo.models import Hoyo, HoyoSeko
from hoyo.tests.factories import HoyoFactory, HoyoScheduleFactory, HoyoSekoFactory
from items.tests.factories import ItemFactory
from masters.models import ChobunDaishiMaster
from masters.tests.factories import (
    ChobunDaishiMasterFactory,
    ScheduleFactory,
    SekoStyleFactory,
    ServiceFactory,
)
from seko.models import Seko
from seko.tests.factories import (
    HenreihinKakegamiFactory,
    KojinFactory,
    MoshuFactory,
    SekoAlbumFactory,
    SekoFactory,
    SekoItemFactory,
    SekoScheduleFactory,
    SekoServiceFactory,
    SekoVideoFactory,
)
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class SekoDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        MoshuFactory(seko=self.seko)
        KojinFactory(seko=self.seko, kojin_num=2)
        KojinFactory(seko=self.seko, kojin_num=1)
        SekoScheduleFactory(seko=self.seko, display_num=3)
        SekoScheduleFactory(seko=self.seko, display_num=4)
        SekoScheduleFactory(seko=self.seko, display_num=1)
        SekoScheduleFactory(seko=self.seko, display_num=2)
        SekoVideoFactory(seko=self.seko)
        SekoVideoFactory(seko=self.seko)
        SekoVideoFactory(seko=self.seko)
        SekoAlbumFactory(seko=self.seko, display_num=3)
        SekoAlbumFactory(seko=self.seko, display_num=5)
        SekoAlbumFactory(seko=self.seko, display_num=2)
        SekoAlbumFactory(seko=self.seko, display_num=1)
        SekoAlbumFactory(seko=self.seko, display_num=4)
        SekoItemFactory(seko=self.seko)
        SekoItemFactory(seko=self.seko)
        SekoServiceFactory(seko=self.seko)
        SekoServiceFactory(seko=self.seko)
        HenreihinKakegamiFactory(seko=self.seko)
        FocFeeFactory(company=self.seko.seko_company)
        TokushoFactory(company=self.seko.seko_company)
        daishi_1: ChobunDaishiMaster = ChobunDaishiMasterFactory()
        daishi_2: ChobunDaishiMaster = ChobunDaishiMasterFactory()
        ChobunDaishiFactory(company=self.seko.seko_company, daishi=daishi_2)
        ChobunDaishiFactory(company=self.seko.seko_company, daishi=daishi_1)
        FocFeeFactory(company=self.seko.seko_department)
        TokushoFactory(company=self.seko.seko_department)
        ChobunDaishiFactory(company=self.seko.seko_department)
        for _ in range(4):
            hoyo_seko: HoyoSeko = HoyoSekoFactory(seko=self.seko)
            HoyoScheduleFactory(hoyo_seko=hoyo_seko)
            HoyoScheduleFactory(hoyo_seko=hoyo_seko)
            HoyoScheduleFactory(hoyo_seko=hoyo_seko)
        seko_af: SekoAf = SekoAfFactory(seko=self.seko)
        af_group: AfGroup = AfGroupFactory()
        af_wish_1: AfWish = AfWishFactory(
            seko_af=seko_af, af_type=AfterFollowFactory(af_group=af_group, display_num=2)
        )
        AfActivityDetailFactory(af_wish=af_wish_1)
        AfActivityDetailFactory(af_wish=af_wish_1)
        AfActivityDetailFactory(af_wish=af_wish_1)
        AfActivityDetailFactory(af_wish=af_wish_1)
        af_wish_2: AfWish = AfWishFactory(
            seko_af=seko_af, af_type=AfterFollowFactory(af_group=af_group, display_num=1)
        )
        self.af_activity_sample = AfActivityDetailFactory(af_wish=af_wish_2)
        AfActivityDetailFactory(af_wish=af_wish_2)
        AfWishFactory(
            seko_af=seko_af, af_type=AfterFollowFactory(af_group=af_group, display_num=3)
        )
        self.af_wish_sample = af_wish_2

        today: date = timezone.localdate()
        AdvertiseFactory(
            company=self.seko.seko_company,
            begin_date=today + relativedelta(days=-2),
            end_date=today + relativedelta(days=-1),
        )
        AdvertiseFactory(
            company=self.seko.seko_company,
            begin_date=today + relativedelta(days=-1),
            end_date=today + relativedelta(days=1),
        )
        AdvertiseFactory(
            company=self.seko.seko_company,
            begin_date=today,
            end_date=today + relativedelta(days=2),
        )
        AdvertiseFactory(
            company=self.seko.seko_company, begin_date=today, end_date=today, del_flg=True
        )
        AdvertiseFactory(
            company=self.seko.seko_company,
            begin_date=today + relativedelta(days=1),
            end_date=today + relativedelta(days=3),
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_detail_succeed(self) -> None:
        """施行詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.seko.id)
        self.assertIsNotNone(record['fuho_site_admission_ts'])
        self.assertEqual(type(record['seko_style']), dict)

        moshu_dict: Dict = record['moshu']
        self.assertIsNone(moshu_dict['agree_ts'])
        self.assertEqual(moshu_dict['soke_site_del_flg'], self.seko.moshu.soke_site_del_flg)
        self.assertEqual(moshu_dict['mail_flg'], self.seko.moshu.mail_flg)

        # ソート順
        prev_num = -1
        for kojin_dict in record['kojin']:
            self.assertGreaterEqual(kojin_dict['kojin_num'], prev_num)
            prev_num = kojin_dict['kojin_num']

        prev_num = -1
        for schedule_dict in record['schedules']:
            self.assertGreaterEqual(schedule_dict['display_num'], prev_num)
            prev_num = schedule_dict['display_num']

        prev_num = -1
        for album_dict in record['albums']:
            self.assertGreaterEqual(album_dict['display_num'], prev_num)
            prev_num = album_dict['display_num']

        items: List[Dict] = record['items']
        self.assertEqual(len(items), 2)
        self.assertSetEqual(
            set([rec['item']['id'] for rec in items]),
            set([rec.item.pk for rec in self.seko.items.all()]),
        )
        services: List[Dict] = record['services']
        self.assertEqual(len(services), 2)
        self.assertSetEqual(
            set([rec['hall_service']['id'] for rec in services]),
            set([rec.hall_service.pk for rec in self.seko.services.all()]),
        )
        kakegami: Dict = record['henrei_kakegami']
        self.assertEqual(kakegami['omotegaki'], self.seko.henrei_kakegami.omotegaki)
        self.assertEqual(kakegami['mizuhiki'], self.seko.henrei_kakegami.mizuhiki)

        seko_company: Dict = record['seko_company']
        self.assertEqual(seko_company['id'], self.seko.seko_company.pk)
        self.assertEqual(seko_company['base_name'], self.seko.seko_company.base_name)
        self.assertEqual(
            seko_company['tokusho']['responsible_name'],
            self.seko.seko_company.tokusho.responsible_name,
        )
        self.assertEqual(
            seko_company['focfee']['monthly_fee'], self.seko.seko_company.focfee.monthly_fee
        )
        self.assertEqual(len(seko_company['chobun_daishi_masters']), 2)
        self.assertLess(
            seko_company['chobun_daishi_masters'][0]['id'],
            seko_company['chobun_daishi_masters'][1]['id'],
        )
        self.assertEqual(len(seko_company['advertises']), 2)

        seko_department: Dict = record['seko_department']
        self.assertEqual(seko_department['id'], self.seko.seko_department.pk)
        self.assertEqual(seko_department['base_name'], self.seko.seko_department.base_name)
        self.assertEqual(
            seko_department['tokusho']['responsible_name'],
            self.seko.seko_department.tokusho.responsible_name,
        )
        self.assertEqual(
            seko_department['focfee']['monthly_fee'], self.seko.seko_department.focfee.monthly_fee
        )
        self.assertEqual(len(seko_department['chobun_daishi_masters']), 1)
        self.assertEqual(
            seko_department['chobun_daishi_masters'][0]['id'],
            self.seko.seko_department.chobun_daishi_masters.first().pk,
        )

        self.assertEqual(len(record['hoyo_seko']), 4)

        self.assertEqual(record['seko_af']['contact_way']['id'], self.seko.seko_af.contact_way.pk)
        self.assertEqual(
            record['seko_af']['contact_way']['contact_way'],
            self.seko.seko_af.contact_way.contact_way,
        )
        self.assertEqual(record['seko_af']['note'], self.seko.seko_af.note)

        self.assertEqual(len(record['seko_af']['wishes']), 3)
        self.assertEqual(record['seko_af']['wishes'][0]['id'], self.af_wish_sample.pk)
        self.assertEqual(
            record['seko_af']['wishes'][0]['af_type']['id'], self.af_wish_sample.af_type.pk
        )
        self.assertEqual(
            record['seko_af']['wishes'][0]['af_type']['name'], self.af_wish_sample.af_type.name
        )
        self.assertEqual(
            record['seko_af']['wishes'][0]['answered_flg'], self.af_wish_sample.answered_flg
        )
        self.assertEqual(
            record['seko_af']['wishes'][0]['proposal_status'], self.af_wish_sample.proposal_status
        )
        self.assertEqual(
            record['seko_af']['wishes'][0]['order_status'], self.af_wish_sample.order_status
        )
        self.assertEqual(
            record['seko_af']['wishes'][0]['order_date'],
            self.af_wish_sample.order_date.isoformat(),
        )
        self.assertEqual(
            record['seko_af']['wishes'][0]['order_chance'], self.af_wish_sample.order_chance
        )

        self.assertEqual(len(record['seko_af']['wishes'][0]['activities']), 2)
        self.assertEqual(
            record['seko_af']['wishes'][0]['activities'][0]['id'], self.af_activity_sample.pk
        )
        self.assertEqual(
            record['seko_af']['wishes'][0]['activities'][0]['activity_ts'],
            self.af_activity_sample.activity_ts.astimezone(tz).isoformat(),
        )
        self.assertEqual(
            record['seko_af']['wishes'][0]['activities'][0]['activity'],
            self.af_activity_sample.activity,
        )

    def test_seko_detail_failed_with_notfound(self) -> None:
        """施行詳細は無効になった施行を返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_seko_detail_succeed_without_auth(self) -> None:
        """施行詳細取得APIは認証がなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.seko.id)

    def test_seko_detail_ignore_disabled_hoyoseko(self) -> None:
        """施行詳細APIは無効化された法要施行を返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        correct_hoyo_seko: Optional[HoyoSeko] = None
        for idx, hoyo_seko in enumerate(self.seko.hoyo_seko.all()):
            if idx == 0:
                correct_hoyo_seko = hoyo_seko
            else:
                hoyo_seko.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(len(record['hoyo_seko']), 1)
        record_hoyo_seko: Dict = record['hoyo_seko'][0]
        self.assertEqual(record_hoyo_seko['id'], correct_hoyo_seko.pk)
        self.assertEqual(record_hoyo_seko['hoyo_name'], correct_hoyo_seko.hoyo_name)
        self.assertEqual(
            record_hoyo_seko['hoyo_planned_date'], correct_hoyo_seko.hoyo_planned_date.isoformat()
        )
        self.assertEqual(
            record_hoyo_seko['hoyo_activity_date'],
            correct_hoyo_seko.hoyo_activity_date.isoformat(),
        )
        self.assertEqual(record_hoyo_seko['begin_time'], correct_hoyo_seko.begin_time.isoformat())
        self.assertEqual(record_hoyo_seko['end_time'], correct_hoyo_seko.end_time.isoformat())
        self.assertEqual(len(record_hoyo_seko['schedules']), 3)


class SekoApproveViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko: Seko = SekoFactory(fuho_site_admission_ts=None)
        MoshuFactory(seko=self.seko)
        KojinFactory(seko=self.seko, kojin_num=2)
        KojinFactory(seko=self.seko, kojin_num=1)
        SekoScheduleFactory(seko=self.seko, display_num=3)
        SekoScheduleFactory(seko=self.seko, display_num=4)
        SekoScheduleFactory(seko=self.seko, display_num=1)
        SekoScheduleFactory(seko=self.seko, display_num=2)
        SekoVideoFactory(seko=self.seko)
        SekoVideoFactory(seko=self.seko)
        SekoVideoFactory(seko=self.seko)
        SekoAlbumFactory(seko=self.seko, display_num=3)
        SekoAlbumFactory(seko=self.seko, display_num=5)
        SekoAlbumFactory(seko=self.seko, display_num=2)
        SekoAlbumFactory(seko=self.seko, display_num=1)
        SekoAlbumFactory(seko=self.seko, display_num=4)
        SekoItemFactory(seko=self.seko)
        SekoItemFactory(seko=self.seko)
        SekoServiceFactory(seko=self.seko)
        SekoServiceFactory(seko=self.seko)
        HenreihinKakegamiFactory(seko=self.seko)
        self.hoyo_1: Hoyo = HoyoFactory(
            company=self.seko.seko_company, style=self.seko.seko_style.hoyo_style
        )
        self.hoyo_2: Hoyo = HoyoFactory(
            company=self.seko.seko_company, style=self.seko.seko_style.hoyo_style
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_approve_success(self) -> None:
        """施行を承認する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:seko_approve', kwargs={'pk': self.seko.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.seko.id)
        self.assertIsNotNone(record['fuho_site_admission_ts'])

        # 法要施行レコードが自動的に作られる
        self.assertEqual(len(record['hoyo_seko']), 2)
        for hoyoseko, hoyo in zip(record['hoyo_seko'], [self.hoyo_1, self.hoyo_2]):
            self.assertEqual(hoyoseko['hoyo']['id'], hoyo.pk)
            self.assertEqual(hoyoseko['hoyo_name'], hoyo.name)
            self.assertIsNotNone(hoyoseko['hoyo_planned_date'])
            self.assertIsNone(hoyoseko['hoyo_activity_date'])
            self.assertIsNone(hoyoseko['begin_time'])
            self.assertIsNone(hoyoseko['end_time'])
            self.assertIsNone(hoyoseko['hall'])
            self.assertTrue(hoyoseko['dine_flg'])
            self.assertEqual(hoyoseko['seshu_name'], self.seko.moshu.name)
            self.assertEqual(
                hoyoseko['kojin_seshu_relationship'], self.seko.moshu.kojin_moshu_relationship
            )
            self.assertEqual(hoyoseko['zip_code'], self.seko.moshu.zip_code)
            self.assertEqual(hoyoseko['prefecture'], self.seko.moshu.prefecture)
            self.assertEqual(hoyoseko['address_1'], self.seko.moshu.address_1)
            self.assertEqual(hoyoseko['address_2'], self.seko.moshu.address_2)
            self.assertEqual(hoyoseko['address_3'], self.seko.moshu.address_3)
            self.assertEqual(hoyoseko['tel'], self.seko.moshu.tel)
            self.assertIsNone(hoyoseko['hoyo_sentence'])
            self.assertIsNone(hoyoseko['shishiki_name'])
            self.assertEqual(hoyoseko['reply_limit_date'], date(2999, 12, 31).isoformat())
            self.assertEqual(hoyoseko['hoyo_site_end_date'], date(2999, 12, 31).isoformat())
            self.assertIsNone(hoyoseko['note'])
            self.assertIsNone(hoyoseko['staff'])
            self.assertFalse(hoyoseko['del_flg'])

    def test_seko_approve_failed_with_notfound(self) -> None:
        """施行承認APIが存在しない施行を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build(fuho_site_admission_ts=None)

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:seko_approve', kwargs={'pk': non_saved_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_seko_approve_failed_with_disabled_seko(self) -> None:
        """施行承認APIが無効になった施行を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:seko_approve', kwargs={'pk': self.seko.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_seko_approve_failed_by_already_approved(self) -> None:
        """施行承認APIが既に承認済みの施行を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko.fuho_site_admission_ts = timezone.localtime()
        self.seko.save()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:seko_approve', kwargs={'pk': self.seko.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])


class SekoUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko_company = BaseFactory(base_type=Base.OrgType.COMPANY)
        seko_department = BaseFactory(base_type=Base.OrgType.DEPARTMENT, parent=self.seko_company)

        self.seko = SekoFactory(seko_company=self.seko_company)
        self.moshu = MoshuFactory(seko=self.seko)
        self.kojin_1 = KojinFactory(seko=self.seko)
        self.kojin_2 = KojinFactory(seko=self.seko)

        hall1 = BaseFactory(base_type=Base.OrgType.HALL)
        hall2 = BaseFactory(base_type=Base.OrgType.HALL)
        self.schedule_1 = SekoScheduleFactory(seko=self.seko, hall=hall1)
        self.schedule_2 = SekoScheduleFactory(seko=self.seko, hall=hall2)
        self.schedule_3 = SekoScheduleFactory(seko=self.seko, hall=hall1)
        self.schedule_4 = SekoScheduleFactory(seko=self.seko, hall=hall2)

        self.video_1 = SekoVideoFactory(seko=self.seko)
        self.video_2 = SekoVideoFactory(seko=self.seko)
        self.video_3 = SekoVideoFactory(seko=self.seko)

        self.item_1 = SekoItemFactory(seko=self.seko)
        self.item_2 = SekoItemFactory(seko=self.seko)

        self.service_1 = SekoServiceFactory(seko=self.seko)
        self.service_2 = SekoServiceFactory(seko=self.seko)

        self.kakegami = HenreihinKakegamiFactory(seko=self.seko)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=seko_department)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        dummyfile: str = (
            'data:image/gif;base64,R0lGODlhAQABAIAAAP///////yH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=='
        )

        order_staff = StaffFactory()
        seko_staff = StaffFactory()
        af_staff = StaffFactory()
        seko_company = BaseFactory(base_type=Base.OrgType.COMPANY)
        seko_department = BaseFactory(base_type=Base.OrgType.DEPARTMENT)
        seko_style = SekoStyleFactory()
        new_seko_data: Seko = SekoFactory.build(
            order_staff=order_staff,
            seko_staff=seko_staff,
            af_staff=af_staff,
            seko_company=seko_company,
            seko_department=seko_department,
            seko_style=seko_style,
        )

        new_moshu_data = MoshuFactory.build(seko=new_seko_data)
        moshu: Dict = {
            'name': new_moshu_data.name,
            'kana': new_moshu_data.kana,
            'kojin_moshu_relationship': new_moshu_data.kojin_moshu_relationship,
            'zip_code': new_moshu_data.zip_code,
            'prefecture': new_moshu_data.prefecture,
            'address_1': new_moshu_data.address_1,
            'address_2': new_moshu_data.address_2,
            'address_3': new_moshu_data.address_3,
            'tel': new_moshu_data.tel,
            'mobile_num': new_moshu_data.mobile_num,
            'mail_address': new_moshu_data.mail_address,
            'password': new_moshu_data.password,
            'agree_ts': new_moshu_data.agree_ts,
            'soke_site_del_request_flg': new_moshu_data.soke_site_del_request_flg,
            'soke_site_del_flg': new_moshu_data.soke_site_del_flg,
            'mail_flg': new_moshu_data.mail_flg,
        }
        kojin: List[Dict] = []
        new_kojin_data_1 = self.kojin_1
        new_kojin_data_1.name = 'updated'
        new_kojin_data_2 = KojinFactory.build(seko=new_seko_data, name='added')
        for kojin_data in [new_kojin_data_1, new_kojin_data_2]:
            kojin.append(
                {
                    'kojin_num': kojin_data.kojin_num,
                    'name': kojin_data.name,
                    'kana': kojin_data.kana,
                    'kaimyo': kojin_data.kaimyo,
                    'iei_file_name': dummyfile,
                    'moshu_kojin_relationship': kojin_data.moshu_kojin_relationship,
                    'birth_date': kojin_data.birth_date.strftime('%Y-%m-%d'),
                    'death_date': kojin_data.death_date.strftime('%Y-%m-%d'),
                    'age_kbn': kojin_data.age_kbn,
                    'age': kojin_data.age,
                    'note': kojin_data.note,
                }
            )

        schedules: List[Dict] = []
        new_schedule_data_1 = self.schedule_1
        new_schedule_data_1.schedule_name = 'updated'
        new_schedule_data_1.schedule = ScheduleFactory()
        new_schedule_data_1.hall = BaseFactory(base_type=Base.OrgType.HALL)
        new_schedule_data_2 = SekoScheduleFactory.build(
            seko=new_seko_data,
            schedule=ScheduleFactory(),
            schedule_name='added',
            hall=BaseFactory(base_type=Base.OrgType.HALL),
        )
        for schedule_data in [new_schedule_data_1, new_schedule_data_2]:
            schedules.append(
                {
                    'schedule': schedule_data.schedule.id,
                    'schedule_name': schedule_data.schedule_name,
                    'hall': schedule_data.hall.id,
                    'hall_name': schedule_data.hall_name,
                    'hall_zip_code': schedule_data.hall_zip_code,
                    'hall_address': schedule_data.hall_address,
                    'hall_tel': schedule_data.hall_tel,
                    'schedule_date': schedule_data.schedule_date.strftime('%Y-%m-%d'),
                    'begin_time': schedule_data.begin_time,
                    'end_time': schedule_data.end_time,
                    'display_num': schedule_data.display_num,
                }
            )

        videos: List[Dict] = []
        new_video_data_1 = self.video_1
        new_video_data_1.title = 'updated'
        new_video_data_2 = SekoVideoFactory.build(seko=new_seko_data, title='added')
        for video in [new_video_data_1, new_video_data_2]:
            videos.append(
                {
                    'title': video.title,
                    'live_begin_ts': video.live_begin_ts,
                    'live_end_ts': video.live_end_ts,
                    'delivery_end_ts': video.delivery_end_ts,
                    'youtube_code': video.youtube_code,
                }
            )

        new_items: List[Dict] = []
        new_item_data_1 = self.item_1
        new_item_data_2 = SekoItemFactory.build(seko=new_seko_data, item=ItemFactory())
        new_item_data_2.id = None
        for sekoitem in [new_item_data_1, new_item_data_2]:
            new_items.append(
                {
                    'id': sekoitem.id,
                    'item': sekoitem.item.pk,
                }
            )

        services: List[Dict] = []
        new_service_data_1 = self.service_1
        new_service_data_2 = SekoServiceFactory.build(
            seko=new_seko_data, hall_service=ServiceFactory()
        )
        new_service_data_2.id = None
        for service in [new_service_data_1, new_service_data_2]:
            services.append(
                {
                    'id': service.id,
                    'hall_service': service.hall_service.pk,
                    'limit_ts': service.limit_ts,
                    'henreihin_select_flg': service.henreihin_select_flg,
                }
            )

        new_kakegami = HenreihinKakegamiFactory.build(seko=new_seko_data)
        kakegami_data = {
            'omotegaki': new_kakegami.omotegaki,
            'mizuhiki': new_kakegami.mizuhiki,
            'package_type_name': new_kakegami.package_type_name,
            'okurinushi_name': new_kakegami.okurinushi_name,
        }

        return {
            'order_staff': new_seko_data.order_staff.id,
            'seko_staff': new_seko_data.seko_staff.id,
            'af_staff': new_seko_data.af_staff.id,
            'seko_company': new_seko_data.seko_company.id,
            'seko_department': new_seko_data.seko_department.id,
            'death_date': new_seko_data.death_date.strftime('%Y-%m-%d'),
            'seko_date': new_seko_data.seko_date.strftime('%Y-%m-%d'),
            'soke_name': new_seko_data.soke_name,
            'soke_kana': new_seko_data.soke_kana,
            'seko_style': new_seko_data.seko_style.id,
            'seko_style_name': new_seko_data.seko_style_name,
            'fuho_site_admission_ts': new_seko_data.fuho_site_admission_ts.isoformat(),
            'fuho_site_end_date': new_seko_data.fuho_site_end_date.strftime('%Y-%m-%d'),
            'fuho_sentence': new_seko_data.fuho_sentence,
            'fuho_contact_name': new_seko_data.fuho_contact_name,
            'fuho_contact_tel': new_seko_data.fuho_contact_tel,
            'henreihin_rate': new_seko_data.henreihin_rate,
            'invoice_file_name': b64encode(new_seko_data.invoice_file_name.read()),
            'note': new_seko_data.note,
            'moshu': moshu,
            'kojin': kojin,
            'schedules': schedules,
            'videos': videos,
            'items': new_items,
            'services': services,
            'henrei_kakegami': kakegami_data,
        }

    def test_seko_update_succeed(self) -> None:
        """施行を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['moshu']['agree_ts'] = timezone.now()
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['soke_name'], params['soke_name'])
        self.assertEqual(record['soke_kana'], params['soke_kana'])
        self.assertIsNotNone(record['fuho_site_admission_ts'])
        moshu: Dict = record['moshu']
        self.assertEqual(moshu['name'], params['moshu']['name'])
        self.assertEqual(moshu['kana'], params['moshu']['kana'])
        self.assertIsNotNone(moshu['agree_ts'])
        self.assertEqual(moshu['soke_site_del_flg'], params['moshu']['soke_site_del_flg'])
        self.assertEqual(moshu['mail_flg'], params['moshu']['mail_flg'])
        kojin: List[Dict] = record['kojin']
        self.assertEqual(len(kojin), 2)
        self.assertSetEqual(
            set([rec['name'] for rec in kojin]), set([rec['name'] for rec in params['kojin']])
        )
        schedules: List[Dict] = record['schedules']
        self.assertEqual(len(schedules), 2)
        self.assertSetEqual(
            set([rec['schedule_name'] for rec in schedules]),
            set([rec['schedule_name'] for rec in params['schedules']]),
        )
        videos: List[Dict] = record['videos']
        self.assertEqual(len(videos), 2)
        self.assertSetEqual(
            set([rec['title'] for rec in videos]),
            set([rec['title'] for rec in params['videos']]),
        )
        items: List[Dict] = record['items']
        self.assertEqual(len(items), 2)
        self.assertSetEqual(
            set([rec['item']['id'] for rec in items]),
            set([rec['item'] for rec in params['items']]),
        )
        self.assertIsNotNone(items[0]['created_at'])
        self.assertIsNotNone(items[0]['updated_at'])
        services: List[Dict] = record['services']
        self.assertEqual(len(services), 2)
        self.assertSetEqual(
            set([rec['hall_service']['id'] for rec in services]),
            set([rec['hall_service'] for rec in params['services']]),
        )
        self.assertIsNotNone(services[0]['created_at'])
        self.assertIsNotNone(services[0]['updated_at'])
        kakegami: Dict = record['henrei_kakegami']
        self.assertEqual(kakegami['omotegaki'], params['henrei_kakegami']['omotegaki'])
        self.assertEqual(kakegami['mizuhiki'], params['henrei_kakegami']['mizuhiki'])
        self.assertIsNotNone(kakegami['created_at'])
        self.assertIsNotNone(kakegami['updated_at'])

    def test_seko_update_succeed_with_some_empty_field(self) -> None:
        """施行の更新は請求書ファイル、喪主のパスワード、故人の遺影ファイルが空文字でも成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['moshu']['password'] = ''
        for a_kojin in params['kojin']:
            a_kojin['iei_file_name'] = ''
        params['invoice_file_name'] = ''
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['soke_name'], params['soke_name'])
        self.assertEqual(record['soke_kana'], params['soke_kana'])
        self.assertIsNotNone(record['fuho_site_admission_ts'])
        moshu: Dict = record['moshu']
        self.assertEqual(moshu['name'], params['moshu']['name'])
        self.assertEqual(moshu['kana'], params['moshu']['kana'])
        kojin: List[Dict] = record['kojin']
        self.assertEqual(len(kojin), 2)
        self.assertSetEqual(
            set([rec['name'] for rec in kojin]), set([rec['name'] for rec in params['kojin']])
        )
        schedules: List[Dict] = record['schedules']
        self.assertEqual(len(schedules), 2)
        self.assertSetEqual(
            set([rec['schedule_name'] for rec in schedules]),
            set([rec['schedule_name'] for rec in params['schedules']]),
        )
        videos: List[Dict] = record['videos']
        self.assertEqual(len(videos), 2)
        self.assertSetEqual(
            set([rec['title'] for rec in videos]),
            set([rec['title'] for rec in params['videos']]),
        )

    def test_seko_update_succeed_with_some_field_are_none(self) -> None:
        """施行の更新は請求書ファイル、故人の遺影ファイルがNoneでも成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        for a_kojin in params['kojin']:
            a_kojin['iei_file_name'] = None
        params['invoice_file_name'] = None
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertIsNone(record['invoice_file_name'])
        for kojin in record['kojin']:
            self.assertIsNone(kojin['iei_file_name'])

    def test_seko_update_succeed_without_some_field(self) -> None:
        """施行の更新は請求書ファイル、喪主のパスワード、故人の遺影ファイルの項目がなくても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['moshu'].pop('password')
        for a_kojin in params['kojin']:
            a_kojin.pop('iei_file_name')
        params.pop('invoice_file_name')
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['soke_name'], params['soke_name'])
        self.assertEqual(record['soke_kana'], params['soke_kana'])
        self.assertIsNotNone(record['fuho_site_admission_ts'])
        moshu: Dict = record['moshu']
        self.assertEqual(moshu['name'], params['moshu']['name'])
        self.assertEqual(moshu['kana'], params['moshu']['kana'])
        kojin: List[Dict] = record['kojin']
        self.assertEqual(len(kojin), 2)
        self.assertSetEqual(
            set([rec['name'] for rec in kojin]), set([rec['name'] for rec in params['kojin']])
        )
        schedules: List[Dict] = record['schedules']
        self.assertEqual(len(schedules), 2)
        self.assertSetEqual(
            set([rec['schedule_name'] for rec in schedules]),
            set([rec['schedule_name'] for rec in params['schedules']]),
        )
        videos: List[Dict] = record['videos']
        self.assertEqual(len(videos), 2)
        self.assertSetEqual(
            set([rec['title'] for rec in videos]),
            set([rec['title'] for rec in params['videos']]),
        )

    def test_seko_update_not_require_soke_site_del_flg_and_mail_flg(self) -> None:
        """施行更新API(PUT)は葬家専用ページ削除と案内メールフラグを入力必須にしない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        del params['moshu']['soke_site_del_flg']
        del params['moshu']['mail_flg']
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        moshu: Dict = record['moshu']
        self.assertEqual(moshu['soke_site_del_flg'], self.moshu.soke_site_del_flg)
        self.assertEqual(moshu['mail_flg'], self.moshu.mail_flg)

    def test_seko_update_can_remove_videos(self) -> None:
        """施行更新API(PUT)で施行ビデオリストが空の場合は既存のビデオを削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['videos'] = []
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['soke_name'], params['soke_name'])
        self.assertEqual(record['soke_kana'], params['soke_kana'])
        self.assertIsNotNone(record['fuho_site_admission_ts'])
        moshu: Dict = record['moshu']
        self.assertEqual(moshu['name'], params['moshu']['name'])
        self.assertEqual(moshu['kana'], params['moshu']['kana'])
        kojin: List[Dict] = record['kojin']
        self.assertEqual(len(kojin), 2)
        self.assertSetEqual(
            set([rec['name'] for rec in kojin]), set([rec['name'] for rec in params['kojin']])
        )
        schedules: List[Dict] = record['schedules']
        self.assertEqual(len(schedules), 2)
        self.assertSetEqual(
            set([rec['schedule_name'] for rec in schedules]),
            set([rec['schedule_name'] for rec in params['schedules']]),
        )
        videos: List[Dict] = record['videos']
        self.assertEqual(len(videos), 0)

    def test_seko_update_remains_videos_on_patch(self) -> None:
        """施行更新API(PATCH)で施行ビデオリストの項目がない場合は既存のビデオは残る"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params.pop('videos')
        response = self.api_client.patch(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['soke_name'], params['soke_name'])
        self.assertEqual(record['soke_kana'], params['soke_kana'])
        self.assertIsNotNone(record['fuho_site_admission_ts'])
        moshu: Dict = record['moshu']
        self.assertEqual(moshu['name'], params['moshu']['name'])
        self.assertEqual(moshu['kana'], params['moshu']['kana'])
        kojin: List[Dict] = record['kojin']
        self.assertEqual(len(kojin), 2)
        self.assertSetEqual(
            set([rec['name'] for rec in kojin]), set([rec['name'] for rec in params['kojin']])
        )
        schedules: List[Dict] = record['schedules']
        self.assertEqual(len(schedules), 2)
        self.assertSetEqual(
            set([rec['schedule_name'] for rec in schedules]),
            set([rec['schedule_name'] for rec in params['schedules']]),
        )
        videos: List[Dict] = record['videos']
        self.assertEqual(len(videos), 3)

    def test_seko_update_fills_moshu(self) -> None:
        """施行を更新する際、喪主の登録がない場合はレコードを登録する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 登録済みの喪主を一旦削除する
        self.moshu.delete()
        self.seko.refresh_from_db()
        self.assertFalse(hasattr(self.seko, 'moshu'))

        params: Dict = self.basic_params()
        params['kojin'] = []
        params['schedules'] = []
        params['videos'] = []
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['soke_name'], params['soke_name'])
        self.assertEqual(record['soke_kana'], params['soke_kana'])
        self.assertEqual(type(record['seko_style']), int)
        self.assertIsNotNone(record['fuho_site_admission_ts'])
        moshu: Dict = record['moshu']
        self.assertEqual(moshu['name'], params['moshu']['name'])
        self.assertEqual(moshu['kana'], params['moshu']['kana'])

    def test_seko_update_fills_kakegami(self) -> None:
        """施行を更新する際、返礼品掛け紙の登録がない場合はレコードを登録する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 登録済みの返礼品掛け紙を一旦削除する
        self.kakegami.delete()
        self.seko.refresh_from_db()
        self.assertFalse(hasattr(self.seko, 'henrei_kakegami'))

        params: Dict = self.basic_params()
        params['kojin'] = []
        params['schedules'] = []
        params['videos'] = []
        params['items'] = []
        params['services'] = []
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['soke_name'], params['soke_name'])
        self.assertEqual(record['soke_kana'], params['soke_kana'])
        self.assertEqual(type(record['seko_style']), int)
        self.assertIsNotNone(record['fuho_site_admission_ts'])
        kakegami: Dict = record['henrei_kakegami']
        self.assertEqual(kakegami['omotegaki'], params['henrei_kakegami']['omotegaki'])
        self.assertEqual(kakegami['mizuhiki'], params['henrei_kakegami']['mizuhiki'])

    def test_seko_update_tel_or_mobile_num_is_required(self) -> None:
        """施行更新APIでは喪主の電話番号か携帯電話番号のいずれかは入力必須"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 電話番号が空文字でもOK
        params: Dict = self.basic_params()
        params['seko_company'] = self.seko_company.id
        params['moshu']['tel'] = ''

        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        moshu: Dict = record['moshu']
        self.assertEqual(moshu['tel'], '')
        self.assertEqual(moshu['mobile_num'], params['moshu']['mobile_num'])

        # 携帯電話番号が空文字でもOK
        params: Dict = self.basic_params()
        params['seko_company'] = self.seko_company.id
        params['moshu']['mobile_num'] = ''

        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        moshu: Dict = record['moshu']
        self.assertEqual(moshu['tel'], params['moshu']['tel'])
        self.assertEqual(moshu['mobile_num'], '')

        # 電話番号と携帯電話番号の両方が空文字だとNG
        # params: Dict = self.basic_params()
        # params['moshu']['tel'] = ''
        # params['moshu']['mobile_num'] = ''

        # response = self.api_client.put(
        #     reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
        #     data=params,
        #     format='json',
        # )
        # self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # record: Dict = response.json()
        # self.assertIsNotNone(record['moshu']['tel'])

    def test_seko_update_succeed_without_service_limit_ts(self) -> None:
        """施行更新APIは施行サービスのlimit_tsがNoneでも、項目がなくても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['services'][0]['limit_ts'] = None
        del params['services'][1]['limit_ts']
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        for service in record['services']:
            self.assertIsNone(service['limit_ts'])

    def test_seko_update_failed_without_auth(self) -> None:
        """施行更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_seko_update_failed_by_another_company_staff(self) -> None:
        """施行更新APIが他の会社に所属するスタッフによる操作を拒否する"""
        another_copmany_staff = StaffFactory()
        refresh = RefreshToken.for_user(another_copmany_staff)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])


class SekoDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko_company = BaseFactory(base_type=Base.OrgType.COMPANY)
        seko_department = BaseFactory(base_type=Base.OrgType.DEPARTMENT, parent=self.seko_company)

        self.seko = SekoFactory(seko_company=self.seko_company, del_flg=False)
        MoshuFactory(seko=self.seko)
        self.kojin_1 = KojinFactory(seko=self.seko, del_flg=False)
        self.kojin_2 = KojinFactory(seko=self.seko, del_flg=False)

        hall1 = BaseFactory(base_type=Base.OrgType.HALL)
        hall2 = BaseFactory(base_type=Base.OrgType.HALL)
        SekoScheduleFactory(seko=self.seko, hall=hall1)
        SekoScheduleFactory(seko=self.seko, hall=hall2)
        SekoScheduleFactory(seko=self.seko, hall=hall1)
        SekoScheduleFactory(seko=self.seko, hall=hall2)

        SekoVideoFactory(seko=self.seko)
        SekoVideoFactory(seko=self.seko)
        SekoVideoFactory(seko=self.seko)

        SekoItemFactory(seko=self.seko)
        SekoItemFactory(seko=self.seko)
        SekoServiceFactory(seko=self.seko)
        SekoServiceFactory(seko=self.seko)
        HenreihinKakegamiFactory(seko=self.seko)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=seko_department)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_delete_succeed(self) -> None:
        """施行を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 施行のdel_flgがTrueになるだけ
        db_seko: Seko = Seko.objects.get(pk=self.seko.id)
        self.assertTrue(db_seko.del_flg)
        for kojin_rec in db_seko.kojin.all():
            self.assertFalse(kojin_rec.del_flg)

    def test_seko_delete_failed_by_notfound(self) -> None:
        """施行削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('seko:seko_detail', kwargs={'pk': non_saved_seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_delete_failed_by_already_deleted(self) -> None:
        """施行削除APIが論理削除済みの施行を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko.del_flg = True
        self.seko.save()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_delete_failed_without_auth(self) -> None:
        """施行削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_seko_delete_failed_by_another_company_staff(self) -> None:
        """施行削除APIが他の会社に所属するスタッフによる操作を拒否する"""
        another_copmany_staff = StaffFactory()
        refresh = RefreshToken.for_user(another_copmany_staff)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
