
<div class="container">
  <div class="inner" *ngIf="!loading">
    <div class="contents" *ngIf="message">
      <h2>領収書ダウンロード</h2>
      <div class="message">{{message}}</div>
    </div>
    <div class="contents" *ngIf="!message">
      <h2>領収書ダウンロード</h2>
      <div class="input-area">
        <div class="line">
          <div class="label required">宛名</div>
          <div class="input atena" #atenaEm [class.error]="isErrorField(atenaEm)">
            <input type="text" maxlength="20" [(ngModel)]="atena">
          </div>
        </div>
      </div>
      <div class="message">{{message2}}</div>
    </div>
    <div class="button-area" *ngIf="!message">
      <a class="button grey" (click)="downloadReceipt()">ダウンロード</a>
    </div>
  </div>
</div>

