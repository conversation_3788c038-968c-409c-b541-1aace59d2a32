from io import BytesIO
from operator import itemgetter
from typing import Dict, List

from django.core.mail import EmailMessage
from django.db import transaction
from django.db.models import Prefetch, Q, QuerySet
from django.http import FileResponse
from django.template.loader import get_template
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters
from rest_framework import generics, status, views
from rest_framework.exceptions import NotFound, PermissionDenied, ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from weasyprint import HTML

from bases.models import Base
from henrei.models import Hen<PERSON>ihinKoden, HenreihinKumotsu, OrderHenreihin
from henrei.serializers import (
    HenreiCreateSerializer,
    HenreiKodenCreateSerializer,
    HenreiKodenSerializer,
    HenreiKumotsuCreateSerializer,
    HenreiKumotsuSerializer,
    HenreiListSerializer,
    HenreiSerializer,
    OrderHenreiListSerializer,
    OrderHenreiUpdateStatusSerializer,
    PlaceHenreiOrderResultSerializer,
    PlaceHenreiOrderSerializer,
)
from orders.models import Entry
from seko.models import Moshu, SekoSchedule
from utils.filters import InListFilter
from utils.permissions import IsMoshu
from utils.view_mixins import AddContextMixin


class HenreiListFilter(filters.FilterSet):
    company_id = filters.NumberFilter(field_name='company_id')
    seko_department = filters.NumberFilter(method='filter_seko_department')
    seko_id = filters.NumberFilter(field_name='seko_id')
    order_status = InListFilter(field_name='order_status')
    hall_id = filters.NumberFilter(field_name='hall_id')
    seko_date = filters.DateFilter(field_name='seko_date')
    supplier_id = filters.NumberFilter(field_name='supplier_id')
    soke_name = filters.CharFilter(field_name='soke_name', lookup_expr='icontains')
    kojin_name = filters.CharFilter(field_name='kojin_name', lookup_expr='icontains')
    moshu_name = filters.CharFilter(field_name='moshu_name', lookup_expr='icontains')
    is_cancel = filters.BooleanFilter(method='filter_is_cancel')

    def filter_is_cancel(self, queryset, name, value):
        if value:
            return queryset.filter(cancel_ts__isnull=False)
        return queryset.filter(cancel_ts__isnull=True)

    def filter_seko_department(self, queryset, name, value):
        departments = Base.objects.filter(pk=value).get_cached_trees()
        if not departments:
            error_message: str = _(
                'Select a valid choice. That choice is not one of the available choices.'
            )
            raise ValidationError({'seko_department': error_message})
        department_ids: List[int] = [
            base.pk for base in departments[0].get_descendants(include_self=True)
        ]
        return queryset.filter(Q(seko_department_id__in=department_ids))


class HenreiList(generics.ListAPIView):

    serializer_class = HenreiListSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):

        henrei_list_columns: List[str] = [
            'henreihin_id',
            'henreihin_type',
            'seko_id',
            'seko_date',
            'entry_id',
            'detail_id',
            'seko_department_id',
            'seko_department_name',
            'hall_name',
            'soke_name',
            'kojin_name',
            'moshu_name',
            'henreihin_name',
            'henreihin_price',
            'supplier_name',
            'order_status',
            'cancel_ts',
            'company_id',
            'hall_id',
            'supplier_id',
        ]
        kumotsu_qs = HenreihinKumotsu.henrei_list.values_list(*henrei_list_columns)
        kumotsu_filter = HenreiListFilter(
            data=self.request.query_params, queryset=kumotsu_qs, request=self.request
        )
        kumotsu_list = list(kumotsu_filter.qs.values())

        koden_qs = HenreihinKoden.henrei_list.values_list(*henrei_list_columns)
        koen_filter = HenreiListFilter(
            data=self.request.query_params, queryset=koden_qs, request=self.request
        )
        koden_list = list(koen_filter.qs.values())

        result = kumotsu_list + koden_list
        result = sorted(result, key=itemgetter('entry_id', 'henreihin_name'))

        return result


class OrderHenreiListFilter(filters.FilterSet):
    seko_company_id = filters.NumberFilter(field_name='seko__seko_company_id')
    seko_department = filters.NumberFilter(method='filter_seko_department')
    supplier_id = filters.NumberFilter(field_name='supplier')
    order_henrei_id = filters.NumberFilter(field_name='pk')
    order_status = InListFilter(field_name='order_status')
    order_ts_from = filters.DateFilter(field_name='order_ts', lookup_expr='date__gte')
    order_ts_to = filters.DateFilter(field_name='order_ts', lookup_expr='date__lte')

    def filter_seko_department(self, queryset, name, value):
        departments = Base.objects.filter(pk=value).get_cached_trees()
        if not departments:
            error_message: str = _(
                'Select a valid choice. That choice is not one of the available choices.'
            )
            raise ValidationError({'seko_department': error_message})
        department_ids: List[int] = [
            base.pk for base in departments[0].get_descendants(include_self=True)
        ]
        return queryset.filter(Q(seko__seko_department__in=department_ids))


class OrderHenreiList(generics.ListAPIView):

    queryset = OrderHenreihin.order_list
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = OrderHenreiListFilter
    serializer_class = OrderHenreiListSerializer
    permission_classes = [IsAuthenticated]


class OrderHenreiCreate(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, format=None):
        serializer = HenreiCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        instance: List[OrderHenreihin] = serializer.save()
        read_serializer = HenreiSerializer(instance)
        return Response(read_serializer.data, status=status.HTTP_201_CREATED)


class OrderHenreiDetail(AddContextMixin, generics.RetrieveUpdateAPIView):

    queryset = OrderHenreihin.objects.prefetch_related(
        Prefetch('henrei_koden', HenreihinKoden.objects.order_by('detail_koden')),
        Prefetch('henrei_kumotsu', HenreihinKumotsu.objects.order_by('detail_kumotsu')),
    )
    serializer_class = HenreiSerializer
    permission_classes = [IsAuthenticated]


class OrderHenreiUpdateStatus(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, format=None):
        serializer = OrderHenreiUpdateStatusSerializer(data=request.data)
        serializer.context['staff'] = request.user
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        instances: List[OrderHenreihin] = serializer.save()
        read_serializer = HenreiSerializer(instances, many=True)
        return Response(read_serializer.data)


class HenreiKodenList(generics.CreateAPIView):

    queryset = HenreihinKoden.objects.all()
    serializer_class = HenreiKodenCreateSerializer
    permission_classes = [IsMoshu]

    def get_serializer_context(self) -> Dict:
        context: Dict = super().get_serializer_context()
        context['moshu'] = self.request.user
        return context


class HenreiKodenDetail(generics.RetrieveUpdateDestroyAPIView):

    queryset = HenreihinKoden.objects.all()
    serializer_class = HenreiKodenSerializer
    permission_classes = [IsAuthenticated]

    def delete(self, request, *args, **kwargs):
        instance: HenreihinKoden = self.get_object()
        if (
            not isinstance(request.user, Moshu)
            or request.user != instance.detail_koden.entry_detail.entry.seko.moshu
        ):
            raise PermissionDenied(_("Allowed only for seko's moshu."))
        if instance.select_status > 1:
            raise PermissionDenied(_('Order is already confirmed.'))
        return super().delete(request, *args, **kwargs)


class HenreiKumotsuList(generics.CreateAPIView):

    queryset = HenreihinKumotsu.objects.all()
    serializer_class = HenreiKumotsuCreateSerializer
    permission_classes = [IsMoshu]

    def get_serializer_context(self) -> Dict:
        context: Dict = super().get_serializer_context()
        context['moshu'] = self.request.user
        return context


class HenreiKumotsuDetail(generics.RetrieveUpdateDestroyAPIView):

    queryset = HenreihinKumotsu.objects.all()
    serializer_class = HenreiKumotsuSerializer
    permission_classes = [IsAuthenticated]

    def delete(self, request, *args, **kwargs):
        instance: HenreihinKumotsu = self.get_object()
        if (
            not isinstance(request.user, Moshu)
            or request.user != instance.detail_kumotsu.entry_detail.entry.seko.moshu
        ):
            raise PermissionDenied(_("Allowed only for seko's moshu."))
        if instance.select_status > 1:
            raise PermissionDenied(_('Order is already confirmed.'))
        return super().delete(request, *args, **kwargs)


def make_order_henrei_pdf(order_henrei: OrderHenreihin, base_url: str) -> BytesIO:
    item_column_list: List[str] = [
        'henreihin_name',
        'henreihin_hinban',
        'entry_zip_code',
        'entry_prefecture',
        'entry_address_1',
        'entry_address_2',
        'entry_address_3',
        'entry_name',
        'entry_tel',
        'entry_id',
        'supplier_id',
    ]

    kumotsu_qs = HenreihinKumotsu.henrei_items.filter(henrei_order=order_henrei).values_list(
        *item_column_list
    )
    koden_qs = HenreihinKoden.henrei_items.filter(henrei_order=order_henrei).values_list(
        *item_column_list
    )

    item_list = list(kumotsu_qs.values()) + list(koden_qs.values())
    item_list = sorted(item_list, key=itemgetter('entry_id', 'henreihin_name'))
    for idx, item in enumerate(item_list, start=1):
        item['new_page'] = idx % 4 == 0

    entry = (
        Entry.objects.select_related(
            'seko', 'seko__seko_company', 'seko__moshu', 'seko__henrei_kakegami'
        )
        .prefetch_related(
            Prefetch(
                'seko__schedules',
                queryset=SekoSchedule.objects.filter(schedule_id=2),
                to_attr='schedule_2',
            )
        )
        .get(pk=item_list[0]['entry_id'])
    )

    html_text = get_template('pdf/henrei.html').render(
        {
            'order_henrei': order_henrei,
            'item_list': item_list,
            'entry': entry,
            'schedule': entry.seko.schedule_2,
            'supplier': order_henrei.supplier,
        }
    )

    buffer = BytesIO()
    HTML(string=html_text, base_url=base_url).write_pdf(buffer)
    buffer.seek(0)

    return buffer


class OrderHenreiPDF(generics.RetrieveAPIView):
    queryset = OrderHenreihin.objects.all()

    def retrieve(self, request, *args, **kwargs):
        order_henrei = self.get_object()

        buffer = make_order_henrei_pdf(
            order_henrei=order_henrei, base_url=request.build_absolute_uri()
        )

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'返礼品発注書-{order_henrei.pk}.pdf',
            content_type='application/pdf',
        )
        return response


class OrderHenreiPDFFax(views.APIView):

    permission_classes = [IsAuthenticated]

    @transaction.atomic
    def post(self, request, pk, format=None):
        order_henrei = OrderHenreiPDF.queryset.filter(Q(pk=pk)).first()
        if not order_henrei:
            raise NotFound(f'No {OrderHenreihin._meta.object_name} matches the given query.')

        order_henrei.set_order_status(2, request.user)
        sender_address = order_henrei.seko.seko_company.tokusho.from_name
        if order_henrei.seko.order_staff.mail_address:
            order_staff_mail_address = [order_henrei.seko.order_staff.mail_address]
        else:
            order_staff_mail_address = []

        if order_henrei.supplier.order_mail_send_flg and order_henrei.supplier.mail_address:
            recipient_address = order_henrei.supplier.mail_address
            mail_body = get_template('mail/order.txt').render(
                {
                    'company': order_henrei.seko.seko_company.base_name,
                }
            )
            message = EmailMessage(
                subject='注文書の送付',
                body=mail_body,
                from_email=sender_address,
                to=[recipient_address],
                bcc=order_staff_mail_address,
            )
        else:
            recipient_address = f'{order_henrei.supplier.fax_digits()}@cl1.faximo.jp'
            message = EmailMessage(
                subject='===<<<',
                body=None,
                from_email=sender_address,
                to=[recipient_address],
            )
        buffer = make_order_henrei_pdf(
            order_henrei=order_henrei, base_url=request.build_absolute_uri()
        )
        message.attach(
            filename=f'order_henreihin_{pk}.pdf',
            content=buffer.getvalue(),
            mimetype='application/pdf',
        )
        message.send(fail_silently=False)
        return Response(status=status.HTTP_200_OK)


class PlaceHenreiOrder(views.APIView):
    permission_classes = [IsMoshu]

    def post(self, request, format=None):
        serializer = PlaceHenreiOrderSerializer(data=request.data)
        serializer.context['moshu'] = request.user
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        result: Dict[str, QuerySet] = serializer.save()
        read_serializer = PlaceHenreiOrderResultSerializer(result)
        return Response(read_serializer.data)
