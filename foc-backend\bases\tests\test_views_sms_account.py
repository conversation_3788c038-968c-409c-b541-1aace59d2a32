from typing import Dict

from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base, SmsAccount
from bases.tests.factories import BaseFactory, SmsAccountFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class SmsAccountCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()
        self.sms_account_data: SmsAccount = SmsAccountFactory.build(company=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_sms_account_create_succeed(self) -> None:
        """SMSアカウントを追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.sms_account_data)
        response = self.api_client.post(
            reverse('bases:sms_account', kwargs={'base_id': self.base.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertEqual(record['token'], self.sms_account_data.token)
        self.assertEqual(record['client_id'], self.sms_account_data.client_id)
        self.assertEqual(record['sms_code'], self.sms_account_data.sms_code)

        new_sms_account = SmsAccount.objects.get(pk=self.base.pk)
        self.assertEqual(new_sms_account.company, self.base)

    def test_sms_account_create_failed_by_notfound(self) -> None:
        """SMSアカウント追加APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.post(
            reverse('bases:sms_account', kwargs={'base_id': non_saved_base.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_sms_account_create_failed_by_multiple_sms_account(self) -> None:
        """SMSアカウント追加APIが既にSMSアカウントレコードを持っている拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        SmsAccountFactory(company=self.base)
        params: Dict = model_to_dict(self.sms_account_data)
        response = self.api_client.post(
            reverse('bases:sms_account', kwargs={'base_id': self.base.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_sms_account_create_failed_without_auth(self) -> None:
        """SMSアカウント追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = model_to_dict(self.sms_account_data)
        response = self.api_client.post(
            reverse('bases:sms_account', kwargs={'base_id': self.base.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SmsAccountDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.sms_account = SmsAccountFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_sms_account_detail_succeed(self) -> None:
        """SMSアカウント詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:sms_account', kwargs={'base_id': self.sms_account.company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['token'], self.sms_account.token)
        self.assertEqual(record['client_id'], self.sms_account.client_id)
        self.assertEqual(record['sms_code'], self.sms_account.sms_code)

    def test_sms_account_detail_succeed_without_auth(self) -> None:
        """SMSアカウント詳細取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:sms_account', kwargs={'base_id': self.sms_account.company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['token'], self.sms_account.token)
        self.assertEqual(record['client_id'], self.sms_account.client_id)
        self.assertEqual(record['sms_code'], self.sms_account.sms_code)

    def test_sms_account_detail_failed_by_notfound_base(self) -> None:
        """SMSアカウント詳細APIが存在しない拠点IDを指定して失敗する"""
        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:sms_account', kwargs={'base_id': non_saved_base.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_sms_account_detail_failed_by_base_missing_sms_account(self) -> None:
        """SMSアカウント詳細APIがSMSアカウントレコードのない拠点IDを指定して失敗する"""
        non_child_base: Base = BaseFactory()
        params: Dict = {}
        response = self.api_client.get(
            reverse('bases:sms_account', kwargs={'base_id': non_child_base.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class SmsAccountUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.sms_account = SmsAccountFactory()
        self.new_sms_account_data = SmsAccountFactory.build(company=self.sms_account.company)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_sms_account_update_succeed(self) -> None:
        """施行アルバムを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = model_to_dict(self.new_sms_account_data)
        response = self.api_client.put(
            reverse('bases:sms_account', kwargs={'base_id': self.sms_account.company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['token'], self.new_sms_account_data.token)
        self.assertEqual(record['client_id'], self.new_sms_account_data.client_id)
        self.assertEqual(record['sms_code'], self.new_sms_account_data.sms_code)

    def test_sms_account_update_failed_by_notfound_base(self) -> None:
        """SMSアカウント更新APIが存在しない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_base: Base = BaseFactory.build()
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:sms_account', kwargs={'base_id': non_saved_base.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_sms_account_update_failed_by_illegal_base(self) -> None:
        """SMSアカウント更新APIがSMSアカウントレコードのない拠点IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_child_base: Base = BaseFactory()
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:sms_account', kwargs={'base_id': non_child_base.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_sms_account_update_failed_without_auth(self) -> None:
        """SMSアカウント更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('bases:sms_account', kwargs={'base_id': self.sms_account.company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
