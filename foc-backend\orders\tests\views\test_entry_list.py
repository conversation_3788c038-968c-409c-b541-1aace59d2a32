from decimal import Decimal
from random import choice
from typing import Dict, List
from unittest.mock import patch

from dateutil.relativedelta import relativedelta
from django.core import mail
from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import FocFeeFactory, TokushoFactory
from henrei.models import HenreihinKoden
from henrei.tests.factories import HenreihinKodenFactory, HenreihinKumotsuFactory
from items.tests.factories import ItemFactory, ItemSupplierFactory
from masters.tests.factories import ChobunDaishiMasterFactory, PaymentTypeFactory, ScheduleFactory
from orders.epsilon import ContractType, ResponsePayment
from orders.models import (
    Entry,
    EntryDetail,
    EntryDetailChobun,
    EntryDetailKoden,
    EntryDetailKumotsu,
    EntryDetailMsg,
)
from orders.tests.factories import (
    EntryDetailChobunFactory,
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryDetailMsgFactory,
    EntryFactory,
)
from seko.models import Seko
from seko.tests.factories import KojinFactory, MoshuFactory, SekoFactory, SekoScheduleFactory
from suppliers.models import Supplier
from suppliers.tests.factories import SupplierFactory

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake_provider = Faker(locale='ja_JP')


class EntryListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        seko_1: Seko = SekoFactory()
        self.entry_1 = EntryFactory()
        self.entry_2 = EntryFactory(seko=seko_1)
        self.entry_2.entry_ts = self.entry_1.entry_ts + relativedelta(days=-1)
        self.entry_2.save()
        self.entry_3 = EntryFactory(seko=seko_1)
        self.entry_3.entry_ts = self.entry_1.entry_ts + relativedelta(days=-2)
        self.entry_3.save()

        HenreihinKodenFactory(
            detail_koden=EntryDetailKodenFactory(
                entry_detail=EntryDetailFactory(entry=self.entry_1)
            )
        )
        HenreihinKumotsuFactory(
            detail_kumotsu=EntryDetailKumotsuFactory(
                entry_detail=EntryDetailFactory(entry=self.entry_1)
            )
        )
        EntryDetailChobunFactory(entry_detail=EntryDetailFactory(entry=self.entry_1))
        EntryDetailMsgFactory(entry_detail=EntryDetailFactory(entry=self.entry_1))

        self.api_client = APIClient()

    def test_entry_list_succeed(self) -> None:
        """申込一覧を返す"""
        params: Dict = {}
        response = self.api_client.get(reverse('orders:entry_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_entry_list_ignore_disabled_seko(self) -> None:
        """申込一覧APIは無効化された施行は返さない"""
        self.entry_1.seko.disable()

        params: Dict = {}
        response = self.api_client.get(reverse('orders:entry_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_entry_query_seko_company(self) -> None:
        """申込一覧APIで葬儀社を検索する"""
        params: Dict = {'seko_company': self.entry_1.seko.seko_company.pk}
        response = self.api_client.get(reverse('orders:entry_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        params: Dict = {'seko_company': self.entry_2.seko.seko_company.pk}
        response = self.api_client.get(reverse('orders:entry_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_entry_query_entry_ts(self) -> None:
        """申込一覧APIを申込日時(範囲指定)で絞り込む"""
        # Fromのみ
        params: Dict = {'entry_ts_from': self.entry_2.entry_ts.date().isoformat()}
        response = self.api_client.get(reverse('orders:entry_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        # Toのみ
        params: Dict = {'entry_ts_to': self.entry_2.entry_ts.date().isoformat()}
        response = self.api_client.get(reverse('orders:entry_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        # FromとTo
        params: Dict = {
            'entry_ts_from': self.entry_2.entry_ts.date().isoformat(),
            'entry_ts_to': self.entry_2.entry_ts.date().isoformat(),
        }
        response = self.api_client.get(reverse('orders:entry_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)


class EntryCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.entry_data = EntryFactory.build(seko=SekoFactory(), payment=PaymentTypeFactory())
        MoshuFactory(seko=self.entry_data.seko)
        KojinFactory(seko=self.entry_data.seko, kojin_num=2)
        KojinFactory(seko=self.entry_data.seko, kojin_num=1)
        SekoScheduleFactory(
            seko=self.entry_data.seko, display_num=3, schedule=ScheduleFactory(id=1)
        )
        SekoScheduleFactory(
            seko=self.entry_data.seko, display_num=4, schedule=ScheduleFactory(id=2)
        )
        SekoScheduleFactory(
            seko=self.entry_data.seko, display_num=1, schedule=ScheduleFactory(id=3)
        )
        SekoScheduleFactory(
            seko=self.entry_data.seko, display_num=2, schedule=ScheduleFactory(id=4)
        )
        TokushoFactory(company=self.entry_data.seko.seko_company)

        FocFeeFactory(company=self.entry_data.seko.seko_company)

        supplier_1: Supplier = SupplierFactory()
        supplier_2: Supplier = SupplierFactory()

        self.detail_data_1 = EntryDetailFactory.build(entry=self.entry_data, item=ItemFactory())
        ItemSupplierFactory(
            item=self.detail_data_1.item, supplier=supplier_1, default_supplier_flg=True
        )
        ItemSupplierFactory(item=self.detail_data_1.item, supplier=supplier_2)
        self.detail_chobun_data_1 = EntryDetailChobunFactory.build(
            entry_detail=self.detail_data_1, daishi=ChobunDaishiMasterFactory()
        )
        self.detail_koden_data_1 = EntryDetailKodenFactory.build(entry_detail=self.detail_data_1)
        self.henrei_koden_data_1 = HenreihinKodenFactory.build(
            detail_koden=self.detail_koden_data_1,
            henreihin=ItemFactory(),
        )
        ItemSupplierFactory(
            item=self.henrei_koden_data_1.henreihin, supplier=supplier_1, default_supplier_flg=True
        )
        ItemSupplierFactory(item=self.henrei_koden_data_1.henreihin, supplier=supplier_2)
        self.detail_kumotsu_data_1 = EntryDetailKumotsuFactory.build(
            entry_detail=self.detail_data_1
        )
        self.detail_msg_data_1 = EntryDetailMsgFactory.build(entry_detail=self.detail_data_1)

        self.detail_data_2 = EntryDetailFactory.build(
            entry=self.entry_data, item=ItemFactory(), item_tax=0, item_tax_pct=0, tax_adjust=0
        )
        ItemSupplierFactory(item=self.detail_data_2.item, supplier=supplier_1)
        ItemSupplierFactory(
            item=self.detail_data_2.item, supplier=supplier_2, default_supplier_flg=True
        )
        self.detail_chobun_data_2 = EntryDetailChobunFactory.build(
            entry_detail=self.detail_data_2, daishi=ChobunDaishiMasterFactory()
        )
        self.detail_koden_data_2 = EntryDetailKodenFactory.build(entry_detail=self.detail_data_2)
        self.henrei_koden_data_2 = HenreihinKodenFactory.build(
            detail_koden=self.detail_koden_data_2,
            henreihin=ItemFactory(),
        )
        ItemSupplierFactory(item=self.henrei_koden_data_2.henreihin, supplier=supplier_1)
        ItemSupplierFactory(
            item=self.henrei_koden_data_2.henreihin, supplier=supplier_2, default_supplier_flg=True
        )
        self.detail_kumotsu_data_2 = EntryDetailKumotsuFactory.build(
            entry_detail=self.detail_data_2
        )
        self.detail_msg_data_2 = EntryDetailMsgFactory.build(entry_detail=self.detail_data_2)

        self.api_client = APIClient()

    def basic_params(self) -> Dict:

        details: List[Dict] = []
        billing_amount: Decimal = Decimal('0')
        for detail in [self.detail_data_1, self.detail_data_2]:
            chobun: Dict = {
                'okurinushi_company': detail.chobun.okurinushi_company,
                'okurinushi_title': detail.chobun.okurinushi_title,
                'okurinushi_company_kana': detail.chobun.okurinushi_company_kana,
                'okurinushi_name': detail.chobun.okurinushi_name,
                'okurinushi_kana': detail.chobun.okurinushi_kana,
                'okurinushi_zip_code': detail.chobun.okurinushi_zip_code,
                'okurinushi_prefecture': detail.chobun.okurinushi_prefecture,
                'okurinushi_address_1': detail.chobun.okurinushi_address_1,
                'okurinushi_address_2': detail.chobun.okurinushi_address_2,
                'okurinushi_address_3': detail.chobun.okurinushi_address_3,
                'okurinushi_tel': detail.chobun.okurinushi_tel,
                'renmei1': detail.chobun.renmei1,
                'renmei_kana1': detail.chobun.renmei_kana1,
                'renmei2': detail.chobun.renmei2,
                'renmei_kana2': detail.chobun.renmei_kana2,
                'atena': detail.chobun.atena,
                'honbun': detail.chobun.honbun,
                'daishi': detail.chobun.daishi.pk,
                'note': detail.chobun.note,
            }
            henrei_koden: Dict = {
                'henreihin': detail.koden.henrei_koden.henreihin.pk,
                'henreihin_hinban': detail.koden.henrei_koden.henreihin_hinban,
                'henreihin_name': detail.koden.henrei_koden.henreihin_name,
                'henreihin_price': detail.koden.henrei_koden.henreihin_price,
                'henreihin_tax': detail.koden.henrei_koden.henreihin_tax,
                'henreihin_tax_pct': detail.koden.henrei_koden.henreihin_tax_pct,
                'keigen_flg': detail.koden.henrei_koden.keigen_flg,
                'customer_self_select_flg': detail.koden.henrei_koden.customer_self_select_flg,
                'select_status': detail.koden.henrei_koden.select_status,
            }
            koden: Dict = {
                'koden_commission': detail.koden.koden_commission,
                'koden_commission_tax': detail.koden.koden_commission_tax,
                'koden_commission_tax_pct': detail.koden.koden_commission_tax_pct,
                'tax_adjust': detail.koden.tax_adjust,
                'henreihin_fuyo_flg': detail.koden.henreihin_fuyo_flg,
                'henrei_koden': henrei_koden,
            }
            kumotsu: Dict = {
                'okurinushi_company': detail.kumotsu.okurinushi_company,
                'okurinushi_title': detail.kumotsu.okurinushi_title,
                'okurinushi_company_kana': detail.kumotsu.okurinushi_company_kana,
                'okurinushi_name': detail.kumotsu.okurinushi_name,
                'okurinushi_kana': detail.kumotsu.okurinushi_kana,
                'okurinushi_zip_code': detail.kumotsu.okurinushi_zip_code,
                'okurinushi_prefecture': detail.kumotsu.okurinushi_prefecture,
                'okurinushi_address_1': detail.kumotsu.okurinushi_address_1,
                'okurinushi_address_2': detail.kumotsu.okurinushi_address_2,
                'okurinushi_address_3': detail.kumotsu.okurinushi_address_3,
                'okurinushi_tel': detail.kumotsu.okurinushi_tel,
                'renmei1': detail.kumotsu.renmei1,
                'renmei_kana1': detail.kumotsu.renmei_kana1,
                'renmei2': detail.kumotsu.renmei2,
                'renmei_kana2': detail.kumotsu.renmei_kana2,
                'note': detail.kumotsu.note,
            }
            message: Dict = {
                'relation_ship': detail.message.relation_ship,
                'honbun': detail.message.honbun,
            }
            if hasattr(detail, 'koden'):
                billing_amount += Decimal(str(detail.koden.koden_commission))
            billing_amount += Decimal(str(detail.item_unit_price)) * Decimal(str(detail.quantity))
            details.append(
                {
                    'item': detail.item.pk,
                    'item_hinban': detail.item_hinban,
                    'item_name': detail.item_name,
                    'item_unit_price': detail.item_unit_price,
                    'item_tax': detail.item_tax,
                    'item_tax_pct': detail.item_tax_pct,
                    'keigen_flg': detail.keigen_flg,
                    'quantity': detail.quantity,
                    'tax_adjust': detail.tax_adjust,
                    'chobun': chobun,
                    'koden': koden,
                    'kumotsu': kumotsu,
                    'message': message,
                }
            )

        return {
            'seko': self.entry_data.seko.pk,
            'payment': self.entry_data.payment.pk,
            'entry_name': self.entry_data.entry_name,
            'entry_name_kana': self.entry_data.entry_name_kana,
            'entry_zip_code': self.entry_data.entry_zip_code,
            'entry_prefecture': self.entry_data.entry_prefecture,
            'entry_address_1': self.entry_data.entry_address_1,
            'entry_address_2': self.entry_data.entry_address_2,
            'entry_address_3': self.entry_data.entry_address_3,
            'entry_tel': self.entry_data.entry_tel,
            'entry_mail_address': self.entry_data.entry_mail_address,
            'details': details,
            'contract_type': choice([member.value for member in list(ContractType)]),
            'epsilon_token': fake_provider.pystr(min_chars=28, max_chars=28),
            'billing_amount': billing_amount,
            'con_count': 1,
        }

    def assertEntry(self, result: Dict, instance: Entry) -> None:
        self.assertEqual(result['seko'], instance.seko.pk)
        self.assertEqual(result['entry_name'], instance.entry_name)
        self.assertEqual(result['entry_name_kana'], instance.entry_name_kana)
        self.assertEqual(result['entry_zip_code'], instance.entry_zip_code)
        self.assertEqual(result['entry_prefecture'], instance.entry_prefecture)
        self.assertEqual(result['entry_address_1'], instance.entry_address_1)
        self.assertEqual(result['entry_address_2'], instance.entry_address_2)
        self.assertEqual(result['entry_address_3'], instance.entry_address_3)
        self.assertEqual(result['entry_tel'], instance.entry_tel)
        self.assertEqual(result['entry_mail_address'], instance.entry_mail_address)
        self.assertIsNotNone(result['entry_ts'])
        self.assertIsNone(result['cancel_ts'])
        self.assertEqual(result['receipt_count'], 0)
        self.assertIsNotNone(result['created_at'])
        self.assertIsNotNone(result['updated_at'])
        payment_dict: Dict = result['payment']
        self.assertEqual(payment_dict['id'], instance.payment.pk)
        self.assertEqual(payment_dict['name'], instance.payment.name)

    def assertEntryDetail(self, result: Dict, instance: EntryDetail) -> None:
        self.assertEqual(result['item'], instance.item.pk)
        self.assertEqual(result['item_hinban'], instance.item_hinban)
        self.assertEqual(result['item_name'], instance.item_name)
        self.assertEqual(result['item_unit_price'], instance.item_unit_price)
        self.assertEqual(result['item_tax'], instance.item_tax)
        self.assertEqual(result['item_tax_pct'], instance.item_tax_pct)
        self.assertEqual(result['keigen_flg'], instance.keigen_flg)
        self.assertEqual(result['quantity'], instance.quantity)
        self.assertEqual(result['tax_adjust'], instance.tax_adjust)
        self.assertIsNotNone(result['created_at'])
        self.assertIsNotNone(result['updated_at'])

    def assertEntryDetailChobun(self, result: Dict, instance: EntryDetailChobun) -> None:
        self.assertEqual(result['okurinushi_company'], instance.okurinushi_company)
        self.assertEqual(result['okurinushi_title'], instance.okurinushi_title)
        self.assertEqual(result['okurinushi_company_kana'], instance.okurinushi_company_kana)
        self.assertEqual(result['okurinushi_name'], instance.okurinushi_name)
        self.assertEqual(result['okurinushi_kana'], instance.okurinushi_kana)
        self.assertEqual(result['okurinushi_zip_code'], instance.okurinushi_zip_code)
        self.assertEqual(result['okurinushi_prefecture'], instance.okurinushi_prefecture)
        self.assertEqual(result['okurinushi_address_1'], instance.okurinushi_address_1)
        self.assertEqual(result['okurinushi_address_2'], instance.okurinushi_address_2)
        self.assertEqual(result['okurinushi_address_3'], instance.okurinushi_address_3)
        self.assertEqual(result['okurinushi_tel'], instance.okurinushi_tel)
        self.assertEqual(result['renmei1'], instance.renmei1)
        self.assertEqual(result['renmei_kana1'], instance.renmei_kana1)
        self.assertEqual(result['renmei2'], instance.renmei2)
        self.assertEqual(result['renmei_kana2'], instance.renmei_kana2)
        self.assertEqual(result['atena'], instance.atena)
        self.assertEqual(result['honbun'], instance.honbun)
        self.assertEqual(result['note'], instance.note)
        self.assertFalse(result['printed_flg'])
        self.assertIsNotNone(result['created_at'])
        self.assertIsNotNone(result['updated_at'])
        daishi_dict: Dict = result['daishi']
        self.assertEqual(daishi_dict['id'], instance.daishi.pk)
        self.assertEqual(daishi_dict['name'], instance.daishi.name)

    def assertEntryDetailKoden(self, result: Dict, instance: EntryDetailKoden) -> None:
        self.assertEqual(result['koden_commission'], instance.koden_commission)
        self.assertEqual(result['koden_commission_tax'], instance.koden_commission_tax)
        self.assertEqual(result['koden_commission_tax_pct'], instance.koden_commission_tax_pct)
        self.assertEqual(result['tax_adjust'], instance.tax_adjust)
        self.assertEqual(result['henreihin_fuyo_flg'], instance.henreihin_fuyo_flg)

    def assertEntryDetailKumotsu(self, result: Dict, instance: EntryDetailKumotsu) -> None:
        self.assertEqual(result['okurinushi_company'], instance.okurinushi_company)
        self.assertEqual(result['okurinushi_title'], instance.okurinushi_title)
        self.assertEqual(result['okurinushi_company_kana'], instance.okurinushi_company_kana)
        self.assertEqual(result['okurinushi_name'], instance.okurinushi_name)
        self.assertEqual(result['okurinushi_kana'], instance.okurinushi_kana)
        self.assertEqual(result['okurinushi_zip_code'], instance.okurinushi_zip_code)
        self.assertEqual(result['okurinushi_prefecture'], instance.okurinushi_prefecture)
        self.assertEqual(result['okurinushi_address_1'], instance.okurinushi_address_1)
        self.assertEqual(result['okurinushi_address_2'], instance.okurinushi_address_2)
        self.assertEqual(result['okurinushi_address_3'], instance.okurinushi_address_3)
        self.assertEqual(result['okurinushi_tel'], instance.okurinushi_tel)
        self.assertEqual(result['renmei1'], instance.renmei1)
        self.assertEqual(result['renmei_kana1'], instance.renmei_kana1)
        self.assertEqual(result['renmei2'], instance.renmei2)
        self.assertEqual(result['renmei_kana2'], instance.renmei_kana2)
        self.assertEqual(result['note'], instance.note)
        self.assertEqual(result['order_status'], 1)
        self.assertIsNone(result['order_ts'])
        self.assertIsNone(result['delivery_ts'])
        self.assertIsNone(result['order_staff'])
        self.assertIsNone(result['order_note'])
        self.assertIsNotNone(result['created_at'])
        self.assertIsNotNone(result['updated_at'])
        supplier_dict: Dict = result['supplier']
        default_supplier: Supplier = instance.entry_detail.item.default_supplier()
        self.assertEqual(supplier_dict['id'], default_supplier.pk)
        self.assertEqual(supplier_dict['name'], default_supplier.name)

    def assertEntryDetailMsg(self, result: Dict, instance: EntryDetailMsg) -> None:
        self.assertEqual(result['relation_ship'], instance.relation_ship)
        self.assertEqual(result['honbun'], instance.honbun)
        self.assertEqual(result['release_status'], 1)
        self.assertIsNotNone(result['created_at'])
        self.assertIsNotNone(result['updated_at'])

    def assertHenreihinKoden(self, result: Dict, instance: HenreihinKoden) -> None:
        self.assertEqual(result['henreihin'], instance.henreihin.pk)
        self.assertEqual(result['henreihin_hinban'], instance.henreihin_hinban)
        self.assertEqual(result['henreihin_name'], instance.henreihin_name)
        self.assertEqual(result['henreihin_price'], instance.henreihin_price)
        self.assertEqual(result['henreihin_tax'], instance.henreihin_tax)
        self.assertEqual(result['henreihin_tax_pct'], instance.henreihin_tax_pct)
        self.assertEqual(result['keigen_flg'], instance.keigen_flg)
        self.assertEqual(result['customer_self_select_flg'], instance.customer_self_select_flg)
        self.assertEqual(result['select_status'], instance.select_status)
        self.assertIsNone(result['henrei_order'])
        self.assertEqual(result['order_status'], 0)
        self.assertIsNotNone(result['created_at'])
        self.assertIsNotNone(result['updated_at'])
        supplier_dict: Dict = result['supplier']
        default_supplier: Supplier = instance.henreihin.default_supplier()
        self.assertEqual(supplier_dict['id'], default_supplier.pk)
        self.assertEqual(supplier_dict['name'], default_supplier.name)

    def fake_register_payment(self) -> ResponsePayment:
        return ResponsePayment(
            order_number=fake_provider.pyint(min_value=1),
            result_code=fake_provider.pystr_format(string_format='########'),
            pareq=fake_provider.pystr_format(string_format='########'),
            tds2_url=fake_provider.pystr_format(string_format='http://########'),
            trans_code=fake_provider.pystr_format(string_format='########'),
        )

    @override_settings(ENABLE_EPSILON_PAYMENT=True)
    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_succeed(self, mock_post, mock_origin) -> None:
        """申し込み(香典以外)を追加する"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        params: Dict = self.basic_params()
        for detail_params in params['details']:
            del detail_params['koden']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEntry(record, self.entry_data)

        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

        self.assertTrue('chobun' in detail_1)
        chobun_1: Dict = detail_1.get('chobun', {})
        self.assertEntryDetailChobun(chobun_1, self.detail_chobun_data_1)

        self.assertTrue('kumotsu' in detail_1)
        kumotsu_1: Dict = detail_1.get('kumotsu', {})
        self.assertEntryDetailKumotsu(kumotsu_1, self.detail_kumotsu_data_1)

        self.assertTrue('message' in detail_1)
        message_1: Dict = detail_1.get('message', {})
        self.assertEntryDetailMsg(message_1, self.detail_msg_data_1)

        # イプシロン決済請求登録は1回だけ行われる
        self.assertEqual(mock_post.call_count, 1)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    @override_settings(ENABLE_EPSILON_PAYMENT=True)
    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_chobun_succeed(self, mock_post, mock_origin) -> None:
        """申し込み(弔文)を追加する"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        params: Dict = self.basic_params()
        for detail_params in params['details']:
            del detail_params['koden']
            del detail_params['kumotsu']
            del detail_params['message']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEntry(record, self.entry_data)

        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

        self.assertTrue('chobun' in detail_1)
        chobun_1: Dict = detail_1.get('chobun', {})
        self.assertEntryDetailChobun(chobun_1, self.detail_chobun_data_1)

        # イプシロン決済請求登録は1回だけ行われる
        self.assertEqual(mock_post.call_count, 1)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    @override_settings(ENABLE_EPSILON_PAYMENT=True)
    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_koden_succeed(self, mock_post, mock_origin) -> None:
        """申し込み(香典)を追加する"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        params: Dict = self.basic_params()
        for detail_params in params['details']:
            del detail_params['chobun']
            del detail_params['kumotsu']
            del detail_params['message']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEntry(record, self.entry_data)

        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

        self.assertTrue('koden' in detail_1)
        koden_1: Dict = detail_1.get('koden', {})
        self.assertEntryDetailKoden(koden_1, self.detail_koden_data_1)

        self.assertTrue('henrei_koden' in koden_1)
        henrei_koden_1: Dict = koden_1.get('henrei_koden', {})
        self.assertHenreihinKoden(henrei_koden_1, self.henrei_koden_data_1)

        # イプシロン決済請求登録は1回だけ行われる
        self.assertEqual(mock_post.call_count, 1)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    @override_settings(ENABLE_EPSILON_PAYMENT=True)
    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_koden_succeed_without_henrei(self, mock_post, mock_origin) -> None:
        """申し込み(香典、返礼品はなし)を追加する"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        params: Dict = self.basic_params()
        for detail_params in params['details']:
            del detail_params['chobun']
            del detail_params['koden']['henrei_koden']
            del detail_params['kumotsu']
            del detail_params['message']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEntry(record, self.entry_data)

        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

        self.assertTrue('koden' in detail_1)
        koden_1: Dict = detail_1.get('koden', {})
        self.assertEntryDetailKoden(koden_1, self.detail_koden_data_1)

        self.assertIsNone(koden_1['henrei_koden'])

        # イプシロン決済請求登録は1回だけ行われる
        self.assertEqual(mock_post.call_count, 1)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    @override_settings(ENABLE_EPSILON_PAYMENT=True)
    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_kumotsu_succeed(self, mock_post, mock_origin) -> None:
        """申し込み(供花供物)を追加する"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        params: Dict = self.basic_params()
        for detail_params in params['details']:
            del detail_params['chobun']
            del detail_params['koden']
            del detail_params['message']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEntry(record, self.entry_data)

        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

        self.assertTrue('kumotsu' in detail_1)
        kumotsu_1: Dict = detail_1.get('kumotsu', {})
        self.assertEntryDetailKumotsu(kumotsu_1, self.detail_kumotsu_data_1)

        # イプシロン決済請求登録は1回だけ行われる
        self.assertEqual(mock_post.call_count, 1)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    @override_settings(ENABLE_EPSILON_PAYMENT=True)
    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_message_succeed(self, mock_post, mock_origin) -> None:
        """申し込み(追悼メッセージ)を追加する"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        params: Dict = self.basic_params()
        for detail_params in params['details']:
            del detail_params['chobun']
            del detail_params['koden']
            del detail_params['kumotsu']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEntry(record, self.entry_data)

        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

        self.assertTrue('message' in detail_1)
        message_1: Dict = detail_1.get('message', {})
        self.assertEntryDetailMsg(message_1, self.detail_msg_data_1)

        # イプシロン決済請求登録は1回だけ行われる
        self.assertEqual(mock_post.call_count, 1)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_skips_epsilon_when_billing_is_zero(self, mock_post, mock_origin) -> None:
        """申し込みの決済金額が0の場合はイプシロン決済請求を行わない"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        # 決済金額が0の場合は数点のパラメータが削除される
        params: Dict = self.basic_params()
        params['billing_amount'] = 0
        del params['contract_type']
        del params['epsilon_token']
        del params['payment']
        for detail_params in params['details']:
            del detail_params['chobun']
            del detail_params['koden']
            del detail_params['kumotsu']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record['seko'], self.entry_data.seko.pk)
        self.assertEqual(record['entry_name'], self.entry_data.entry_name)
        self.assertEqual(record['entry_name_kana'], self.entry_data.entry_name_kana)
        self.assertEqual(record['entry_zip_code'], self.entry_data.entry_zip_code)
        self.assertEqual(record['entry_prefecture'], self.entry_data.entry_prefecture)
        self.assertEqual(record['entry_address_1'], self.entry_data.entry_address_1)
        self.assertEqual(record['entry_address_2'], self.entry_data.entry_address_2)
        self.assertEqual(record['entry_address_3'], self.entry_data.entry_address_3)
        self.assertEqual(record['entry_tel'], self.entry_data.entry_tel)
        self.assertEqual(record['entry_mail_address'], self.entry_data.entry_mail_address)
        self.assertIsNotNone(record['entry_ts'])
        self.assertIsNone(record['cancel_ts'])
        self.assertEqual(record['receipt_count'], 0)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])
        self.assertIsNone(record['payment'])

        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

        self.assertTrue('message' in detail_1)
        message_1: Dict = detail_1.get('message', {})
        self.assertEntryDetailMsg(message_1, self.detail_msg_data_1)

        # イプシロン決済請求登録は行わない
        self.assertEqual(mock_post.call_count, 0)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_skips_epsilon_when_billing_is_zero_and_others_are_null(
        self, mock_post, mock_origin
    ) -> None:
        """申し込みの決済金額が0の場合はイプシロン決済請求を行わない-null版"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        # 決済金額が0の場合は数点のパラメータが削除される
        params: Dict = self.basic_params()
        params['billing_amount'] = 0
        params['contract_type'] = None
        params['epsilon_token'] = None
        params['payment'] = None
        for detail_params in params['details']:
            del detail_params['chobun']
            del detail_params['koden']
            del detail_params['kumotsu']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record['seko'], self.entry_data.seko.pk)
        self.assertEqual(record['entry_name'], self.entry_data.entry_name)
        self.assertEqual(record['entry_name_kana'], self.entry_data.entry_name_kana)
        self.assertEqual(record['entry_zip_code'], self.entry_data.entry_zip_code)
        self.assertEqual(record['entry_prefecture'], self.entry_data.entry_prefecture)
        self.assertEqual(record['entry_address_1'], self.entry_data.entry_address_1)
        self.assertEqual(record['entry_address_2'], self.entry_data.entry_address_2)
        self.assertEqual(record['entry_address_3'], self.entry_data.entry_address_3)
        self.assertEqual(record['entry_tel'], self.entry_data.entry_tel)
        self.assertEqual(record['entry_mail_address'], self.entry_data.entry_mail_address)
        self.assertIsNotNone(record['entry_ts'])
        self.assertIsNone(record['cancel_ts'])
        self.assertEqual(record['receipt_count'], 0)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])
        self.assertIsNone(record['payment'])

        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

        self.assertTrue('message' in detail_1)
        message_1: Dict = detail_1.get('message', {})
        self.assertEntryDetailMsg(message_1, self.detail_msg_data_1)

        # イプシロン決済請求登録は行わない
        self.assertEqual(mock_post.call_count, 0)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_message_without_payment_succeed(self, mock_post, mock_origin) -> None:
        """申し込み(追悼メッセージ)を追加する(Epsilon決済:OFF)"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        params: Dict = self.basic_params()
        for detail_params in params['details']:
            del detail_params['chobun']
            del detail_params['koden']
            del detail_params['kumotsu']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEntry(record, self.entry_data)

        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

        self.assertTrue('message' in detail_1)
        message_1: Dict = detail_1.get('message', {})
        self.assertEntryDetailMsg(message_1, self.detail_msg_data_1)

        # イプシロン決済請求登録は行わない
        self.assertEqual(mock_post.call_count, 0)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    @override_settings(ENABLE_EPSILON_PAYMENT=True)
    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_succeed_empty_item_hinban(self, mock_post, mock_origin) -> None:
        """申込追加APIが品番が空でも成功する"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        self.detail_data_1.item_hinban = None
        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(len(record['details']), 2)
        detail_1: Dict = record['details'][0]
        self.assertEntryDetail(detail_1, self.detail_data_1)

    @override_settings(ENABLE_EPSILON_PAYMENT=True)
    @override_settings(ORDER_EMAIL_BCC=['<EMAIL>', '<EMAIL>'])
    @patch('orders.views.request_origin')
    @patch('orders.views.register_payment')
    def test_entry_create_succeed_with_bcc(self, mock_post, mock_origin) -> None:
        """申込追加APIがBCC設定有効でも成功する"""
        mock_post.return_value = self.fake_register_payment()
        mock_origin.return_value = 'http://frontend:9999'

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # メールが1件BCCつきで送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(
            sent_mail.subject, f'てれ葬儀こころ【{self.entry_data.seko.soke_name}家】：お申込みを受け付けました'
        )
        self.assertEqual(sent_mail.to, [self.entry_data.entry_mail_address])

    def test_entry_create_ignore_some_fields(self) -> None:
        """申込追加APIはキャンセル日時などの入力を無視する"""
        params: Dict = self.basic_params()
        params['cancel_ts'] = timezone.now()
        params['receipt_count'] = fake_provider.pyint(10, 100)
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertIsNone(record['cancel_ts'])
        self.assertEqual(record['receipt_count'], 0)

    def test_entry_create_fail_without_epsilon_related_fields(self) -> None:
        """申込追加APIがイプシロン決済関連のフィールド未入力で失敗する"""
        params: Dict = self.basic_params()
        del params['billing_amount']
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['billing_amount'])

    @override_settings(ENABLE_EPSILON_PAYMENT=True)
    def test_entry_create_fail_without_epsilon_related_fields_are_null(self) -> None:
        """申込追加APIがイプシロン決済を実行する場合にイプシロン決済関連のフィールドがnullで失敗する"""
        params: Dict = self.basic_params()
        if params['billing_amount'] < 1:
            params['billing_amount'] = 1
        params['contract_type'] = None
        params['epsilon_token'] = None
        params['payment'] = None
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['contract_type'])
        self.assertIsNotNone(record['epsilon_token'])
        self.assertIsNotNone(record['payment'])

    def test_entry_create_fail_without_illegal_contract_type(self) -> None:
        """申込追加APIが存在しないContractTypeを指定して失敗する"""
        params: Dict = self.basic_params()
        params['contract_type'] = -1
        response = self.api_client.post(reverse('orders:entry_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['contract_type'])
