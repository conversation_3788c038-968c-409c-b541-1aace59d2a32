@import "../../common/dialog/dialog.component.scss";
:host ::ng-deep .dialogList {
  .dialog {
    .image {
      margin-bottom: 30px;
      text-align: center;
      @media screen and (max-width: 480px) {
        img {
          width: 38px;
        }
      }
    }
    .text {
        color: #dd0037;
        font-weight: bold;
        max-width: none;
        line-height: 1.75;
        text-align: center;
        a {
          font-weight: bold;
          text-decoration: underline;
        }
    }
    .action {
        margin-top: 30px;
        .btn {
            width: 180px;
            margin: 0 auto;
            padding: 20px 0;
        }
    }
  }
}

@media screen and (max-width: 480px) {
    .dialogList {
        .dialog {
            .image {
                img {
                    width: 38px;
                }
            }
            .text {
                max-width: none;
                line-height: 1.75;
            }
            .action {
                margin-top: 30px;
                .btn {
                    width: 180px;
                    margin: 0 auto;
                    padding: 20px 0;
                }
            }
        }
    }
}
