# Generated by Django 3.1.5 on 2021-01-15 03:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bases', '0013_smsaccount'),
    ]

    operations = [
        migrations.CreateModel(
            name='AfGroup',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('name', models.TextField(verbose_name='name')),
                ('display_num', models.IntegerField(verbose_name='display order')),
                ('del_flg', models.BooleanField(default=False, verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('create_user_id', models.IntegerField(verbose_name='created by')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('update_user_id', models.IntegerField(verbose_name='updated by')),
                (
                    'department',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='af_groups',
                        to='bases.base',
                        verbose_name='department',
                    ),
                ),
            ],
            options={
                'db_table': 'm_af_group',
            },
        ),
    ]
