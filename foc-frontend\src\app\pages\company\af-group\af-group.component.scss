.contents {
  .menu_title {
    .icon.big {
      margin-right: 20px;
    }
    .icon.combo {
      position: absolute;
      left: 45px;
    }
  }
  .table_fixed.body {
    max-height: calc(100% - 180px) !important;
  }
  >.table_fixed tr {
    .base_name {
      width: 30%;
    }
    .display_num {
      width: 60px;
    }
    .operation {
      width: 80px;
    }
  }
}
.ui.modal {
  .input_area >.line >label {
    min-width: 150px;
  }
  .ui.header {
    display: flex;
    .icon.large {
      margin-right: 15px;
    }
    .icon.combo {
      position: absolute;
      left: 40px;
    }
    >span {
      min-width: 120px;
    }
  }
  .title {
    font-size: smaller;
    margin-left: 20px;
    border: none;
    padding-bottom: 0;
    .data {
      margin-left: 10px;
      font-weight: normal;
    }
  }
  &.af.list tr {
    .display_num {
      width: 60px;
    }
    .operation {
      width: 60px;
    }
  }

}