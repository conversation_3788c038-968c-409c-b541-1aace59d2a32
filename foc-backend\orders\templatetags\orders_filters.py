import base64
from datetime import date

from django import template

from masters.models import Wareki

register = template.Library()


@register.filter
def format_honbun(text) -> list:
    result = [line[:21] for line in text.splitlines()]
    return result[:10]


@register.filter
def vertical_text(text) -> str:
    if text is None:
        return ''
    return (
        text.replace('', ' ')
        .replace('ー', '｜')
        .replace('－', '｜')
        .replace('-', '｜')
        .replace('−', '｜')
        .replace('。', '<div>゜</div>')
        .replace('、', '<div> `</div>')
        .replace('(', '<div style="margin:0 0 1px 0">︵</div>')
        .replace(')', '<div style="margin:1px 0 0 0">︶</div>')
        .replace('（', '<div style="margin:0 0 1px 0">︵</div>')
        .replace('）', '<div style="margin:1px 0 0 0">︶</div>')[1:-1]
    )


@register.filter
def wareki_day(the_date: date) -> str:
    return Wareki.japanese_era(the_date, full_date=True, kanji_date=True)


@register.filter
def base64_encode(path) -> str:
    if not path:
        return ''
    with open(path, 'rb') as image_file:
        encoded_img = base64.b64encode(image_file.read()).decode('ascii')
    return encoded_img


@register.filter
def address_num_to_kanji(text) -> str:
    if text is None:
        return ''
    return (
        text.replace('0', '〇')
        .replace('1', '一')
        .replace('2', '二')
        .replace('3', '三')
        .replace('4', '四')
        .replace('5', '五')
        .replace('6', '六')
        .replace('7', '七')
        .replace('8', '八')
        .replace('9', '九')
        .replace('０', '〇')
        .replace('１', '一')
        .replace('２', '二')
        .replace('３', '三')
        .replace('４', '四')
        .replace('５', '五')
        .replace('６', '六')
        .replace('７', '七')
        .replace('８', '八')
        .replace('９', '九')
    )
