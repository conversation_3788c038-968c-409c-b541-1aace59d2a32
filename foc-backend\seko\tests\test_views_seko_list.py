from base64 import b64encode
from datetime import date
from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.models import AfGroup, AfWish, SekoAf
from after_follow.tests.factories import (
    AfActivityDetailFactory,
    AfGroupFactory,
    AfterFollowFactory,
    AfWishFactory,
    SekoAfFactory,
)
from bases.models import Base
from bases.tests.factories import BaseFactory
from items.tests.factories import ItemFactory
from masters.tests.factories import ScheduleFactory, SekoStyleFactory, ServiceFactory
from seko.models import SekoAnswer, SekoInquiry
from seko.tests.factories import (
    HenreihinKakegamiFactory,
    KojinFactory,
    MoshuFactory,
    SekoAlbumFactory,
    SekoAnswerFactory,
    SekoFactory,
    SekoInquiryFactory,
    SekoItemFactory,
    SekoScheduleFactory,
    SekoServiceFactory,
    SekoVideoFactory,
)
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class SekoListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.seko_staff = StaffFactory()
        self.company = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.department = BaseFactory(base_type=Base.OrgType.DEPARTMENT, parent=self.company)
        self.hall = BaseFactory(base_type=Base.OrgType.HALL, parent=self.department)

        self.seko_1 = SekoFactory(
            seko_company=self.company,
            seko_department=self.department,
            soke_name='鈴木三郎',
            soke_kana='スズキサブロウ',
            seko_staff=self.seko_staff,
            seko_date=date(2020, 9, 25),
            death_date=date(2020, 9, 20),
        )
        KojinFactory(seko=self.seko_1, name='鈴木太郎', kana='スズキタロウ')
        KojinFactory(seko=self.seko_1, name='鈴木次郎', kana='スズキジロウ')
        MoshuFactory(seko=self.seko_1, name='鈴木四郎', kana='スズキシロウ')
        SekoScheduleFactory(seko=self.seko_1)
        SekoScheduleFactory(seko=self.seko_1, hall=self.hall)

        self.seko_2 = SekoFactory(
            soke_name='田中太郎',
            soke_kana='タナカタロウ',
            af_staff=self.seko_staff,
            seko_date=date(2020, 9, 23),
            death_date=date(2020, 9, 18),
        )
        KojinFactory(seko=self.seko_2, name='田中三郎', kana='タナカサブロウ')
        KojinFactory(seko=self.seko_2, name='田中四郎', kana='タナカシロウ')
        MoshuFactory(seko=self.seko_2, name='田中京子', kana='タナカキョウコ')
        SekoScheduleFactory(seko=self.seko_2, hall=self.hall)
        SekoScheduleFactory(seko=self.seko_2, hall=self.hall)
        SekoVideoFactory(seko=self.seko_2)
        SekoAlbumFactory(seko=self.seko_2)
        SekoAlbumFactory(seko=self.seko_2)

        self.seko_3 = SekoFactory(
            seko_department=self.hall,
            soke_name='山中太一',
            soke_kana='ヤマナカタイチ',
            order_staff=self.seko_staff,
            seko_staff=self.seko_staff,
            seko_date=date(2020, 9, 24),
            death_date=date(2020, 9, 19),
        )
        KojinFactory(seko=self.seko_3, name='山中次郎', kana='ヤマナカジロウ', kojin_num=2)
        KojinFactory(seko=self.seko_3, name='山中三郎', kana='ヤマナカサブロウ', kojin_num=1)
        MoshuFactory(seko=self.seko_3, name='山中四郎', kana='ヤマナカシロウ')
        SekoScheduleFactory(seko=self.seko_3, display_num=2)
        SekoScheduleFactory(seko=self.seko_3, display_num=1)
        SekoItemFactory(seko=self.seko_3)
        SekoItemFactory(seko=self.seko_3)
        SekoServiceFactory(seko=self.seko_3)
        SekoServiceFactory(seko=self.seko_3)
        HenreihinKakegamiFactory(seko=self.seko_3)
        seko_af: SekoAf = SekoAfFactory(seko=self.seko_3)
        af_group: AfGroup = AfGroupFactory()
        af_wish_1: AfWish = AfWishFactory(
            seko_af=seko_af, af_type=AfterFollowFactory(af_group=af_group, display_num=2)
        )
        AfActivityDetailFactory(af_wish=af_wish_1)
        AfActivityDetailFactory(af_wish=af_wish_1)
        AfActivityDetailFactory(af_wish=af_wish_1)
        AfActivityDetailFactory(af_wish=af_wish_1)
        af_wish_2: AfWish = AfWishFactory(
            seko_af=seko_af, af_type=AfterFollowFactory(af_group=af_group, display_num=1)
        )
        self.af_activity_sample = AfActivityDetailFactory(af_wish=af_wish_2)
        AfActivityDetailFactory(af_wish=af_wish_2)
        AfWishFactory(
            seko_af=seko_af, af_type=AfterFollowFactory(af_group=af_group, display_num=3)
        )
        self.af_wish_sample = af_wish_2

        self.inquiry_1: SekoInquiry = SekoInquiryFactory(seko=self.seko_3, query_group_id=1)
        self.answer_1: SekoAnswer = SekoAnswerFactory(inquiry=self.inquiry_1)
        self.inquiry_2: SekoInquiry = SekoInquiryFactory(seko=self.seko_3, query_group_id=2)
        SekoAnswerFactory(inquiry=self.inquiry_2)
        SekoAnswerFactory(inquiry=self.inquiry_2)
        SekoAnswerFactory(inquiry=self.inquiry_2)
        self.inquiry_3: SekoInquiry = SekoInquiryFactory(seko=self.seko_3, query_group_id=1)

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_list_succeed(self) -> None:
        """施行一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        # 施行日の昇順になっているか
        prev_seko_date = None
        for seko in records:
            this_seko_date = seko.get('seko_date')
            if prev_seko_date:
                self.assertGreaterEqual(this_seko_date, prev_seko_date)
            prev_seko_date = this_seko_date

    def test_seko_list_ignores_deleted(self) -> None:
        """施行一覧は無効化されたものは返さない + 名称系項目追加 + 関連テーブルソート順"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko_1.disable()
        self.seko_2.disable()

        params: Dict = {}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)
        seko_dict: Dict = records[0]
        self.assertEqual(seko_dict.get('soke_name'), self.seko_3.soke_name)

        # 名称系項目追加
        self.assertEqual(seko_dict.get('order_staff_name'), self.seko_3.order_staff.name)
        self.assertEqual(seko_dict.get('seko_staff_name'), self.seko_3.seko_staff.name)
        self.assertEqual(seko_dict.get('af_staff_name'), self.seko_3.af_staff.name)
        self.assertEqual(seko_dict.get('seko_company_name'), self.seko_3.seko_company.base_name)
        self.assertEqual(
            seko_dict.get('seko_department_name'), self.seko_3.seko_department.base_name
        )

        moshu_dict: Dict = seko_dict.get('moshu', {})
        self.assertIsNone(moshu_dict['agree_ts'])
        self.assertEqual(moshu_dict['soke_site_del_flg'], self.seko_3.moshu.soke_site_del_flg)
        self.assertEqual(moshu_dict['mail_flg'], self.seko_3.moshu.mail_flg)

        # ソート順
        prev_num = -1
        for kojin_dict in seko_dict.get('kojin', []):
            self.assertGreaterEqual(kojin_dict['kojin_num'], prev_num)
            prev_num = kojin_dict['kojin_num']

        prev_num = -1
        for schedule_dict in seko_dict.get('schedules', []):
            self.assertGreaterEqual(schedule_dict['display_num'], prev_num)
            prev_num = schedule_dict['display_num']

        # アフターフォロー
        self.assertEqual(
            seko_dict['seko_af']['contact_way']['id'], self.seko_3.seko_af.contact_way.pk
        )
        self.assertEqual(
            seko_dict['seko_af']['contact_way']['contact_way'],
            self.seko_3.seko_af.contact_way.contact_way,
        )
        self.assertEqual(seko_dict['seko_af']['note'], self.seko_3.seko_af.note)

        self.assertEqual(len(seko_dict['seko_af']['wishes']), 3)
        self.assertEqual(seko_dict['seko_af']['wishes'][0]['id'], self.af_wish_sample.pk)
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['af_type']['id'], self.af_wish_sample.af_type.pk
        )
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['af_type']['name'], self.af_wish_sample.af_type.name
        )
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['answered_flg'], self.af_wish_sample.answered_flg
        )
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['proposal_status'],
            self.af_wish_sample.proposal_status,
        )
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['order_status'], self.af_wish_sample.order_status
        )
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['order_date'],
            self.af_wish_sample.order_date.isoformat(),
        )
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['order_chance'], self.af_wish_sample.order_chance
        )

        self.assertEqual(len(seko_dict['seko_af']['wishes'][0]['activities']), 2)
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['activities'][0]['id'], self.af_activity_sample.pk
        )
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['activities'][0]['activity_ts'],
            self.af_activity_sample.activity_ts.astimezone(tz).isoformat(),
        )
        self.assertEqual(
            seko_dict['seko_af']['wishes'][0]['activities'][0]['activity'],
            self.af_activity_sample.activity,
        )

        # 施行問合せと回答
        inquiry_ids = [inquiry_dict['id'] for inquiry_dict in seko_dict['inquiries']]
        self.assertEqual(inquiry_ids, [self.inquiry_1.pk, self.inquiry_3.pk, self.inquiry_2.pk])

        rec_inquiry: Dict = seko_dict['inquiries'][0]
        self.assertEqual(rec_inquiry['id'], self.inquiry_1.pk)
        self.assertTrue('seko' not in rec_inquiry)
        self.assertEqual(rec_inquiry['query_group_id'], self.inquiry_1.query_group_id)
        self.assertEqual(rec_inquiry['query'], self.inquiry_1.query)
        self.assertIsNotNone(rec_inquiry['created_at'])
        self.assertIsNotNone(rec_inquiry['updated_at'])

        self.assertEqual(len(rec_inquiry['answers']), 1)
        rec_answer: Dict = rec_inquiry['answers'][0]
        self.assertEqual(rec_answer['id'], self.answer_1.pk)
        self.assertEqual(rec_answer['answer'], self.answer_1.answer)
        self.assertEqual(rec_answer['staff']['id'], self.answer_1.staff.pk)
        self.assertEqual(rec_answer['staff']['name'], self.answer_1.staff.name)
        self.assertEqual(rec_answer['del_flg'], self.answer_1.del_flg)
        self.assertIsNotNone(rec_answer['created_at'])
        self.assertIsNotNone(rec_answer['updated_at'])

    def test_seko_query_seko_company(self) -> None:
        """施行一覧で葬儀社を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'seko_company': self.company.id}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_seko_query_seko_department(self) -> None:
        """施行一覧で部門を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'seko_department': self.department.id}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # seko_departmentは子拠点も検索対象となるのでseko_1と3がヒットする
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)
        self.assertEqual(
            set([record['id'] for record in records]), set([self.seko_1.pk, self.seko_3.pk])
        )

    def test_seko_query_seko_department_fail_with_nonexistent(self) -> None:
        """施行一覧で存在しない部門を検索して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_existent_department = BaseFactory.build(base_type=Base.OrgType.DEPARTMENT)

        params: Dict = {'seko_department': non_existent_department.id}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['seko_department'])

    def test_seko_query_schedules_hall(self) -> None:
        """施行一覧で式場を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'hall': self.hall.id}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        # hallが一致するSekoScheduleは3件だがSeko単位では2件
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_seko_query_soke_name(self) -> None:
        """施行一覧で葬家名を部分一致で検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'soke_name': '中太'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        params: Dict = {'soke_kana': 'ナカタ'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_seko_query_kojin_name(self) -> None:
        """施行一覧で故人名を部分一致で検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 氏名
        params: Dict = {'kojin_name': '次郎'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        # 一致するKojinは2件だがSeko単位では1件
        params: Dict = {'kojin_name': '山中'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        # カナ
        params: Dict = {'kojin_kana': 'ジロ'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        # 一致するKojinは4件だがSeko単位では2件
        params: Dict = {'kojin_kana': 'ナカ'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_seko_query_moshu_name(self) -> None:
        """施行一覧で喪主名を部分一致で検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'moshu_name': '四郎'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        params: Dict = {'moshu_kana': 'ナカ'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_seko_query_staff(self) -> None:
        """施行一覧で担当者を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 受注担当
        params: Dict = {'order_staff': self.seko_staff.id}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        # 施行担当
        params: Dict = {'seko_staff': self.seko_staff.id}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        # AF担当
        params: Dict = {'af_staff': self.seko_staff.id}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_seko_query_seko_date(self) -> None:
        """施行一覧で施行日を範囲指定(片側でも可能)で検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'seko_date_after': '2020-09-23', 'seko_date_before': '2020-09-24'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        params: Dict = {'seko_date_after': '2020-09-25'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        params: Dict = {'seko_date_before': '2020-09-23'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_seko_query_death_date(self) -> None:
        """施行一覧で(基準)死亡日を範囲指定(片側でも可能)で検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'death_date_after': '2020-09-18', 'death_date_before': '2020-09-19'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        params: Dict = {'death_date_after': '2020-09-20'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        params: Dict = {'death_date_before': '2020-09-18'}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_seko_list_failed_without_auth(self) -> None:
        """施行一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        order_staff = StaffFactory()
        seko_staff = StaffFactory()
        af_staff = StaffFactory()
        seko_company = BaseFactory(base_type=Base.OrgType.COMPANY)
        seko_department = BaseFactory(base_type=Base.OrgType.DEPARTMENT)
        seko_style = SekoStyleFactory()
        self.seko_data = SekoFactory.build(
            order_staff=order_staff,
            seko_staff=seko_staff,
            af_staff=af_staff,
            seko_company=seko_company,
            seko_department=seko_department,
            seko_style=seko_style,
            fuho_site_admission_ts=None,
        )
        self.moshu_data = MoshuFactory.build(seko=self.seko_data)
        self.kojin_data_1 = KojinFactory.build(seko=self.seko_data)
        self.kojin_data_2 = KojinFactory.build(seko=self.seko_data)

        hall1 = BaseFactory(base_type=Base.OrgType.HALL)
        hall2 = BaseFactory(base_type=Base.OrgType.HALL)
        self.schedule_data_1 = SekoScheduleFactory.build(
            seko=self.seko_data, hall=hall1, schedule=ScheduleFactory()
        )
        self.schedule_data_2 = SekoScheduleFactory.build(
            seko=self.seko_data, hall=hall2, schedule=ScheduleFactory()
        )
        self.schedule_data_3 = SekoScheduleFactory.build(
            seko=self.seko_data, hall=hall1, schedule=ScheduleFactory()
        )
        self.schedule_data_4 = SekoScheduleFactory.build(
            seko=self.seko_data, hall=hall2, schedule=ScheduleFactory()
        )

        self.video_data_1 = SekoVideoFactory.build(seko=self.seko_data)
        self.video_data_2 = SekoVideoFactory.build(seko=self.seko_data)
        self.video_data_3 = SekoVideoFactory.build(seko=self.seko_data)

        self.item_data_1 = SekoItemFactory.build(seko=self.seko_data, item=ItemFactory())
        self.item_data_2 = SekoItemFactory.build(seko=self.seko_data, item=ItemFactory())

        self.service_data_1 = SekoServiceFactory.build(
            seko=self.seko_data, hall_service=ServiceFactory()
        )
        self.service_data_2 = SekoServiceFactory.build(
            seko=self.seko_data, hall_service=ServiceFactory()
        )

        self.kakegami_data = HenreihinKakegamiFactory.build(seko=self.seko_data)

        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        dummyfile: str = (
            'data:image/gif;base64,R0lGODlhAQABAIAAAP///////yH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=='
        )
        moshu: Dict = {
            'name': self.moshu_data.name,
            'kana': self.moshu_data.kana,
            'kojin_moshu_relationship': self.moshu_data.kojin_moshu_relationship,
            'zip_code': self.moshu_data.zip_code,
            'prefecture': self.moshu_data.prefecture,
            'address_1': self.moshu_data.address_1,
            'address_2': self.moshu_data.address_2,
            'address_3': self.moshu_data.address_3,
            'tel': self.moshu_data.tel,
            'mobile_num': self.moshu_data.mobile_num,
            'mail_address': self.moshu_data.mail_address,
            'password': self.moshu_data.password,
            'agree_ts': self.moshu_data.agree_ts,
            'soke_site_del_request_flg': self.moshu_data.soke_site_del_request_flg,
            'soke_site_del_flg': self.moshu_data.soke_site_del_flg,
            'mail_flg': self.moshu_data.mail_flg,
        }
        kojin: List[Dict] = []
        for kojin_data in [self.kojin_data_1, self.kojin_data_2]:
            kojin.append(
                {
                    'kojin_num': kojin_data.kojin_num,
                    'name': kojin_data.name,
                    'kana': kojin_data.kana,
                    'kaimyo': kojin_data.kaimyo,
                    'iei_file_name': dummyfile,
                    'moshu_kojin_relationship': kojin_data.moshu_kojin_relationship,
                    'birth_date': kojin_data.birth_date.strftime('%Y-%m-%d'),
                    'death_date': kojin_data.death_date.strftime('%Y-%m-%d'),
                    'age_kbn': kojin_data.age_kbn,
                    'age': kojin_data.age,
                    'note': kojin_data.note,
                }
            )
        schedules: List[Dict] = []
        for schedule in [
            self.schedule_data_1,
            self.schedule_data_2,
            self.schedule_data_3,
            self.schedule_data_4,
        ]:
            schedules.append(
                {
                    'schedule': schedule.schedule.id,
                    'schedule_name': schedule.schedule_name,
                    'hall': schedule.hall.id,
                    'hall_name': schedule.hall_name,
                    'hall_zip_code': schedule.hall_zip_code,
                    'hall_address': schedule.hall_address,
                    'hall_tel': schedule.hall_tel,
                    'schedule_date': schedule.schedule_date.strftime('%Y-%m-%d'),
                    'begin_time': schedule.begin_time,
                    'end_time': schedule.end_time,
                    'display_num': schedule.display_num,
                }
            )
        videos: List[Dict] = []
        for video in [self.video_data_1, self.video_data_2, self.video_data_3]:
            videos.append(
                {
                    'title': video.title,
                    'live_begin_ts': video.live_begin_ts,
                    'live_end_ts': video.live_end_ts,
                    'delivery_end_ts': video.delivery_end_ts,
                    'youtube_code': video.youtube_code,
                }
            )
        new_items: List[Dict] = []
        for sekoitem in [self.item_data_1, self.item_data_2]:
            new_items.append(
                {
                    'item': sekoitem.item.pk,
                }
            )

        services: List[Dict] = []
        for service in [self.service_data_1, self.service_data_2]:
            services.append(
                {
                    'hall_service': service.hall_service.pk,
                    'limit_ts': service.limit_ts,
                    'henreihin_select_flg': service.henreihin_select_flg,
                }
            )

        kakegami = {
            'omotegaki': self.kakegami_data.omotegaki,
            'mizuhiki': self.kakegami_data.mizuhiki,
            'package_type_name': self.kakegami_data.package_type_name,
            'okurinushi_name': self.kakegami_data.okurinushi_name,
        }

        return {
            'order_staff': self.seko_data.order_staff.id,
            'seko_staff': self.seko_data.seko_staff.id,
            'af_staff': self.seko_data.af_staff.id,
            'seko_company': self.seko_data.seko_company.id,
            'seko_department': self.seko_data.seko_department.id,
            'death_date': self.seko_data.death_date.strftime('%Y-%m-%d'),
            'seko_date': self.seko_data.seko_date.strftime('%Y-%m-%d'),
            'soke_name': self.seko_data.soke_name,
            'soke_kana': self.seko_data.soke_kana,
            'seko_style': self.seko_data.seko_style.id,
            'seko_style_name': self.seko_data.seko_style_name,
            'fuho_site_end_date': self.seko_data.fuho_site_end_date.strftime('%Y-%m-%d'),
            'fuho_sentence': self.seko_data.fuho_sentence,
            'fuho_contact_name': self.seko_data.fuho_contact_name,
            'fuho_contact_tel': self.seko_data.fuho_contact_tel,
            'henreihin_rate': self.seko_data.henreihin_rate,
            'invoice_file_name': b64encode(self.seko_data.invoice_file_name.read()),
            'note': self.seko_data.note,
            'moshu': moshu,
            'kojin': kojin,
            'schedules': schedules,
            'videos': videos,
            'items': new_items,
            'services': services,
            'henrei_kakegami': kakegami,
        }

    def test_seko_create_succeed(self) -> None:
        """施行を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['moshu']['agree_ts'] = timezone.now()
        response = self.api_client.post(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record.get('soke_name'), self.seko_data.soke_name)
        self.assertEqual(record.get('soke_kana'), self.seko_data.soke_kana)
        self.assertIsNone(record.get('fuho_site_admission_ts'))
        moshu: Dict = record.get('moshu', {})
        self.assertEqual(moshu.get('name'), self.moshu_data.name)
        self.assertEqual(moshu.get('kana'), self.moshu_data.kana)
        self.assertIsNotNone(moshu.get('agree_ts'))
        self.assertEqual(moshu.get('soke_site_del_flg'), self.moshu_data.soke_site_del_flg)
        self.assertEqual(moshu.get('mail_flg'), self.moshu_data.mail_flg)
        kojin: List[Dict] = record.get('kojin', [])
        self.assertEqual(len(kojin), 2)
        for kojin_rec in kojin:
            self.assertIsNotNone(kojin_rec.get('name'))
            self.assertIsNotNone(kojin_rec.get('kana'))
        schedules: List[Dict] = record.get('schedules', [])
        self.assertEqual(len(schedules), 4)
        for schedule in schedules:
            self.assertIsNotNone(schedule.get('schedule_name'))
            self.assertIsNotNone(schedule.get('hall_name'))
        videos: List[Dict] = record.get('videos', [])
        self.assertEqual(len(videos), 3)
        for video in videos:
            self.assertIsNotNone(video.get('title'))
            self.assertIsNotNone(video.get('youtube_code'))

        items: List[Dict] = record.get('items', [])
        self.assertEqual(len(items), 2)
        self.assertSetEqual(
            set([rec['item']['id'] for rec in items]),
            set([self.item_data_1.item.pk, self.item_data_2.item.pk]),
        )
        self.assertIsNotNone(items[0]['created_at'])
        self.assertIsNotNone(items[0]['updated_at'])
        services: List[Dict] = record.get('services', [])
        self.assertEqual(len(services), 2)
        self.assertSetEqual(
            set([rec['hall_service']['id'] for rec in services]),
            set([self.service_data_1.hall_service.pk, self.service_data_2.hall_service.pk]),
        )
        self.assertIsNotNone(services[0]['created_at'])
        self.assertIsNotNone(services[0]['updated_at'])
        kakegami: Dict = record.get('henrei_kakegami', {})
        self.assertEqual(kakegami.get('omotegaki'), self.kakegami_data.omotegaki)
        self.assertEqual(kakegami.get('mizuhiki'), self.kakegami_data.mizuhiki)
        self.assertIsNotNone(kakegami['created_at'])
        self.assertIsNotNone(kakegami['updated_at'])

    def test_seko_create_succeed_with_some_empty_field(self) -> None:
        """施行の追加は請求書ファイル、喪主のパスワード、故人の遺影ファイルが空文字でも成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['moshu']['password'] = ''
        new_kojin: List[Dict] = []
        for a_kojin in params['kojin']:
            b_kojin: Dict = a_kojin.copy()
            b_kojin['iei_file_name'] = ''
            new_kojin.append(b_kojin)
        params['kojin'] = new_kojin
        params['invoice_file_name'] = ''
        response = self.api_client.post(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record.get('soke_name'), self.seko_data.soke_name)
        self.assertEqual(record.get('soke_kana'), self.seko_data.soke_kana)
        self.assertIsNone(record.get('fuho_site_admission_ts'))
        moshu: Dict = record.get('moshu', {})
        self.assertEqual(moshu.get('name'), self.moshu_data.name)
        self.assertEqual(moshu.get('kana'), self.moshu_data.kana)
        kojin: List[Dict] = record.get('kojin', [])
        self.assertEqual(len(kojin), 2)
        for kojin_rec in kojin:
            self.assertIsNotNone(kojin_rec.get('name'))
            self.assertIsNotNone(kojin_rec.get('kana'))
        schedules: List[Dict] = record.get('schedules', [])
        self.assertEqual(len(schedules), 4)
        for schedule in schedules:
            self.assertIsNotNone(schedule.get('schedule_name'))
            self.assertIsNotNone(schedule.get('hall_name'))
        videos: List[Dict] = record.get('videos', [])
        self.assertEqual(len(videos), 3)
        for video in videos:
            self.assertIsNotNone(video.get('title'))
            self.assertIsNotNone(video.get('youtube_code'))

    def test_seko_create_succeed_without_some_field(self) -> None:
        """施行の追加は請求書ファイル、喪主のパスワード、故人の遺影ファイルの項目がなくても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['moshu'].pop('password')
        new_kojin: List[Dict] = []
        for a_kojin in params['kojin']:
            new_kojin.append(
                dict(
                    [(key, val) for (key, val) in a_kojin.items() if key not in ['iei_file_name']]
                )
            )
        params['kojin'] = new_kojin
        params.pop('invoice_file_name')
        response = self.api_client.post(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record.get('soke_name'), self.seko_data.soke_name)
        self.assertEqual(record.get('soke_kana'), self.seko_data.soke_kana)
        self.assertEqual(type(record.get('seko_style')), int)
        self.assertIsNone(record.get('fuho_site_admission_ts'))
        moshu: Dict = record.get('moshu', {})
        self.assertEqual(moshu.get('name'), self.moshu_data.name)
        self.assertEqual(moshu.get('kana'), self.moshu_data.kana)
        kojin: List[Dict] = record.get('kojin', [])
        self.assertEqual(len(kojin), 2)
        for kojin_rec in kojin:
            self.assertIsNotNone(kojin_rec.get('name'))
            self.assertIsNotNone(kojin_rec.get('kana'))
        schedules: List[Dict] = record.get('schedules', [])
        self.assertEqual(len(schedules), 4)
        for schedule in schedules:
            self.assertIsNotNone(schedule.get('schedule_name'))
            self.assertIsNotNone(schedule.get('hall_name'))
        videos: List[Dict] = record.get('videos', [])
        self.assertEqual(len(videos), 3)
        for video in videos:
            self.assertIsNotNone(video.get('title'))
            self.assertIsNotNone(video.get('youtube_code'))

    def test_seko_create_not_require_soke_site_del_flg_and_mail_flg(self) -> None:
        """施行追加APIは葬家専用ページ削除と案内メールフラグを入力必須にしない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        del params['moshu']['soke_site_del_flg']
        del params['moshu']['mail_flg']
        response = self.api_client.post(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        moshu: Dict = record.get('moshu', {})
        self.assertFalse(moshu['soke_site_del_flg'])
        self.assertTrue(moshu['mail_flg'])

    def test_seko_create_tel_or_mobile_num_is_required(self) -> None:
        """施行追加APIでは喪主の電話番号か携帯電話番号のいずれかは入力必須"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 電話番号が空文字でもOK
        params: Dict = self.basic_params()
        params['moshu']['tel'] = ''

        response = self.api_client.post(reverse('seko:seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        moshu: Dict = record.get('moshu', {})
        self.assertEqual(moshu.get('tel'), '')
        self.assertEqual(moshu.get('mobile_num'), params['moshu']['mobile_num'])

        # 携帯電話番号が空文字でもOK
        params: Dict = self.basic_params()
        params['moshu']['mobile_num'] = ''

        response = self.api_client.post(reverse('seko:seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        moshu: Dict = record.get('moshu', {})
        self.assertEqual(moshu.get('tel'), params['moshu']['tel'])
        self.assertEqual(moshu.get('mobile_num'), '')

        # 電話番号と携帯電話番号の両方が空文字だとNG
        # params: Dict = self.basic_params()
        # params['moshu']['tel'] = ''
        # params['moshu']['mobile_num'] = ''

        # response = self.api_client.post(reverse('seko:seko_list'), data=params, format='json')
        # self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # record: Dict = response.json()
        # self.assertIsNotNone(record['moshu'].get('tel'))

    def test_seko_create_succeed_without_kakegami(self) -> None:
        """施行追加APIは返礼品掛け紙の項目がなくても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params.pop('henrei_kakegami')
        response = self.api_client.post(reverse('seko:seko_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertIsNone(record['henrei_kakegami'])

    def test_seko_create_failed_without_auth(self) -> None:
        """施行追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}

        response = self.api_client.post(reverse('seko:seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
