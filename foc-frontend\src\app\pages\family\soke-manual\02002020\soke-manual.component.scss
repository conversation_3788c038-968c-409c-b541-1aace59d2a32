@import "src/assets/scss/customer/setting";
.container .inner .contents {
  margin: 0px auto 50px;
  padding-bottom: 80px;
  &.cm {
    padding: 20px 0;

  }
  h2 {
    margin: 50px auto 40px;
    @media screen and (max-width: 560px) {
      margin: 40px auto 35px;
    }
  }
  &.with-background-image {
    background: url(../../../../../assets/img/customer/bg.png) no-repeat #ffffff;
    background-size: 40%;
    background-position: right bottom;
  }

  .box {
    width: 80%;
    margin: 0 auto;
    text-align: left;
    line-height: 35px;
    padding: 10px 0;
  }

  .box > p{
    padding: 30px 0;
    line-height: 25px;    
    font-weight: bold;
    color: #c41d5d;
  }

  .box  img{
    width: 100%;
  }
  .box .menu {
    margin-bottom: -550px;
    .index {
      width: 80%;
      position: relative;
      margin: auto;
      cursor: pointer;
      background-color: #ffffff;
      opacity: 0;
      &:hover {
        color: inherit;
        opacity: 0.5;
    }
    }
  }
  .box .detail {
    .tag {
      position: relative;
    }
  }
}