from django.db import models
from django.utils.translation import gettext_lazy as _

from bases.models import Base
from masters.models import Schedule, Service


class ServiceReceptionTerm(models.Model):
    class UnitType(models.IntegerChoices):
        DATE = 1, _('Date')
        HOUR = 2, _('Hour')

    department = models.ForeignKey(
        Base,
        models.DO_NOTHING,
        verbose_name=_('department'),
        related_name='service_reception_terms',
    )
    service = models.ForeignKey(
        Service,
        models.DO_NOTHING,
        verbose_name=_('service'),
        related_name='service_reception_terms',
    )
    schedule = models.ForeignKey(
        Schedule,
        models.DO_NOTHING,
        verbose_name=_('schedule'),
        related_name='service_reception_terms',
    )
    limit_time = models.IntegerField(_('limit time'))
    unit = models.IntegerField(_('unit'), choices=UnitType.choices)
    limit_hour = models.IntegerField(_('limit hour'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    class Meta:
        db_table = 'm_service_reception_term'
