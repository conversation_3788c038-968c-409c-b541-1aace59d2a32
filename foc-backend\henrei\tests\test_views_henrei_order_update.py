from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from henrei.models import OrderHenreihin
from henrei.tests.factories import (
    HenreihinKodenFactory,
    HenreihinKumotsuFactory,
    OrderHenreihinFactory,
)
from staffs.models import Staff
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

tz = timezone.get_current_timezone()
jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake_provider = Faker(locale='ja_JP')


class OrderHenreihinUpdateStatusViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.henrei_order_1 = OrderHenreihinFactory(
            order_status=1, order_ts=None, order_staff=None
        )
        HenreihinKodenFactory(
            henrei_order=self.henrei_order_1,
            order_status=self.henrei_order_1.order_status,
            supplier=self.henrei_order_1.supplier,
        )
        HenreihinKodenFactory(
            henrei_order=self.henrei_order_1,
            order_status=self.henrei_order_1.order_status,
            supplier=self.henrei_order_1.supplier,
        )
        HenreihinKumotsuFactory(
            henrei_order=self.henrei_order_1,
            order_status=self.henrei_order_1.order_status,
            supplier=self.henrei_order_1.supplier,
        )
        self.henrei_order_2 = OrderHenreihinFactory(
            order_status=1, order_ts=None, order_staff=None
        )
        HenreihinKodenFactory(
            henrei_order=self.henrei_order_2,
            order_status=self.henrei_order_2.order_status,
            supplier=self.henrei_order_2.supplier,
        )
        HenreihinKumotsuFactory(
            henrei_order=self.henrei_order_2,
            order_status=self.henrei_order_2.order_status,
            supplier=self.henrei_order_2.supplier,
        )
        HenreihinKumotsuFactory(
            henrei_order=self.henrei_order_2,
            order_status=self.henrei_order_2.order_status,
            supplier=self.henrei_order_2.supplier,
        )
        self.henrei_order_3 = OrderHenreihinFactory(
            order_status=1, order_ts=None, order_staff=None
        )
        HenreihinKodenFactory(
            henrei_order=self.henrei_order_3,
            order_status=self.henrei_order_3.order_status,
            supplier=self.henrei_order_3.supplier,
        )
        HenreihinKumotsuFactory(
            henrei_order=self.henrei_order_3,
            order_status=self.henrei_order_3.order_status,
            supplier=self.henrei_order_3.supplier,
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'ids': [elem.pk for elem in [self.henrei_order_1, self.henrei_order_2]],
            'order_status': 2,
        }

    def test_henrei_order_status_update_succeed(self) -> None:
        """返礼品発注(複数)の発注ステータスを2以外に更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['order_status'] = 3
        response = self.api_client.post(
            reverse('henrei:henrei_order_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(len(record), 2)
        # 2以外の場合はorder_tsとorder_staffは変更なし
        for record_order, self_order in zip(record, [self.henrei_order_1, self.henrei_order_2]):
            self.assertEqual(record_order['order_status'], 3)
            self.assertIsNone(record_order['order_ts'])
            self.assertIsNone(record_order['order_staff'])
            for record_koden in record_order['henrei_koden']:
                self.assertEqual(record_koden['order_status'], 3)
            for record_kumotsu in record_order['henrei_kumotsu']:
                self.assertEqual(record_kumotsu['order_status'], 3)

        for henrei_order in [self.henrei_order_1, self.henrei_order_2, self.henrei_order_3]:
            henrei_order.refresh_from_db()
        self.assertEqual(self.henrei_order_1.order_status, 3)
        self.assertEqual(self.henrei_order_2.order_status, 3)
        self.assertEqual(self.henrei_order_3.order_status, 1)

    def test_henrei_order_status_update_succeed_2(self) -> None:
        """返礼品発注(複数)の発注ステータスを2に更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_order_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(len(record), 2)
        for record_order, self_order in zip(record, [self.henrei_order_1, self.henrei_order_2]):
            self.assertEqual(record_order['order_status'], 2)
            self.assertIsNotNone(record_order['order_ts'])
            self.assertEqual(record_order['order_staff'], self.staff.pk)
            for record_koden in record_order['henrei_koden']:
                self.assertEqual(record_koden['order_status'], 2)
            for record_kumotsu in record_order['henrei_kumotsu']:
                self.assertEqual(record_kumotsu['order_status'], 2)

        for henrei_order in [self.henrei_order_1, self.henrei_order_2, self.henrei_order_3]:
            henrei_order.refresh_from_db()
        self.assertEqual(self.henrei_order_1.order_status, 2)
        self.assertEqual(self.henrei_order_2.order_status, 2)
        self.assertEqual(self.henrei_order_3.order_status, 1)

    def test_henrei_order_status_update_succeed_back_to_2(self) -> None:
        """返礼品発注(複数)の発注ステータスを2に更新する際、既にorder_tsが入っている場合は更新しない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        another_staff: Staff = StaffFactory()
        for henrei_order in [self.henrei_order_1, self.henrei_order_2]:
            henrei_order.order_status = 3
            henrei_order.order_ts = timezone.localtime()
            henrei_order.order_staff = another_staff
            henrei_order.save()
            for henrei_koden in henrei_order.henrei_koden.all():
                henrei_koden.order_status = 3
                henrei_koden.save()
            for henrei_kumotsu in henrei_order.henrei_kumotsu.all():
                henrei_kumotsu.order_status = 3
                henrei_kumotsu.save()

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_order_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(len(record), 2)
        for record_order, self_order in zip(record, [self.henrei_order_1, self.henrei_order_2]):
            self.assertEqual(record_order['order_status'], 2)
            self.assertEqual(
                record_order['order_ts'], self_order.order_ts.astimezone(tz).isoformat()
            )
            self.assertEqual(record_order['order_staff'], another_staff.pk)
            for record_koden in record_order['henrei_koden']:
                self.assertEqual(record_koden['order_status'], 2)
            for record_kumotsu in record_order['henrei_kumotsu']:
                self.assertEqual(record_kumotsu['order_status'], 2)

        for henrei_order in [self.henrei_order_1, self.henrei_order_2, self.henrei_order_3]:
            henrei_order.refresh_from_db()
        self.assertEqual(self.henrei_order_1.order_status, 2)
        self.assertEqual(self.henrei_order_2.order_status, 2)
        self.assertEqual(self.henrei_order_3.order_status, 1)

    def test_henrei_order_status_update_failed_by_illegal_pk(self) -> None:
        """返礼品発注の発注ステータス更新APIが不正なPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_order: OrderHenreihin = OrderHenreihinFactory.build()
        params: Dict = self.basic_params()
        params['ids'].append(non_saved_order.pk)

        response = self.api_client.post(
            reverse('henrei:henrei_order_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('ids'))

    def test_henrei_order_status_update_failed_without_auth(self) -> None:
        """返礼品発注の発注ステータス更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_order_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
