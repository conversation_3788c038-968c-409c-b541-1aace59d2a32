
<div class="container">
  <div class="inner with_footer">
    <div class="contents">
      <div class="menu_title">
        <i class="building icon big"></i>
        拠点
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini" (click)="reLoadData()">
          <i class="sync icon"></i>最新情報取得
        </button>
      </div>
      <div class="ui attached tabular menu">
        <div class="item" data-tab="company" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN"><i class="building outline icon"></i>葬儀社</div>
        <div class="item" data-tab="depart"><i class="gopuram icon"></i>部門</div>
        <div class="item" data-tab="hall"><i class="landmark icon"></i>式場</div>
      </div>
      <div class="ui attached tab segment company" data-tab="company" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN">
        <div class="ui left floated left pagination menu" *ngIf="pagination_company.pages.length > 1">
          <div class="count" *ngIf="company_list?.length">
            全{{company_list?.length}}件中
            {{(pagination_company.current-1)*page_per_count+1}}件
            ～
            {{pagination_company.current===pagination_company.pages.length?company_list?.length:pagination_company.current*page_per_count}}件表示
          </div>
          <a class="icon item" [class.disabled]="pagination_company.current===1" (click)="pageTo(pagination_company, pagination_company.current-1)">
            <i class="left chevron icon"></i>
          </a>
          <ng-container *ngFor="let page of pagination_company.pages">
            <a class="item" [class.current]="page===pagination_company.current" (click)="pageTo(pagination_company, page)">{{page}}</a>
          </ng-container>
          <a class="icon item" [class.disabled]="pagination_company.current===pagination_company.pages.length" (click)="pageTo(pagination_company, pagination_company.current+1)">
            <i class="right chevron icon"></i>
          </a>
        </div>
        <div class="table_fixed head">
          <table class="ui celled structured unstackable table">
            <thead>
              <tr class="center aligned" [class.no-data]="!company_list?.length">
                <th class="base_name">名称</th>
                <th class="zip_code">郵便番号</th>
                <th class="prefecture">都道府県</th>
                <th class="address_1">住所1</th>
                <th class="address_2">住所2</th>
                <th class="address_3">住所3</th>
                <th class="company_code">会社コード</th>
                <th class="tel">TEL</th>
                <th class="fax">FAX</th>
                <th class="logo">ロゴ</th>
                <th class="calc_type">税計算区分</th>
                <th class="operation"></th>
              </tr>
            </thead>
          </table>
        </div>
        <div class="table_fixed body">
          <table class="ui celled structured unstackable table">
            <tbody>
              <ng-container *ngFor="let base of company_list; index as i">
                <tr (click)="showBase($event, base)" *ngIf="i >= (pagination_company.current-1)*page_per_count && i < pagination_company.current*page_per_count" >
                  <td class="base_name" title="{{base.base_name}}">{{base.base_name}}</td>
                  <td class="zip_code" title="{{base.zip_code}}">{{base.zip_code}}</td>
                  <td class="prefecture" title="{{base.prefecture}}">{{base.prefecture}}</td>
                  <td class="address_1" title="{{base.address_1}}">{{base.address_1}}</td>
                  <td class="address_2" title="{{base.address_2}}">{{base.address_2}}</td>
                  <td class="address_3" title="{{base.address_3}}">{{base.address_3}}</td>
                  <td class="company_code" title="{{base.company_code}}">{{base.company_code}}</td>
                  <td class="tel" title="{{base.tel}}">{{base.tel}}</td>
                  <td class="fax" title="{{base.fax}}">{{base.fax}}</td>
                  <td class="logo"><div class="image"><img src="{{base.company_logo_file}}" *ngIf="base.company_logo_file"></div></td>
                  <td class="calc_type" title="{{calcTypeName(base.calc_type)}}">{{calcTypeName(base.calc_type)}}</td>
                  <td class="center aligned button operation">
                    <i class="large server icon" title="サービス" (click)="showService(base)"></i>
                    <i class="large money check alternate icon" title="利用料" (click)="showFocFee(base)"></i>
                    <i class="large gavel icon" title="特商法" (click)="showTokusho(base)"></i>
                    <i class="large sms icon" title="SMSアカウント" (click)="showSmsAccount(base)"></i>
                    <i class="large trash alternate icon" title="削除" (click)="deleteData(base)" [class.disabled]="base.children && base.children.length"></i>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </div>
        <div class="table_fixed body">
          <table class="ui celled structured unstackable table">
            <tbody>
              <tr title="新規追加" (click)="showBase($event, null, Const.BASE_TYPE_COMPANY, Const.BASE_ID_MSI)">
                <td colspan="12" class="center aligned"><i class="large add icon"></i></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="ui attached tab segment depart" data-tab="depart">
        <div class="search_area">
          <div class="line">
            <label>葬儀社</label>
            <com-dropdown #departCompanyComboEm [settings]="departCompanyCombo" [(selectedValue)]="depart_company_id" (selectedItemChange)="departCompanyChange($event)"></com-dropdown>
          </div>
        </div>
        <div class="ui left floated left pagination menu" *ngIf="pagination_depart.pages.length > 1">
          <div class="count" *ngIf="depart_list?.length">
            全{{depart_list?.length}}件中
            {{(pagination_depart.current-1)*page_per_count+1}}件
            ～
            {{pagination_depart.current===pagination_depart.pages.length?depart_list?.length:pagination_depart.current*page_per_count}}件表示
          </div>
          <a class="icon item" [class.disabled]="pagination_depart.current===1" (click)="pageTo(pagination_depart, pagination_depart.current-1)">
            <i class="left chevron icon"></i>
          </a>
          <ng-container *ngFor="let page of pagination_depart.pages">
            <a class="item" [class.current]="page===pagination_depart.current" (click)="pageTo(pagination_depart, page)">{{page}}</a>
          </ng-container>
          <a class="icon item" [class.disabled]="pagination_depart.current===pagination_depart.pages.length" (click)="pageTo(pagination_depart, pagination_depart.current+1)">
            <i class="right chevron icon"></i>
          </a>
        </div>
        <div class="table_fixed head">
          <table class="ui celled structured unstackable table">
            <thead>
              <tr class="center aligned" [class.no-data]="!depart_list?.length">
                <th class="display_num">表示順</th>
                <th class="base_name">名称</th>
                <th class="zip_code">郵便番号</th>
                <th class="prefecture">都道府県</th>
                <th class="address_1">住所1</th>
                <th class="address_2">住所2</th>
                <th class="address_3">住所3</th>
                <th class="tel">TEL</th>
                <th class="fax">FAX</th>
                <th class="fdn_code">FDNコード</th>
                <th class="operation"></th>
              </tr>
            </thead>
          </table>
        </div>
        <div class="table_fixed body">
          <table class="ui celled structured unstackable table">
            <tbody>
              <ng-container *ngFor="let base of depart_list; index as i">
                <tr (click)="showBase($event, base)" *ngIf="i >= (pagination_depart.current-1)*page_per_count && i < pagination_depart.current*page_per_count" >
                  <td class="display_num center aligned" title="{{base.display_num}}">{{base.display_num}}</td>
                  <td class="base_name" title="{{base.base_name}}">{{base.base_name}}</td>
                  <td class="zip_code" title="{{base.zip_code}}">{{base.zip_code}}</td>
                  <td class="prefecture" title="{{base.prefecture}}">{{base.prefecture}}</td>
                  <td class="address_1" title="{{base.address_1}}">{{base.address_1}}</td>
                  <td class="address_2" title="{{base.address_2}}">{{base.address_2}}</td>
                  <td class="address_3" title="{{base.address_3}}">{{base.address_3}}</td>
                  <td class="tel" title="{{base.tel}}">{{base.tel}}</td>
                  <td class="fax" title="{{base.fax}}">{{base.fax}}</td>
                  <td class="fdn_code" title="{{displayFdnCode(base.fdn_code)}}">{{displayFdnCode(base.fdn_code)}}</td>
                  <td class="center aligned button operation">
                    <i class="large trash alternate icon" title="削除" (click)="deleteData(base)" [class.disabled]="base.children && base.children.length"></i>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </div>
        <div class="table_fixed body" *ngIf="depart_company_id">
          <table class="ui celled structured unstackable table">
            <tbody>
              <tr title="新規追加" (click)="showBase($event, null, Const.BASE_TYPE_DEPART, depart_company_id, depart_company_code)">
                <td colspan="11" class="center aligned"><i class="large add icon"></i></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="ui attached tab segment hall" data-tab="hall">
        <div class="search_area">
          <div class="line">
            <label>葬儀社</label>
            <com-dropdown #hallCompanyComboEm [settings]="hallCompanyCombo" [(selectedValue)]="hall_company_id" (selectedItemChange)="hallCompanyChange($event, departComboEm)"></com-dropdown>
            <label>部門</label>
            <com-dropdown #departComboEm [settings]="departCombo" [(selectedValue)]="hall_depart_id" (selectedItemChange)="hallDepartChange($event)"></com-dropdown>
          </div>
        </div>
        <div class="ui left floated left pagination menu" *ngIf="pagination_hall.pages.length > 1">
          <div class="count" *ngIf="hall_list?.length">
            全{{hall_list?.length}}件中
            {{(pagination_hall.current-1)*page_per_count+1}}件
            ～
            {{pagination_hall.current===pagination_hall.pages.length?hall_list?.length:pagination_hall.current*page_per_count}}件表示
          </div>
          <a class="icon item" [class.disabled]="pagination_hall.current===1" (click)="pageTo(pagination_hall, pagination_hall.current-1)">
            <i class="left chevron icon"></i>
          </a>
          <ng-container *ngFor="let page of pagination_hall.pages">
            <a class="item" [class.current]="page===pagination_hall.current" (click)="pageTo(pagination_hall, page)">{{page}}</a>
          </ng-container>
          <a class="icon item" [class.disabled]="pagination_hall.current===pagination_hall.pages.length" (click)="pageTo(pagination_hall, pagination_hall.current+1)">
            <i class="right chevron icon"></i>
          </a>
        </div>
        <div class="table_fixed head">
          <table class="ui celled structured unstackable table">
            <thead>
              <tr class="center aligned" [class.no-data]="!hall_list?.length">
                <th class="display_num">表示順</th>
                <th class="base_name">名称</th>
                <th class="zip_code">郵便番号</th>
                <th class="prefecture">都道府県</th>
                <th class="address_1">住所1</th>
                <th class="address_2">住所2</th>
                <th class="address_3">住所3</th>
                <th class="tel">TEL</th>
                <th class="fax">FAX</th>
                <th class="fdn_code">FDNコード</th>
                <th class="map">地図</th>
                <th class="operation"></th>
              </tr>
            </thead>
          </table>
        </div>
        <div class="table_fixed body">
          <table class="ui celled structured unstackable table">
            <tbody>
              <ng-container *ngFor="let base of hall_list; index as i">
                <tr (click)="showBase($event, base)" *ngIf="i >= (pagination_hall.current-1)*page_per_count && i < pagination_hall.current*page_per_count" >
                  <td class="display_num center aligned" title="{{base.display_num}}">{{base.display_num}}</td>
                  <td class="base_name" title="{{base.base_name}}">{{base.base_name}}</td>
                  <td class="zip_code" title="{{base.zip_code}}">{{base.zip_code}}</td>
                  <td class="prefecture" title="{{base.prefecture}}">{{base.prefecture}}</td>
                  <td class="address_1" title="{{base.address_1}}">{{base.address_1}}</td>
                  <td class="address_2" title="{{base.address_2}}">{{base.address_2}}</td>
                  <td class="address_3" title="{{base.address_3}}">{{base.address_3}}</td>
                  <td class="tel" title="{{base.tel}}">{{base.tel}}</td>
                  <td class="fax" title="{{base.fax}}">{{base.fax}}</td>
                  <td class="fdn_code" title="{{displayFdnCode(base.fdn_code)}}">{{displayFdnCode(base.fdn_code)}}</td>
                  <td class="map"><div class="image"><img src="{{base.company_map_file}}" *ngIf="base.company_map_file"></div></td>
                  <td class="center aligned button operation">
                    <i class="large trash alternate icon" title="削除" (click)="deleteData(base)" [class.disabled]="base.children && base.children.length"></i>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </div>
        <div class="table_fixed body" *ngIf="hall_depart_id">
          <table class="ui celled structured unstackable table">
            <tbody>
              <tr title="新規追加" (click)="showBase($event, null, Const.BASE_TYPE_PLACE_SELF, hall_depart_id, hall_company_code)">
                <td colspan="12" class="center aligned"><i class="large add icon"></i></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="ui modal small base" id="base-edit">
        <div class="header ui"><i class="building outline icon large"></i>
          {{base_edit.base_type===Const.BASE_TYPE_COMPANY?'葬儀社':base_edit.base_type===Const.BASE_TYPE_DEPART?'部門':'式場'}}{{base_edit_type===1?'登録':'編集'}}
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <label class="required">名称</label>
              <div class="ui input">
                <input type="text" [class.error]="errField.get('base_name')" autocomplete="off" id="base_name" [(ngModel)]="base_edit.base_name">
              </div>
              <ng-container *ngIf="base_edit.base_type!==Const.BASE_TYPE_COMPANY">
                <label class="required">表示順</label>
                <div class="ui input tiny">
                  <input type="number" min="1" [class.error]="errField.get('display_num')" [(ngModel)]="base_edit.display_num">
                </div>
              </ng-container>
              <ng-container *ngIf="base_edit.base_type===Const.BASE_TYPE_COMPANY">
                <label class="required">会社コード</label>
                <div class="ui input">
                  <input type="tel" [class.error]="errField.get('company_code')" autocomplete="off" id="company_code" [(ngModel)]="base_edit.company_code" [disabled]="base_edit.children && base_edit.children.length">
                </div>
              </ng-container>
              <label class="required" [class.hide]="!(base_edit.base_type===Const.BASE_TYPE_PLACE_SELF || base_edit.base_type===Const.BASE_TYPE_PLACE_OTHER)">自営/他営</label>
              <div class="ui radio checkbox" [class.hide]="!(base_edit.base_type===Const.BASE_TYPE_PLACE_SELF || base_edit.base_type===Const.BASE_TYPE_PLACE_OTHER)">
                <input type="radio" name="base_type" [value]="Const.BASE_TYPE_PLACE_SELF" [(ngModel)]="base_edit.base_type" [checked]="base_edit.base_type===Const.BASE_TYPE_PLACE_SELF">
                <label>自営</label>
              </div>
              <div class="ui radio checkbox" [class.hide]="!(base_edit.base_type===Const.BASE_TYPE_PLACE_SELF || base_edit.base_type===Const.BASE_TYPE_PLACE_OTHER)">
                <input type="radio" name="base_type" [value]="Const.BASE_TYPE_PLACE_OTHER" [(ngModel)]="base_edit.base_type" [checked]="base_edit.base_type===Const.BASE_TYPE_PLACE_OTHER">
                <label>他営</label>
              </div>
            </div>
            <div class="line">
              <label class="required">郵便番号</label>
              <div class="ui left icon input">
                <i class="tenge icon"></i>
                <input [class.error]="errField.get('zip_code')" type="tel" placeholder="ハイフンなし" autocomplete="off" id="zip_code" maxlength="7" [(ngModel)]="base_edit.zip_code" (input)="zipcodeChange()" (paste)="pasteZipcode($event)">
              </div>
              <label class="required">都道府県</label>
              <div class="ui input">
                <input [class.error]="errField.get('prefecture')" type="text" autocomplete="off" id="prefecture" [(ngModel)]="base_edit.prefecture">
              </div>
            </div>
            <div class="line">
              <label class="required">住所</label>
              <div class="ui input">
                <input [class.error]="errField.get('address_1')" type="text" autocomplete="off" id="address_1" [(ngModel)]="base_edit.address_1">
              </div>
              <div class="ui input divided">
                <input [class.error]="errField.get('address_2')" type="text" autocomplete="off" id="address_2" [(ngModel)]="base_edit.address_2">
              </div>
              <div class="ui input divided">
                <input [class.error]="errField.get('address_3')" type="text" autocomplete="off" id="address_3" [(ngModel)]="base_edit.address_3">
              </div>
            </div>
            <div class="line">
              <label class="required">電話番号</label>
              <div class="ui left icon input">
                <i class="phone alternate icon"></i>
                <input [class.error]="errField.get('tel')" type="tel" autocomplete="off" id="tel" maxlength="15" [(ngModel)]="base_edit.tel">
              </div>
              <label class="required">FAX番号</label>
              <div class="ui left icon input">
                <i class="fax icon"></i>
                <input [class.error]="errField.get('fax')" type="tel" autocomplete="off" id="fax" maxlength="15" [(ngModel)]="base_edit.fax">
              </div>
            </div>
            <div class="line" [class.hide]="base_edit.base_type!==Const.BASE_TYPE_COMPANY">
              <label class="required">税計算区分</label>
              <div class="ui radio checkbox">
                <input type="radio" name="tax_calc_type" [value]="Const.CALC_TYPE_FLOOR" [(ngModel)]="base_edit.calc_type" [checked]="base_edit.calc_type===Const.CALC_TYPE_FLOOR">
                <label>切捨て</label>
              </div>
              <div class="ui radio checkbox">
                <input type="radio" name="tax_calc_type" [value]="Const.CALC_TYPE_CEIL" [(ngModel)]="base_edit.calc_type" [checked]="base_edit.calc_type===Const.CALC_TYPE_CEIL">
                <label>切上げ</label>
              </div>
              <div class="ui radio checkbox">
                <input type="radio" name="tax_calc_type" [value]="Const.CALC_TYPE_ROUND" [(ngModel)]="base_edit.calc_type" [checked]="base_edit.calc_type===Const.CALC_TYPE_ROUND">
                <label>四捨五入</label>
              </div>
            </div>
            <div class="line" [class.hide]="base_edit.base_type===Const.BASE_TYPE_COMPANY">
              <label>FDNコード</label>
              <div class="ui input">
                <input [class.error]="errField.get('fdn_code')" type="tel" autocomplete="off" id="fdn_code" [(ngModel)]="base_edit.fdn_code">
              </div>
            </div>
            <div class="line" [class.hide]="base_edit.base_type!==Const.BASE_TYPE_COMPANY">
              <label>ロゴ</label>
              <div class="ui card image" (dragover)="onDragOverImage($event)" (drop)="onDropImage($event)">
                <div class="ui image center aligned" (click)="selectFile()" title="ロゴファイル追加">
                  <ng-container *ngIf="!base_edit.logo_file && !base_edit.company_logo_file">
                  <i class="image icon huge"></i>
                  <div class="noimage">No image</div>
                  <div class="desc">この枠をクリックしてファイルを選択するか、ファイルをこの枠にドラッグ・アンド・ドロップしてください。</div>
                  </ng-container>
                  <img src="{{base_edit.logo_file?base_edit.logo_file:base_edit.company_logo_file}}" *ngIf="base_edit.logo_file || base_edit.company_logo_file">
                </div>
                <div class="clear" *ngIf="base_edit.logo_file || base_edit.company_logo_file"><i class="delete icon" (click)="clearImage()" title="クリア"></i></div>
              </div>
            </div>
            <div class="line" [class.hide]="base_edit.base_type!==Const.BASE_TYPE_PLACE_SELF && base_edit.base_type!==Const.BASE_TYPE_PLACE_OTHER">
              <label>地図</label>
              <div class="ui card image" (dragover)="onDragOverImage($event)" (drop)="onDropImage($event, 2)">
                <div class="ui image center aligned" (click)="selectFile(2)" title="地図ファイル追加">
                  <ng-container *ngIf="!base_edit.map_file && !base_edit.company_map_file">
                  <i class="image icon huge"></i>
                  <div class="noimage">No image</div>
                  <div class="desc">この枠をクリックしてファイルを選択するか、ファイルをこの枠にドラッグ・アンド・ドロップしてください。</div>
                  </ng-container>
                  <img src="{{base_edit.map_file?base_edit.map_file:base_edit.company_map_file}}" *ngIf="base_edit.map_file || base_edit.company_map_file">
                </div>
                <div class="clear" *ngIf="base_edit.map_file || base_edit.company_map_file"><i class="delete icon" (click)="clearImage(2)" title="クリア"></i></div>
              </div>
            </div>
            <div class="line">
              <label style="min-width:130px;">注文通知先</label>
              <div class="ui input full">
                <input [class.error]="errField.get('order_mail_address')" type="email" autocomplete="off" id="order_mail_address" [(ngModel)]="base_edit.order_mail_address">
              </div>
            </div>
            <div class="line">
              <div class="description">　　
                顧客の注文メールがBCCで送られます。複数メールアドレスを登録する場合は、カンマ区切りでご入力ください。
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveBase()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal tiny service" id="service-edit">
        <div class="header ui"><i class="server icon large"></i>サービス選択</div>

        <div class="content">
          <div class="item" *ngFor="let service of service_list" [class.selected]="service.selected" (click)="selectService(service)">
            <i class="icon huge {{service.icon}}"></i>
            <div class="subtitle">{{service.name}}</div>
            <div class="ui checkbox">
              <input type="checkbox"  [(ngModel)]="service.selected">
              <label></label>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveService()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal mini fee" id="focfee-edit">
        <div class="header ui"><i class="money check alternate icon large"></i>利用料</div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area">
            <div class="sub_title">
              月額利用料
            </div>
            <div class="line">
              <div class="ui input mini monthly">
                <input type="number" min="0" [class.error]="errField.get('monthly_fee')" autocomplete="off" id="monthly_fee" [(ngModel)]="focfee_edit.monthly_fee">
              </div>
              <label class="plane">円</label>
            </div>
            <div class="sub_title">
              サービス利用料
            </div>
            <div class="line">
              <label class="required">弔文</label>
              <div class="ui input tiny">
                <input type="number" min="0" [class.error]="errField.get('chobun_fee')" autocomplete="off" id="chobun_fee" [(ngModel)]="focfee_edit.chobun_fee">
              </div>
              <label class="plane">％</label>
              <!--
              <label class="plane">{{focfee_edit.chobun_fee_unit===1?'％':focfee_edit.chobun_fee_unit===2?'円':''}}</label>
              <div class="ui radio checkbox">
                <input type="radio" name="chobun_fee_unit" [value]="1" [(ngModel)]="focfee_edit.chobun_fee_unit" [checked]="focfee_edit.chobun_fee_unit===1">
                <label>パーセント</label>
              </div>
              <div class="ui radio checkbox">
                <input type="radio" name="chobun_fee_unit" [value]="2" [(ngModel)]="focfee_edit.chobun_fee_unit" [checked]="focfee_edit.chobun_fee_unit===2">
                <label>金額</label>
              </div>
            </div>
            <div class="line">
              -->
              <label class="required divided">供花・供物</label>
              <div class="ui input tiny">
                <input type="number" min="0" [class.error]="errField.get('kumotsu_fee')" autocomplete="off" id="kumotsu_fee" [(ngModel)]="focfee_edit.kumotsu_fee">
              </div>
              <label class="plane">％</label>
              <!--
              <label class="plane">{{focfee_edit.kumotsu_fee_unit===1?'％':focfee_edit.kumotsu_fee_unit===2?'円':''}}</label>
              <div class="ui radio checkbox">
                <input type="radio" name="kumotsu_fee_unit" [value]="1" [(ngModel)]="focfee_edit.kumotsu_fee_unit" [checked]="focfee_edit.kumotsu_fee_unit===1">
                <label>パーセント</label>
              </div>
              <div class="ui radio checkbox">
                <input type="radio" name="kumotsu_fee_unit" [value]="2" [(ngModel)]="focfee_edit.kumotsu_fee_unit" [checked]="focfee_edit.kumotsu_fee_unit===2">
                <label>金額</label>
              </div>
              -->
            </div>
            <div class="line">
              <label class="required">香典</label>
              <div class="ui input tiny">
                <input type="number" min="0" [class.error]="errField.get('koden_fee')" autocomplete="off" id="koden_fee" [(ngModel)]="focfee_edit.koden_fee">
              </div>
              <label class="plane">％</label>
              <!--
              <label class="plane">{{focfee_edit.koden_fee_unit===1?'％':focfee_edit.koden_fee_unit===2?'円':''}}</label>
              <div class="ui radio checkbox">
                <input type="radio" name="koden_fee_unit" [value]="1" [(ngModel)]="focfee_edit.koden_fee_unit" [checked]="focfee_edit.koden_fee_unit===1">
                <label>パーセント</label>
              </div>
              <div class="ui radio checkbox">
                <input type="radio" name="koden_fee_unit" [value]="2" [(ngModel)]="focfee_edit.koden_fee_unit" [checked]="focfee_edit.koden_fee_unit===2">
                <label>金額</label>
              </div>
            </div>
            <div class="line">
              -->
              <label class="required divided">返礼品</label>
              <div class="ui input tiny">
                <input type="number" min="0" [class.error]="errField.get('henreihin_fee')" autocomplete="off" id="henreihin_fee" [(ngModel)]="focfee_edit.henreihin_fee">
              </div>
              <label class="plane">％</label>
              <!--
              <label class="plane">{{focfee_edit.henreihin_fee_unit===1?'％':focfee_edit.henreihin_fee_unit===2?'円':''}}</label>
              <div class="ui radio checkbox">
                <input type="radio" name="henreihin_fee_unit" [value]="1" [(ngModel)]="focfee_edit.henreihin_fee_unit" [checked]="focfee_edit.henreihin_fee_unit===1">
                <label>パーセント</label>
              </div>
              <div class="ui radio checkbox">
                <input type="radio" name="henreihin_fee_unit" [value]="2" [(ngModel)]="focfee_edit.henreihin_fee_unit" [checked]="focfee_edit.henreihin_fee_unit===2">
                <label>金額</label>
              </div>
              -->
            </div>
            <div class="sub_title">
              GMO契約コード
            </div>
            <div class="line">
              <label>供物・弔文</label>
              <div class="ui input mini">
                <input type="tel" autocomplete="off" maxlength="8" [(ngModel)]="focfee_edit.gmo_code">
              </div>
            </div>
            <div class="line">
              <label>香典</label>
              <div class="ui input mini">
                <input type="tel" autocomplete="off" maxlength="8" [(ngModel)]="focfee_edit.gmo_code_koden">
              </div>
            </div>
            <div class="line">
              <label>GMOテスト環境使用</label>
              <div class="ui toggle checkbox">
                <input type="checkbox" [(ngModel)]="focfee_edit.gmo_test_flg">
                <label></label>
              </div>
            </div>
            <div class="line">
              <label>3Dセキュア認証利用</label>
              <div class="ui toggle checkbox">
                <input type="checkbox" [(ngModel)]="focfee_edit.secure_chk_flg">
                <label></label>
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveFocFee()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal tiny law" id="tokusho-edit">
        <div class="header ui"><i class="gavel icon large"></i>特商法</div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <label class="required">運用責任者</label>
              <div class="ui input full required">
                <input [class.error]="errField.get('responsible_name')" type="text" autocomplete="off" id="responsible_name" [(ngModel)]="tokusho_edit.responsible_name">
              </div>
            </div>
            <div class="line">
              <label class="required">メールアドレス</label>
              <div class="ui input full required">
                <input [class.error]="errField.get('mail_address')" type="email" autocomplete="off" id="mail_address" [(ngModel)]="tokusho_edit.mail_address">
              </div>
            </div>
            <div class="line">
              <label class="required">faximo送信元</label>
              <div class="ui input full required">
                <input [class.error]="errField.get('from_name')" type="email" autocomplete="off" id="from_name" [(ngModel)]="tokusho_edit.from_name">
              </div>
            </div>
            <div class="line">
              <label class="required">URL</label>
              <div class="ui input full required">
                <input [class.error]="errField.get('url')" type="url" autocomplete="off" id="url" [(ngModel)]="tokusho_edit.url">
              </div>
            </div>
            <div class="line">
              <label class="huge">商品提供時期(供花・供物)</label>
              <div class="ui input full">
                <input [class.error]="errField.get('kumotsu_offer_timing')" type="text" autocomplete="off" id="kumotsu_offer_timing" [(ngModel)]="tokusho_edit.kumotsu_offer_timing">
              </div>
            </div>
            <div class="line">
              <label class="huge">商品提供時期(香典)</label>
              <div class="ui input full">
                <input [class.error]="errField.get('koden_offer_timing')" type="text" autocomplete="off" id="koden_offer_timing" [(ngModel)]="tokusho_edit.koden_offer_timing">
              </div>
            </div>
            <div class="line">
              <label class="huge">商品提供時期(返礼品)</label>
              <div class="ui input full">
                <input [class.error]="errField.get('henrei_offer_timing')" type="text" autocomplete="off" id="henrei_offer_timing" [(ngModel)]="tokusho_edit.henrei_offer_timing">
              </div>
            </div>
            <div class="line">
              <label class="huge">事業者番号</label>
              <div class="ui input full">
                <input [class.error]="errField.get('business_no')" type="text" autocomplete="off" id="business_no" [(ngModel)]="tokusho_edit.business_no">
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveTokusho()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal tiny sms-account" id="sms-account-edit">
        <div class="header ui"><i class="sms icon large"></i>SMSアカウント</div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <label class="large required">トークン</label>
              <div class="ui input full required">
                <input [class.error]="errField.get('token')" type="text" autocomplete="off" id="token" [(ngModel)]="sms_account_edit.token">
              </div>
            </div>
            <div class="line">
              <label class="large required">クライアントID</label>
              <div class="ui input full required">
                <input [class.error]="errField.get('client_id')" type="text" autocomplete="off" id="client_id" [(ngModel)]="sms_account_edit.client_id">
              </div>
            </div>
            <div class="line">
              <label class="large">SMSコード</label>
              <div class="ui input full">
                <input [class.error]="errField.get('sms_code')" type="tel" autocomplete="off" id="sms_code" [(ngModel)]="sms_account_edit.sms_code">
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveSmsAccount()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal small order-mail" id="order-mail-edit">
        <div class="header ui"><i class="at icon large"></i>注文通知先登録【葬儀社】</div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <label style="min-width:130px;">メールアドレス</label>
              <div class="ui input full">
                <input type="email" autocomplete="off" id="order_mail_address" [(ngModel)]="order_mail_edit.order_mail_address">
              </div>
            </div>
            <div class="line">
              <div class="description">　　
                顧客の注文メールがBCCで送られます。複数メールアドレスを登録する場合は、カンマ区切りでご入力ください。
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveOrderMail()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
      <input type="file" #imageFileUpload id="imageFileUpload" name="imageFileUpload" style="display:none;" />

    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" (click)="showOrderMail()" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN">
      <i class="at icon"></i>注文通知先
    </button>
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
