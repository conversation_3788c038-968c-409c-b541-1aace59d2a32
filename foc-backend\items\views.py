from django.db.models import Q
from django_filters import rest_framework as filters
from rest_framework import generics, status, views
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from items.models import Item
from items.serializers import ItemImportImageSerializer, ItemListSerializer, ItemSerializer
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class ItemList(AddContextMixin, generics.ListCreateAPIView):

    queryset = Item.objects.filter(Q(del_flg=False)).order_by('service_id', 'display_num', 'id')
    serializer_class = ItemSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['base', 'service', 'hinban']
    permission_classes = [IsAuthenticated]


class ItemDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 商品を取得します
    """

    queryset = Item.objects.filter(Q(del_flg=False)).all()
    serializer_class = ItemSerializer
    permission_classes = [IsAuthenticated]


class ItemBulkUpsert(AddContextMixin, generics.UpdateAPIView):

    serializer_class = ItemListSerializer
    permission_classes = [IsAuthenticated]

    def update(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, child=ItemSerializer())
        serializer.bulk_save()
        return Response({}, status=status.HTTP_200_OK)


class ItemImportImage(views.APIView):

    permission_classes = [IsAuthenticated]

    def post(self, request, base_id, format=None):
        serializer = ItemImportImageSerializer(
            data=request.data, context={'base_id': base_id}, many=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response({}, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
