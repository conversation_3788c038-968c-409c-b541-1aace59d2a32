from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView

from staffs import views

app_name = 'staffs'
urlpatterns = [
    path('login/', views.StaffTokenObtainPair.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('me/', views.StaffMe.as_view(), name='me'),
    path('lower/<int:base_id>/', views.StaffListLowerBases.as_view(), name='list_lower_bases'),
    path('', views.StaffList.as_view(), name='staff_list'),
    path('<int:pk>/', views.StaffDetail.as_view(), name='staff_detail'),
]
