from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from faqs.models import Faq
from faqs.tests.factories import FaqFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
tz = timezone.get_current_timezone()


class FaqDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.faq = FaqFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_faq_detail_succeed(self) -> None:
        """FAQ詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.faq.pk)
        self.assertEqual(record['company'], self.faq.company.pk)
        self.assertEqual(record['question'], self.faq.question)
        self.assertEqual(record['answer'], self.faq.answer)
        self.assertEqual(record['display_num'], self.faq.display_num)
        self.assertEqual(record['del_flg'], self.faq.del_flg)
        self.assertEqual(record['created_at'], self.faq.created_at.astimezone(tz).isoformat())
        self.assertEqual(record['create_user_id'], self.faq.create_user_id)
        self.assertEqual(record['updated_at'], self.faq.updated_at.astimezone(tz).isoformat())
        self.assertEqual(record['update_user_id'], self.faq.update_user_id)

    def test_faq_detail_failed_by_notfound(self) -> None:
        """FAQ詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_faq: Faq = FaqFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('faqs:faq_detail', kwargs={'pk': non_saved_faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_faq_detail_failed_by_already_deleted(self) -> None:
        """FAQ詳細APIは無効になったFAQを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.faq.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_faq_detail_failed_without_auth(self) -> None:
        """FAQ詳細APIはAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_faq_detail_allow_from_moshu(self) -> None:
        """FAQ詳細APIが喪主からの呼び出しを許可する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class FaqUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.faq = FaqFactory()
        self.new_faq_data = FaqFactory.build()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'question': self.new_faq_data.question,
            'answer': self.new_faq_data.answer,
            'display_num': self.new_faq_data.display_num,
        }

    def test_faq_update_succeed(self) -> None:
        """FAQを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['company'], self.faq.company.pk)
        self.assertEqual(record['question'], self.new_faq_data.question)
        self.assertEqual(record['answer'], self.new_faq_data.answer)
        self.assertEqual(record['display_num'], self.new_faq_data.display_num)
        self.assertEqual(record['del_flg'], self.faq.del_flg)
        self.assertEqual(record['create_user_id'], self.faq.create_user_id)
        self.assertEqual(record['created_at'], self.faq.created_at.astimezone(tz).isoformat())
        self.assertEqual(record['update_user_id'], self.staff.pk)
        self.assertIsNotNone(record['updated_at'])

    def test_faq_update_ignore_some_fields(self) -> None:
        """FAQ更新APIは特定のフィールドへの更新を無視する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['company'] = BaseFactory().pk
        params['del_flg'] = True
        response = self.api_client.put(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['company'], self.faq.company.pk)
        self.assertFalse(record['del_flg'])
        self.assertEqual(record['create_user_id'], self.faq.create_user_id)
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_faq_update_failed_by_notfound(self) -> None:
        """FAQ更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_faq: Faq = FaqFactory.build()

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('faqs:faq_detail', kwargs={'pk': non_saved_faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_faq_update_failed_by_already_deleted(self) -> None:
        """FAQ更新APIが論理削除済みのFAQを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.faq.disable()

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_faq_update_failed_without_auth(self) -> None:
        """FAQ更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_faq_update_deny_from_moshu(self) -> None:
        """FAQ更新APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])


class FaqDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.faq = FaqFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_faq_delete_succeed(self) -> None:
        """FAQを論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # FAQのdel_flgがTrueになる
        db_faq: Faq = Faq.objects.get(pk=self.faq.pk)
        self.assertTrue(db_faq.del_flg)
        self.assertEqual(db_faq.create_user_id, self.faq.create_user_id)
        self.assertEqual(db_faq.update_user_id, self.staff.pk)

    def test_faq_delete_failed_by_notfound(self) -> None:
        """FAQ削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_faq: Faq = FaqFactory.build()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('faqs:faq_detail', kwargs={'pk': non_saved_faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_faq_delete_failed_by_already_deleted(self) -> None:
        """FAQ削除APIが論理削除済みのFAQを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.faq.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_faq_delete_failed_without_auth(self) -> None:
        """FAQ削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_faq_delete_deny_from_moshu(self) -> None:
        """FAQ削除APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('faqs:faq_detail', kwargs={'pk': self.faq.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
