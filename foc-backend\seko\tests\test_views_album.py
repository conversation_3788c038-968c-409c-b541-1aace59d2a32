from base64 import b64encode
from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from seko.models import Seko, SekoAlbum
from seko.tests.factories import SekoAlbumFactory, SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


class SekoAlbumListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.seko = SekoFactory()
        self.album_1 = SekoAlbumFactory(seko=self.seko)
        self.album_2 = SekoAlbumFactory(seko=self.seko)
        self.album_3 = SekoAlbumFactory(seko=self.seko)

        self.another_seko = SekoFactory()
        SekoAlbumFactory(seko=self.another_seko)
        SekoAlbumFactory(seko=self.another_seko)

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_album_list_succeed(self) -> None:
        """施行アルバム一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        albums: Dict[int, SekoAlbum] = dict(
            [(rec.id, rec) for rec in [self.album_1, self.album_2, self.album_3]]
        )

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:album_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        for rec in records:
            album = albums[rec['id']]
            self.assertIsNotNone(rec.get('file_name'))
            self.assertEqual(rec.get('display_num'), album.display_num)

    def test_seko_album_list_failed_by_notfound(self) -> None:
        """施行アルバム一覧取得APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:album_list', kwargs={'seko_id': non_saved_seko.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_list_failed_without_auth(self) -> None:
        """施行アルバム一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:album_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoAlbumDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.album = SekoAlbumFactory()

        self.staff = StaffFactory()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_album_detail_succeed(self) -> None:
        """施行アルバム詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:album_detail', kwargs={'seko_id': self.album.seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('id'), self.album.id)
        self.assertIsNotNone(record.get('file_name'))
        self.assertEqual(record.get('display_num'), self.album.display_num)

    def test_seko_album_detail_failed_by_notfound(self) -> None:
        """施行アルバム詳細取得APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_album: SekoAlbum = SekoAlbumFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:album_detail',
                kwargs={'seko_id': self.album.seko.id, 'pk': non_saved_album.pk},
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_detail_failed_by_notfound_seko(self) -> None:
        """施行アルバム詳細取得APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:album_detail', kwargs={'seko_id': non_saved_seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_detail_failed_by_illegal_seko(self) -> None:
        """施行アルバム詳細取得APIがアルバムが紐付いていない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_parent_seko: Seko = SekoFactory()
        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:album_detail', kwargs={'seko_id': non_parent_seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_detail_failed_without_auth(self) -> None:
        """施行アルバム詳細取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse(
                'seko:album_detail', kwargs={'seko_id': self.album.seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoAlbumCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.seko = SekoFactory()

        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_album_create_succeed(self) -> None:
        """施行アルバムを追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        new_album_data: SekoAlbum = SekoAlbumFactory.build()
        params: Dict = {
            'file_name': b64encode(new_album_data.file_name.read()),
            'display_num': new_album_data.display_num,
        }
        response = self.api_client.post(
            reverse('seko:album_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('file_name'))
        self.assertEqual(record.get('display_num'), new_album_data.display_num)

        new_album = SekoAlbum.objects.get(pk=record['id'])
        self.assertEqual(new_album.seko, self.seko)

    def test_seko_album_create_failed_by_notfound(self) -> None:
        """施行アルバム追加APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:album_list', kwargs={'seko_id': non_saved_seko.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_create_failed_without_auth(self) -> None:
        """施行アルバム追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('seko:album_list', kwargs={'seko_id': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoAlbumUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.album = SekoAlbumFactory()

        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_album_update_succeed(self) -> None:
        """施行アルバムを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        new_album_data: SekoAlbum = SekoAlbumFactory.build(seko=self.album.seko)

        params: Dict = {
            'file_name': b64encode(new_album_data.file_name.read()),
            'display_num': new_album_data.display_num,
        }
        response = self.api_client.put(
            reverse(
                'seko:album_detail', kwargs={'seko_id': self.album.seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('display_num'), new_album_data.display_num)

    def test_seko_album_update_failed_by_notfound(self) -> None:
        """施行アルバム更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_album: SekoAlbum = SekoAlbumFactory.build()
        params: Dict = {}
        response = self.api_client.put(
            reverse(
                'seko:album_detail',
                kwargs={'seko_id': self.album.seko.id, 'pk': non_saved_album.pk},
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_update_failed_by_notfound_seko(self) -> None:
        """施行アルバム更新APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.put(
            reverse(
                'seko:album_detail', kwargs={'seko_id': non_saved_seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_update_failed_by_illegal_seko(self) -> None:
        """施行アルバム更新APIがアルバムが紐付いていない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_parent_seko: Seko = SekoFactory()
        params: Dict = {}
        response = self.api_client.put(
            reverse(
                'seko:album_detail', kwargs={'seko_id': non_parent_seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_update_failed_without_auth(self) -> None:
        """施行アルバム更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse(
                'seko:album_detail', kwargs={'seko_id': self.album.seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SekoDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.album = SekoAlbumFactory()

        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_seko_album_delete_succeed(self) -> None:
        """施行アルバムを削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        album_id: int = self.album.pk
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:album_detail', kwargs={'seko_id': self.album.seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # アルバムが物理削除されている
        self.assertIsNone(SekoAlbum.objects.filter(pk=album_id).first())

    def test_seko_album_delete_failed_by_notfound(self) -> None:
        """施行アルバム削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:album_detail',
                kwargs={'seko_id': self.album.seko.id, 'pk': non_saved_seko.id},
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_delete_failed_by_notfound_seko(self) -> None:
        """施行アルバム削除APIが存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:album_detail', kwargs={'seko_id': non_saved_seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_delete_failed_by_illegal_seko(self) -> None:
        """施行アルバム削除APIがアルバムが紐付いていない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_parent_seko: Seko = SekoFactory()
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:album_detail', kwargs={'seko_id': non_parent_seko.id, 'pk': self.album.pk}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_seko_album_delete_failed_without_auth(self) -> None:
        """施行アルバム削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse(
                'seko:album_detail', kwargs={'seko_id': self.album.seko.id, 'pk': self.album.id}
            ),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
