from typing import Dict

import faker
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from henrei.models import OrderHenreihin
from henrei.tests.factories import (
    HenreihinKodenFactory,
    HenreihinKumotsuFactory,
    OrderHenreihinFactory,
)
from seko.tests.factories import SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

fake_provider = faker.Faker('ja_JP')
tz = timezone.get_current_timezone()
jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class OrderHenreiDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.order_henrei = OrderHenreihinFactory(
            order_ts=tz.localize(fake_provider.past_datetime())
        )
        HenreihinKodenFactory(henrei_order=self.order_henrei)
        HenreihinKodenFactory(henrei_order=self.order_henrei)
        HenreihinKumotsuFactory(henrei_order=self.order_henrei)
        HenreihinKumotsuFactory(henrei_order=self.order_henrei)
        HenreihinKumotsuFactory(henrei_order=self.order_henrei)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_order_henrei_detail_succeed(self) -> None:
        """返礼品発注詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('henrei:order_detail', kwargs={'pk': self.order_henrei.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['seko'], self.order_henrei.seko.pk)
        self.assertEqual(record['order_ts'], self.order_henrei.order_ts.isoformat())
        self.assertEqual(record['order_staff'], self.order_henrei.order_staff.pk)
        self.assertEqual(record['order_status'], self.order_henrei.order_status)
        self.assertEqual(record['order_note'], self.order_henrei.order_note)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

        self.assertEqual(len(record['henrei_koden']), 2)
        for record_koden, db_koden in zip(
            record['henrei_koden'], self.order_henrei.henrei_koden.order_by('detail_koden')
        ):
            self.assertEqual(record_koden['detail_koden'], db_koden.detail_koden.pk)
            self.assertEqual(record_koden['henreihin_hinban'], db_koden.henreihin_hinban)
            self.assertEqual(record_koden['henreihin_name'], db_koden.henreihin_name)
            self.assertEqual(record_koden['henreihin_price'], db_koden.henreihin_price)
            self.assertEqual(record_koden['henreihin_tax'], db_koden.henreihin_tax)
            self.assertEqual(record_koden['henreihin_tax_pct'], db_koden.henreihin_tax_pct)
        self.assertEqual(len(record['henrei_kumotsu']), 3)
        for record_kumotsu, db_kumotsu in zip(
            record['henrei_kumotsu'], self.order_henrei.henrei_kumotsu.order_by('detail_kumotsu')
        ):
            self.assertEqual(record_kumotsu['detail_kumotsu'], db_kumotsu.detail_kumotsu.pk)
            self.assertEqual(record_kumotsu['henreihin_hinban'], db_kumotsu.henreihin_hinban)
            self.assertEqual(record_kumotsu['henreihin_name'], db_kumotsu.henreihin_name)
            self.assertEqual(record_kumotsu['henreihin_price'], db_kumotsu.henreihin_price)
            self.assertEqual(record_kumotsu['henreihin_tax'], db_kumotsu.henreihin_tax)
            self.assertEqual(record_kumotsu['henreihin_tax_pct'], db_kumotsu.henreihin_tax_pct)

    def test_order_henrei_detail_failed_notfound(self) -> None:
        """返礼品発注詳細APIは存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_order_henrei: OrderHenreihin = OrderHenreihinFactory.build(order_status=1)
        params: Dict = {}
        response = self.api_client.get(
            reverse('henrei:order_detail', kwargs={'pk': non_saved_order_henrei.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_order_henrei_detail_failed_without_auth(self) -> None:
        """返礼品発注詳細APIはAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('henrei:order_detail', kwargs={'pk': self.order_henrei.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class OrderHenreiUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.order_henrei = OrderHenreihinFactory()
        HenreihinKodenFactory(henrei_order=self.order_henrei)
        HenreihinKodenFactory(henrei_order=self.order_henrei)
        HenreihinKumotsuFactory(henrei_order=self.order_henrei)
        HenreihinKumotsuFactory(henrei_order=self.order_henrei)
        HenreihinKumotsuFactory(henrei_order=self.order_henrei)
        self.new_order_henrei_data = OrderHenreihinFactory.build(
            seko=SekoFactory(),
            order_ts=tz.localize(fake_provider.past_datetime()),
            order_staff=StaffFactory(),
            order_status=1,
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'order_ts': self.new_order_henrei_data.order_ts,
            'order_staff': self.new_order_henrei_data.order_staff.pk,
            'order_status': self.new_order_henrei_data.order_status,
            'order_note': self.new_order_henrei_data.order_note,
        }

    def test_order_henrei_update_succeed(self) -> None:
        """返礼品発注を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('henrei:order_detail', kwargs={'pk': self.order_henrei.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['seko'], self.order_henrei.seko.pk)
        self.assertEqual(record['order_ts'], self.new_order_henrei_data.order_ts.isoformat())
        self.assertEqual(record['order_staff'], self.new_order_henrei_data.order_staff.pk)
        self.assertEqual(record['order_status'], self.new_order_henrei_data.order_status)
        self.assertEqual(record['order_note'], self.new_order_henrei_data.order_note)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

        self.assertEqual(len(record['henrei_koden']), 2)
        for record_koden, db_koden in zip(
            record['henrei_koden'],
            self.new_order_henrei_data.henrei_koden.order_by('detail_koden'),
        ):
            self.assertEqual(record_koden['detail_koden'], db_koden.detail_koden.pk)
            self.assertEqual(record_koden['henreihin_hinban'], db_koden.henreihin_hinban)
            self.assertEqual(record_koden['henreihin_name'], db_koden.henreihin_name)
            self.assertEqual(record_koden['henreihin_price'], db_koden.henreihin_price)
            self.assertEqual(record_koden['henreihin_tax'], db_koden.henreihin_tax)
            self.assertEqual(record_koden['henreihin_tax_pct'], db_koden.henreihin_tax_pct)
        self.assertEqual(len(record['henrei_kumotsu']), 3)
        for record_kumotsu, db_kumotsu in zip(
            record['henrei_kumotsu'],
            self.new_order_henrei_data.henrei_kumotsu.order_by('detail_kumotsu'),
        ):
            self.assertEqual(record_kumotsu['detail_kumotsu'], db_kumotsu.detail_kumotsu.pk)
            self.assertEqual(record_kumotsu['henreihin_hinban'], db_kumotsu.henreihin_hinban)
            self.assertEqual(record_kumotsu['henreihin_name'], db_kumotsu.henreihin_name)
            self.assertEqual(record_kumotsu['henreihin_price'], db_kumotsu.henreihin_price)
            self.assertEqual(record_kumotsu['henreihin_tax'], db_kumotsu.henreihin_tax)
            self.assertEqual(record_kumotsu['henreihin_tax_pct'], db_kumotsu.henreihin_tax_pct)

    def test_order_henrei_patch_order_status(self) -> None:
        """返礼品発注更新API(PATCH)で発注ステータスを2以外に更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'order_status': 1}
        response = self.api_client.patch(
            reverse('henrei:order_detail', kwargs={'pk': self.order_henrei.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertIsNone(record['order_ts'])
        self.assertEqual(record['order_staff'], self.order_henrei.order_staff.pk)
        self.assertEqual(record['order_status'], 1)

        # OrderHenreihin、HenreihinKoden、HenreihinKumotsuのorder_statusがすべて変わる
        order_henrei: OrderHenreihin = OrderHenreihin.objects.get(pk=self.order_henrei.pk)
        self.assertEqual(order_henrei.order_status, 1)
        for koden in order_henrei.henrei_koden.all():
            self.assertEqual(koden.order_status, 1)
        for kumotsu in order_henrei.henrei_kumotsu.all():
            self.assertEqual(kumotsu.order_status, 1)

    def test_order_henrei_patch_order_status_2(self) -> None:
        """返礼品発注更新API(PATCH)で発注ステータスを2に更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'order_status': 2}
        response = self.api_client.patch(
            reverse('henrei:order_detail', kwargs={'pk': self.order_henrei.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 発注ステータス2の場合はorder_tsとorder_staffも自動的に上書きする
        record: Dict = response.json()
        self.assertIsNotNone(record['order_ts'])
        self.assertEqual(record['order_staff'], self.staff.pk)
        self.assertEqual(record['order_status'], 2)

        # OrderHenreihin、HenreihinKoden、HenreihinKumotsuのorder_statusがすべて変わる
        order_henrei: OrderHenreihin = OrderHenreihin.objects.get(pk=self.order_henrei.pk)
        self.assertEqual(order_henrei.order_status, 2)
        for koden in order_henrei.henrei_koden.all():
            self.assertEqual(koden.order_status, 2)
        for kumotsu in order_henrei.henrei_kumotsu.all():
            self.assertEqual(kumotsu.order_status, 2)

    def test_order_henrei_update_failed_notfound(self) -> None:
        """返礼品発注更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_order_henrei: OrderHenreihin = OrderHenreihinFactory.build()
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('henrei:order_detail', kwargs={'pk': non_saved_order_henrei.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_order_henrei_update_failed_without_auth(self) -> None:
        """返礼品発注更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('henrei:order_detail', kwargs={'pk': self.order_henrei.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
