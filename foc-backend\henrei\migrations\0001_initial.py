# Generated by Django 3.1.2 on 2020-11-10 09:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('seko', '0010_moshu_agree_ts'),
        ('orders', '0001_initial'),
        ('suppliers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('items', '0003_auto_20201104_1540'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderHenreihin',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'order_ts',
                    models.DateTimeField(blank=True, null=True, verbose_name='ordered at'),
                ),
                ('order_status', models.IntegerField(verbose_name='order status')),
                ('order_note', models.TextField(blank=True, null=True, verbose_name='order note')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'order_staff',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='order staff',
                    ),
                ),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='henrei_orders',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_order_henreihin',
            },
        ),
        migrations.CreateModel(
            name='HenreihinKoden',
            fields=[
                (
                    'detail_koden',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='koden',
                        serialize=False,
                        to='orders.entrydetailkoden',
                        verbose_name='entry detail koden',
                    ),
                ),
                ('henreihin_hinban', models.TextField(verbose_name='item hinban')),
                ('henreihin_name', models.TextField(verbose_name='item name')),
                ('henreihin_price', models.IntegerField(verbose_name='item price')),
                ('henreihin_tax', models.IntegerField(verbose_name='item tax')),
                ('henreihin_tax_pct', models.IntegerField(verbose_name='item tax percentage')),
                ('keigen_flg', models.BooleanField(verbose_name='tax reduced')),
                (
                    'customer_self_select_flg',
                    models.BooleanField(verbose_name='selected by customer self'),
                ),
                ('select_status', models.IntegerField(verbose_name='select status')),
                ('order_status', models.IntegerField(verbose_name='order status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'henreihin',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='items.item',
                        verbose_name='henrei item',
                    ),
                ),
                (
                    'order',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='koden',
                        to='henrei.orderhenreihin',
                        verbose_name='henrei order',
                    ),
                ),
                (
                    'supplier',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='suppliers.supplier',
                        verbose_name='supplier',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_henreihin_koden',
            },
        ),
    ]
