
<div class="container">
  <div class="inner">
    <div class="contents">
      <h2>芳名{{haveService(Const.SERVICE_ID_KODEN)?'・香典':''}}帳</h2>
      <div class="service-label">サービス内容で絞り込む</div>
      <div class="service-check">
        <div class="ui checkbox chobun" *ngIf="haveService(Const.SERVICE_ID_CHOBUN)">
          <input type="checkbox" [(ngModel)]="homei_filter.chobun" (change)="filterHomeiList()">
          <label><div class="image chobun"></div>弔文</label>
        </div>
        <div class="ui checkbox" *ngIf="haveService(Const.SERVICE_ID_KUMOTSU)">
          <input type="checkbox" [(ngModel)]="homei_filter.kumotsu" (change)="filterHomeiList()">
          <label><div class="image kumotsu"></div>供花・供物</label>
        </div>
        <div class="ui checkbox" *ngIf="haveService(Const.SERVICE_ID_KODEN)">
          <input type="checkbox" [(ngModel)]="homei_filter.koden" (change)="filterHomeiList()">
          <label><div class="image koden"></div>香典</label>
        </div>
        <div class="ui checkbox" *ngIf="haveService(Const.SERVICE_ID_MESSAGE)">
          <input type="checkbox" [(ngModel)]="homei_filter.msg" (change)="filterHomeiList()">
          <label><div class="image msg"></div>追悼メッセージ</label>
        </div>
      </div>
      <div class="title_area">
        <table>
          <tbody>
            <tr>
              <td class="entry_name">申込者</td>
              <td class="okurinushi">送り主</td>
              <td class="service">サービス</td>
              <td class="detail">詳細</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="data_area" *ngFor="let homei of homei_list">
        <div class="ui accordion">
          <div class="title">
            <table>
              <tbody>
                <tr>
                  <td class="entry_name">{{homei.entry_name}}</td>
                  <td class="okurinushi">{{homei.okurinushi_name}}</td>
                  <td class="service">
                    <div>
                      <div class="image {{getClassName(homei.service_id)}}"></div>
                      <div class="name">{{homei.service_name}}</div>
                    </div>
                  </td>
                  <td class="detail">
                    <i class="chevron down icon small"></i>
                    <i class="chevron up icon small"></i>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="content">
            <table>
              <tbody>
                <tr>
                  <td class="label">申込番号</td>
                  <td class="data">{{homei.entry_id}}</td>
                </tr>
                <tr>
                  <td class="label">申込日時</td>
                  <td class="data">{{homei.entry_ts|date:"yyyy/MM/dd H:mm"}}</td>
                </tr>
                <tr>
                  <td class="label">品目</td>
                  <td class="data">{{homei.item_name}}</td>
                </tr>
                <tr>
                  <td class="label">住所</td>
                  <td class="data">
                    <div>{{homei.entry_zip_code}}</div>
                    <div>{{homei.entry_prefecture}}{{homei.entry_address_1}}</div>
                    <div>{{homei.entry_address_2}}{{homei.entry_address_3}}</div>
                  </td>
                </tr>
                <tr>
                  <td class="label">電話番号</td>
                  <td class="data">{{homei.entry_tel}}</td>
                </tr>
                <tr *ngIf="homei.koden || homei.kumotsu">
                  <td class="label">摘要</td>
                  <td class="data">
                    <ng-container *ngIf="homei.koden">
                      ¥{{homei.price | number}}
                      <ng-container *ngIf="homei.koden.henrei_koden">
                        （{{homei.koden.henrei_koden.henreihin_hinban}}）
                        {{homei.koden.henrei_koden.henreihin_name}}
                        ¥{{homei.koden.henrei_koden.henreihin_price | number}}
                      </ng-container>
                    </ng-container>
                    <ng-container *ngIf="homei.kumotsu">
                      ¥{{homei.price | number}}
                      <ng-container *ngIf="homei.kumotsu.henrei_kumotsu">
                        （{{homei.kumotsu.henrei_kumotsu.henreihin_hinban}}）
                        {{homei.kumotsu.henrei_kumotsu.henreihin_name}}
                        ¥{{homei.kumotsu.henrei_kumotsu.henreihin_price | number}}
                      </ng-container>
                    </ng-container>
                  </td>
                </tr>
                <ng-container *ngIf="homei.message">
                  <tr>
                    <td class="label">間柄</td>
                    <td class="data">{{homei.message.relation_ship}}</td>
                  </tr>
                  <tr>
                    <td class="label">メッセージ</td>
                    <td class="data">
                      <div class="honbun">{{homei.message.honbun}}</div>
                    </td>
                  </tr>
                  <tr>
                    <td class="label">公開区分</td>
                    <td class="data">
                      <div class="ui radio checkbox">
                        <input type="radio" name="release_status{{homei.entry_detail_id}}" [value]="Const.RELEASE_STATUS_ID_OK" [(ngModel)]="homei.message.release_status" [checked]="homei.message.release_status===Const.RELEASE_STATUS_ID_OK">
                        <label>公開OK</label>
                      </div>
                      <div class="ui radio checkbox">
                        <input type="radio" name="release_status{{homei.entry_detail_id}}" [value]="Const.RELEASE_STATUS_ID_NG" [(ngModel)]="homei.message.release_status" [checked]="homei.message.release_status===Const.RELEASE_STATUS_ID_NG">
                        <label>公開NG</label>
                      </div>
                      <a class="button grey" (click)="saveMsgConfirm(homei)">公開区分更新</a>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="total_area">返礼品合計： <span class="price">¥ {{henreihin_sum | number}}</span></div>
    </div>
    <div class="button-area">
      <a class="button grey" (click)="downloadHomeiPdf(Const.SERVICE_ID_KUMOTSU)" *ngIf="haveService(Const.SERVICE_ID_KUMOTSU)">芳名帳(供花供物)出力</a>
      <a class="button grey" (click)="downloadHomeiPdf(Const.SERVICE_ID_CHOBUN)" *ngIf="haveService(Const.SERVICE_ID_CHOBUN)">芳名帳(弔文)出力</a>
      <a class="button grey" (click)="downloadHomeiPdf(Const.SERVICE_ID_MESSAGE)" *ngIf="haveService(Const.SERVICE_ID_MESSAGE)">芳名帳(追悼MSG)出力</a>
      <a class="button grey" (click)="downloadHomeiPdf(Const.SERVICE_ID_KODEN)" *ngIf="haveService(Const.SERVICE_ID_KODEN)">香典帳出力</a>
      <a class="button grey" (click)="downloadMsgPdf()" *ngIf="haveService(Const.SERVICE_ID_MESSAGE)">追悼MSG出力</a>
    </div>
    <div class="ui modal confirm" id="msg-confirm">
      <div class="content">
        公開区分を更新します。<br>よろしいでしょうか？
      </div>
      <div class="button-area">
        <a class="button" (click)="cancelConfirm()">キャンセル</a>
        <a class="button grey" (click)="saveMsgData()">確定</a>
      </div>
    </div>
    <div class="ui modal confirm" id="message-popup">
      <div class="content red">
        {{message}}
      </div>
      <div class="button-area">
        <a class="button" (click)="closePopup()">閉じる</a>
      </div>
    </div>
  </div>
</div>
