# FOCシステム セキュリティ脆弱性分析レポート

## 概要

FOCシステムにおけるJWTアクセストークンに会社コード情報が含まれていない状況での、潜在的なデータ漏洩リスクとセキュリティ脆弱性を分析し、対策案を提示します。

## 1. 現在のセキュリティ実装状況

### 1.1 JWT認証の現状
```python
# JWTトークンの構造（utils/authentication.py）
{
  "user_id": 123,
  "user_class": "Staff",  # 'Staff' or 'Moshu'
  "exp": 1640995200,
  "iat": 1640991600,
  "jti": "abc123..."
}
```

**問題点:**
- JWTトークンに`company_code`が含まれていない
- 会社コードは認証後に`request.user.company_code`から取得
- フロントエンドでセッションストレージから会社コードを取得・操作可能

### 1.2 会社コード取得方式
```python
# スタッフの場合
staff = request.user
company_code = staff.company_code  # 直接取得

# 喪主の場合
moshu = request.user
company_code = moshu.seko.base.company_code  # リレーション経由
```

### 1.3 テナント分離の実装
- **システム管理会社**: 全テナント会社のデータ参照可能
- **テナント会社**: 自社データのみ参照可能
- **権限判定**: `request.user.company_code`ベース

## 2. 潜在的セキュリティ脆弱性

### 2.1 【高リスク】クエリパラメータ改ざんによるデータ漏洩

#### 脆弱なAPI一覧

**施行管理API:**
```javascript
// フロントエンド実装例（seko.component.ts）
request.seko_company = this.form_data.company_id;  // セッションから取得
// GET /seko/?seko_company=1008&seko_date_after=2025-06-27

// 攻撃例: seko_company=1009 に改ざん
// → 他社の施行データが漏洩
```

**香典管理API:**
```javascript
// koden.component.ts
request.company_id = this.form_data.company_id;
// GET /koden/?company_id=1008

// 攻撃例: company_id=1009 に改ざん
// → 他社の香典データが漏洩
```

**弔文管理API:**
```javascript
// chobun.component.ts
request.company_id = this.form_data.company_id;
// GET /chobun/?company_id=1008

// 攻撃例: company_id=1009 に改ざん
// → 他社の弔文データが漏洩
```

**メッセージ管理API:**
```javascript
// message.component.ts
request.company_id = this.form_data.company_id;
// GET /message/?company_id=1008

// 攻撃例: company_id=1009 に改ざん
// → 他社のメッセージデータが漏洩
```

**イベントフィルタAPI:**
```javascript
// event-filter.component.ts
request.company = this.form_data.company_id;
// GET /moshu/?company=1008

// 攻撃例: company=1009 に改ざん
// → 他社の喪主データが漏洩
```

#### 攻撃シナリオ
1. **正常ログイン**: 正規ユーザーがテナント会社Aでログイン
2. **セッション操作**: ブラウザ開発者ツールでセッションストレージを編集
3. **パラメータ改ざん**: APIリクエストの会社IDをテナント会社Bに変更
4. **データ漏洩**: テナント会社Bのデータが取得可能

### 2.2 【中リスク】バックエンドでの不十分な権限チェック

#### 問題のあるAPI実装

**施行詳細API（seko/views.py）:**
```python
class SekoDetail(generics.RetrieveUpdateDestroyAPIView):
    def get_object(self) -> Seko:
        obj: Seko = super().get_object()
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            # 更新・削除時のみ権限チェック
            user_bases: List[Base] = Base.objects.filter(
                pk=self.request.user.base_id
            ).get_cached_trees()
            if obj.seko_company not in user_bases[0].get_ancestors(include_self=True):
                raise PermissionDenied(_("Denied operation by another company's user"))
        return obj
```

**問題点:**
- **GET（参照）時に権限チェックなし**
- 他社データの参照が可能

**NowlSekoList API:**
```python
class NowlSekoList(generics.ListAPIView):
    queryset = (
        Seko.objects.filter(Q(del_flg=False), Q(seko_company__company_code='01001000'))
        # ハードコードされた会社コード
    )
```

**問題点:**
- **特定会社のデータのみ固定表示**
- 動的な会社コードフィルタリングなし

### 2.3 【中リスク】FDN連携APIでの会社コード検証不備

```python
# FdnSekoCreater.validate_data()
company: Base = Base.objects.filter(
    Q(del_flg=False),
    Q(base_type=Base.OrgType.COMPANY),
    Q(company_code=self.request.user.company_code),  # ユーザーの会社コードのみ
).first()
```

**問題点:**
- リクエストパラメータの会社コードとユーザーの会社コードの整合性チェックなし
- システム管理会社ユーザーの場合の特別処理なし

### 2.4 【低リスク】フロントエンドでの会社コードチェック

```javascript
// func-util.ts
static checkCompanyCode(company_code) {
    const base_list = sessionSvc.get('base_list');
    // セッションストレージベースのチェック
    // → クライアントサイドで改ざん可能
}
```

**問題点:**
- クライアントサイドでの検証のみ
- セッションストレージの改ざんで回避可能

## 3. 具体的な攻撃手法

### 3.1 セッションストレージ改ざん攻撃

**手順:**
1. ブラウザ開発者ツールを開く
2. Application → Session Storage → `staff_login_info`を編集
3. `staff.company_code`を他社コードに変更
4. APIリクエスト時に改ざんされた会社コードが使用される

**影響範囲:**
- 施行データ（seko）
- 香典データ（koden）
- 弔文データ（chobun）
- メッセージデータ（message）
- 喪主データ（moshu）

### 3.2 HTTPリクエスト改ざん攻撃

**手順:**
1. ブラウザ開発者ツールのNetworkタブを開く
2. APIリクエストを右クリック → "Edit and Resend"
3. クエリパラメータの`seko_company`や`company_id`を変更
4. 改ざんされたリクエストを送信

**例:**
```bash
# 正常なリクエスト
GET /seko/?seko_company=1008&seko_date_after=2025-06-27

# 改ざんされたリクエスト
GET /seko/?seko_company=1009&seko_date_after=2025-06-27
```

### 3.3 APIクライアント直接攻撃

**手順:**
1. 正規ユーザーのJWTトークンを取得
2. curlやPostmanで直接APIを呼び出し
3. 会社IDパラメータを他社IDに変更

**例:**
```bash
curl -H "Authorization: Bearer <valid_jwt_token>" \
     "https://api.foc.example.com/seko/?seko_company=1009"
```

## 4. 対策案

### 4.1 【最優先】JWTトークンへの会社コード追加

#### 実装案
```python
# utils/authentication.py
class ClassableRefreshToken(RefreshToken):
    @classmethod
    def for_user(cls, user):
        token = super().for_user(user)
        token['user_class'] = user._meta.object_name
        
        # 会社コードをJWTに追加
        if isinstance(user, Moshu):
            token['company_code'] = user.seko.base.company_code
        else:  # Staff
            token['company_code'] = user.company_code
            
        return token
```

#### JWT検証強化
```python
class ClassableJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        user = super().get_user(validated_token)
        
        # JWTの会社コードとユーザーの会社コードを検証
        jwt_company_code = validated_token.get('company_code')
        if isinstance(user, Moshu):
            user_company_code = user.seko.base.company_code
        else:
            user_company_code = user.company_code
            
        if jwt_company_code != user_company_code:
            raise AuthenticationFailed('Company code mismatch')
            
        return user
```

### 4.2 【高優先】バックエンドでの強制的な会社コードフィルタリング

#### ベースクラスの実装
```python
# utils/permissions.py
class CompanyFilterMixin:
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # システム管理会社の場合は全データアクセス可能
        if self.request.user.base.base_type == Base.OrgType.ADMIN:
            return queryset
            
        # テナント会社の場合は自社データのみ
        return queryset.filter(
            seko_company__company_code=self.request.user.company_code
        )
```

#### 各APIビューでの適用
```python
class SekoList(CompanyFilterMixin, generics.ListAPIView):
    queryset = Seko.objects.filter(del_flg=False)
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # クエリパラメータの会社コードを無視
        # JWTの会社コードで強制フィルタリング
        return queryset.filter(
            seko_company__company_code=self.request.user.company_code
        )
```

### 4.3 【高優先】リクエストパラメータ検証の強化

#### デコレータによる検証
```python
# utils/decorators.py
def validate_company_access(view_func):
    def wrapper(self, request, *args, **kwargs):
        # クエリパラメータの会社コード取得
        requested_company = request.GET.get('seko_company') or request.GET.get('company_id')
        
        if requested_company:
            # システム管理会社以外は自社コードのみ許可
            if (self.request.user.base.base_type != Base.OrgType.ADMIN and 
                str(requested_company) != str(self.request.user.company_code)):
                raise PermissionDenied('Access denied to other company data')
                
        return view_func(self, request, *args, **kwargs)
    return wrapper
```

### 4.4 【中優先】フロントエンドでの会社コード固定化

#### セッション改ざん防止
```typescript
// service/security.service.ts
export class SecurityService {
  private readonly COMPANY_CODE_KEY = 'verified_company_code';
  
  // ログイン時に会社コードをハッシュ化して保存
  setVerifiedCompanyCode(companyCode: string): void {
    const hash = this.generateHash(companyCode + this.getDeviceFingerprint());
    sessionStorage.setItem(this.COMPANY_CODE_KEY, hash);
  }
  
  // API呼び出し時に検証
  validateCompanyCode(companyCode: string): boolean {
    const storedHash = sessionStorage.getItem(this.COMPANY_CODE_KEY);
    const expectedHash = this.generateHash(companyCode + this.getDeviceFingerprint());
    return storedHash === expectedHash;
  }
}
```

### 4.5 【中優先】監査ログの実装

#### アクセスログ記録
```python
# utils/middleware.py
class SecurityAuditMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # リクエスト前の処理
        self.log_request(request)
        
        response = self.get_response(request)
        
        # レスポンス後の処理
        self.log_response(request, response)
        
        return response
        
    def log_request(self, request):
        if hasattr(request, 'user') and request.user.is_authenticated:
            # 会社コード不一致の検出
            requested_company = request.GET.get('seko_company') or request.GET.get('company_id')
            if requested_company and str(requested_company) != str(request.user.company_code):
                logger.warning(f'Company code mismatch: user={request.user.id}, '
                             f'user_company={request.user.company_code}, '
                             f'requested_company={requested_company}')
```

## 5. 実装優先度

### Phase 1（緊急対応）
1. **バックエンドでの強制的な会社コードフィルタリング**
2. **リクエストパラメータ検証の強化**
3. **監査ログの実装**

### Phase 2（短期対応）
1. **JWTトークンへの会社コード追加**
2. **JWT検証強化**
3. **フロントエンドでの会社コード固定化**

### Phase 3（長期対応）
1. **包括的なセキュリティテストの実施**
2. **ペネトレーションテストの実施**
3. **セキュリティ監視システムの構築**

## 6. 影響範囲

### 6.1 修正が必要なAPI
- `/seko/` - 施行管理API
- `/koden/` - 香典管理API  
- `/chobun/` - 弔文管理API
- `/message/` - メッセージ管理API
- `/moshu/` - 喪主管理API
- `/orders/` - 注文管理API
- `/henrei/` - 返礼品管理API

### 6.2 修正が必要なフロントエンドコンポーネント
- `seko.component.ts`
- `koden.component.ts`
- `chobun.component.ts`
- `message.component.ts`
- `event-filter.component.ts`

## 7. テスト計画

### 7.1 セキュリティテスト項目
1. **パラメータ改ざんテスト**
2. **セッションストレージ改ざんテスト**
3. **JWT改ざんテスト**
4. **権限昇格テスト**
5. **データ漏洩テスト**

### 7.2 テストシナリオ例
```bash
# テナント会社Aのユーザーでログイン
# テナント会社Bのデータにアクセス試行
curl -H "Authorization: Bearer <tenant_a_token>" \
     "https://api.foc.example.com/seko/?seko_company=<tenant_b_id>"

# 期待結果: 403 Forbidden
```

## 8. 追加の脆弱性詳細

### 8.1 【高リスク】注文管理APIの脆弱性

```python
# orders/views.py - FdnOrderCreater
def get_queryset(self):
    company: Base = Base.objects.filter(
        Q(del_flg=False),
        Q(base_type=Base.OrgType.COMPANY),
        Q(company_code=self.request.user.company_code),
    ).first()
```

**問題点:**
- リクエストボディの`company_code`パラメータとユーザーの会社コードの整合性チェックなし
- FDN連携時の会社コード検証が不十分

### 8.2 【中リスク】システム管理会社の権限制御不備

```javascript
// func-util.ts
static getCompanyList() {
    const login_company = this.getLoginCompany();
    if (login_company.base_type === CommonConstants.BASE_TYPE_ADMIN) {
        return login_company.children;  // 全テナント会社を返す
    }
}
```

**問題点:**
- システム管理会社ユーザーが全テナントデータにアクセス可能
- 適切な権限分離が実装されていない

### 8.3 【中リスク】ガード機能の不備

```typescript
// admin.guard.ts
canActivate(): boolean {
    const login_info = this.sessionSvc.get('staff_login_info');
    if (!([CommonConstants.BASE_TYPE_ADMIN, CommonConstants.BASE_TYPE_COMPANY]
          .includes(login_info.staff.base.base_type))) {
        return false;
    }
}
```

**問題点:**
- セッションストレージベースの権限チェック
- クライアントサイドで改ざん可能

## 9. 緊急対応が必要なAPI一覧

### 9.1 データ漏洩リスクが高いAPI

| API | エンドポイント | 脆弱なパラメータ | リスクレベル |
|---|---|---|---|
| 施行一覧 | `GET /seko/` | `seko_company` | **高** |
| 香典一覧 | `GET /koden/` | `company_id` | **高** |
| 弔文一覧 | `GET /chobun/` | `company_id` | **高** |
| メッセージ一覧 | `GET /message/` | `company_id` | **高** |
| 喪主一覧 | `GET /moshu/` | `company` | **高** |
| 注文一覧 | `GET /orders/` | `company_code` | **高** |
| 返礼品一覧 | `GET /henrei/` | `company_id` | **高** |
| 法要一覧 | `GET /hoyo/` | `company_id` | **高** |

### 9.2 権限チェックが不十分なAPI

| API | 問題点 | 対策の緊急度 |
|---|---|---|
| `SekoDetail` | GET時の権限チェックなし | **緊急** |
| `NowlSekoList` | ハードコードされた会社コード | **高** |
| `FdnSekoCreater` | リクエストパラメータ検証不備 | **高** |
| `FdnOrderCreater` | 会社コード整合性チェックなし | **高** |

## 10. 実装サンプルコード

### 10.1 セキュアなAPIビューの実装例

```python
# utils/mixins.py
class SecureCompanyFilterMixin:
    """会社コードによる安全なデータフィルタリング"""

    def get_queryset(self):
        queryset = super().get_queryset()

        # システム管理会社の特別処理
        if self.is_system_admin():
            # 明示的に指定された会社のデータのみ
            requested_company = self.get_requested_company_code()
            if requested_company:
                return queryset.filter(
                    seko_company__company_code=requested_company
                )
            return queryset.none()  # 会社指定なしの場合は空

        # テナント会社は自社データのみ
        return queryset.filter(
            seko_company__company_code=self.request.user.company_code
        )

    def is_system_admin(self):
        return (hasattr(self.request.user, 'base') and
                self.request.user.base.base_type == Base.OrgType.ADMIN)

    def get_requested_company_code(self):
        # リクエストパラメータから会社コードを安全に取得
        company_params = ['seko_company', 'company_id', 'company']
        for param in company_params:
            value = self.request.GET.get(param)
            if value:
                return value
        return None

    def validate_company_access(self):
        """会社コードアクセス権限の検証"""
        requested_company = self.get_requested_company_code()

        if not requested_company:
            return True

        # システム管理会社は全テナントアクセス可能
        if self.is_system_admin():
            return True

        # テナント会社は自社のみ
        if str(requested_company) != str(self.request.user.company_code):
            raise PermissionDenied(
                f'Access denied: requested company {requested_company}, '
                f'user company {self.request.user.company_code}'
            )
        return True
```

### 10.2 セキュアなシリアライザーの実装例

```python
# utils/serializers.py
class SecureCompanyValidationMixin:
    """会社コード検証機能付きシリアライザー"""

    def validate(self, attrs):
        attrs = super().validate(attrs)

        # 会社コード関連フィールドの検証
        company_fields = ['seko_company', 'company', 'company_id']

        for field in company_fields:
            if field in attrs:
                self.validate_company_field(attrs[field])

        return attrs

    def validate_company_field(self, company_value):
        """会社コードフィールドの検証"""
        user = self.context['request'].user

        # システム管理会社の場合は制限なし
        if (hasattr(user, 'base') and
            user.base.base_type == Base.OrgType.ADMIN):
            return company_value

        # テナント会社の場合は自社のみ許可
        if hasattr(company_value, 'company_code'):
            company_code = company_value.company_code
        else:
            company_code = str(company_value)

        if company_code != user.company_code:
            raise serializers.ValidationError(
                f'Cannot access company {company_code}. '
                f'User belongs to company {user.company_code}'
            )

        return company_value
```

### 10.3 フロントエンドでのセキュア実装例

```typescript
// service/secure-api.service.ts
export class SecureApiService {
  private readonly COMPANY_CODE_HEADER = 'X-Company-Code';

  constructor(private http: HttpClient, private auth: AuthService) {}

  // 会社コード検証付きAPIリクエスト
  secureGet<T>(url: string, params?: any): Observable<T> {
    // JWTから会社コードを取得
    const userCompanyCode = this.auth.getUserCompanyCode();

    // パラメータの会社コードを検証
    if (params) {
      this.validateCompanyParams(params, userCompanyCode);
    }

    // ヘッダーに会社コードを追加
    const headers = new HttpHeaders({
      [this.COMPANY_CODE_HEADER]: userCompanyCode
    });

    return this.http.get<T>(url, { params, headers });
  }

  private validateCompanyParams(params: any, userCompanyCode: string): void {
    const companyFields = ['seko_company', 'company_id', 'company'];

    for (const field of companyFields) {
      if (params[field] && params[field] !== userCompanyCode) {
        // システム管理会社以外は自社コードのみ許可
        if (!this.auth.isSystemAdmin()) {
          throw new Error(`Invalid company code: ${params[field]}`);
        }
      }
    }
  }
}
```

## 11. まとめ

FOCシステムには、JWTトークンに会社コード情報が含まれていないことによる重大なセキュリティ脆弱性が存在します。攻撃者は簡単にクエリパラメータを改ざんして他社のデータにアクセス可能な状況です。

**最も重要な対策:**
1. バックエンドでの強制的な会社コードフィルタリング
2. JWTトークンへの会社コード追加
3. 包括的な権限チェックの実装

**緊急対応が必要な理由:**
- 個人情報保護法違反のリスク
- 企業機密情報の漏洩リスク
- 顧客信頼失墜のリスク
- 法的責任追及のリスク

これらの対策を緊急に実装することで、マルチテナント環境でのデータ分離を確実に実現し、セキュリティリスクを大幅に軽減できます。
