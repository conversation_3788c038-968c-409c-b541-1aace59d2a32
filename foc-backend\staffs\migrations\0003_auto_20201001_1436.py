# Generated by Django 3.1.1 on 2020-10-01 05:36

from django.db import migrations
from django.db.models import F


def forwards(apps, schema_editor):
    Staff = apps.get_model('staffs', 'Staff')
    Staff.objects.update(name=F('staff_id'))


def backwards(apps, schema_editor):
    Staff = apps.get_model('staffs', 'Staff')
    Staff.objects.update(staff_id=F('name'))


class Migration(migrations.Migration):

    dependencies = [
        ('staffs', '0002_auto_20201001_1435'),
    ]

    operations = [migrations.RunPython(forwards, reverse_code=backwards)]
