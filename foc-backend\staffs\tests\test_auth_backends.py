from typing import Dict, Optional

from django.test import TestCase
from django.test.client import RequestFactory
from django.urls import reverse

from bases.models import Base
from bases.tests.factories import BaseFactory
from staffs.auth_backends import CombinationModelBackend
from staffs.models import Staff
from staffs.tests.factories import DEFAULT_PASSWORD, StaffFactory


class AuthBackendTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff: Staff = StaffFactory()
        self.auth_backend = CombinationModelBackend()
        self.request = RequestFactory().post(reverse('staffs:token_obtain_pair'))
        self.basic_params: Dict = {
            'company_code': self.staff.company_code,
            'login_id': self.staff.login_id,
            'password': DEFAULT_PASSWORD,
        }

    def test_authenticate_success(self) -> None:
        """認証バックエンドで認証が成功する"""
        self.assertIsNone(self.staff.last_login)
        authenticated_staff: Optional[Staff] = self.auth_backend.authenticate(
            request=self.request, **self.basic_params
        )
        self.assertEqual(authenticated_staff, self.staff)

        # 最終ログイン日時は更新されるが更新日時は更新されない
        self.assertNotEqual(authenticated_staff.last_login, self.staff.updated_at)
        self.assertEqual(authenticated_staff.updated_at, self.staff.updated_at)

    def test_authenticate_failed_with_less_params(self) -> None:
        """認証バックエンドがパラメータ不足で失敗する"""
        authenticate_kwargs = self.basic_params.copy()
        authenticate_kwargs.pop('company_code')
        authenticated_staff: Optional[Staff] = self.auth_backend.authenticate(
            request=self.request, **authenticate_kwargs
        )
        self.assertIsNone(authenticated_staff)

        authenticate_kwargs = self.basic_params.copy()
        authenticate_kwargs.pop('login_id')
        authenticated_staff: Optional[Staff] = self.auth_backend.authenticate(
            request=self.request, **authenticate_kwargs
        )
        self.assertIsNone(authenticated_staff)

        authenticate_kwargs = self.basic_params.copy()
        authenticate_kwargs.pop('password')
        authenticated_staff: Optional[Staff] = self.auth_backend.authenticate(
            request=self.request, **authenticate_kwargs
        )
        self.assertIsNone(authenticated_staff)

    def test_authenticate_failed_with_no_match_company(self) -> None:
        """認証バックエンドが担当者と会社が合致せず失敗する"""
        another_base: Base = BaseFactory()
        self.basic_params['company_code'] = another_base.company_code
        authenticated_staff: Optional[Staff] = self.auth_backend.authenticate(
            request=self.request, **self.basic_params
        )
        self.assertIsNone(authenticated_staff)

    def test_authenticate_failed_with_disabled_staff(self) -> None:
        """認証バックエンドで担当者が無効化されているため失敗する"""
        self.staff.disable()

        authenticated_staff: Optional[Staff] = self.auth_backend.authenticate(
            request=self.request, **self.basic_params
        )
        self.assertIsNone(authenticated_staff)
