import factory
import faker
from django.utils import timezone
from factory.django import DjangoModelFactory, FileField, ImageField
from factory.fuzzy import <PERSON><PERSON><PERSON><PERSON><PERSON>, FuzzyInteger

from bases.tests.factories import BaseFactory
from items.tests.factories import ItemFactory
from masters.tests.factories import ScheduleFactory, SekoStyleFactory, ServiceFactory
from seko.models import (
    HenreihinKakegami,
    Kojin,
    Moshu,
    Seko,
    SekoAlbum,
    SekoAnswer,
    SekoContact,
    SekoInquiry,
    SekoItem,
    SekoSchedule,
    SekoService,
    SekoVideo,
)
from staffs.tests.factories import StaffFactory

DEFAULT_PASSWORD = 'the-dummy-password'
tz = timezone.get_current_timezone()
fake_provider = faker.Faker('ja_JP')


class SekoFactory(DjangoModelFactory):
    class Meta:
        model = Seko

    id = factory.Sequence(lambda n: n)
    order_staff = factory.SubFactory(StaffFactory)
    seko_staff = factory.SubFactory(StaffFactory)
    af_staff = factory.SubFactory(StaffFactory)
    seko_company = factory.SubFactory(BaseFactory)
    seko_department = factory.SubFactory(BaseFactory)
    death_date = factory.Faker('past_date', start_date='-30d', tzinfo=tz)
    seko_date = factory.Faker('past_date', start_date='-30d', tzinfo=tz)
    soke_name = factory.Faker('name', locale='ja_JP')
    soke_kana = factory.Faker('kana_name', locale='ja_JP')
    seko_style = factory.SubFactory(SekoStyleFactory)
    seko_style_name = factory.Faker('word', locale='ja_JP')
    fuho_site_admission_ts = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    fuho_site_end_date = factory.Faker('future_date', end_date='+30d', tzinfo=tz)
    fuho_sentence = factory.Faker('sentence', locale='ja_JP')
    fuho_contact_name = factory.Faker('name', locale='ja_JP')
    fuho_contact_tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    henreihin_rate = FuzzyInteger(0, 10)
    invoice_file_name = FileField(filename='dummy_invoice_file.pdf', data=fake_provider.binary())
    note = factory.Faker('paragraph', locale='ja_JP')
    del_flg = False


class MoshuFactory(DjangoModelFactory):
    class Meta:
        model = Moshu

    seko = factory.SubFactory(SekoFactory)
    name = factory.Faker('name', locale='ja_JP')
    kana = factory.Faker('kana_name', locale='ja_JP')
    kojin_moshu_relationship = factory.Faker('word', locale='ja_JP')
    zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    prefecture = factory.Faker('prefecture', locale='ja_JP')
    address_1 = factory.Faker('city', locale='ja_JP')
    address_2 = factory.Faker('town', locale='ja_JP')
    address_3 = factory.Faker('chome', locale='ja_JP')
    tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    mobile_num = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    mail_address = factory.Faker('ascii_free_email', locale='ja_JP')
    password = factory.PostGenerationMethodCall('set_password', DEFAULT_PASSWORD)
    agree_ts = None
    soke_site_del_request_flg = False
    soke_site_del_flg = False
    mail_flg = FuzzyChoice([True, False])


class KojinFactory(DjangoModelFactory):
    class Meta:
        model = Kojin

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    kojin_num = FuzzyInteger(0, 100)
    name = factory.Faker('name', locale='ja_JP')
    kana = factory.Faker('kana_name', locale='ja_JP')
    kaimyo = factory.Faker('name', locale='ja_JP')
    iei_file_name = ImageField()
    moshu_kojin_relationship = factory.Faker('word', locale='ja_JP')
    birth_date = factory.Faker('date_of_birth', tzinfo=tz, minimum_age=13)
    death_date = factory.Faker('past_date', start_date='-30d', tzinfo=tz)
    age_kbn = factory.Faker('word', locale='ja_JP')
    age = FuzzyInteger(10)
    note = factory.Faker('paragraph', locale='ja_JP')
    del_flg = False


class SekoScheduleFactory(DjangoModelFactory):
    class Meta:
        model = SekoSchedule

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    schedule = factory.SubFactory(ScheduleFactory)
    schedule_name = factory.Faker('word', locale='ja_JP')
    hall = factory.SubFactory(BaseFactory)
    hall_name = factory.Faker('company', locale='ja_JP')
    hall_zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    hall_address = factory.Faker('address', locale='ja_JP')
    hall_tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    schedule_date = factory.Faker('future_date', end_date='+30d', tzinfo=tz)
    begin_time = factory.Faker('time')
    end_time = factory.Faker('time')
    display_num = FuzzyInteger(0, 100)


class SekoVideoFactory(DjangoModelFactory):
    class Meta:
        model = SekoVideo

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    title = factory.Faker('sentence', locale='ja_JP')
    live_begin_ts = factory.Faker('future_datetime', end_date='+30d', tzinfo=tz)
    live_end_ts = factory.Faker('future_datetime', end_date='+30d', tzinfo=tz)
    delivery_end_ts = factory.Faker('future_datetime', end_date='+30d', tzinfo=tz)
    youtube_code = factory.Faker('image_url')


class SekoAlbumFactory(DjangoModelFactory):
    class Meta:
        model = SekoAlbum

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    file_name = ImageField()
    display_num = FuzzyInteger(0, 100)


class SekoItemFactory(DjangoModelFactory):
    class Meta:
        model = SekoItem

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    item = factory.SubFactory(ItemFactory)
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class SekoServiceFactory(DjangoModelFactory):
    class Meta:
        model = SekoService

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    hall_service = factory.SubFactory(ServiceFactory)
    limit_ts = factory.Faker('future_datetime', tzinfo=tz)
    henreihin_select_flg = True
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class HenreihinKakegamiFactory(DjangoModelFactory):
    class Meta:
        model = HenreihinKakegami

    seko = factory.SubFactory(SekoFactory)
    omotegaki = factory.Faker('word', locale='ja_JP')
    mizuhiki = factory.Faker('word', locale='ja_JP')
    package_type_name = factory.Faker('word', locale='ja_JP')
    okurinushi_name = factory.Faker('word', locale='ja_JP')
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class SekoInquiryFactory(DjangoModelFactory):
    class Meta:
        model = SekoInquiry

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    query_group_id = factory.Faker('pyint', min_value=0, max_value=1000)
    query = factory.Faker('paragraph', locale='ja_JP')
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class SekoAnswerFactory(DjangoModelFactory):
    class Meta:
        model = SekoAnswer

    id = factory.Sequence(lambda n: n)
    inquiry = factory.SubFactory(SekoInquiryFactory)
    answer = factory.Faker('paragraph', locale='ja_JP')
    staff = factory.SubFactory(StaffFactory)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class SekoContactFactory(DjangoModelFactory):
    class Meta:
        model = SekoContact

    seko = factory.SubFactory(SekoFactory)
    name = factory.Faker('name', locale='ja_JP')
    kojin_relationship = factory.Faker('word', locale='ja_JP')
    zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    prefecture = factory.Faker('prefecture', locale='ja_JP')
    address_1 = factory.Faker('city', locale='ja_JP')
    address_2 = factory.Faker('town', locale='ja_JP')
    address_3 = factory.Faker('chome', locale='ja_JP')
    tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    mobile_num = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    mail_address = factory.Faker('ascii_free_email', locale='ja_JP')
