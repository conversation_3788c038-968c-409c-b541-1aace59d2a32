from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from invoices.tests.factories import (
    SalesCompanyFactory,
    SalesDetailFactory,
    SalesIndexFactory,
    SalesSekoDepartmentFactory,
)
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class InvoicePDFViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.sales_index = SalesIndexFactory(sum_ts=timezone.localtime())
        self.sales_company = SalesCompanyFactory(sales_index=self.sales_index)

        sales_department1 = SalesSekoDepartmentFactory(sales_company=self.sales_company)
        sales_department2 = SalesSekoDepartmentFactory(sales_company=self.sales_company)

        SalesDetailFactory(sales_seko_department=sales_department1)
        SalesDetailFactory(sales_seko_department=sales_department1)
        SalesDetailFactory(sales_seko_department=sales_department2)
        SalesDetailFactory(sales_seko_department=sales_department2)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_invoice_pdf_succeed(self) -> None:
        """請求書PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('invoices:invoice_pdf', kwargs={'pk': self.sales_company.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'請求書-{self.sales_company.pk}.pdf')

    def test_invoice_pdf_failed_without_auth(self) -> None:
        """請求書PDF取得APIはAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('invoices:invoice_pdf', kwargs={'pk': self.sales_company.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
