from django.db.models import Q, QuerySet
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.views import TokenObtainPairView

from bases.models import Base
from staffs.models import Staff
from staffs.serializers import (
    StaffParentBasesSerializer,
    StaffSerializer,
    StaffTokenObtainPairSerializer,
)
from utils.permissions import IsStaff
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class StaffTokenObtainPair(TokenObtainPairView):
    serializer_class = StaffTokenObtainPairSerializer


class StaffMe(generics.RetrieveAPIView):
    """
    get: ログインユーザの詳細を取得します
    """

    http_method_names = ('head', 'options', 'get')
    queryset = Staff.objects.all()
    serializer_class = StaffParentBasesSerializer
    permission_classes = [IsStaff]

    def get_object(self):
        return self.request.user


class StaffListLowerBases(generics.ListAPIView):
    """
    get: 指定した拠点と、それに所属する拠点のいずれかに所属する担当者の一覧を取得します
    """

    serializer_class = StaffSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        specified_base: Base = Base.objects.get(Q(pk=self.kwargs['base_id']) & Q(del_flg=False))
        base_ids: QuerySet = (
            specified_base.get_descendants(include_self=True)
            .filter(Q(del_flg=False))
            .values_list('id', flat=True)
        )
        return (
            Staff.objects.select_related('base')
            .filter(Q(base_id__in=base_ids) & Q(del_flg=False))
            .all()
        )


class StaffList(AddContextMixin, generics.CreateAPIView):

    serializer_class = StaffSerializer
    permission_classes = [IsAuthenticated]


class StaffDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 担当者情報を取得します
    put: 担当者情報を更新します
    patch: 担当者情報を更新します
    delete: 担当者情報を(論理)削除します
    """

    queryset = Staff.objects.filter(Q(del_flg=False))
    serializer_class = StaffSerializer
    permission_classes = [IsStaff]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return StaffSerializer
        return self.serializer_class
