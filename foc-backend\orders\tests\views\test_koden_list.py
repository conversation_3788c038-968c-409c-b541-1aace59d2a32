from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from henrei.tests.factories import HenreihinKodenFactory
from orders.models import EntryDetailKoden
from orders.tests.factories import EntryDetailFactory, EntryDetailKodenFactory, EntryFactory
from seko.tests.factories import KojinFactory, MoshuFactory, SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class KodenListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_1 = BaseFactory()
        self.base_2 = BaseFactory()

        self.seko_1 = SekoFactory(seko_company=self.base_1)
        self.seko_2 = SekoFactory(seko_company=self.base_2)
        self.kojin_1 = KojinFactory(kojin_num=1, seko=self.seko_1)
        self.kojin_2 = KojinFactory(kojin_num=1, seko=self.seko_2)
        self.moshu_1 = MoshuFactory(seko=self.seko_1)
        self.moshu_2 = MoshuFactory(seko=self.seko_2)
        self.entry_1 = EntryFactory(seko=self.seko_1)
        self.entry_2 = EntryFactory(seko=self.seko_2)

        entry_detail_21 = EntryDetailFactory(entry=self.entry_2)
        entry_detail_22 = EntryDetailFactory(entry=self.entry_2)
        entry_detail_11 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_12 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_13 = EntryDetailFactory(entry=self.entry_1)

        self.koden_1 = EntryDetailKodenFactory(entry_detail=entry_detail_21)
        self.koden_2 = EntryDetailKodenFactory(entry_detail=entry_detail_22)
        self.koden_3 = EntryDetailKodenFactory(entry_detail=entry_detail_11)
        self.koden_4 = EntryDetailKodenFactory(entry_detail=entry_detail_13)
        self.koden_5 = EntryDetailKodenFactory(entry_detail=entry_detail_12)

        HenreihinKodenFactory(detail_koden=self.koden_1)
        HenreihinKodenFactory(detail_koden=self.koden_2)
        HenreihinKodenFactory(detail_koden=self.koden_3)
        HenreihinKodenFactory(detail_koden=self.koden_4)
        HenreihinKodenFactory(detail_koden=self.koden_5)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_koden_list_succeed(self) -> None:
        """香典一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        response_koden = records[0]
        db_koden = (
            EntryDetailKoden.objects.select_related(
                'entry_detail',
                'entry_detail__entry',
                'entry_detail__entry__seko',
                'entry_detail__entry__seko__moshu',
                'henrei_koden',
            )
            .prefetch_related('entry_detail__entry__seko__kojin')
            .get(pk=response_koden['entry_detail']['id'])
        )

        self.assertEqual(response_koden['koden_commission'], db_koden.koden_commission)
        self.assertEqual(response_koden['entry_detail']['id'], db_koden.entry_detail.id)
        self.assertEqual(
            response_koden['entry_detail']['item_name'], db_koden.entry_detail.item_name
        )
        self.assertEqual(
            response_koden['entry_detail']['entry']['id'], db_koden.entry_detail.entry.id
        )
        self.assertEqual(
            response_koden['entry_detail']['entry']['entry_name'],
            db_koden.entry_detail.entry.entry_name,
        )
        self.assertEqual(
            response_koden['entry_detail']['entry']['seko']['id'],
            db_koden.entry_detail.entry.seko.id,
        )
        self.assertEqual(
            response_koden['entry_detail']['entry']['seko']['soke_name'],
            db_koden.entry_detail.entry.seko.soke_name,
        )
        self.assertEqual(
            response_koden['entry_detail']['entry']['seko']['seko_company'],
            db_koden.entry_detail.entry.seko.seko_company.id,
        )
        self.assertEqual(
            response_koden['entry_detail']['entry']['seko']['kojin'][0]['id'],
            db_koden.entry_detail.entry.seko.kojin.get().id,
        )
        self.assertEqual(
            response_koden['entry_detail']['entry']['seko']['moshu']['name'],
            db_koden.entry_detail.entry.seko.moshu.name,
        )
        self.assertEqual(
            response_koden['henrei_koden']['henreihin_name'], db_koden.henrei_koden.henreihin_name
        )

        # 申込IDの昇順になっているか
        entry_ids = [record['entry_detail']['entry']['id'] for record in records]
        self.assertEqual(entry_ids, sorted(entry_ids))

    def test_koden_list_ignores_deleted_seko(self) -> None:
        """削除した施行の香典は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko_1.disable()

        params: Dict = {}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_koden_list_ignores_when_kojin_num_is_not_equal_1(self) -> None:
        """故人番号が1ではない香典は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.kojin_2.kojin_num = 2
        self.kojin_2.save()

        params: Dict = {}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_koden_list_filter_by_company_id(self) -> None:
        """香典一覧で葬儀社IDを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'company_id': self.base_1.id}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_koden_list_filter_by_entry_id(self) -> None:
        """香典一覧で申込IDを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'entry_id': self.entry_2.id}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_koden_list_filter_by_entry_name(self) -> None:
        """香典一覧で申込者氏名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.entry_1.entry_name = '1一'
        self.entry_2.entry_name = '1一2二'
        self.entry_1.save()
        self.entry_2.save()

        params: Dict = {'entry_name': '2二'}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_koden_list_filter_by_soke_name(self) -> None:
        """香典一覧で葬家名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko_1.soke_name = '1一'
        self.seko_2.soke_name = '1一2二'
        self.seko_1.save()
        self.seko_2.save()

        params: Dict = {'soke_name': '一2'}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_koden_list_filter_by_kojin_name(self) -> None:
        """香典一覧で故人名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.kojin_1.name = '1一'
        self.kojin_2.name = '1一2二'
        self.kojin_1.save()
        self.kojin_2.save()

        params: Dict = {'kojin_name': '2二'}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_koden_list_filter_by_moshu_name(self) -> None:
        """香典一覧で喪主名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.moshu_1.name = '1一'
        self.moshu_2.name = '2二1一'
        self.moshu_1.save()
        self.moshu_2.save()

        params: Dict = {'moshu_name': '二1'}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_koden_list_filter_by_is_cancel(self) -> None:
        """香典一覧でキャンセル状況を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.entry_1.cancel_ts = timezone.now() - timezone.timedelta(30)
        self.entry_1.save()

        params: Dict = {'is_cancel': True}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        params: Dict = {'is_cancel': False}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_koden_list_failed_without_auth(self) -> None:
        """香典一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('orders:koden_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
