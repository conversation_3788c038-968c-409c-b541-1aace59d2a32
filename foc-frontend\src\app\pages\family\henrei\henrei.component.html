
<div class="container">
  <div class="inner">
    <div class="contents">
      <h2>返礼品を送る</h2>
      <div class="service-label">サービス内容で絞り込む</div>
      <div class="service-check">
        <div class="ui checkbox">
          <input type="checkbox" [(ngModel)]="homei_filter.kumotsu" (change)="filterHomeiList()">
          <label><div class="image kumotsu"></div>供花・供物</label>
        </div>
        <div class="ui checkbox" *ngIf="haveKoden()">
          <input type="checkbox" [(ngModel)]="homei_filter.koden" (change)="filterHomeiList()">
          <label><div class="image koden"></div>香典</label>
        </div>
      </div>
      <div class="title_area">
        <table>
          <tbody>
            <tr>
              <td class="entry_name">申込者</td>
              <td class="okurinushi">送り主</td>
              <td class="service">サービス</td>
              <td class="detail">詳細</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="data_area" *ngFor="let homei of homei_list">
        <div class="ui accordion">
          <div class="title">
            <table>
              <tbody>
                <tr>
                  <td class="entry_name">{{homei.entry_name}}</td>
                  <td class="okurinushi">{{homei.okurinushi_name}}</td>
                  <td class="service">
                    <div>
                      <div class="image {{getClassName(homei.service_id)}}"></div>
                      <div class="name">{{homei.service_name}}</div>
                    </div>
                  </td>
                  <td class="detail">
                    <i class="chevron down icon small"></i>
                    <i class="chevron up icon small"></i>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="content">
            <table>
              <tbody>
                <tr>
                  <td class="label">申込番号</td>
                  <td class="data">{{homei.entry_id}}</td>
                </tr>
                <tr>
                  <td class="label">品目</td>
                  <td class="data">{{homei.item_name}}</td>
                </tr>
                <tr>
                  <td class="label">摘要</td>
                  <td class="data">
                    ¥{{homei.price | number}}
                    <ng-container *ngIf="homei.koden?.henrei_koden">
                      （{{homei.koden.henrei_koden.henreihin_hinban}}）
                      {{homei.koden.henrei_koden.henreihin_name}}
                      ¥{{homei.koden.henrei_koden.henreihin_price | number}}
                      <a class="button grey" *ngIf="homei.koden.henrei_koden.select_status !== Const.SELECT_STATUS_ID_CONFIRM" (click)="deleteConfirm(homei)">返礼品取消</a>
                    </ng-container>
                    <ng-container *ngIf="homei.kumotsu?.henrei_kumotsu">
                      （{{homei.kumotsu.henrei_kumotsu.henreihin_hinban}}）
                      {{homei.kumotsu.henrei_kumotsu.henreihin_name}}
                      ¥{{homei.kumotsu.henrei_kumotsu.henreihin_price | number}}
                      <a class="button grey" *ngIf="homei.kumotsu.henrei_kumotsu.select_status !== Const.SELECT_STATUS_ID_CONFIRM" (click)="deleteConfirm(homei)">返礼品取消</a>
                    </ng-container>
                  </td>
                </tr>
                <tr *ngIf="homei.koden?.henrei_koden || homei.kumotsu?.henrei_kumotsu">
                  <td class="label">選択</td>
                  <td class="data">
                    <ng-container *ngIf="homei.koden?.henrei_koden">
                      {{homei.koden.henrei_koden.customer_self_select_flg?'顧客':'葬家'}}
                    </ng-container>
                    <ng-container *ngIf="homei.kumotsu?.henrei_kumotsu">
                      葬家
                    </ng-container>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="henreihin_button"><a class="button {{getHenreiClassName(homei)}}" (click)="showItemList(homei)">{{getHenreiName(homei)}}</a></div>
      </div>
      <div class="total_area">確定金額： <span class="price">¥ {{henreihin_sum1 | number}}</span></div>
      <div class="total_area">未確定金額： <span class="price">¥ {{henreihin_sum2 | number}}</span></div>
      <div class="total_area">返礼品金額合計： <span class="price">¥ {{(henreihin_sum1 + henreihin_sum2) | number}}</span></div>
    </div>
    <div class="button-area">
      <a class="button pink" (click)="showConfirmList()">注文確認</a>
    </div>

    <div class="ui modal item" id="item-list">
      <div class="header ui">
        <div><div class="label">申込者</div><div class="data" *ngIf="selected_homei">{{selected_homei.entry_name}}</div></div>
        <div>
          <div class="label">宛先</div>
          <div class="data" *ngIf="selected_homei">
            <div>{{selected_homei.entry_zip_code}}</div>
            <div>{{selected_homei.entry_prefecture}}{{selected_homei.entry_address_1}}</div>
            <div>{{selected_homei.entry_address_2}}{{selected_homei.entry_address_3}}</div>
          </div>
        </div>
        <div class="price_filter">
          <div class="label">返礼品価格帯</div>
          <div class="data">
            <input type="number" [(ngModel)]="item_filter.price_from">
            <div>〜</div>
            <input type="number" [(ngModel)]="item_filter.price_to">
            <a class="button grey" (click)="filterItemList()">検索</a>
          </div>
        </div>
      </div>
      <div class="content scrolling">
        <ng-container *ngIf="item_list?.length">
          <div class="item-box" [class.selected]="item.selected" (click)="selectItem(item)" *ngFor="let item of item_list">
            <div class="title-box">
              <div class="name">{{item.name}}</div>
              <div class="price">{{item.item_price | number}}円(税込)</div>
              <div class="hinban">{{item.hinban}}</div>
            </div>
            <div class="image-box" [class.no-image]="!item.image_file">
              <img [src]="item.image_file" *ngIf="item.image_file" (error)="imageLoadError(item)">
              <ng-container *ngIf="!item.image_file">
                <div class="no-image">
                  <i class="image icon huge"></i>
                  <div class="noimage">No image</div>
                </div>
              </ng-container>

            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="!item_list?.length">
          <div class="no-data">選択可能な返礼品がございません。</div>          
        </ng-container>
      </div>
    </div>

    <div class="ui modal confirm-list" id="confirm-list">
      <div class="header ui">
        注文確認
      </div>
      <div class="content scrolling">
        <ng-container *ngFor="let homei of confirm_list">
          <div class="entry-box">
            <div class="entry-detail">
              <div class="label">宛先</div>
              <div class="data">
                <div>{{homei.entry_zip_code}}</div>
                <div>{{homei.entry_prefecture}}{{homei.entry_address_1}}</div>
                <div>{{homei.entry_address_2}}{{homei.entry_address_3}}</div>
                <div>{{homei.entry_name}}様</div>
              </div>
            </div>
            <div class="item-box">
              <div class="title-box">
                <div class="name">{{homei.item_name}}</div>
                <div class="price">{{homei.item_price | number}}円(税込)</div>
                <div class="hinban">{{homei.item_hinban}}</div>
              </div>
              <div class="image-box" [class.no-image]="!homei.image_file">
                <img [src]="homei.image_file" *ngIf="homei.image_file" (error)="imageLoadError(homei)">
                <ng-container *ngIf="!homei.image_file">
                  <div class="no-image">
                    <i class="image icon huge"></i>
                    <div class="noimage">No image</div>
                  </div>
                </ng-container>

              </div>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="!confirm_list?.length">
          <div class="no-data">注文可能な返礼品がございません。</div>          
        </ng-container>
      </div>
      <div class="price-area" *ngIf="confirm_list?.length">
        合計金額： ¥ {{henreihin_sum2 | number}}
      </div>
      <div class="button-area">
        <a class="button pink" [class.disabled]="!confirm_list?.length" (click)="confirmHenrei()">注文送信</a>
        <a class="button grey" (click)="closeConfirmList()">キャンセル</a>
      </div>
    </div>

    <div class="ui modal confirm" id="henrei-confirm">
      <div class="content">
        返礼品を取り消します。<br>よろしいでしょうか？
      </div>
      <div class="button-area">
        <a class="button" (click)="cancelConfirm()">キャンセル</a>
        <a class="button grey" (click)="deleteHenrei()">確定</a>
      </div>
    </div>
    <div class="ui modal confirm" id="message-popup">
      <div class="content red">
        {{message}}
      </div>
      <div class="button-area">
        <a class="button grey" (click)="closePopup()">閉じる</a>
      </div>
    </div>
  </div>
</div>

