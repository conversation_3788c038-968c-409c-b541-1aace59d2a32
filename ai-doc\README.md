# FOCプロジェクト 開発ドキュメント

## 概要

このディレクトリには、FOC（Funeral Online system Connect）プロジェクトにおける新規機能開発のためのガイドドキュメントが含まれています。

## 技術スタック概要

- **フロントエンド**: Angular 10 + TypeScript + Fomantic UI
- **バックエンド**: Django 3.1 + Django REST Framework + JWT認証
- **データベース**: PostgreSQL/SQLite
- **CI/CD**: GitHub Actions
- **コンテナ**: Docker

## ドキュメント構成

### 📋 [development-flow-guide.md](./development-flow-guide.md)
**新規機能開発フローの全体像**

新規機能（例：ユーザープロフィール編集機能）を追加する際の一般的な開発フローを説明します。

**内容:**
- フロントエンド側の実装フロー
  - 新しい画面コンポーネントやルーティングの追加方法
  - フォーム入力や状態管理（SessionService）
  - APIとの通信部分（HttpClientService）
- バックエンド側の実装フロー
  - ルーティングの追加（Django URL設定）
  - ビジネスロジックの処理箇所（ビュー、シリアライザー）
  - DBモデル・マイグレーションの変更（Django ORM）
- フロント↔バックエンド連携の仕様
  - APIのエンドポイント設計方針
  - バリデーション・エラーハンドリング
  - リクエスト・レスポンスの形式
- 認証認可の影響
  - ログイン済ユーザーしかアクセスできないAPI保護の実装方法
- テスト・レビュー・デプロイの流れ
  - テストケースの追加方法（フロント/バック）
  - CIのトリガーと自動テストの実行フロー
  - 本番デプロイまでのステップ

### 💻 [implementation-examples.md](./implementation-examples.md)
**具体的な実装例とコードサンプル**

実際のコードを交えた詳細な実装例を提供します。

**内容:**
- フロントエンド実装例
  - Angularコンポーネントの完全な実装例
  - TypeScriptでの型定義とバリデーション
  - HTMLテンプレートとSCSSスタイル
  - HttpClientServiceの拡張方法
  - ルーティング設定の追加
- バックエンド実装例
  - Djangoシリアライザーの実装
  - APIビューの実装（RetrieveUpdateAPIView）
  - URL設定の追加
  - モデルの拡張とマイグレーション
- テスト実装例
  - フロントエンドのユニットテスト（Jasmine + Karma）
  - バックエンドのユニットテスト（Django Test Framework）
  - E2Eテスト（Protractor）
  - ファクトリーパターンの活用

### 🔄 [処理フロー図](./README.md#処理フロー図)
**フロントエンド→バックエンド処理フロー**

ユーザーの操作からバックエンドAPIまでの処理の流れを図解します。

### 📋 [feature-list.md](./feature-list.md)
**全機能一覧**

FOCプロジェクトの全機能を体系的に整理したドキュメントです。

**内容:**
- 企業向け機能（CompanyFrameComponent）
  - 認証・ユーザー管理、施行管理、拠点管理、仕入先管理、商品管理
  - 注文管理、返礼品管理、弔文管理、供物管理、香典管理
  - 請求書管理、FAQ管理、広告管理、アフターフォロー、法要管理
- 顧客向け機能（CustomerFrameComponent）
  - 訃報機能、法要機能、注文機能、返礼品機能、お問い合わせ機能
- 家族向け機能（FamilyFrameComponent）
  - 葬家認証、葬家専用機能
- 共通機能・管理機能・外部連携機能

### 🔌 [api-list.md](./api-list.md)
**全API一覧**

バックエンドAPIエンドポイントの完全な一覧です。

**内容:**
- 認証API（スタッフ・葬家）
- 各種管理API（スタッフ、拠点、施行、仕入先、商品、注文等）
- マスターデータAPI
- 外部連携API
- API設計パターンと命名規則

### 🏗️ [project-specific-guide.md](./project-specific-guide.md)
**FOCプロジェクト固有の設定とベストプラクティス**

プロジェクト特有の設計パターンや慣習について説明します。

**内容:**
- プロジェクト構造の理解
  - フロントエンド・バックエンドのディレクトリ構成
  - 各層の責務と役割
- 認証・認可システム
  - カスタマイズされたJWT認証の仕組み
  - スタッフ・葬家の認証分離
  - 権限ガードの使い分け
- API設計パターン
  - RESTful API設計方針
  - シリアライザーの継承パターン
  - ビューの継承パターン
- フロントエンド設計パターン
  - コンポーネント設計方針
  - フレームコンポーネントパターン
  - HTTP通信パターン
- データベース設計パターン
  - 共通フィールドパターン
  - マスターデータ管理パターン
- テスト戦略
  - ファクトリーパターンの活用
  - 認証テストパターン
- 環境設定とデプロイ
  - 環境変数管理
  - Docker設定

## 開発フロー概要

### 1. 事前準備
1. 要件定義と設計
2. 既存コードベースの理解
3. 必要な権限・認証要件の確認

### 2. 実装
1. **バックエンド先行開発**
   - モデル設計・マイグレーション
   - シリアライザー実装
   - APIビュー実装
   - ユニットテスト作成

2. **フロントエンド開発**
   - コンポーネント実装
   - ルーティング設定
   - API通信実装
   - ユニットテスト作成

### 3. テスト・統合
1. 統合テスト実行
2. E2Eテスト実行
3. 手動テスト・検証

### 4. レビュー・デプロイ
1. コードレビュー
2. CI/CDパイプライン実行
3. ステージング環境デプロイ
4. 本番環境デプロイ

## 開発環境セットアップ

### フロントエンド
```bash
cd foc-frontend
npm install
npm start  # 開発サーバー起動（http://localhost:4200）
```

### バックエンド
```bash
cd foc-backend
poetry install
poetry run python manage.py migrate
poetry run python manage.py runserver  # 開発サーバー起動（http://localhost:8000）
```

### テスト実行
```bash
# フロントエンド
cd foc-frontend
npm test

# バックエンド
cd foc-backend
poetry run python manage.py test
```

## 重要な注意点

### セキュリティ
- JWT トークンの適切な管理
- 認証・認可の実装
- バリデーションの実装（フロント・バック両方）
- CORS設定の確認

### パフォーマンス
- データベースクエリの最適化
- フロントエンドでのLazy Loading
- 適切なキャッシュ戦略

### 保守性
- 一貫したコーディングスタイル
- 適切なテストカバレッジ
- ドキュメントの更新
- コードレビューの実施

## 処理フロー図

以下の図は、ユーザーがURLをクリックしたり保存操作を行った際の、フロントエンドからバックエンドまでの処理フローを示しています：

### フロー説明

1. **フロントエンド層**
   - ユーザー操作 → ルーティング → フレームコンポーネント → 認証ガード → 対象コンポーネント
   - コンポーネント初期化 → HttpClientService → JWT認証ヘッダー付与 → HTTP Request

2. **バックエンド層**
   - Django URLディスパッチャー → View特定 → JWT認証 → 権限チェック
   - ビューメソッド実行 → Serializer → バリデーション → データベース操作

3. **レスポンス処理**
   - HTTP Response → フロントエンド受信 → エラーハンドリング → 画面更新

## 参考リンク

- [Angular 公式ドキュメント](https://angular.io/docs)
- [Django 公式ドキュメント](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Fomantic UI](https://fomantic-ui.com/)

## 更新履歴

- 2024-12-26: 初版作成
  - 開発フローガイド作成
  - 実装例集作成
  - プロジェクト固有ガイド作成
  - 処理フロー図作成
  - 全機能一覧作成
  - 全API一覧作成

---

このドキュメントは、FOCプロジェクトでの効率的な開発を支援するために作成されました。不明な点や改善提案があれば、開発チームまでお知らせください。
