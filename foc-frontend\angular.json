{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"foc-frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/foc-frontend", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": false, "assets": ["src/favicon_com.ico", "src/favicon_cus.ico", "src/assets", {"glob": "**/*", "input": "node_modules/ng2-pdfjs-viewer/pdfjs", "output": "/assets/pdfjs"}], "styles": ["node_modules/fomantic-ui-css/semantic.min.css", "src/assets/css/flexslider.css", "src/styles.scss"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/fomantic-ui-css/semantic.min.js", "src/assets/js/jquery.flexslider-min.js", "src/assets/js/jquery.autoKana.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "es5": {"tsConfig": "./tsconfig-es5.app.json"}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "foc-frontend:build"}, "configurations": {"production": {"browserTarget": "foc-frontend:build:production"}, "es5": {"browserTarget": "foc-frontend:build:es5"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "foc-frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon_com.ico", "src/favicon_cus.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "foc-frontend:serve"}, "configurations": {"production": {"devServerTarget": "foc-frontend:serve:production"}}}}}}, "defaultProject": "foc-frontend"}