
@import "src/assets/scss/company/setting";
.container .inner .contents {
  .menu_title {
    .icon.big {
      margin-right: 20px;
    }
    .icon.combo {
      position: absolute;
      left: 45px;
    }
  }
  min-height: 400px;
  .search_area .line {
    .ui.input.small {
      max-width: 150px;
    }
    margin-bottom: 10px;
  }
  .af_group {
    font-weight: bold;
    margin-bottom: 5px;
  }
  .af-check {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
    margin-top: 0;
    margin-left: 10px;
    box-shadow: none;
    padding-bottom: 0;
    .ui.checkbox {
      margin-left: 20px;
      margin-bottom: 10px;
      label {
        font-weight: normal;
      }
    }

  }
  .input_area {
    position: absolute;
    bottom: 30px;
    width: 90%;
    left: 10px;
    .description {
      margin-left: 20px;
    }
  }
  .ui.pagination.menu {
    margin-top: 5px;
  }
  .table_fixed.body {
    max-height: calc(100% - 360px);
    &.no-page-nav {
      max-height: calc(100% - 330px);
    }
  }
  >.table_fixed tr {
    line-height: 1;
    td {
      >div:not(.checkbox) {
        min-height: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &.center {
          text-align: center;
        }
        &:not(:last-child) {
          min-height: 19px;
          border-bottom: dotted 1px $background-light1;
          margin: 0 -10px 5px;
          padding-left: 10px;
          padding-bottom: 5px;
        }
      }
    }
    .check {
      width: 50px;
      padding-left: 15px;
    }
    .moshu_id {
      width: 9%;
    }
    .relationship {
      width: 15%;
    }
    .moshu_name {
      width: 20%;
    }
    .mobile_num {
      width: 15%;
    }
  }
}
