# Generated by Django 3.1.2 on 2020-11-04 03:32

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chobun_daishi', '0002_auto_20201102_1237'),
        ('masters', '0007_service_tax'),
        ('bases', '0009_base_chobun_daishi_masters'),
    ]

    operations = [
        migrations.AlterField(
            model_name='base',
            name='chobun_daishi_masters',
            field=models.ManyToManyField(
                blank=True,
                null=True,
                related_name='bases',
                through='chobun_daishi.ChobunDaishi',
                to='masters.ChobunDaishiMaster',
                verbose_name='chobun daishi master',
            ),
        ),
        migrations.CreateModel(
            name='BaseService',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'base',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='bases.base',
                        verbose_name='base',
                    ),
                ),
                (
                    'service',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='masters.service',
                        verbose_name='service',
                    ),
                ),
            ],
            options={
                'db_table': 'm_base_service',
            },
        ),
        migrations.AddField(
            model_name='base',
            name='services',
            field=models.ManyToManyField(
                related_name='bases',
                through='bases.BaseService',
                to='masters.Service',
                verbose_name='services',
            ),
        ),
    ]
