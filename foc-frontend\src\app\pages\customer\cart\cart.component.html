
<div class="container">
  <div class="inner">
    <div class="contents header">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="navigation">
      <div class="item current"><span>カート</span><span>内容確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込者</span><span>情報登録</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込内容</span><span>確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>お支払い</span><span>手続き</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込み</span><span>完了</span></div>
    </div>
    <div class="contents" *ngIf="entry?.details?.length">
      <h2>カート内容確認</h2>
      <table class="detail">
        <tbody>
          <ng-container *ngFor="let detail of entry.details">
          <tr class="pc">
            <ng-container *ngIf="detail.service.id!==Const.SERVICE_ID_KODEN && detail.service.id!==Const.SERVICE_ID_MESSAGE">
              <td class="name">{{detail.service.name}}<br>
                <span class="item_name" [class.remark]="detail.keigen_flg">{{detail.item_name}}</span>
              </td>
              <td class="quantity">数量 {{detail.quantity}}</td>
              <td class="price">¥{{detail.item_unit_price * detail.quantity | number}}</td>
            </ng-container>
            <ng-container *ngIf="detail.service.id===Const.SERVICE_ID_KODEN">
              <td class="name" colspan="2">
                御香典(非課税)<br><span class="system_name">システム利用料</span>
              </td>
              <td class="price">
                ¥{{detail.item_unit_price * detail.quantity | number}}<br>
                <span class="system_price">¥{{detail.koden.koden_commission | number}}</span>
              </td>
            </ng-container>
            <ng-container *ngIf="detail.service.id===Const.SERVICE_ID_MESSAGE">
              <td class="name" colspan="2">{{detail.service.name}}</td>
              <td class="price">¥{{detail.item_unit_price * detail.quantity | number}}</td>
            </ng-container>
            <td class="operate pc">
              <div class="button-area">
                <ng-container *ngIf="!isNotInService(detail.service.id) && !isNotInServiceTerm(detail.service.id, detail.item) && !iteminvalid(detail.item)">
                  <a class="button grey" (click)="editDetail(detail)">変更</a>
                </ng-container>
                <a class="button grey light" (click)="deleteConfirm(detail)">削除</a>
              </div>
            </td>
          </tr>
          <tr class="sp">
            <td class="operate sp" colspan="4">
              <div class="button-area">
                <ng-container *ngIf="!isNotInService(detail.service.id) && !isNotInServiceTerm(detail.service.id, detail.item) && !iteminvalid(detail.item)">
                  <a class="button grey" (click)="editDetail(detail)">変更</a>
                </ng-container>
                <a class="button grey light" (click)="deleteConfirm(detail)">削除</a>
              </div>
            </td>
          </tr>
          <tr class="divided">
            <td class="expired" colspan="4">
              <ng-container *ngIf="isNotInService(detail.service.id)">
              *このサービスは申込することができません。
            </ng-container>
            <ng-container *ngIf="isNotInServiceTerm(detail.service.id, detail.item)">
              *申込期限が過ぎましたので、申込することができません。
            </ng-container>
            <ng-container *ngIf="!isNotInService(detail.service.id) && !isNotInServiceTerm(detail.service.id, detail.item) && iteminvalid(detail.item)">
              *この商品は現在、取り扱っておりません。
            </ng-container>
            </td>
          </tr>
          </ng-container>
        </tbody>
      </table>
      <table class="summary">
        <tbody>
          <tr *ngIf="sum_tax_8.price">
            <td class="summary_name remark">内税消費税（8%）対象額<br>内税消費税</td>
            <td class="price">¥{{sum_tax_8.price | number}}<br>¥{{sum_tax_8.tax | number}}</td>
          </tr>
          <tr *ngIf="sum_tax_10.price">
            <td class="summary_name">内税消費税（10%）対象額<br>内税消費税</td>
            <td class="price">¥{{sum_tax_10.price | number}}<br>¥{{sum_tax_10.tax | number}}</td>
          </tr>
          <tr *ngIf="sum_tax_0.price">
            <td class="summary_name">非課税対象額</td>
            <td class="price">¥{{sum_tax_0.price | number}}</td>
          </tr>
          <tr class="total">
            <td class="summary_name">合計金額（税込）</td>
            <td class="price">¥{{entry.billing_amount | number}}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="contents empty" *ngIf="!entry?.details?.length">
      カートに内容がございません。
    </div>
    <div class="button-area">
      <ng-container *ngFor="let service of seko_info.services">
        <a class="button lightgrey" (click)="goServiceLink(service.hall_service)" 
        *ngIf="service.hall_service.id!==Const.SERVICE_ID_HENREIHIN && !isExpierd(service) && canContinue(service.hall_service)">
        {{getServiceName(service.hall_service)}} ></a>
      </ng-container>
      <a class="button grey" [class.disabled]="!canOrder()" (click)="saveData()">ご注文手続き ></a>
    </div>

    <div class="ui modal confirm" id="delete-confirm">
      <div class="content">
        カートから削除します。<br>よろしいでしょうか？
      </div>
      <div class="button-area">
        <a class="button" (click)="cancelConfirm()">キャンセル</a>
        <a class="button grey" (click)="deleteData()">確定</a>
      </div>
    </div>

  </div>
</div>
