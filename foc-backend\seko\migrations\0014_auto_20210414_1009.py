# Generated by Django 3.1.7 on 2021-04-14 01:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('seko', '0013_sekoanswer_sekoinquiry'),
    ]

    operations = [
        migrations.AddField(
            model_name='seko',
            name='fdn_code',
            field=models.TextField(blank=True, null=True, verbose_name='fdn code'),
        ),
        migrations.AlterField(
            model_name='moshu',
            name='mail_address',
            field=models.TextField(blank=True, null=True, verbose_name='mail address'),
        ),
        migrations.AlterField(
            model_name='seko',
            name='af_staff',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='af_seko',
                to=settings.AUTH_USER_MODEL,
                verbose_name='af_staff',
            ),
        ),
        migrations.AlterField(
            model_name='seko',
            name='fuho_contact_name',
            field=models.TextField(blank=True, null=True, verbose_name='fuho contact name'),
        ),
        migrations.AlterField(
            model_name='seko',
            name='fuho_contact_tel',
            field=models.CharField(
                blank=True, max_length=15, null=True, verbose_name='fuho contact tel'
            ),
        ),
        migrations.AlterField(
            model_name='seko',
            name='fuho_sentence',
            field=models.TextField(blank=True, null=True, verbose_name='fuho sentence'),
        ),
    ]
