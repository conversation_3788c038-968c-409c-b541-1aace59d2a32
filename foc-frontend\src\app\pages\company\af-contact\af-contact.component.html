
<div class="container">
  <div class="inner with_footer">
    <div class="contents tiny">
      <div class="menu_title">
        <i class="hand holding medical icon big"></i>
        <i class="linkify icon combo"></i>
        AF連絡方法
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" [(selectedName)]="company_name" (selectedItemChange)="companyChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="af_contact_list?.length">
          全{{af_contact_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?af_contact_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination, pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(pagination, page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination, pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!af_contact_list?.length">
              <th class="contact_way">連絡方法</th>
              <th class="display_num">表示順</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let af_contact of af_contact_list; index as i">
              <tr (click)="showData($event, af_contact)" *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
                <td class="contact_way" title="{{af_contact.contact_way}}">{{af_contact.contact_way}}</td>
                <td class="center aligned display_num" title="{{af_contact.display_num}}">{{af_contact.display_num}}</td>
                <td class="center aligned button operation">
                  <i class="large trash alternate icon" title="削除" (click)="deleteData(af_contact)"></i>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="table_fixed body" *ngIf="company_id">
        <table class="ui celled structured unstackable table">
          <tbody>
            <tr title="新規追加" (click)="showData($event)">
              <td colspan="3" class="center aligned"><i class="large add icon"></i></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal mini" id="af-contact-edit">
        <div class="header ui">
          <span>
            <i class="hand holding medical icon large"></i>
            <i class="linkify icon mini combo"></i>AF連絡方法{{edit_type===1?'登録':'編集'}} 
          </span>
          <span class="title" *ngIf="company_name">【葬儀社:<span class="data">{{company_name}}</span>】</span>
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="af_contact_edit">
            <div class="line">
              <label class="required">連絡方法</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="contact_way" [(ngModel)]="af_contact_edit.contact_way">
              </div>
            </div>
            <div class="line">
              <label class="required">表示順</label>
              <div class="ui input tiny">
                <input type="number" min="1" [(ngModel)]="af_contact_edit.display_num">
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeAfContactEdit()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
