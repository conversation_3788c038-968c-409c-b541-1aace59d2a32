import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { HuhoApproveComponent } from './huho-approve.component';

describe('HuhoApproveComponent', () => {
  let component: HuhoApproveComponent;
  let fixture: ComponentFixture<HuhoApproveComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ HuhoApproveComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HuhoApproveComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
