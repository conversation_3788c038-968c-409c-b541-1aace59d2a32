import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SokepolicyComponent } from './sokepolicy.component';

describe('SokepolicyComponent', () => {
  let component: SokepolicyComponent;
  let fixture: ComponentFixture<SokepolicyComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SokepolicyComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SokepolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
