from rest_framework import serializers

from service_reception_terms.models import ServiceReceptionTerm
from utils.serializer_mixins import AddUserIdMixin


class ServiceReceptionTermSerializer(AddUserIdMixin, serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)

    class Meta:
        model = ServiceReceptionTerm
        fields = '__all__'
        extra_kwargs = {'department': {'required': False}}
        read_only_fields = ['create_user_id', 'created_at', 'update_user_id', 'updated_at']
