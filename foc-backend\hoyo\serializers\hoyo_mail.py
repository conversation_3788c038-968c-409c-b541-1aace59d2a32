from typing import Dict, List

from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from drf_extra_fields.relations import PresentablePrimaryKeyRelatedField
from rest_framework import serializers

from hoyo.models import Hoyo, HoyoMail, HoyoMailTarget
from hoyo.serializers import HoyoSerializer
from seko.models import Seko
from seko.serializers import SekoScheduleSerializer, SekoSerializer
from staffs.serializers import StaffSerializer
from utils.sms import send_seko_template_sms


class SekoForHoyoMailSerializer(SekoSerializer):
    schedules = SekoScheduleSerializer(many=True)


class HoyoMailTargetSerializerBase(serializers.ModelSerializer):
    class Meta:
        model = HoyoMailTarget
        read_only_fields = ['created_at', 'updated_at']


class HoyoMailTargetByHoyoMailSerializer(HoyoMailTargetSerializerBase):
    seko = SekoForHoyoMailSerializer(required=False)

    class Meta(HoyoMailTargetSerializerBase.Meta):
        exclude = ['hoyo_mail']


class HoyoMailSerializerBase(serializers.ModelSerializer):
    staff = StaffSerializer(required=False)

    class Meta:
        model = HoyoMail
        read_only_fields = ['staff', 'targets', 'created_at', 'updated_at']


class HoyoMailSerializer(HoyoMailSerializerBase):
    hoyo = PresentablePrimaryKeyRelatedField(
        queryset=Hoyo.objects.filter(Q(del_flg=False)),
        presentation_serializer=HoyoSerializer,
        required=False,
        allow_null=True,
    )
    targets = HoyoMailTargetByHoyoMailSerializer(many=True, required=False)
    target_seko_list = serializers.PrimaryKeyRelatedField(
        queryset=Seko.objects.filter(Q(del_flg=False)), many=True, write_only=True, required=False
    )

    class Meta(HoyoMailSerializerBase.Meta):
        fields = '__all__'
        extra_kwargs = {'select_ts': {'required': False, 'allow_null': True}}

    def validate_target_seko_list(self, value: List[Seko]):
        seko_errors: List = []
        for seko in value:
            if not hasattr(seko.seko_company, 'sms_account'):
                seko_errors.append(f'Seko ID={seko.pk}: SMS Account info not found.')
            if not hasattr(seko, 'moshu'):
                seko_errors.append(f'Seko ID={seko.pk}: Moshu info not found.')
            if not hasattr(seko, 'seko_contact'):
                seko_errors.append(f'Seko ID={seko.pk}: Seko contact info not found.')
            elif not seko.seko_contact.mobile_num:
                seko_errors.append(f'Seko ID={seko.pk}: no mobile phone number.')
        if seko_errors:
            raise serializers.ValidationError(seko_errors)
        return value

    @transaction.atomic
    def create(self, validated_data: Dict) -> HoyoMail:
        target_seko_list = validated_data.pop('target_seko_list', [])
        for key in ['select_ts', 'send_ts']:
            if key not in validated_data or validated_data[key] is None:
                validated_data[key] = timezone.localtime()

        validated_data['staff'] = self.context['staff']
        instance = super().create(validated_data)
        for seko in target_seko_list:
            if send_seko_template_sms(seko=seko, message_template=validated_data['content']):
                HoyoMailTarget.objects.create(hoyo_mail=instance, seko=seko)

        return instance
