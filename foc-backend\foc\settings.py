"""
Django settings for foc project.

Generated by 'django-admin startproject' using Django 3.1.1.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

from pathlib import Path

import environ
from django.utils.timezone import timedelta

import utils.jwt_keys as jwt_keys

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

env = environ.Env()
environ.Env.read_env(str(BASE_DIR / '.env'))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env.str('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env.bool('DEBUG', default=True)

ALLOWED_HOSTS = env.list('ALLOWED_HOSTS', default=[])

ENABLE_SILK = env.bool('ENABLE_SILK', default=False)
if ENABLE_SILK:
    SILKY_META = True

# Application definition

INSTALLED_APPS = [
    'whitenoise.runserver_nostatic',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',
    'rest_framework',
    'corsheaders',
    'django_filters',
    'mptt',
    'sequences.apps.SequencesConfig',
]
if ENABLE_SILK:
    INSTALLED_APPS.append('silk')
PROJECT_APPS = [
    'masters.apps.MastersConfig',
    'bases.apps.BasesConfig',
    'staffs.apps.StaffsConfig',
    'seko.apps.SekoConfig',
    'suppliers.apps.SuppliersConfig',
    'items.apps.ItemsConfig',
    'fuho_samples.apps.FuhoSamplesConfig',
    'chobun_daishi.apps.ChobunDaishiConfig',
    'service_reception_terms.apps.ServiceReceptionTermsConfig',
    'orders.apps.OrdersConfig',
    'henrei.apps.HenreiConfig',
    'inquiry.apps.InquiryConfig',
    'invoices.apps.InvoicesConfig',
    'hoyo.apps.HoyoConfig',
    'after_follow.apps.AfterFollowConfig',
    'event_mails.apps.EventMailsConfig',
    'faqs.apps.FaqsConfig',
    'advertises.apps.AdvertisesConfig',
]
INSTALLED_APPS.extend(PROJECT_APPS)

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'corsheaders.middleware.CorsMiddleware',
]
if ENABLE_SILK:
    MIDDLEWARE.insert(0, 'silk.middleware.SilkyMiddleware')

ROOT_URLCONF = 'foc.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'foc.wsgi.application'

NUMBER_GROUPING = 3

# Database
# https://docs.djangoproject.com/en/3.1/ref/settings/#databases

DATABASES = {'default': dict(env.db(default='sqlite:///db.sqlite3'), ATOMIC_REQUEST=True)}

AUTHENTICATION_BACKENDS = [
    'staffs.auth_backends.CombinationModelBackend',
    'seko.auth_backends.MoshuAuthBackend',
]
AUTH_USER_MODEL = 'staffs.Staff'
SILENCED_SYSTEM_CHECKS = ['auth.W004']


# Password validation
# https://docs.djangoproject.com/en/3.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

LANGUAGE_CODE = 'ja'

TIME_ZONE = 'Asia/Tokyo'

USE_I18N = True

USE_L10N = True

USE_TZ = True

LOCALE_PATHS = (Path(BASE_DIR) / 'locales',)

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.1/howto/static-files/

STATIC_URL = 'static/'
static_root = env.str('STATIC_ROOT', default=None)
if static_root:
    STATIC_ROOT = static_root
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'


MEDIA_URL = env.str('MEDIA_URL', default='media/')
MEDIA_ROOT = env.str('MEDIA_ROOT', default=None)

# メール送信
if env.bool('EMAIL_ON_CONSOLE', default=False):
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
elif env.bool('EMAIL_VIA_SENDGRID', default=False):
    EMAIL_BACKEND = 'sendgrid_backend.SendgridBackend'
    SENDGRID_API_KEY = env.str('SENDGRID_API_KEY')
    # ↓DEBUG=Trueの状態でSENDGRIDからメールを送信したい場合にFalseにする
    SENDGRID_SANDBOX_MODE_IN_DEBUG = env.bool('SENDGRID_SANDBOX_MODE_IN_DEBUG', default=True)
    SENDGRID_TRACK_EMAIL_OPENS = env.bool('SENDGRID_TRACK_EMAIL_OPENS', default=False)
    SENDGRID_TRACK_CLICKS_HTML = env.bool('SENDGRID_TRACK_CLICKS_HTML', default=False)
    SENDGRID_TRACK_CLICKS_PLAIN = env.bool('SENDGRID_TRACK_CLICKS_PLAIN', default=False)
else:
    EMAIL_HOST = env.str('EMAIL_HOST', default='localhost')
    EMAIL_PORT = env.int('EMAIL_PORT', default=25)
EMAIL_FROM = env.str('EMAIL_FROM', default='<EMAIL>')
ORDER_EMAIL_BCC = env.list('ORDER_EMAIL_BCC', cast=str, default=[])
SUPPORT_EMAIL_BCC = env.list('SUPPORT_EMAIL_BCC', cast=str, default=[])
INQUIRY_EMAIL_BCC = env.list('INQUIRY_EMAIL_BCC', cast=str, default=[])
INVOICE_EMAIL_BCC = env.list('INVOICE_EMAIL_BCC', cast=str, default=[])

# SMS送信
SMS_REQUEST_URL = env.str('SMS_REQUEST_URL', default='https://qpd-api.aossms.com/p5/api/mt.json')

# django-cors-headers
CORS_ORIGIN_ALLOW_ALL = True
FRONTEND_HOSTNAME = env.str('FRONTEND_HOSTNAME', default='')

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': ('utils.authentication.ClassableJWTAuthentication',),
}

# Load key pair for JWT
private_key_filename = env.str('JWT_PRIVATE_KEY_FILE', default=None)
private_key_path = Path(BASE_DIR) / private_key_filename if private_key_filename else None
privkey, pubkey = jwt_keys.fetch_keysets_bytes(private_key_path)

SIMPLE_JWT = {
    'AUTH_HEADER_TYPES': ('JWT', 'Bearer'),
    'ALGORITHM': 'RS256',
    'SIGNING_KEY': privkey,
    'VERIFYING_KEY': pubkey,
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=env.int('JWT_EXPIRATION_MINUTES', 5)),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=env.int('JWT_REFRESH_EXPIRATION_DAYS', 1)),
    'ISSUER': env.str('JWT_ISSUER', None),
    'AUDIENCE': env.str('JWT_AUDIENCE', None),
}

# Adviced by python manage.py check --deploy
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
X_FRAME_OPTIONS = 'DENY'

# Behind Traefik
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
USE_X_FORWARDED_HOST = True
USE_X_FORWARDED_PORT = True
FORCE_SCRIPT_NAME = env.str('FORCE_SCRIPT_NAME', '')
if env.bool('USE_TLS', default=False):
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_SSL_REDIRECT = True
    SECURE_HSTS_PRELOAD = True

ENABLE_EPSILON_PAYMENT = env.bool('ENABLE_EPSILON_PAYMENT', False)
EPSILON_REGISTER_PAYMENT_URL = env.str(
    'EPSILON_REGISTER_PAYMENT_URL', 'https://beta.epsilon.jp/cgi-bin/order/direct_card_payment.cgi'
)
EPSILON_REGISTER_PAYMENT_URL_TEST = env.str(
    'EPSILON_REGISTER_PAYMENT_URL_TEST',
    'https://beta.epsilon.jp/cgi-bin/order/direct_card_payment.cgi',
)
