# FOCシステム セキュリティ脆弱性対策案

## 1. 問題の概要

### 1.1 現在の問題

FOCシステムでは、JWTトークンに会社コード情報が含まれておらず、フロントエンドのセッションストレージから会社コード情報を取得しています。これにより、以下の重大なセキュリティ脆弱性が存在します。

#### **主要な脆弱性**
1. **会社コード改ざん攻撃**: リクエストパラメータの会社コードを他社に変更
2. **IDベースAPI攻撃**: `/seko/{id}/`のようなAPIで他社のIDを指定
3. **セッションストレージ改ざん**: ブラウザ開発者ツールでセッション情報を改ざん
4. **権限昇格攻撃**: テナント会社がシステム管理者権限に昇格

### 1.2 攻撃対象API

#### **直接パラメータ攻撃対象**
- `GET /api/seko/?seko_company=1009` - 施行一覧API
- `GET /api/staffs/?company_id=1009` - スタッフ一覧API
- `GET /api/bases/all/` - 拠点一覧API（権限フィルタリング不備）

#### **IDベース攻撃対象**
- `GET /api/seko/{id}/` - 施行詳細API（会社コード情報含む）
- `GET /api/staffs/{id}/` - スタッフ詳細API（company_code含む）
- `GET /api/bases/{id}/` - 拠点詳細API（company_code含む）
- `GET /api/moshu/{id}/` - 喪主詳細API（関連施行の会社情報含む）
- `GET /api/suppliers/{id}/` - 発注先詳細API（company_id含む）

## 2. 被害例

### 2.1 会社コード改ざん攻撃

#### **攻撃手順**
```javascript
// 1. テナント会社A（company_code=1001）で正常ログイン
// 2. 他社データを狙った攻撃リクエスト
GET /api/seko/?seko_company=1009
Authorization: Bearer <valid_jwt_token>
```

#### **被害内容**
```json
{
  "results": [
    {
      "id": 5678,
      "seko_company": {"company_code": "1009", "base_name": "B社本社"},
      "moshu_name": "山田花子",
      "moshu_tel": "090-1234-5678",
      "total_amount": 1500000,
      "payment_status": "paid"
    }
  ]
}
```
**→ 他社の個人情報・売上情報が大量漏洩**

### 2.2 IDベース攻撃

#### **攻撃手順**
```javascript
// 1. テナント会社A（company_code=1001）で正常ログイン
// 2. 他社の施行IDを推測して攻撃
GET /api/seko/9999/  // 他社の施行データ
Authorization: Bearer <valid_jwt_token>
```

#### **被害内容**
```json
{
  "id": 9999,
  "seko_company": {"company_code": "1009"},
  "moshu": {
    "name": "田中太郎",
    "tel": "03-1234-5678",
    "mail_address": "<EMAIL>"
  },
  "kojin": [{"name": "田中一郎", "age": 75}]
}
```
**→ 他社の施行詳細・個人情報が漏洩**

## 3. 対策案

### 3.1 認証レベルでの包括的対策

**基本方針**: 認証段階で全ての攻撃を阻止し、ビューレベルでの実装ミスも防ぐ

#### **対策の特徴**
1. **二重チェック機構**: パラメータ攻撃 + IDベース攻撃の両方を阻止
2. **最小限の改修**: 主要ファイル2つのみの修正
3. **実装ミス耐性**: ビューの実装に依存しない根本的対策
4. **運用継続性**: 既存システムへの影響最小化

### 3.2 改修対象ファイル

1. **`utils/authentication.py`** - JWT認証強化
2. **`bases/views.py`** - BaseFullListの権限フィルタリング

## 4. 実装詳細

### 4.1 JWT認証強化（utils/authentication.py）

```python
# utils/authentication.py

# 【新規追加】インポート文の追加
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework.request import Request  # 【新規追加】
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import AuthenticationFailed, InvalidToken
from rest_framework_simplejwt.settings import api_settings
from rest_framework_simplejwt.state import User
from rest_framework_simplejwt.tokens import RefreshToken

from bases.models import Base  # 【新規追加】
from seko.models import Moshu, Seko  # 【新規追加】Sekoを追加
from staffs.models import Staff  # 【新規追加】
from suppliers.models import Supplier  # 【新規追加】


class ClassableRefreshToken(RefreshToken):
    # 【既存コード - 変更なし】
    @classmethod
    def for_user(cls, user):
        if isinstance(user, Moshu):
            setattr(user, 'id', user.seko.pk)
        token = super().for_user(user)
        token['user_class'] = user._meta.object_name
        if isinstance(user, Moshu):
            delattr(user, 'id')
        return token


class ClassableJWTAuthentication(JWTAuthentication):
    # 【新規追加】authenticateメソッド全体
    def authenticate(self, request: Request):
        """
        リクエストを認証し、(user, token) のタプルを返す。
        親クラスの認証処理に加え、会社コードのアクセス権限チェックを行う。
        """
        auth_tuple = super().authenticate(request)

        if auth_tuple is None:
            return None

        user, validated_token = auth_tuple

        # 会社コードの検証（パラメータベース + IDベース）
        self.validate_company_access(request, user)

        return user, validated_token

    # 【既存メソッド - 変更なし】
    def get_user(self, validated_token):
        try:
            user_id = validated_token[api_settings.USER_ID_CLAIM]
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user identification'))

        try:
            user_class: str = validated_token['user_class']
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user class'))

        try:
            if user_class == 'Moshu':
                user = Moshu.objects.get(Q(pk=user_id) & Q(seko__del_flg=False))
            else:
                user = User.objects.get(**{api_settings.USER_ID_FIELD: user_id})
        except (Moshu.DoesNotExist, User.DoesNotExist):
            raise AuthenticationFailed(_('User not found'), code='user_not_found')

        if not user.is_active:
            raise AuthenticationFailed(_('User is inactive'), code='user_inactive')

        return user

    # 【新規追加】validate_company_accessメソッド全体
    def validate_company_access(self, request: Request, user):
        """
        【核心機能】リクエストパラメータ/ボディの会社コードと認証ユーザーの会社コードを検証

        1. 直接的な会社コードパラメータのチェック
        2. IDベースAPIでのリソース所有者チェック

        テナントユーザーが他社のデータにアクセスしようとする不正なリクエストを
        認証段階で完全にブロックする。
        """
        # ユーザーの会社情報と権限を取得
        if isinstance(user, Moshu):
            user_company_code = user.seko.seko_company.company_code
            is_admin = user.seko.seko_company.base_type == Base.OrgType.ADMIN
        else:  # Staff
            user_company_code = user.company_code
            is_admin = user.base.base_type == Base.OrgType.ADMIN

        # 【重要】システム管理者はチェックをスキップ
        if is_admin:
            return

        # 1. 直接的な会社コードパラメータのチェック
        self._validate_direct_company_params(request, user_company_code)

        # 2. IDベースAPIでのリソース所有者チェック
        self._validate_resource_ownership(request, user_company_code)

    # 【新規追加】_validate_direct_company_paramsメソッド全体
    def _validate_direct_company_params(self, request: Request, user_company_code: str):
        """直接的な会社コードパラメータの検証"""
        def get_requested_company_code(req: Request) -> str | None:
            """リクエストから会社コードを取得する"""
            company_params = ['seko_company', 'company_id', 'company', 'company_code']

            # GETクエリパラメータをチェック
            for param in company_params:
                value = req.query_params.get(param)
                if value:
                    return value

            # POST/PUT/PATCHリクエストボディをチェック
            if hasattr(req, 'data') and req.data and isinstance(req.data, dict):
                for param in company_params:
                    value = req.data.get(param)
                    if value:
                        return value
            return None

        requested_company_code = get_requested_company_code(request)

        if requested_company_code and str(requested_company_code) != str(user_company_code):
            raise AuthenticationFailed(
                _('Company code in request does not match authenticated user.'),
                code='company_code_mismatch',
            )

    # 【新規追加】_validate_resource_ownershipメソッド全体
    def _validate_resource_ownership(self, request: Request, user_company_code: str):
        """IDベースAPIでのリソース所有者チェック"""
        path = request.path
        method = request.method

        # GETリクエストのみチェック（他社データ参照を防ぐため）
        if method != 'GET':
            return

        # URLパターンからリソースIDを抽出
        resource_id = self._extract_resource_id(path)
        if not resource_id:
            return

        # リソース種別に応じた所有者チェック
        if '/seko/' in path:
            self._check_seko_ownership(resource_id, user_company_code)
        elif '/staffs/' in path:
            self._check_staff_ownership(resource_id, user_company_code)
        elif '/bases/' in path:
            self._check_base_ownership(resource_id, user_company_code)
        elif '/moshu/' in path:
            self._check_moshu_ownership(resource_id, user_company_code)
        elif '/suppliers/' in path:
            self._check_supplier_ownership(resource_id, user_company_code)

    # 【新規追加】_extract_resource_idメソッド全体
    def _extract_resource_id(self, path: str) -> int | None:
        """URLパスからリソースIDを抽出"""
        import re
        # /api/seko/123/ や /seko/123/ のようなパターンからIDを抽出
        match = re.search(r'/(\w+)/(\d+)/', path)
        if match:
            return int(match.group(2))
        return None

    # 【新規追加】_check_seko_ownershipメソッド全体
    def _check_seko_ownership(self, seko_id: int, user_company_code: str):
        """施行データの所有者チェック"""
        try:
            seko = Seko.objects.select_related('seko_company').get(id=seko_id, del_flg=False)
            if seko.seko_company.company_code != user_company_code:
                raise AuthenticationFailed(
                    _('Access denied: Seko belongs to different company.'),
                    code='resource_access_denied',
                )
        except Seko.DoesNotExist:
            # リソースが存在しない場合は404で処理されるため、ここでは何もしない
            pass

    # 【新規追加】_check_staff_ownershipメソッド全体
    def _check_staff_ownership(self, staff_id: int, user_company_code: str):
        """スタッフデータの所有者チェック"""
        try:
            staff = Staff.objects.get(id=staff_id, del_flg=False)
            if staff.company_code != user_company_code:
                raise AuthenticationFailed(
                    _('Access denied: Staff belongs to different company.'),
                    code='resource_access_denied',
                )
        except Staff.DoesNotExist:
            pass

    # 【新規追加】_check_base_ownershipメソッド全体
    def _check_base_ownership(self, base_id: int, user_company_code: str):
        """拠点データの所有者チェック"""
        try:
            base = Base.objects.get(id=base_id, del_flg=False)
            if base.company_code != user_company_code:
                raise AuthenticationFailed(
                    _('Access denied: Base belongs to different company.'),
                    code='resource_access_denied',
                )
        except Base.DoesNotExist:
            pass

    # 【新規追加】_check_moshu_ownershipメソッド全体
    def _check_moshu_ownership(self, moshu_id: int, user_company_code: str):
        """喪主データの所有者チェック"""
        try:
            moshu = Moshu.objects.select_related('seko__seko_company').get(id=moshu_id)
            if moshu.seko.seko_company.company_code != user_company_code:
                raise AuthenticationFailed(
                    _('Access denied: Moshu belongs to different company.'),
                    code='resource_access_denied',
                )
        except Moshu.DoesNotExist:
            pass

    # 【新規追加】_check_supplier_ownershipメソッド全体
    def _check_supplier_ownership(self, supplier_id: int, user_company_code: str):
        """発注先データの所有者チェック"""
        try:
            supplier = Supplier.objects.get(id=supplier_id, del_flg=False)
            # Supplierモデルのcompany_idフィールドをチェック
            if hasattr(supplier, 'company_id') and supplier.company_id != user_company_code:
                raise AuthenticationFailed(
                    _('Access denied: Supplier belongs to different company.'),
                    code='resource_access_denied',
                )
        except Supplier.DoesNotExist:
            pass
```

### 4.2 BaseFullListの権限フィルタリング（bases/views.py）

```python
# bases/views.py

# 【既存インポート - 変更なし】
from typing import Dict, Optional
from urllib.parse import urljoin

from django.conf import settings
from django.core.mail import EmailMessage
from django.db.models import Prefetch, Q
from django.template.loader import get_template
from django.utils.translation import gettext_lazy as _
from rest_framework import generics, mixins, views
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from rest_framework.response import Response

from bases.models import Base, FocFee, SmsAccount, Tokusho
from bases.serializers import (
    BaseDownwardSerializer,
    BaseInquirySerializer,
    BaseSerializer,
    FocFeeSerializer,
    SmsAccountSerializer,
    TokushoSerializer,
)
from masters.models import Service
from utils.requests import request_origin
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class BaseFullList(generics.ListAPIView):
    """
    get: 全拠点の一覧を階層構造で取得します
    【改修】権限に応じてフィルタリングされます
    """

    serializer_class = BaseDownwardSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self, queryset=None):
        # 【既存コード】
        base_queryset = Base.objects.filter(Q(del_flg=False))

        # 【新規追加】権限に応じたフィルタリング
        user = self.request.user

        # ユーザーの会社情報と権限を取得
        if hasattr(user, 'seko') and user.seko:  # Moshu
            user_company_code = user.seko.seko_company.company_code
            user_base_type = user.seko.seko_company.base_type
        else:  # Staff
            user_company_code = user.company_code
            user_base_type = user.base.base_type if hasattr(user, 'base') else None

        # システム管理会社の場合：全ての会社情報を返す
        if user_base_type == Base.OrgType.ADMIN:
            filtered_queryset = base_queryset
        else:
            # 【重要】テナント会社の場合：自社の情報のみを返す
            filtered_queryset = base_queryset.filter(
                company_code=user_company_code
            )

        # 【既存コード - 変更なし】
        return (
            filtered_queryset
            .prefetch_related(
                'tokusho',
                'focfee',
                'sms_account',
                Prefetch('services', queryset=Service.objects.order_by('id')),
            )
            .get_cached_trees()
        )


# 【既存クラス - 変更なし】
class BaseList(AddContextMixin, generics.CreateAPIView):
    serializer_class = BaseSerializer
    permission_classes = [IsAuthenticated]


class BaseDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 拠点情報を取得します
    put: 拠点情報を更新します
    patch: 拠点情報を更新します
    delete: 拠点情報を(論理)削除します
    """

    queryset = Base.objects.filter(Q(del_flg=False)).prefetch_related(
        'tokusho', 'focfee', 'sms_account'
    )
    serializer_class = BaseDownwardSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return BaseSerializer
        return self.serializer_class
```

## 5. 対策効果

### 5.1 攻撃阻止の流れ

#### **パラメータ攻撃の阻止**
```python
# 攻撃リクエスト: GET /api/seko/?seko_company=1009
# テナント会社A（company_code=1001）のユーザー

# ClassableJWTAuthentication.validate_company_access()の処理
requested_company_code = "1009"  # 攻撃者が指定した他社コード
user_company_code = "1001"       # 実際のユーザーの会社コード

# 会社コード不一致を検出
if "1009" != "1001":  # True
    raise AuthenticationFailed('Company code mismatch')

# 結果: 401 Unauthorized → 攻撃完全阻止 ✅
```

#### **IDベース攻撃の阻止**
```python
# 攻撃リクエスト: GET /api/seko/9999/
# テナント会社A（company_code=1001）のユーザー

# 1. URLから施行ID=9999を抽出
resource_id = 9999

# 2. データベースから施行データを取得
seko = Seko.objects.get(id=9999)
seko_company_code = seko.seko_company.company_code  # "1009"

# 3. 所有者不一致を検出
if "1009" != "1001":  # True
    raise AuthenticationFailed('Access denied: Seko belongs to different company')

# 結果: 401 Unauthorized → 攻撃完全阻止 ✅
```

### 5.2 対策範囲

#### **✅ 阻止される攻撃**
1. **直接的なパラメータ改ざん**
   - `GET /api/seko/?seko_company=1009`
   - `GET /api/staffs/?company_id=1009`
   - `POST /api/seko/ {"seko_company": "1009"}`

2. **IDベースAPI攻撃**
   - `GET /api/seko/9999/` - 他社の施行データ
   - `GET /api/staffs/8888/` - 他社のスタッフデータ
   - `GET /api/bases/7777/` - 他社の拠点データ

3. **拠点一覧の情報漏洩**
   - テナント会社は自社拠点のみ表示
   - システム管理者は全社拠点を表示

#### **✅ 管理者の正常動作**
```python
# システム管理者（base_type=ADMIN）の場合
if is_admin:
    return  # 全チェックをスキップ

# 管理者は他社データアクセスが正常に動作
GET /api/seko/?seko_company=1009  # ✅ 正常動作
GET /api/seko/9999/               # ✅ 正常動作
```

## 6. 実装手順

### 6.1 実装ステップ

#### Step 1: authentication.pyの修正
1. `validate_company_access`メソッドの追加
2. `_validate_direct_company_params`メソッドの実装
3. `_validate_resource_ownership`メソッドの実装
4. 各リソース所有者チェックメソッドの実装
5. `authenticate`メソッドでの呼び出し追加

#### Step 2: bases/views.pyの修正
1. `BaseFullList.get_queryset`メソッドに権限フィルタリング追加

#### Step 3: テスト
1. **直接パラメータ攻撃テスト**
   ```bash
   curl -H "Authorization: Bearer <token>" \
        "http://localhost:8000/api/seko/?seko_company=1009"
   # 期待結果: 401 Unauthorized
   ```

2. **IDベース攻撃テスト**
   ```bash
   curl -H "Authorization: Bearer <token>" \
        "http://localhost:8000/api/seko/9999/"
   # 期待結果: 401 Unauthorized
   ```

### 6.2 フロントエンド対応
**不要** - バックエンドのみの修正で完結

## 7. 期待される効果

### 7.1 包括的なセキュリティ効果

#### **✅ 全API自動保護**
- **パラメータベースAPI**: 会社コードパラメータの自動検証
- **IDベースAPI**: リソース所有者の自動チェック
- **拠点一覧API**: 権限ベースの適切なフィルタリング
- 個別API改修が完全に不要

#### **✅ 実装ミス耐性**
- ビューの実装に依存しない根本的対策
- 開発者のミスによるデータ漏洩を完全防止
- 新規API追加時も自動的に保護

#### **✅ 運用継続性**
- 既存ユーザーへの影響なし
- 再ログイン不要
- フロントエンド変更不要

#### **✅ 攻撃パターン網羅**
- **直接攻撃**: `?seko_company=1009`
- **間接攻撃**: `/seko/9999/`
- **権限昇格**: base_listの適切なフィルタリング
- **総当たり攻撃**: ID推測による他社データアクセス

## 8. 結論

### 8.1 最適解としての評価

この**認証レベルでの包括的な会社コード検証**は、FOCシステムのセキュリティ対策として**最も効率的で効果的**な解決策です。

#### **核心的な利点**
1. **最小限の工数で最大の効果**
2. **全API自動保護**による包括的なセキュリティ
3. **攻撃パターン完全網羅**（パラメータ + ID攻撃）
4. **実装ミス耐性**による長期的な安全性確保
5. **運用継続性**による既存システムへの影響最小化

#### **技術的な優位性**
- **認証段階での阻止**により、不正リクエストがビューに到達する前に完全に排除
- **二重チェック機構**（パラメータ + リソース所有者）による確実な防御
- **JWTトークンの信頼性**を活用した確実な権限チェック
- **システム管理者の正常動作**を適切に保護

この対策により、FOCシステムのマルチテナント環境における**データ分離とセキュリティが完全に確保**され、**あらゆる攻撃パターンに対する包括的な防御**が実現されます。
