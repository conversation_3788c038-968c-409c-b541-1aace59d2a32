.container {
  .inner {
    .contents {
      .ui.segment {
        height: calc(100% - 120px);
        display: flex;
        flex-wrap: wrap;
        .item-box {
          .image-box {
            position: relative;
            width: 200px;
            height: 300px;
            background: #fff;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
            opacity: .8;
            &:hover {
              box-shadow: 0 5px 20px rgba(0,0,0,.3);
              --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
              -webkit-transform: translateY(-2px);
              transform: translateY(-2px);
              opacity: 1;
            }
            &.selected {
              box-shadow: 0 5px 20px rgba(0,0,0,.3);
              --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
              -webkit-transform: translateY(-2px);
              transform: translateY(-2px);
              opacity: 1;
            }
            img {
              width: 200px;
              height: 282px;
              border-radius: 5px;
            }
            .ui.checkbox {
              position: absolute;
              bottom: 5px;
              left: 90px;;
            }
          }
          .name {
            text-align: center;
          }
        }
      }
    }
  }
}
