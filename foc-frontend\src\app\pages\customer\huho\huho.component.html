
<div class="container">
  <div class="inner" *ngIf="!is_loading && (!seko_id || !seko_data)">
    <!--ログイン-->
    <div class="contents with-background-image top">
      <div class="fuho_num">
        <div class="label">ご葬儀番号：</div>
        <div class="ui input company-id tiny">
          <input #companyIdEm type="tel" oninput="value = value.replace(/[^0-9]+/i,'');" [(ngModel)]="company_id">
        </div>
        <div>-</div>
        <div class="ui input seko-id tiny">
          <input #sekoyIdEm type="tel" oninput="value = value.replace(/[^0-9]+/i,'');" [(ngModel)]="seko_id">
        </div>
      </div>
      <div class="box01_02">
        <button class="ui button teal" (click)="getSeko(companyIdEm, sekoyIdEm)">
          次へ
        </button>
      </div>
      <div class="box01_01" *ngIf="err_msg">
        <div class="error_msg">{{err_msg}}</div>
      </div>
    </div>
  </div>
  <div class="inner" *ngIf="seko_id && seko_data">
    <!--訃報-->
    <div class="contents with-background-image">
      <h2>訃報</h2>
      <div class="box01_01">
        <ng-container *ngFor="let kojin of seko_data.kojin">
          <div class="kojin_info"><div class="name">故　{{kojin.name}}　儀</div>　<div class="age">{{kojin.age_kbn}} {{kojin.age}} 歳</div></div>
          <p class="death_date">{{utils.getYearDisplay(kojin.death_date, wareki_list, !seko_data.seireki_display_flg)}}{{kojin.death_date|date:"M月d日"}}（{{utils.getWeekName(kojin.death_date)}}）逝去いたしました</p>
        </ng-container>
      </div>
      <div class="box01_02">{{seko_data.fuho_sentence}}</div>
    </div>

    <!--記-->
    <div class="contents with-background-image">
      <a href="/howtoshare" class="howtoshare" target="_blank">
        WEB訃報共有方法
      </a>
      <h2 class="adj-position">記</h2>
      <div class="box02">
        <table>
          <tr *ngIf="seko_id===1126117">
            <td scope="col" colspan="2" style="color:red;line-height:1.5;font-size:1rem;padding-left:0;">
              ※お知らせ<br>
              一部の機種でお別れ会の日時が間違って表示される事象が発生しております。<br>
              正しい日時は12月05日（日）　11:00～13:00でございます。<br>
              ご迷惑をおかけしまして誠に申し訳ございません。<br>
              ご確認くださいます様、お願い申し上げます。<br>
              お問い合わせは、こころサポーセンターまでお願いいたします。
            </td>
          </tr>
          <tr>
            <th scope="col" colspan="2">日程</th>
          </tr>
          <tr *ngFor="let schedule of seko_data.schedules">
            <td>・{{schedule.schedule_name}}</td>
            <td>
              <div>
                <div class="schedule">
                  <P>{{schedule.schedule_date|date:"M月d日"}}（{{utils.getWeekName(schedule.schedule_date)}}）</P>
                  <P>{{schedule.display_begin_time}} 〜 {{schedule.display_end_time}} </P>
                </div>
                <P>{{schedule.hall_name}}</P>
              </div>
            </td>
          </tr>
          <tr *ngIf="seko_data.moshu_display_place===0">
            <th scope="row">喪主</th>
            <td class="space_bottom">{{seko_data.moshu.name}}（{{seko_data.moshu.kojin_moshu_relationship}}）</td>
          </tr>
          <tr *ngIf="seko_data.free1_label || seko_data.free1_data">
            <th scope="row">{{seko_data.free1_label}}</th>
            <td class="space_bottom">{{seko_data.free1_data}}</td>
          </tr>
          <tr *ngIf="seko_data.moshu_display_place===1">
            <th scope="row">喪主</th>
            <td class="space_bottom">{{seko_data.moshu.name}}（{{seko_data.moshu.kojin_moshu_relationship}}）</td>
          </tr>
          <tr *ngIf="seko_data.free2_label || seko_data.free2_data">
            <th scope="row">{{seko_data.free2_label}}</th>
            <td class="space_bottom">{{seko_data.free2_data}}</td>
          </tr>
          <tr *ngIf="seko_data.moshu_display_place===2">
            <th scope="row">喪主</th>
            <td class="space_bottom">{{seko_data.moshu.name}}（{{seko_data.moshu.kojin_moshu_relationship}}）</td>
          </tr>
          <tr *ngIf="seko_data.free3_label || seko_data.free3_data">
            <th scope="row">{{seko_data.free3_label}}</th>
            <td class="space_bottom">{{seko_data.free3_data}}</td>
          </tr>
          <tr *ngIf="seko_data.moshu_display_place===3">
            <th scope="row">喪主</th>
            <td class="space_bottom">{{seko_data.moshu.name}}（{{seko_data.moshu.kojin_moshu_relationship}}）</td>
          </tr>
          <tr *ngIf="seko_data.free4_label || seko_data.free4_data">
            <th scope="row">{{seko_data.free4_label}}</th>
            <td class="space_bottom">{{seko_data.free4_data}}</td>
          </tr>
          <tr *ngIf="seko_data.moshu_display_place===4">
            <th scope="row">喪主</th>
            <td class="space_bottom">{{seko_data.moshu.name}}（{{seko_data.moshu.kojin_moshu_relationship}}）</td>
          </tr>
          <tr>
            <th scope="row">形式</th>
            <td class="space_bottom">{{seko_data.seko_style_name}}</td>
          </tr>
          <tr class="box02_01">
            <th scope="col" colspan="2">式場</th>
          </tr>
          <tr *ngIf="place">
            <td colspan="2">・{{place.hall_name}}</td>
          </tr>
          <tr *ngIf="place">
            <td colspan="2">
              <div class="place">
                <p>{{place.hall_address}}</p>
                <p>TEL：{{place.hall_tel}}</p>
                <img class="map" *ngIf="place.hall?.company_map_file" src="{{place.hall.company_map_file}}">
                <p class="button map space_bottom">
                  <a href="https://www.google.com/maps/search/?api=1&query={{place.hall_address}}" target="_blank">
                    <pre>Google Mapで開く   ></pre>
                  </a>
                </p>
              </div>
            </td>
          </tr>
          <tr class="box02_01">
            <th scope="col" colspan="2">葬儀に関するお問い合わせ</th>
          </tr>
          <tr>
            <td colspan="2">・{{seko_data.fuho_contact_name}}</td>
          </tr>
          <tr>
            <td colspan="2">
              <div class="place">
                <p>TEL：{{seko_data.fuho_contact_tel}}</p>
              </div>
            </td>
          </tr>
          <tr class="box02_01">
            <th scope="col" colspan="2">操作に関するお問い合わせ</th>
          </tr>
          <tr>
            <td colspan="2">・葬儀オンラインサービスこころサポートセンター</td>
          </tr>
          <tr>
            <td colspan="2">
              <div class="place">
                <p>TEL：0120-691-484</p>
              </div>
            </td>
          </tr>
          <tr>
            <td colspan="2">
              <div class="place">
                <p>営業時間：09:00～21:00/年中無休</p>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>

    <!--メモリアルコーナー-->
    <div class="contents" *ngIf="seko_data.albums?.length">
      <h2>メモリアルコーナー</h2>
      <div class="memorial">
        <div class="flexslider" id="flexslider-image">
          <ul class="slides">
            <li *ngFor="let album of seko_data.albums">
              <img src="{{album.file_name}}">
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!--広告-->
    <div class="contents cm" *ngIf="hasCmVideo()">
      <div class="box04">
        <ng-container *ngFor="let video of seko_data.videos">
          <ng-container  *ngIf="!video.live_begin_ts && !isExpired(video.delivery_end_ts)">
            <!--<p>{{video.title}}</p>-->
            <a class="movie" *ngIf="video.youtube_code" target="_blank" [href]="video.youtube_code">
              <iframe
                [src]="video.youtube_url" 
                title="YouTube video player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
              >
              </iframe>
            </a>
          </ng-container>
        </ng-container>
      </div>
    </div>

    <!--動画配信-->
    <div class="contents with-background-image" *ngIf="hasLiveVideo()">
      <h2>動画配信</h2>
      <div class="box04">
        <p>インターネットにて動画配信を行います。</p>
        <p>直接葬儀に参列できない方も一緒に故人を送り出すことができます。</p>
        <a class="movie live" *ngIf="video_live_url" target="_blank" [href]="video_live_url">
        </a>
        <div class="stream">
          <table>
          <ng-container *ngFor="let video of seko_data.videos">
            <ng-container  *ngIf="video.live_begin_ts">
              <tr>
                <th scope="row">{{video.title}}</th>
                <td>
                  <div class="schedule">
                    <P>{{video.display_live_begin_date}}</P>
                    <P>{{video.display_live_begin_time}} 〜　{{video.display_live_end_time}} </P>
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan="2" class="button archive">
                  <div>
                    <p *ngIf="!isExpired(video.delivery_end_ts)"><a target="_blank" [href]="video.youtube_code">動画を視聴する　＞</a></p>
                    <p class="p_small">{{video.display_delivery_end_ts}}まで視聴可能</p>
                  </div>
                </td>
              </tr>
            </ng-container>
          </ng-container>
          </table>
        </div>
      </div>
    </div>
    <!--備考-->
    <div class="contents with-background-image" *ngIf="seko_data.note">
      <h2>備考</h2>
      <div class="box04">
        <p>{{seko_data.note}}</p>
      </div>
    </div>
    <!--お悔やみを送る-->
    <div class="service-menu" *ngIf="seko_data?.services?.length">
      <h2>オンラインでお悔やみを送る</h2>
      <div class="item">
        <ng-container *ngFor="let service of seko_data.services">
          <div [class]="getServiceClass(service.hall_service)" *ngIf="service.hall_service.id!==Const.SERVICE_ID_HENREIHIN">
            <a (click)="goServiceLink(service.hall_service)">
              <p>{{getServiceName(service.hall_service)}}</p>
              <p class="p_small" *ngIf="service?.display_limit_ts&&(!service?.is_expired||service.hall_service.id!==Const.SERVICE_ID_KUMOTSU||!is_kumotsu_have_sogigo_item)">{{service.display_limit_ts}} まで</p>
              <p class="p_small" *ngIf="service?.is_expired&&service.hall_service.id===Const.SERVICE_ID_KUMOTSU&&is_kumotsu_have_sogigo_item">ご葬家様宅へお届けします</p>
            </a>
          </div>
        </ng-container>
      </div>
    </div>
    
	</div>
</div>
