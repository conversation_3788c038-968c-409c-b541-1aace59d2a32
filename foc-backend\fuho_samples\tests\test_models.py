from django.test import TestCase

from fuho_samples.models import FuhoSample
from fuho_samples.tests.factories import FuhoSampleFactory


class FuhoSampleModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.fuho_sample: FuhoSample = FuhoSampleFactory()

    def test_fuho_disable(self) -> None:
        """訃報サンプルを無効化(論理削除)する"""
        self.fuho_sample.disable()
        self.assertTrue(self.fuho_sample.del_flg)
