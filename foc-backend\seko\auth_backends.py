from typing import Optional

from django.contrib.auth.backends import BaseBackend
from django.db.models import Q

from seko.models import Moshu, Seko


class MoshuAuthBackend(BaseBackend):
    def authenticate(self, request, seko_id=None, password=None) -> Optional[Moshu]:
        seko: Seko = Seko.objects.filter(Q(pk=seko_id), Q(del_flg=False)).first()
        if not seko or not hasattr(seko, 'moshu'):
            return None

        if seko.moshu.check_password(password):
            seko.moshu.update_last_login()
            return seko.moshu
        return None

    def get_user(self, user_id):
        try:
            return Moshu.objects.get(Q(pk=user_id) & Q(seko__del_flg=False))
        except Moshu.DoesNotExist:
            return None
