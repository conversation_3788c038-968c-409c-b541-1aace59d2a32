from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.tests.factories import (
    AfActivityDetailFactory,
    AfContactWayFactory,
    AfterFollowFactory,
    AfWishFactory,
    SekoAfFactory,
)
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class SekoAfUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko_af = SekoAfFactory()
        self.af_wish_1 = AfWishFactory(seko_af=self.seko_af)
        self.af_wish_2 = AfWishFactory(seko_af=self.seko_af)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:

        self.new_seko_af_data = SekoAfFactory.build(
            seko=self.seko_af.seko, contact_way=AfContactWayFactory()
        )
        af_wish_data_1 = AfWishFactory.build(
            seko_af=self.new_seko_af_data, af_type=AfterFollowFactory()
        )
        af_wish_data_2 = AfWishFactory.build(
            seko_af=self.new_seko_af_data, af_type=AfterFollowFactory()
        )
        af_wish_data_3 = AfWishFactory.build(
            seko_af=self.new_seko_af_data, af_type=AfterFollowFactory()
        )

        af_wishes: List[Dict] = []
        for af_wish_data in [af_wish_data_1, af_wish_data_2, af_wish_data_3]:
            af_wishes.append(
                {
                    'af_type': af_wish_data.af_type.pk,
                    'answered_flg': af_wish_data.answered_flg,
                    'proposal_status': af_wish_data.proposal_status,
                    'order_status': af_wish_data.order_status,
                    'order_date': af_wish_data.order_date.isoformat(),
                    'order_chance': af_wish_data.order_chance,
                }
            )

        return {
            'seko': self.new_seko_af_data.seko.pk,
            'contact_way': self.new_seko_af_data.contact_way.pk,
            'note': self.new_seko_af_data.note,
            'wishes': af_wishes,
        }

    def test_seko_af_update_succeed(self) -> None:
        """施行AFを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('after_follow:seko_af_detail', kwargs={'pk': self.seko_af.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['seko'], self.new_seko_af_data.seko.pk)
        self.assertEqual(
            record['contact_way']['contact_way'], self.new_seko_af_data.contact_way.contact_way
        )
        self.assertEqual(
            record['contact_way']['display_num'], self.new_seko_af_data.contact_way.display_num
        )
        self.assertEqual(record['note'], self.new_seko_af_data.note)

        # AfWishは総入れ替えなのでPKがすべて変わっている
        self.assertEqual(len(record['wishes']), 3)
        self.assertEqual(
            set([rec['id'] for rec in record['wishes']])
            & set([self.af_wish_1.pk, self.af_wish_2.pk]),
            set([]),
        )

    def test_seko_af_update_can_remove_wishes(self) -> None:
        """施行AF更新API(PUT)でAF希望項目リストが空の場合は既存のAF希望項目を削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['wishes'] = []
        response = self.api_client.put(
            reverse('after_follow:seko_af_detail', kwargs={'pk': self.seko_af.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(len(record['wishes']), 0)

    def test_seko_af_update_remains_wishes_on_patch(self) -> None:
        """施行AF更新API(PATCH)でAF希望項目リストのキー自体がない場合は既存のAF希望項目は残る"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params.pop('wishes')
        response = self.api_client.patch(
            reverse('after_follow:seko_af_detail', kwargs={'pk': self.seko_af.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(len(record['wishes']), 2)

    def test_seko_af_update_fail_by_activities_exist(self) -> None:
        """施行AF更新APIは指定した施行に紐付くAF活動内容が1件でもあったら失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        AfActivityDetailFactory(af_wish=self.af_wish_2)
        response = self.api_client.patch(
            reverse('after_follow:seko_af_detail', kwargs={'pk': self.seko_af.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['seko'])

    def test_seko_af_update_failed_without_auth(self) -> None:
        """施行AF更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('after_follow:seko_af_detail', kwargs={'pk': self.seko_af.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
