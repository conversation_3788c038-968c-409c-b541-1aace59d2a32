import { Component, OnInit, Inject, LOCALE_ID, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from 'src/app/pages/common/confirm-dialog/confirm-dialog.component';
import { SessionService } from 'src/app/service/session.service';
import { HttpClientService } from 'src/app/service/http-client.service';
import { LoaderService } from 'src/app/service/loader.service';
import { ZipcodeService } from 'src/app/service/zipcode.service';
import { ChobunGetRequest, ChobunGetResponse, ChobunPutRequest } from 'src/app/interfaces/order';
import { AddressGetRequest } from 'src/app/interfaces/master';
import { Utils } from 'src/app/const/func-util';
import { CommonConstants } from 'src/app/const/const';
declare var $;

@Component({
  selector: 'app-com-chobun',
  templateUrl: './chobun.component.html',
  styleUrls: ['./chobun.component.scss']
})
export class ChobunComponent implements OnInit {
  Const = CommonConstants;

  host_url = window.location.origin;
  login_info = this.sessionSvc.get('staff_login_info');
  company_list = Utils.getCompanyList();
  login_company = Utils.getLoginCompany();
  companyCombo = { values: [], clearable: false, showOnFocus: false, disableSearch: true };
  sekoBaseCombo = { values: [], clearable: true };
  form_data = {
    company_id: null,
    seko_department: null,
    entry_id: null,
    okurinushi_name: null,
    entry_name: null,
    entry_tel: null,
    renmei: null,
    okurinushi_company: null,
    okurinushi_title: null,
    soke_name: null,
    kojin_name: null,
    moshu_name: null,
    printed: false,
    not_printed: true,
    canceled: false,
    not_canceled: true,
  };
  searched = false;
  chobun_list = null;

  page_per_count = 20;
  pagination = {
    pages: new Array(),
    current: 0
  };
  receipt_url = null;
  selected_entry = null;
  chobun_edit: ChobunGetResponse = null;
  message = null;
  constructor(
    private router: Router,
    private httpClientService: HttpClientService,
    private sessionSvc: SessionService,
    private loaderSvc: LoaderService,
    private dialog: MatDialog,
    private zipcodeSvc: ZipcodeService,
    @Inject(LOCALE_ID) private locale: string,
  ) { }


  ngOnInit() {
    this.initControl();
    // setTimeout(() => {
    //   $('.checkbox').checkbox();
    // }, 100);
  }

  initControl() {
    if (this.company_list) {
      this.companyCombo.values = this.company_list.map(value => ({ name: value.base_name, value: value.id, data: value }));
      if (this.login_company) {
        if (this.login_company.base_type === CommonConstants.BASE_TYPE_COMPANY) {
          this.form_data.company_id = this.login_company.id;
          this.sekoBaseCombo.values = Utils.getSekoBaseList(this.login_company);
          if (this.login_info.staff.base.base_type !== CommonConstants.BASE_TYPE_COMPANY) {
            this.form_data.seko_department = this.login_info.staff.base.id;
          }
        } else {
          this.companyCombo.disableSearch = false;
          this.form_data.company_id = this.company_list[0].id;
          this.sekoBaseCombo.values = Utils.getSekoBaseList(this.company_list[0]);
        }
      }
    }
  }
  companyChange(event, sekoBaseComboEm) {
    this.sekoBaseCombo.values = [];
    this.form_data.seko_department = null;
    if (event && event.data) {
      this.sekoBaseCombo.values = Utils.getSekoBaseList(event.data);
    }
    sekoBaseComboEm.clear();
    sekoBaseComboEm.initComponent();
  }
  checkClick(flg_name, ...params: any[]) {

    for (const param of params) {
      if (param) {
        this.form_data[flg_name] = !this.form_data[flg_name];
        return;
      }
    }
  }
  async searchChobun() {
    this.sessionSvc.clear('message');
    const request = new ChobunGetRequest();
    request.company_id = this.form_data.company_id;
    request.seko_department = this.form_data.seko_department;
    request.entry_id = this.form_data.entry_id;
    request.okurinushi_name = this.form_data.okurinushi_name;
    request.entry_name = this.form_data.entry_name;
    request.entry_tel = this.form_data.entry_tel;
    request.renmei = this.form_data.renmei;
    request.okurinushi_company = this.form_data.okurinushi_company;
    request.okurinushi_title = this.form_data.okurinushi_title;
    request.soke_name = this.form_data.soke_name;
    request.kojin_name = this.form_data.kojin_name;
    request.moshu_name = this.form_data.moshu_name;
    if (this.form_data.printed && !this.form_data.not_printed) {
      request.printed_flg = true;
    } else if (!this.form_data.printed && this.form_data.not_printed) {
      request.printed_flg = false;
    }
    if (this.form_data.canceled && !this.form_data.not_canceled) {
      request.is_cancel = true;
    } else if (!this.form_data.canceled && this.form_data.not_canceled) {
      request.is_cancel = false;
    }
    if (!this.loaderSvc.isLoading) {
      this.loaderSvc.call();
    }
    this.pagination.pages = new Array();
    this.pagination.current = 0;
    this.httpClientService.getChobunList(request).then((response) => {
      Utils.log(response);
      this.searched = true;
      this.chobun_list = response;

      if (this.chobun_list && this.chobun_list.length) {
        for (const chobun of this.chobun_list) {
          if (chobun.entry_detail.entry.entry_ts) {
            chobun.entry_detail.entry.entry_ts = new Date(chobun.entry_detail.entry.entry_ts);
          }
        }
      }
      this.calcPagination();
      this.loaderSvc.dismiss();

    }).catch(error => {
      this.loaderSvc.dismiss();
    });
  }
  calcPagination() {
    if (!this.chobun_list || !this.chobun_list.length) {
      return;
    }
    const count = Math.ceil(this.chobun_list.length / this.page_per_count);
    for (let i = 1; i <= count; i++) {
      this.pagination.pages.push(i);
    }
    this.pagination.current = 1;
  }
  pageTo(page_num) {
    this.pagination.current = page_num;
  }
  clearForm(companyComboEm, sekoBaseComboEm) {

    this.form_data = {
      company_id: null,
      seko_department: null,
      entry_id: null,
      okurinushi_name: null,
      entry_name: null,
      entry_tel: null,
      renmei: null,
      okurinushi_company: null,
      okurinushi_title: null,
      soke_name: null,
      kojin_name: null,
      moshu_name: null,
      printed: false,
      not_printed: true,
      canceled: true,
      not_canceled: true,
    };
    if (this.login_company) {
      if (this.login_company.base_type === CommonConstants.BASE_TYPE_COMPANY) {
        this.form_data.company_id = this.login_company.id;
        if (this.login_info.staff.base.base_type === CommonConstants.BASE_TYPE_COMPANY) {
          sekoBaseComboEm.clear();
        } else {
          this.form_data.seko_department = this.login_info.staff.base.id;
          sekoBaseComboEm.setValue(this.form_data.seko_department);
        }
      } else {
        this.form_data.company_id = this.company_list[0].id;
        this.sekoBaseCombo.values = Utils.getSekoBaseList(this.company_list[0]);
        sekoBaseComboEm.clear();
        sekoBaseComboEm.initComponent();
      }
      companyComboEm.setValue(this.form_data.company_id);
    }

  }

  downloadPDF(detail_id) {
    this.sessionSvc.clear('message');
    if (detail_id) {
      this.loaderSvc.call();
      this.httpClientService.downloadChobunPdf(detail_id)
        .then((response) => {
          this.localStreamDownload(response, detail_id);
          this.sessionSvc.save('message', { type: 'info', title: '弔文PDFを出力しました。' });
          this.loaderSvc.dismiss();
          this.searchChobun();
        }).catch(error => {
          this.sessionSvc.save('message', { type: 'error', title: '弔文PDFの出力が失敗しました。' });
          this.loaderSvc.dismiss();
        });
    }
  }

  localStreamDownload(stream, detail_id) {
    const blob = new Blob([stream]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    const file_name = 'chobun-' + detail_id + '.pdf';

    if (window.navigator.msSaveBlob) {
      window.navigator.msSaveOrOpenBlob(blob, file_name);
    } else {
      link.href = url;
      link.download = file_name;
      link.click();
    }
  }

  showUrl(entry_id) {
    if (!entry_id) {
      return;
    }
    this.receipt_url = this.host_url + '/order/' + entry_id + '/receipt';
    $('#receipt-url').modal({
      centered: false,
      closable: true,
      detachable: false,
      transition: 'fade'
    }).modal('show');
  }

  onFocus(event) {
    event.target.select();
  }

  showOrderCancel(entry) {
    if (!entry) {
      return;
    }
    this.selected_entry = entry;
    $('#order-cancel').modal({
      centered: false,
      closable: false,
      detachable: false,
      transition: 'fade'
    }).modal('show');
  }

  cancelOrder() {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        msg1: '申込みをキャンセルします、よろしいですか？',
      },
      width: '100%',
      maxWidth: '450px',
      maxHeight: '90%'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        $('#order-cancel').modal('hide');
        this.loaderSvc.call();
        this.httpClientService.cancelEntry(this.selected_entry.id)
          .then(async (response) => {
            Utils.log(response);
            this.sessionSvc.save('message', { type: 'info', title: '申込みをキャンセルしました。' });
            this.selected_entry.cancel_ts = new Date();
            this.loaderSvc.dismiss();
            setTimeout(() => {
              this.selected_entry = null;
            }, 500);
          })
          .catch(error => {
            this.loaderSvc.dismiss();
            this.sessionSvc.save('message', { type: 'error', title: '申込みのキャンセル処理が失敗しました。' });
            setTimeout(() => {
              this.selected_entry = null;
            }, 500);
          });
      }
    });
  }

  closeCancelOrder() {
    setTimeout(() => {
      this.selected_entry = null;
    }, 500);
  }

  showChobunData(chobun) {
    this.message = null;
    this.chobun_edit = JSON.parse(JSON.stringify(chobun));
    $('#chobun-edit').modal({
      centered: false,
      closable: false,
      detachable: false,
      transition: 'fade'
    }).modal('show');
  }

  async saveChobunData() {
    this.sessionSvc.clear('message');
    if (!this.chobunDataValidate()) {
      return;
    }
    const postData = this.getChobunPostData();
    this.loaderSvc.call();
    await this.httpClientService.updateChobun(this.chobun_edit.entry_detail.id, postData)
      .then(async (response) => {
        Utils.log(response);
        await this.searchChobun();
        this.sessionSvc.save('message', { type: 'info', title: '担当者情報を保存しました。' });
      })
      .catch(error => {
        this.sessionSvc.save('message', { type: 'error', title: '担当者情報の保存が失敗しました。' });
        this.loaderSvc.dismiss();
      });

    $('#chobun-edit').modal('hide');
    setTimeout(() => {
      this.chobun_edit = null;
    }, 500);

  }

  chobunDataValidate() {
    const msgs = new Array();
    this.message = null;
    if (!this.chobun_edit.atena) {
      msgs.push('宛名');
    }
    if (!this.chobun_edit.okurinushi_name) {
      msgs.push('氏名');
    }
    if (!this.chobun_edit.okurinushi_kana) {
      msgs.push('氏名カナ');
    }
    if (!this.chobun_edit.okurinushi_zip_code) {
      msgs.push('電話番号');
    }
    if (!this.chobun_edit.okurinushi_tel) {
      msgs.push('郵便番号');
    }
    if (!this.chobun_edit.okurinushi_prefecture) {
      msgs.push('都道府県');
    }
    if (!this.chobun_edit.okurinushi_address_1) {
      msgs.push('市区町村');
    }
    if (!this.chobun_edit.okurinushi_address_2) {
      msgs.push('所番地');
    }
    if (!this.chobun_edit.honbun) {
      msgs.push('本文');
    }

    if (msgs.length > 0) {
      this.message = {
        type: 'warning',
        title: '必須項目が入力されておりません。',
        msg: msgs.join(',')
      };
      return false;
    }
    const honbun_rows = this.chobun_edit.honbun.split('\n');
    if (honbun_rows.length > 10) {
      this.message = {
        type: 'warning',
        title: '本文は10行以内で入力してください。'
      };
      return false;
    }
    for (const honbun_row of honbun_rows) {
      if (honbun_row.length > 21) {
        this.message = {
          type: 'warning',
          title: '本文は1行に21文字以内で入力してください。'
        };
        return false;
      }
    }

    return true;
  }

  getChobunPostData(): ChobunPutRequest {
    const postRequest = new ChobunPutRequest();
    postRequest.atena = this.chobun_edit.atena;
    postRequest.okurinushi_company = this.chobun_edit.okurinushi_company;
    postRequest.okurinushi_title = this.chobun_edit.okurinushi_title;
    postRequest.okurinushi_company_kana = this.chobun_edit.okurinushi_company_kana;
    postRequest.okurinushi_name = this.chobun_edit.okurinushi_name;
    postRequest.okurinushi_kana = this.chobun_edit.okurinushi_kana;
    postRequest.okurinushi_tel = this.chobun_edit.okurinushi_tel;
    postRequest.okurinushi_zip_code = this.chobun_edit.okurinushi_zip_code;
    postRequest.okurinushi_prefecture = this.chobun_edit.okurinushi_prefecture;
    postRequest.okurinushi_address_1 = this.chobun_edit.okurinushi_address_1;
    postRequest.okurinushi_address_2 = this.chobun_edit.okurinushi_address_2;
    postRequest.okurinushi_address_3 = this.chobun_edit.okurinushi_address_3;
    postRequest.renmei1 = this.chobun_edit.renmei1;
    postRequest.renmei_kana1 = this.chobun_edit.renmei_kana1;
    postRequest.renmei2 = this.chobun_edit.renmei2;
    postRequest.renmei_kana2 = this.chobun_edit.renmei_kana2;
    postRequest.note = this.chobun_edit.note;
    postRequest.honbun = this.chobun_edit.honbun;

    return postRequest;
  }

  closeChobunEdit() {
    setTimeout(() => {
      this.chobun_edit = null;
    }, 500);
  }
  closeMessage() {
    this.message = null;
  }
  zipcodeChange() {
    const request = new AddressGetRequest();
    request.zip_code = this.chobun_edit.okurinushi_zip_code;
    this.zipcodeSvc.getData(request).then(address => {
      if (address) {
        this.chobun_edit.okurinushi_prefecture = address.prefecture;
        this.chobun_edit.okurinushi_address_1 = address.address_1;
        this.chobun_edit.okurinushi_address_2 = address.address_2;
      }
    }).catch(error => { });
  }

}
