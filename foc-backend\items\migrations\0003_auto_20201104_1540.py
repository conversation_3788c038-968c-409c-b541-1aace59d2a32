# Generated by Django 3.1.2 on 2020-11-04 06:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('suppliers', '0001_initial'),
        ('items', '0002_auto_20201030_1411'),
    ]

    operations = [
        migrations.AlterField(
            model_name='item',
            name='suppliers',
            field=models.ManyToManyField(
                related_name='items',
                through='items.ItemSupplier',
                to='suppliers.Supplier',
                verbose_name='suppliers',
            ),
        ),
        migrations.AlterField(
            model_name='itemsupplier',
            name='item',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='item_supplier',
                to='items.item',
                verbose_name='item',
            ),
        ),
        migrations.AlterField(
            model_name='itemsupplier',
            name='supplier',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='item_supplier',
                to='suppliers.supplier',
                verbose_name='supplier',
            ),
        ),
    ]
