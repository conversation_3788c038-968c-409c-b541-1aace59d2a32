import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { HowtoShareComponent } from './howto-share.component';

describe('HowtoShareComponent', () => {
  let component: HowtoShareComponent;
  let fixture: ComponentFixture<HowtoShareComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ HowtoShareComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HowtoShareComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
