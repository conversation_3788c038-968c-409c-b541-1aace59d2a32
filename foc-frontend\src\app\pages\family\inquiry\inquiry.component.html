
<div class="container">
  <div class="inner" *ngIf="!is_loading">
    <div class="contents">
      <h2>お問い合わせ</h2>
      <div class="input-area">
        <div class="line">
          <div class="input query_text">
            <textarea rows="8" [(ngModel)]="query_text" placeholder="お問い合わせ内容を入力してください。"></textarea>
          </div>
        </div>
      </div>
      <div class="button-area">
        <a class="button pink" (click)="saveDataConfirm()">内容確認</a>
      </div>
      <div class="inquiry-list">
        <ng-container *ngIf="inquiry_list?.length">
          <ng-container *ngFor="let inquiry of inquiry_list;index as i">
            <div class="inquiry-data" [class.group]="isGroup(i)">
              <div class="updated_at">
                {{inquiry.updated_at | date: 'yyyy/MM/dd HH:mm'}}
              </div>
              <div class="query">
                {{inquiry.query}}  
              </div>
              <a class="button grey" (click)="showRequeyInput(inquiry)" *ngIf="!isGroup(i)">再質問</a>
            </div>
            <ng-container *ngFor="let answer of inquiry.answers">
              <div class="inquiry-data answer">
                <div class="updated_at">
                  {{answer.updated_at | date: 'yyyy/MM/dd HH:mm'}}
                  <span class="staff">{{answer.staff.name}}</span>
                </div>
                <div class="query answer">
                  {{answer.answer}}  
                </div>
              </div>
            </ng-container>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="!inquiry_list?.length">
          <div class="no-data">お問い合わせ内容がございません。</div>
        </ng-container>
      </div>
    </div>
    <div class="ui modal confirm" id="re-query">
      <div class="title">
        お問い合わせ
      </div>
      <div class="content">
        <div class="input-area">
          <div class="line">
            <div class="input requery_text">
              <textarea rows="8" [(ngModel)]="requery_text" placeholder="お問い合わせ内容を入力してください。"></textarea>
            </div>
          </div>
        </div>
      </div>
      <div class="button-area">
        <a class="button cancel" (click)="cancelRequeyInput()">キャンセル</a>
        <a class="button red" (click)="saveDataConfirm(2)">内容確認</a>
      </div>
    </div>
    <div class="ui modal confirm" id="msg-confirm">
      <div class="title">
        内容確認
      </div>
      <div class="scrolling content">
        {{query_send_text}}
      </div>
      <div class="button-area">
        <a class="button" (click)="cancelConfirm()">キャンセル</a>
        <a class="button red" (click)="saveInquiry()">送信</a>
      </div>
    </div>
    <div class="ui modal confirm" id="message-popup">
      <div class="content red">
        {{message}}
      </div>
      <div class="button-area">
        <a class="button grey" (click)="closePopup()">閉じる</a>
      </div>
    </div>
  </div>
</div>
