

body {
  font-size: 14px;
}
.contents{
  background-color: #ffffff;
  width: 90%;
  max-width: 1000px;
  margin: 0px auto 50px;
  border-radius: 10px;
  font-size: 1rem;
  padding: 25px 20px 80px;
  color: #000000;
  @media screen and (max-width: 560px) {
    padding: 25px 20px 80px;
    margin-top: 10px;
  }
  h1 {
    font-size: 1.3em;
    letter-spacing: 0.1em;
    font-weight: normal;
    line-height: 36px;
    text-align: center;
    text-indent: 0.1em;
    margin-bottom: 30px;
    @media screen and (max-width: 560px) {
      font-size: 1.2em;
      margin-bottom: 25px;
      line-height: initial;
    }
  }
  .siteWrapper {
    .headline {
      padding: 8px 10px;
      background: #c9c9c9;
      margin-bottom: 10px;
      font-size: 1.1em;
      @media screen and (max-width: 560px) {
        font-size: 1em;
        line-height: 1.5;
      }
    }
    .mB20 {
      padding: 10px;
      margin-bottom: 1em;
      clear: both;
    }
    p {
      line-height: 1.7;
      font-size: 0.91em;
      @media screen and (max-width: 560px) {
        line-height: 1.8;
        font-size: 0.8em;
      }
    }
    p.site-txt{
      font-size: 1em;
    }
    .company {
      float: right;
      a {
        display: inline;
      }
      .address {
        font-size: 0.8em;
        @media screen and (max-width: 560px) {
          font-size: 0.7em;
        }
      }
    }
    table {
      margin: 5px 10px 10px 5px;
      font-style: normal;
      font-weight: 200;
      text-align: justify;
      background-color: #ffffff;
      border-top: solid 1px #000000;;
      border-right: solid 1px #000000;
      font-size: 1em;
      th,td {
        padding: 10px;
        border-bottom: solid 1px #000000;
        border-left: solid 1px #000000;
        vertical-align: middle;
        line-height: 1.5;
      }
      th {
        background-color: #c9c9c9;
        font-weight: bold;
        font-size: 0.9em;
      }
      td {
        font-size: 0.8em;
      }
      @media screen and (max-width: 560px) {
          th {
              width: 30%;
              font-size: 0.8em;
          }
          td {
              font-size: 0.7em;
              padding: 8px;
          }
      }
    }
    .indettx {
        text-indent: -1em;
        padding: 0 0 0 1em;
        margin: 0;
    }
    .back  {
      margin-top: 50px;
        & a {
          max-width: 200px;
          font-size: 16px;
          padding: 20px 0;
          margin: 0 auto;
          text-decoration: none;
      }
    }
  }
}
