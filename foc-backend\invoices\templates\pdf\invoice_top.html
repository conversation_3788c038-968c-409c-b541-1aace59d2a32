{% load humanize %}
{% load invoice_filters %}
{% load fuhoshi_filters %}
<!DOCTYPE html>
<html>
<title>請求書</title>
<head>
<style type="text/css">
body {
  font-family: "Noto Serif CJK JP", serif;
  font-size: 12px;
  color: #333;
  margin: 0;
  line-height: 1.6;
}
@media print {
  .break-after {
    page-break-after: always;
  }
  .detail .page-header {
    position: fixed;
    right: 5px;
    top: 2px;
    font-size: 10px;
  }
}
@media screen {
  .detail .page-header {
    position: absolute;
    right: 5px;
    top: 2px;
    font-size: 10px;
  }
}
.page-header-space {
  height: 40px;
}
.top {
  font-size: 12px;
}
.top .title {
  text-align: center;
  font-size: 20px;
  margin-top: 10px
}
.top .page-header {
  position: absolute;
  right: 5px;
  top: 2px;
  font-size: 10px;
}
.top .target-company {
  margin-top: 10px;
  border: solid 1px #000000;
  padding: 5px;
  display: inline-block;
  min-width: 270px;
  max-width: 400px;
}
.top .target-company .name {
  font-size: 14px;
  margin-left: 50px;
}
.top .target-company .right {
  text-align: right;
}
.top .sender-company {
  position: absolute;
  right: 25px;
  top: 90px;
}
.top .sender-company .logo{
  width: 40px;
  height: auto;
  margin-right: 15px;
}
.top .sender-company .indent{
  margin-left: 15px;
}
.top .sender-company .business_no{
  margin-top: -5px;
  font-size: 10px;
}
.top .stamp {
  position: absolute;
  right: 0px;
  top: 90px;
  width: 90px;
  height: auto;
}
.top .description {
  margin-top: 70px;
  padding: 5px;
}
.top .total-price {
  position: absolute;
  right: 5px;
  top: 250px;
  font-size: 11px;
}
.top .total-price .header {
  width: 130px;
  border: solid 1px #000000;
  background-color: #eeeeee;
  text-align: center;
  margin-top: 5px;
  padding: 2px 5px;
}
.top .total-price .data {
  width: 130px;
  font-size: 14px;
  border: solid 1px #000000;
  border-top: 0;
  text-align: right;
  padding: 2px 5px;
}
.top .list {
  width: 98%;
  margin: 20px 5px 0;
  border-spacing: 0;
}
.top .list tr td {
  padding: 1.5px 3px;
}
.top .list tr.header td {
  border-top: solid 1px #000000;
  border-left: dotted 1px #000000;
  border-bottom: solid 1px #000000;
}
.top .list tr.header td:first-child {
  border-left-style: solid;
}
.top .list tr.header td:last-child {
  border-right: solid 1px #000000;
}
.top .list tr.header {
  text-align: center;
  background-color: #eeeeee;
}
.top .list tr.data {
  text-align: center;
}
.top .list .date {
  width: 8%;
}
.top .list .number {
  width: 6%;
}
.top .list .quantity {
  width: 8%;
}
.top .list .quantity1 {
  width: 4%;
}
.top .list .price {
  width: 12%;
}
.top .list .amount {
  width: 20%;
}
.top .list tr.data td {
  border-left: dotted 1px #000000;
  border-bottom: dotted 1px #000000;
}
.top .list tr.data td:first-child {
  border-left-style: solid;
}
.top .list tr.data td:last-child {
  border-right: solid 1px #000000;
}
.top .list tr.data:last-child td {
  border-bottom-style: solid;
}
.top .list tr.data:nth-last-child(2) td {
  border-bottom-style: solid;
}
.top .list tr.data .subject {
  text-align: left;
}
.top .list tr.data .subject span{
  width: 100px;
  display: inline-block;
  text-align: right;
}
.top .list tr.data .indent {
  padding-left: 30px;
}
.top .list tr.data .price {
  text-align: right;
}
.top .list tr.data .amount {
  text-align: right;
}
.top .list tr.data .blank {
  border-left: 0;
  border-bottom: 0;
  text-align: left;
  font-size: 10px;
}
.top .list tr.data .summary {
  border-left-style: solid;
}
.top .bank-title {
  margin-top: 10px;
  padding: 5px;
}
.top .bank {
  margin-left: 20px;
}
.top .bank tr td {
  padding: 1px 3px;
  text-align: left;
}
.top .bank tr .name {
  width: 80px;
}
.top .bank tr .branch {
  width: 100px;
}
.top .bank tr .type {
  width: 40px;
}
.top .bank tr .number {
  width: 70px;
}
.list .small-indent {
  padding-left: 15px !important;
}
.list .indent {
  padding-left: 20px !important;
}
</style>
</head>
<body>
<section class="break-after">
  <div class="top">
    <div class="title">請求書</div>
    <div class="page-header">
      <div>No. {{ sales.company_id }}-{{ sales.id }}</div>
      <div>発行日 {{ sales.invoice_date|date:"Y年n月j日" }}</div>
    </div>
    <div class="target-company">
      <div>〒 {{ sales.zip_code|slice:"0:3" }}-{{ sales.zip_code|slice:"3:7" }}</div>
      <div>{{ sales.address_1|default_if_none:"" }}</div>
      <div>{{ sales.address_2|default_if_none:"" }}</div>
      <div class="name">{{ sales.company_name|default_if_none:"" }}</div>
      <div class="right">御中</div>
    </div>
    <div class="sender-company">
      <div><img class="logo" src="{{ images.logo }}">株式会社エム・エス・アイ</div>
      <div class="business_no">事業者番号：T-*************</div>
      <div>〒 160-0022</div>
      <div>東京都新宿区新宿一丁目1-7</div>
      <div>コスモ新宿御苑ビル6F</div>
      <div class="indent">電話　03-5367-5730（代）</div>
      <div class="indent">FAX　03-5367-5731</div>
    </div>
    <img class="stamp" src="{{ images.stamp }}">
    <div class="description">
      <div>毎々格別のお引き立てを賜わり厚くお礼申し上げます。</div>
      <div>下記の通り御請求申し上げます。</div>
      <div>※振込手数料は貴社にてご負担くださいますよう</div>
      <div>お願い申し上げます。</div>
    </div>
    {% with sales_price=sales.sales_price|default_if_none:0 invoice_tax=sales.invoice_tax|default_if_none:0 koden_fee_commission=sales.koden_fee_commission|default_if_none:0%}
    <div class="total-price">
      <div>{{ sales.pay_date|date:"Y年n月j日" }}までにお振込み下さい。</div>
      <div class="header">今回御請求額</div>
      <div class="data">¥{{ sales_price|add:invoice_tax|add:koden_fee_commission|intcomma }}</div>
    </div>
    <table class="list">
      <tr class="header">
        <td class="date">日付</td>
        <td class="number"></td>
        <td class="subject">件&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</td>
        <td class="quantity" colspan="2">数量</td>
        <td class="price">単&nbsp;&nbsp;価</td>
        <td class="amount">ご請求額</td>
      </tr>
      <tr class="data">
        <td>{{ sales.invoice_date|date:"n/j" }}</td><td></td><td></td>
        <td class="quantity1"></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject">FOC利用料({{sales.invoice_date|first_day|date:"Y年n月j日"}}~{{ sales.invoice_date|date:"n月j日" }})</td>
        <td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">①</td>
        <td class="subject indent">月額費用</td>
        <td>1</td>
        <td>式</td>
        <td class="price">¥{{sales.monthly_fee|default_if_none:0|intcomma}}</td>
        <td class="amount">¥{{sales.monthly_fee|default_if_none:0|intcomma}}</td>
      </tr>
      <tr class="data">
        <td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject">システム利用料({{sales.invoice_date|first_day|date:"Y年n月j日"}}~{{ sales.invoice_date|date:"n月j日" }})</td>
        <td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">①</td>
        <td class="subject indent">弔文</td>
        <td>{{sales.chobun_fee}}</td>
        <td>{{sales.chobun_fee_unit}}</td>
        <td class="price">¥{{sales.chobun_order_price|default_if_none:0|intcomma}}</td>
        <td class="amount">¥{{sales.chobun_order_fee|default_if_none:0|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">②</td>
        <td class="subject indent">供花・供物</td>
        <td>{{sales.kumotsu_fee}}</td>
        <td>{{sales.kumotsu_fee_unit}}</td>
        <td class="price">¥{{sales.kumotsu_order_price|default_if_none:0|intcomma}}</td>
        <td class="amount">¥{{sales.kumotsu_order_fee|default_if_none:0|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">③</td>
        {% with koden_order_price=sales.koden_order_price|default_if_none:0 koden_order_fee=sales.koden_order_fee|default_if_none:0 %}
        <td class="subject indent">香典　※({{koden_order_fee|intcomma}} - {{koden_order_price|add:koden_order_fee|intcomma}} × {{sales.koden_commission_pct|default_if_none:0}}%)</td>
        {% endwith %}
        <td></td>
        <td></td>
        <td></td>
        <td class="amount">¥{{koden_fee_commission|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">④</td>
        <td class="subject indent">返礼品</td>
        <td>{{sales.henreihin_fee}}</td>
        <td>{{sales.henreihin_fee_unit}}</td>
        <td class="price">¥{{sales.henreihin_order_price|default_if_none:0|intcomma}}</td>
        <td class="amount">¥{{sales.henreihin_order_fee|default_if_none:0|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject indent">&nbsp;&nbsp;※明細は別紙に記載</td>
        <td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td>
        <td></td>
        <td class="subject"><span>税抜課税対象額（</span><span>¥{{sales_price|intcomma }}）</span></td>
        <td>{{sales.invoice_tax_pct|default_if_none:0}}</td>
        <td>%</td>
        <td class="subject">消費税等</td>
        <td class="amount">¥{{invoice_tax|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject"><span>税込課税対象額（</span><span>¥{{koden_fee_commission|intcomma }}）</span></td>
        <td>{{sales.invoice_tax_pct|default_if_none:0}}</td>
        <td>%</td>
        <td></td><td></td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject"><span>内消費税（</span><span>¥{{sales.koden_fee_commission_tax|default_if_none:0|intcomma }}）</span></td>
        <td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td>
        <td></td>
        <td class="subject"><span>税抜課税対象額（</span><span>¥0）</span></td>
        <td>8</td>
        <td>%</td>
        <td class="subject">消費税等</td>
        <td class="amount">¥0</td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject"><span>税込課税対象額（</span><span>¥0）</span></td>
        <td>8</td>
        <td>%</td>
        <td></td><td></td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject"><span>内消費税（</span><span>¥0）</span></td>
        <td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td colspan="5" class="blank">※香典システム利用料 - (香典額 + 香典システム利用料) × {{sales.koden_commission_pct|default_if_none:0}}%</td>
        <td class="summary">合&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;計</td>
        <td class="amount">¥{{sales_price|add:invoice_tax|add:koden_fee_commission|intcomma}}</td>
      </tr>
    </table>
    {% endwith %}
    <div class="bank-title">
      <div>お振込先</div>
    </div>
    <table class="bank">
      <tr>
        <td class="name">みずほ銀行</td>
        <td class="branch">新宿西口支店</td>
        <td class="type">普通</td>
        <td class="number">2153514</td>
      </tr>
      <tr>
        <td>りそな銀行</td>
        <td>虎ノ門支店</td>
        <td>普通</td>
        <td>7722799</td>
      </tr>
      <tr>
        <td>三菱UFJ銀行</td>
        <td>四谷支店</td>
        <td>普通</td>
        <td>0118831</td>
      </tr>
      <tr>
        <td>三井住友銀行</td>
        <td>新宿西口支店</td>
        <td>普通</td>
        <td>2631630</td>
      </tr>
      <tr>
        <td></td>
        <td colspan="3">口座名義：株式会社エム・エス・アイ</td>
      </tr>
    </table>
  </div>
</section>
</body>
</html>
