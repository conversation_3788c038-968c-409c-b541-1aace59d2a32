from typing import Dict, List, Set

from django.db import transaction
from django.db.models import Q, QuerySet
from django.utils.translation import gettext_lazy as _
from drf_extra_fields.relations import PresentablePrimaryKeyRelatedField
from rest_framework import exceptions, serializers

from henrei.models import Hen<PERSON><PERSON>inKoden, HenreihinKumotsu, OrderHenreihin
from orders.models import EntryDetail, EntryDetailKoden, EntryDetailKumotsu
from seko.models import Seko
from suppliers.models import Supplier
from suppliers.serializers import SupplierSerializer


class HenreiKodenSerializerBase(serializers.ModelSerializer):
    supplier = PresentablePrimaryKeyRelatedField(
        queryset=Supplier.objects.all(), presentation_serializer=SupplierSerializer, required=False
    )

    class Meta:
        model = HenreihinKoden


class HenreiKodenSerializer(HenreiKodenSerializerBase):
    class Meta(HenreiKodenSerializerBase.Meta):
        fields = '__all__'
        read_only_fields = ['detail_koden', 'created_at', 'updated_at']


class HenreiKodenRelatedSerializer(HenreiKodenSerializerBase):
    class Meta(HenreiKodenSerializerBase.Meta):
        exclude = ['detail_koden']
        read_only_fields = ['supplier', 'order_status', 'created_at', 'updated_at']


class HenreiKodenCreateSerializer(HenreiKodenSerializerBase):
    class Meta(HenreiKodenSerializerBase.Meta):
        fields = '__all__'
        read_only_fields = [
            'customer_self_select_flg',
            'select_status',
            'henrei_order',
            'order_status',
            'created_at',
            'updated_at',
        ]

    def validate_detail_koden(self, value) -> int:
        detail_koden: EntryDetailKoden = EntryDetailKoden.objects.get(Q(pk=value))
        if detail_koden.entry_detail.entry.seko.moshu != self.context['moshu']:
            raise exceptions.PermissionDenied(_('The seko is not yours.'))
        return value

    def create(self, validated_data):
        validated_data['customer_self_select_flg'] = False
        validated_data['select_status'] = 1
        validated_data['henrei_order'] = None
        validated_data['order_status'] = 0
        return super().create(validated_data)


class HenreiKumotsuSerializerBase(serializers.ModelSerializer):
    supplier = PresentablePrimaryKeyRelatedField(
        queryset=Supplier.objects.all(), presentation_serializer=SupplierSerializer, required=False
    )

    class Meta:
        model = HenreihinKumotsu


class HenreiKumotsuSerializer(HenreiKumotsuSerializerBase):
    class Meta(HenreiKumotsuSerializerBase.Meta):
        fields = '__all__'
        read_only_fields = ['detail_kumotsu', 'created_at', 'updated_at']


class HenreiKumotsuRelatedSerializer(HenreiKumotsuSerializerBase):
    class Meta(HenreiKumotsuSerializerBase.Meta):
        exclude = ['detail_kumotsu']
        read_only_fields = ['supplier', 'order_status', 'created_at', 'updated_at']


class HenreiKumotsuCreateSerializer(HenreiKumotsuSerializerBase):
    class Meta(HenreiKumotsuSerializerBase.Meta):
        fields = '__all__'
        read_only_fields = [
            'select_status',
            'henrei_order',
            'order_status',
            'created_at',
            'updated_at',
        ]

    def validate_detail_kumotsu(self, value) -> int:
        detail_kumotsu: EntryDetailKumotsu = EntryDetailKumotsu.objects.get(Q(pk=value))
        if detail_kumotsu.entry_detail.entry.seko.moshu != self.context['moshu']:
            raise exceptions.PermissionDenied(_('The seko is not yours.'))
        return value

    def create(self, validated_data):
        validated_data['select_status'] = 1
        validated_data['henrei_order'] = None
        validated_data['order_status'] = 0
        return super().create(validated_data)


class HenreiListSerializer(serializers.Serializer):
    henreihin_id = serializers.IntegerField()
    henreihin_type = serializers.IntegerField()
    seko_id = serializers.IntegerField()
    entry_id = serializers.IntegerField()
    detail_id = serializers.IntegerField()
    seko_date = serializers.DateField()
    seko_department_name = serializers.CharField()
    hall_name = serializers.CharField()
    soke_name = serializers.CharField()
    kojin_name = serializers.CharField()
    moshu_name = serializers.CharField()
    henreihin_name = serializers.CharField()
    henreihin_price = serializers.IntegerField()
    supplier_id = serializers.IntegerField()
    supplier_name = serializers.CharField()
    order_status = serializers.IntegerField()
    cancel_ts = serializers.DateTimeField()


class OrderHenreiListSerializer(serializers.ModelSerializer):
    seko_date = serializers.DateField(source='seko.seko_date')
    seko_department_name = serializers.CharField(source='seko.seko_department.base_name')
    hall_name = serializers.CharField()
    soke_name = serializers.CharField(source='seko.soke_name')
    kojin_name = serializers.CharField()
    moshu_name = serializers.CharField(source='seko.moshu.name')
    supplier_name = serializers.CharField(source='supplier.name')
    henrei_koden = HenreiKodenSerializer(many=True)
    henrei_kumotsu = HenreiKumotsuSerializer(many=True)

    class Meta:
        model = OrderHenreihin
        fields = [
            'id',
            'seko',
            'seko_date',
            'seko_department_name',
            'hall_name',
            'soke_name',
            'kojin_name',
            'moshu_name',
            'order_status',
            'order_ts',
            'order_note',
            'supplier_name',
            'henrei_koden',
            'henrei_kumotsu',
        ]


class HenreiSerializer(serializers.ModelSerializer):
    supplier = PresentablePrimaryKeyRelatedField(
        queryset=Supplier.objects.all(), presentation_serializer=SupplierSerializer, required=False
    )
    henrei_koden = HenreiKodenSerializer(many=True, required=False)
    henrei_kumotsu = HenreiKumotsuSerializer(many=True, required=False)

    class Meta:
        model = OrderHenreihin
        fields = '__all__'
        read_only_fields = ['seko', 'henrei_koden', 'henrei_kumotsu', 'created_at', 'updated_at']

    @transaction.atomic
    def update(self, instance: OrderHenreihin, validated_data: Dict):
        update_order_status: bool = 'order_status' in validated_data
        updated_instance: OrderHenreihin = super().update(instance, validated_data)
        if update_order_status:
            updated_instance.set_order_status(
                validated_data['order_status'], self.context['staff']
            )

        return updated_instance


class HenreiCreateSerializer(serializers.Serializer):
    entry_details = serializers.ListField(child=serializers.IntegerField(), write_only=True)
    seko = serializers.IntegerField(write_only=True)
    supplier = serializers.IntegerField(write_only=True)

    def validate_entry_details(self, value) -> List[EntryDetail]:
        entry_details_qs = EntryDetail.objects.filter(Q(pk__in=value)).order_by('id')
        db_detail_ids: List[int] = [rec.pk for rec in entry_details_qs]
        notfound_ids: Set = set(value) - set(db_detail_ids)
        if notfound_ids:
            raise exceptions.ValidationError(f'EntryDetail not found: ID={notfound_ids}')

        return entry_details_qs

    def validate_seko(self, value) -> Seko:
        seko: Seko = Seko.objects.filter(Q(pk=value)).first()
        if not seko:
            raise exceptions.ValidationError(f'Seko not found: ID={value}')

        return seko

    def validate_supplier(self, value) -> Supplier:
        supplier: Supplier = Supplier.objects.filter(Q(pk=value)).first()
        if not supplier:
            raise exceptions.ValidationError(f'Supplier not found: ID={value}')

        return supplier

    @transaction.atomic
    def create(self, validated_data) -> OrderHenreihin:
        self.instance: OrderHenreihin = OrderHenreihin.objects.create(
            seko=validated_data['seko'],
            supplier=validated_data['supplier'],
            order_ts=None,
            order_staff_id=None,
            order_status=1,
            order_note=None,
        )
        for entry_detail in validated_data['entry_details']:
            if hasattr(entry_detail, 'koden') and hasattr(entry_detail.koden, 'henrei_koden'):
                henrei_koden: HenreihinKoden = entry_detail.koden.henrei_koden
                henrei_koden.henrei_order = self.instance
                henrei_koden.order_status = 1
                henrei_koden.save()
            if hasattr(entry_detail, 'kumotsu') and hasattr(
                entry_detail.kumotsu, 'henrei_kumotsu'
            ):
                henrei_kumotsu: HenreihinKumotsu = entry_detail.kumotsu.henrei_kumotsu
                henrei_kumotsu.henrei_order = self.instance
                henrei_kumotsu.order_status = 1
                henrei_kumotsu.save()
        return self.instance


class OrderHenreiUpdateStatusSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.IntegerField(), write_only=True)
    order_status = serializers.IntegerField(write_only=True)

    def validate_ids(self, value) -> List[int]:
        henrei_order_ids: Set[int] = set(
            OrderHenreihin.objects.filter(Q(pk__in=value)).values_list('pk', flat=True)
        )
        notfound_ids: Set[int] = set(value) - henrei_order_ids
        if notfound_ids:
            raise serializers.ValidationError(f'Henrei order not found: ID={notfound_ids}')

        return value

    @transaction.atomic
    def save(self, **kwargs):
        self.instance: List[OrderHenreihin] = []
        for henrei_order in OrderHenreihin.objects.filter(
            Q(pk__in=self.validated_data['ids'])
        ).order_by('pk'):
            henrei_order.set_order_status(
                self.validated_data['order_status'], self.context['staff']
            )
            self.instance.append(henrei_order)

        return self.instance


class PlaceHenreiOrderSerializer(serializers.Serializer):
    seko = serializers.IntegerField(write_only=True)

    def validate_seko(self, value) -> Seko:
        seko: Seko = Seko.objects.filter(Q(pk=value) & Q(del_flg=False)).first()
        if not seko:
            raise exceptions.ValidationError('Seko not found')

        if seko.moshu != self.context['moshu']:
            raise exceptions.PermissionDenied('The seko is not yours.')

        return seko

    def save(self, **kwargs) -> Dict[str, QuerySet]:
        seko: Seko = self.validated_data['seko']
        henrei_koden = HenreihinKoden.objects.filter(
            Q(detail_koden__entry_detail__entry__seko=seko) & Q(select_status=1)
        ).order_by('pk')
        for koden in henrei_koden:
            koden.select_status = 2
            koden.save()
        henrei_kumotsu = HenreihinKumotsu.objects.filter(
            Q(detail_kumotsu__entry_detail__entry__seko=seko) & Q(select_status=1)
        ).order_by('pk')
        for kumotsu in henrei_kumotsu:
            kumotsu.select_status = 2
            kumotsu.save()

        return {'henrei_koden': henrei_koden, 'henrei_kumotsu': henrei_kumotsu}


class PlaceHenreiOrderResultSerializer(serializers.Serializer):
    henrei_koden = HenreiKodenSerializer(many=True)
    henrei_kumotsu = HenreiKumotsuSerializer(many=True)
