# 施行管理API仕様書

## 概要

FOCシステムの施行管理関連APIの詳細仕様です。施行（葬儀）の作成、取得、更新、削除、および関連する喪主、故人、スケジュール、サービス、アイテム等の管理機能を提供します。

## 1. 施行一覧・作成API

### 1.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST /seko/` |
| **機能** | 施行一覧取得・新規施行作成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `SekoList` |
| **シリアライザー** | `SekoListSerializer`（GET）、`SekoCreateSerializer`（POST） |

### 1.2 施行一覧取得

**HTTPメソッド:** `GET`

**クエリパラメータ:**

| パラメータ | 型 | 必須 | 説明 | 使用例 |
|---|---|---|---|---|
| `seko_department` | integer | ○ | 施行部門ID（階層検索対応） | `?seko_department=10` |
| `term_from` | datetime | ○ | 施行日時の開始範囲 | `?term_from=2024-01-01T00:00:00` |
| `term_to` | datetime | ○ | 施行日時の終了範囲 | `?term_to=2024-12-31T23:59:59` |
| `soke_name` | string | ○ | 葬家名（部分一致） | `?soke_name=田中` |
| `kojin_name` | string | ○ | 故人名（部分一致） | `?kojin_name=太郎` |
| `order_staff` | integer | ○ | 受注担当者ID | `?order_staff=5` |
| `seko_staff` | integer | ○ | 施行担当者ID | `?seko_staff=10` |
| `af_staff` | integer | ○ | アフター担当者ID | `?af_staff=15` |

**レスポンス例:**
```json
{
  "count": 150,
  "next": "http://localhost:8000/seko/?page=2",
  "previous": null,
  "results": [
    {
      "id": 123,
      "soke_name": "田中家",
      "soke_kana": "タナカケ",
      "seko_style_name": "家族葬",
      "death_date": "2024-06-20",
      "seko_date": "2024-06-25T10:00:00+09:00",
      "fuho_site_end_date": "2024-07-25",
      "order_staff_name": "営業 太郎",
      "seko_staff_name": "施行 花子",
      "af_staff_name": "アフター 次郎",
      "seko_company_name": "株式会社サンプル",
      "seko_department_name": "営業部",
      "koden_sum": 150000,
      "moshu": {
        "name": "田中 太郎",
        "kana": "タナカ タロウ",
        "kojin_moshu_relationship": "長男",
        "zip_code": "1000001",
        "prefecture": "東京都",
        "address_1": "千代田区千代田",
        "tel": "03-1234-5678",
        "mail_address": "<EMAIL>"
      },
      "seko_contact": {
        "name": "田中 花子",
        "kojin_relationship": "配偶者",
        "tel": "090-1234-5678"
      },
      "kojin": [
        {
          "kojin_num": 1,
          "name": "田中 一郎",
          "kana": "タナカ イチロウ",
          "age": 75,
          "death_date": "2024-06-20",
          "death_place": "自宅"
        }
      ],
      "schedules": [
        {
          "id": 1,
          "schedule": {
            "id": 1,
            "schedule_name": "通夜"
          },
          "scheduled_datetime": "2024-06-24T18:00:00+09:00",
          "hall_name": "セレモニーホール本館",
          "display_num": 1
        },
        {
          "id": 2,
          "schedule": {
            "id": 2,
            "schedule_name": "告別式"
          },
          "scheduled_datetime": "2024-06-25T10:00:00+09:00",
          "hall_name": "セレモニーホール本館",
          "display_num": 2
        }
      ],
      "seko_af": {
        "af_staff": {
          "id": 15,
          "name": "アフター 次郎"
        }
      },
      "inquiries": []
    }
  ]
}
```

### 1.3 施行作成

**HTTPメソッド:** `POST`

**Content-Type:** `application/json`

**リクエストボディ:**
```json
{
  "soke_name": "string",
  "soke_kana": "string",
  "seko_style": "integer",
  "seko_style_name": "string",
  "death_date": "date",
  "seko_date": "datetime",
  "fuho_site_end_date": "date",
  "fuho_sentence": "string",
  "fuho_contact_name": "string",
  "fuho_contact_tel": "string",
  "henreihin_rate": "integer",
  "note": "string",
  "order_staff": "integer",
  "seko_staff": "integer",
  "af_staff": "integer",
  "seko_company": "integer",
  "seko_department": "integer",
  "moshu": {
    "name": "string",
    "kana": "string",
    "kojin_moshu_relationship": "string",
    "zip_code": "string",
    "prefecture": "string",
    "address_1": "string",
    "address_2": "string",
    "tel": "string",
    "mail_address": "string",
    "password": "string"
  },
  "kojin": [
    {
      "kojin_num": "integer",
      "name": "string",
      "kana": "string",
      "age": "integer",
      "death_date": "date",
      "death_place": "string"
    }
  ],
  "schedules": [
    {
      "schedule": "integer",
      "scheduled_datetime": "datetime",
      "hall_name": "string",
      "display_num": "integer"
    }
  ],
  "services": [
    {
      "service": "integer",
      "quantity": "integer",
      "unit_price": "integer"
    }
  ],
  "items": [
    {
      "item": "integer",
      "quantity": "integer",
      "unit_price": "integer"
    }
  ]
}
```

**パラメータ詳細:**

| フィールド | 型 | 必須 | 桁数制限 | 説明 | バリデーション |
|---|---|---|---|---|---|
| `soke_name` | string | ✓ | - | 葬家名 | - |
| `soke_kana` | string | ✓ | - | 葬家名カナ | カタカナ |
| `seko_style` | integer | ✓ | - | 施行形式ID | 存在する施行形式IDである必要がある |
| `seko_style_name` | string | ✓ | - | 施行形式表示名 | - |
| `death_date` | date | ✓ | - | 逝去日 | YYYY-MM-DD形式 |
| `seko_date` | datetime | ✓ | - | 施行日時 | ISO8601形式 |
| `fuho_site_end_date` | date | ✓ | - | 訃報サイト終了日 | YYYY-MM-DD形式 |
| `fuho_sentence` | string | ○ | - | 訃報文 | - |
| `fuho_contact_name` | string | ○ | - | 訃報連絡先名 | - |
| `fuho_contact_tel` | string | ○ | 15文字 | 訃報連絡先電話番号 | - |
| `henreihin_rate` | integer | ✓ | - | 返礼品率（%） | 0-100の範囲 |
| `order_staff` | integer | ✓ | - | 受注担当者ID | 存在するスタッフIDである必要がある |
| `seko_staff` | integer | ○ | - | 施行担当者ID | 存在するスタッフIDである必要がある |
| `af_staff` | integer | ○ | - | アフター担当者ID | 存在するスタッフIDである必要がある |
| `seko_company` | integer | ✓ | - | 施行会社ID | 存在する拠点IDである必要がある |
| `seko_department` | integer | ○ | - | 施行部門ID | 存在する拠点IDである必要がある |

**喪主（moshu）パラメータ:**

| フィールド | 型 | 必須 | 桁数制限 | 説明 | バリデーション |
|---|---|---|---|---|---|
| `name` | string | ✓ | - | 喪主名 | - |
| `kana` | string | ✓ | - | 喪主名カナ | カタカナ |
| `kojin_moshu_relationship` | string | ○ | - | 故人との続柄 | - |
| `zip_code` | string | ○ | 7文字 | 郵便番号 | ハイフンなし7桁 |
| `prefecture` | string | ○ | - | 都道府県 | - |
| `address_1` | string | ○ | - | 住所1 | - |
| `address_2` | string | ○ | - | 住所2 | - |
| `tel` | string | ○ | 15文字 | 電話番号 | - |
| `mail_address` | string | ○ | - | メールアドレス | 有効なメール形式 |
| `password` | string | ✓ | - | パスワード | ハッシュ化されて保存 |

**故人（kojin）パラメータ:**

| フィールド | 型 | 必須 | 桁数制限 | 説明 | バリデーション |
|---|---|---|---|---|---|
| `kojin_num` | integer | ✓ | - | 故人番号 | 1から開始の連番 |
| `name` | string | ✓ | - | 故人名 | - |
| `kana` | string | ✓ | - | 故人名カナ | カタカナ |
| `age` | integer | ○ | - | 享年 | 0-150の範囲 |
| `death_date` | date | ○ | - | 逝去日 | YYYY-MM-DD形式 |
| `death_place` | string | ○ | - | 逝去場所 | - |

**スケジュール（schedules）パラメータ:**

| フィールド | 型 | 必須 | 桁数制限 | 説明 | バリデーション |
|---|---|---|---|---|---|
| `schedule` | integer | ✓ | - | スケジュールマスターID | 存在するスケジュールIDである必要がある |
| `scheduled_datetime` | datetime | ✓ | - | 予定日時 | ISO8601形式 |
| `hall_name` | string | ○ | - | 会場名 | - |
| `display_num` | integer | ✓ | - | 表示順序 | 1から開始 |

### 1.4 バリデーション処理

#### 1.4.1 関連データ整合性チェック
```python
# 施行会社の権限チェック
user_bases = Base.objects.filter(pk=request.user.base_id).get_cached_trees()
if seko_company not in user_bases[0].get_ancestors(include_self=True):
    raise PermissionDenied("Denied operation by another company's user")
```

#### 1.4.2 ネストされたオブジェクト作成
```python
@transaction.atomic
def create(self, validated_data):
    moshu_data = validated_data.pop('moshu', {})
    kojin_data = validated_data.pop('kojin', [])
    schedules_data = validated_data.pop('schedules', [])
    
    instance = super().create(validated_data)
    
    # 喪主作成
    if moshu_data:
        Moshu.objects.create(seko=instance, **moshu_data)
    
    # 故人作成
    for kojin_dict in kojin_data:
        Kojin.objects.create(seko=instance, **kojin_dict)
    
    # スケジュール作成
    for schedule_dict in schedules_data:
        SekoSchedule.objects.create(seko=instance, **schedule_dict)
    
    return instance
```

### 1.5 レスポンス仕様

**成功時（201 Created）:**
```json
{
  "id": 123,
  "soke_name": "田中家",
  "soke_kana": "タナカケ",
  "seko_style": 1,
  "seko_style_name": "家族葬",
  "death_date": "2024-06-20",
  "seko_date": "2024-06-25T10:00:00+09:00",
  "fuho_site_end_date": "2024-07-25",
  "fuho_sentence": "田中一郎様のご逝去を悼み、謹んでお知らせいたします。",
  "fuho_contact_name": "田中 太郎",
  "fuho_contact_tel": "03-1234-5678",
  "henreihin_rate": 30,
  "note": "家族葬での実施",
  "order_staff": 5,
  "seko_staff": 10,
  "af_staff": 15,
  "seko_company": 1,
  "seko_department": 10,
  "del_flg": false,
  "created_at": "2024-06-27T10:00:00+09:00",
  "updated_at": "2024-06-27T10:00:00+09:00",
  "moshu": {
    "name": "田中 太郎",
    "kana": "タナカ タロウ",
    "kojin_moshu_relationship": "長男",
    "zip_code": "1000001",
    "prefecture": "東京都",
    "address_1": "千代田区千代田",
    "address_2": "1-1-1",
    "tel": "03-1234-5678",
    "mail_address": "<EMAIL>",
    "created_at": "2024-06-27T10:00:00+09:00",
    "updated_at": "2024-06-27T10:00:00+09:00"
  },
  "kojin": [
    {
      "kojin_num": 1,
      "name": "田中 一郎",
      "kana": "タナカ イチロウ",
      "age": 75,
      "death_date": "2024-06-20",
      "death_place": "自宅",
      "created_at": "2024-06-27T10:00:00+09:00",
      "updated_at": "2024-06-27T10:00:00+09:00"
    }
  ],
  "schedules": [
    {
      "id": 1,
      "schedule": 1,
      "scheduled_datetime": "2024-06-24T18:00:00+09:00",
      "hall_name": "セレモニーホール本館",
      "display_num": 1,
      "created_at": "2024-06-27T10:00:00+09:00",
      "updated_at": "2024-06-27T10:00:00+09:00"
    }
  ]
}
```

**エラー時（400 Bad Request）:**
```json
{
  "soke_name": ["This field is required."],
  "seko_style": ["Invalid pk \"999\" - object does not exist."],
  "moshu": {
    "mail_address": ["Enter a valid email address."]
  },
  "kojin": [
    {
      "age": ["Ensure this value is less than or equal to 150."]
    }
  ]
}
```

### 1.6 使用例

**リクエスト例:**
```bash
curl -X POST http://localhost:8000/seko/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "soke_name": "田中家",
    "soke_kana": "タナカケ",
    "seko_style": 1,
    "seko_style_name": "家族葬",
    "death_date": "2024-06-20",
    "seko_date": "2024-06-25T10:00:00+09:00",
    "fuho_site_end_date": "2024-07-25",
    "henreihin_rate": 30,
    "order_staff": 5,
    "seko_company": 1,
    "moshu": {
      "name": "田中 太郎",
      "kana": "タナカ タロウ",
      "kojin_moshu_relationship": "長男",
      "mail_address": "<EMAIL>",
      "password": "secure_password123"
    },
    "kojin": [
      {
        "kojin_num": 1,
        "name": "田中 一郎",
        "kana": "タナカ イチロウ",
        "age": 75,
        "death_date": "2024-06-20"
      }
    ],
    "schedules": [
      {
        "schedule": 1,
        "scheduled_datetime": "2024-06-24T18:00:00+09:00",
        "hall_name": "セレモニーホール本館",
        "display_num": 1
      }
    ]
  }'
```

## 2. 施行詳細API

### 2.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/PUT/PATCH/DELETE /seko/{id}/` |
| **機能** | 施行詳細取得・更新・削除 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `SekoDetail` |
| **シリアライザー** | `SekoRelatedSerializer` |

### 2.2 施行詳細取得

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 施行ID |

**レスポンス例:**
```json
{
  "id": 123,
  "soke_name": "田中家",
  "soke_kana": "タナカケ",
  "seko_style": {
    "id": 1,
    "style_name": "家族葬",
    "hoyo_style": {
      "id": 1,
      "style_name": "一般法要"
    }
  },
  "death_date": "2024-06-20",
  "seko_date": "2024-06-25T10:00:00+09:00",
  "fuho_site_admission_ts": "2024-06-21T09:00:00+09:00",
  "fuho_site_end_date": "2024-07-25",
  "fuho_sentence": "田中一郎様のご逝去を悼み、謹んでお知らせいたします。",
  "fuho_contact_name": "田中 太郎",
  "fuho_contact_tel": "03-1234-5678",
  "henreihin_rate": 30,
  "invoice_file_name": "data:application/pdf;base64,JVBERi0xLjQ...",
  "attached_file1": "data:application/pdf;base64,JVBERi0xLjQ...",
  "attached_file2": null,
  "attached_file3": null,
  "note": "家族葬での実施",
  "order_staff": 5,
  "seko_staff": 10,
  "af_staff": 15,
  "seko_company": {
    "id": 1,
    "base_name": "株式会社サンプル",
    "tokusho": {
      "responsible_name": "責任者名",
      "mail_address": "<EMAIL>"
    },
    "focfee": {
      "fee_rate": 0.05,
      "min_fee": 1000,
      "max_fee": 50000
    }
  },
  "seko_department": {
    "id": 10,
    "base_name": "営業部"
  },
  "moshu": {
    "name": "田中 太郎",
    "kana": "タナカ タロウ",
    "kojin_moshu_relationship": "長男",
    "zip_code": "1000001",
    "prefecture": "東京都",
    "address_1": "千代田区千代田",
    "address_2": "1-1-1",
    "tel": "03-1234-5678",
    "mail_address": "<EMAIL>",
    "agree_ts": "2024-06-21T10:00:00+09:00",
    "soke_site_del_request_flg": false,
    "soke_site_del_flg": false,
    "mail_flg": true
  },
  "seko_contact": {
    "name": "田中 花子",
    "kojin_relationship": "配偶者",
    "tel": "090-1234-5678",
    "mail_address": "<EMAIL>"
  },
  "kojin": [
    {
      "kojin_num": 1,
      "name": "田中 一郎",
      "kana": "タナカ イチロウ",
      "age": 75,
      "death_date": "2024-06-20",
      "death_place": "自宅"
    }
  ],
  "schedules": [
    {
      "id": 1,
      "schedule": {
        "id": 1,
        "schedule_name": "通夜"
      },
      "scheduled_datetime": "2024-06-24T18:00:00+09:00",
      "hall_name": "セレモニーホール本館",
      "display_num": 1
    },
    {
      "id": 2,
      "schedule": {
        "id": 2,
        "schedule_name": "告別式"
      },
      "scheduled_datetime": "2024-06-25T10:00:00+09:00",
      "hall_name": "セレモニーホール本館",
      "display_num": 2
    }
  ],
  "videos": [
    {
      "id": 1,
      "video_title": "思い出のビデオ",
      "video_url": "https://example.com/video1.mp4",
      "display_num": 1
    }
  ],
  "albums": [
    {
      "id": 1,
      "album_title": "思い出のアルバム",
      "images": [
        {
          "id": 1,
          "image_title": "家族写真",
          "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
          "display_num": 1
        }
      ]
    }
  ],
  "share_images": [
    {
      "id": 1,
      "image_title": "共有画像",
      "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
      "display_num": 1
    }
  ],
  "services": [
    {
      "id": 1,
      "service": {
        "id": 1,
        "service_name": "祭壇装飾"
      },
      "quantity": 1,
      "unit_price": 50000,
      "total_price": 50000
    }
  ],
  "items": [
    {
      "id": 1,
      "item": {
        "id": 1,
        "item_name": "花束"
      },
      "quantity": 3,
      "unit_price": 5000,
      "total_price": 15000
    }
  ],
  "henrei_kakegami": {
    "id": 1,
    "kakegami_title": "返礼品掛紙",
    "kakegami_content": "志"
  },
  "hoyo_seko": [
    {
      "id": 1,
      "hoyo_planned_date": "2024-12-25",
      "hoyo_activity_date": "2024-12-25",
      "hoyo_style": {
        "id": 1,
        "style_name": "一般法要"
      }
    }
  ],
  "seko_af": {
    "id": 1,
    "af_staff": {
      "id": 15,
      "name": "アフター 次郎"
    },
    "wishes": [
      {
        "id": 1,
        "af_type": 1,
        "wish_content": "年賀状停止"
      }
    ]
  },
  "del_flg": false,
  "created_at": "2024-06-27T10:00:00+09:00",
  "updated_at": "2024-06-27T10:00:00+09:00"
}
```

### 2.3 施行更新

**HTTPメソッド:** `PUT/PATCH`

**Content-Type:** `application/json`

**リクエストボディ（PUT）:**
```json
{
  "soke_name": "田中家（更新）",
  "soke_kana": "タナカケ",
  "seko_style": 1,
  "seko_style_name": "家族葬",
  "death_date": "2024-06-20",
  "seko_date": "2024-06-25T10:00:00+09:00",
  "fuho_site_end_date": "2024-07-25",
  "fuho_sentence": "田中一郎様のご逝去を悼み、謹んでお知らせいたします。（更新）",
  "henreihin_rate": 35,
  "note": "家族葬での実施（更新）",
  "moshu": {
    "name": "田中 太郎",
    "kana": "タナカ タロウ",
    "kojin_moshu_relationship": "長男",
    "tel": "03-1234-5679"
  },
  "kojin": [
    {
      "kojin_num": 1,
      "name": "田中 一郎",
      "kana": "タナカ イチロウ",
      "age": 75,
      "death_date": "2024-06-20",
      "death_place": "病院"
    }
  ]
}
```

**リクエストボディ（PATCH）:**
```json
{
  "henreihin_rate": 40,
  "note": "返礼品率を変更"
}
```

### 2.4 施行削除（論理削除）

**HTTPメソッド:** `DELETE`

**レスポンス:** `204 No Content`

**処理内容:**
```python
def destroy(self, request, *args, **kwargs):
    instance = self.get_object()
    instance.disable()  # del_flg = True に設定
    return Response(status=status.HTTP_204_NO_CONTENT)
```

## 3. 喪主ログインAPI

### 3.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /seko/login/` |
| **機能** | 喪主認証・JWTトークン発行 |
| **認証** | 不要 |
| **権限** | なし |
| **実装クラス** | `MoshuTokenObtainPair` |
| **シリアライザー** | `MoshuTokenObtainSerializer` |

### 3.2 喪主ログイン

**HTTPメソッド:** `POST`

**Content-Type:** `application/json`

**リクエストボディ:**
```json
{
  "seko_id": "integer",
  "password": "string"
}
```

**パラメータ詳細:**
| フィールド | 型 | 必須 | 説明 | バリデーション |
|---|---|---|---|---|
| `seko_id` | integer | ✓ | 施行ID | 存在する施行IDである必要がある |
| `password` | string | ✓ | パスワード | 喪主のパスワードと一致する必要がある |

**レスポンス例（成功時）:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzE5NDc0NjAwLCJpYXQiOjE3MTk0NzQzMDAsImp0aSI6IjEyMzQ1Njc4OTAiLCJ1c2VyX2lkIjoxMjMsInVzZXJfdHlwZSI6Im1vc2h1In0...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTcxOTU2MDcwMCwiaWF0IjoxNzE5NDc0MzAwLCJqdGkiOiIwOTg3NjU0MzIxIiwidXNlcl9pZCI6MTIzLCJ1c2VyX3R5cGUiOiJtb3NodSJ9...",
  "user": {
    "id": 123,
    "name": "田中 太郎",
    "kana": "タナカ タロウ",
    "kojin_moshu_relationship": "長男",
    "zip_code": "1000001",
    "prefecture": "東京都",
    "address_1": "千代田区千代田",
    "tel": "03-1234-5678",
    "mail_address": "<EMAIL>",
    "agree_ts": "2024-06-21T10:00:00+09:00",
    "soke_site_del_request_flg": false,
    "soke_site_del_flg": false,
    "mail_flg": true,
    "seko": {
      "id": 123,
      "soke_name": "田中家",
      "seko_style_name": "家族葬",
      "death_date": "2024-06-20",
      "seko_date": "2024-06-25T10:00:00+09:00",
      "fuho_site_end_date": "2024-07-25",
      "seko_contact": {
        "name": "田中 花子",
        "kojin_relationship": "配偶者"
      }
    }
  }
}
```

**エラー時（401 Unauthorized）:**
```json
{
  "detail": "No active account found with the given credentials"
}
```

**リクエスト例:**
```bash
curl -X POST http://localhost:8000/seko/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "seko_id": 123,
    "password": "moshu_password123"
  }'
```

## 4. 施行関連リソースAPI

### 4.1 故人管理API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST /seko/{seko_id}/kojin/` |
| **エンドポイント** | `GET/PUT/PATCH/DELETE /seko/{seko_id}/kojin/{id}/` |
| **機能** | 施行に関連する故人の管理 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `SekoRelatedKojinList`、`SekoRelatedKojinDetail` |
| **シリアライザー** | `KojinSerializer` |

#### 故人一覧取得・作成
**HTTPメソッド:** `GET/POST`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `seko_id` | integer | ✓ | 施行ID |

**リクエストボディ（POST）:**
```json
{
  "kojin_num": 2,
  "name": "田中 花子",
  "kana": "タナカ ハナコ",
  "age": 72,
  "death_date": "2024-06-20",
  "death_place": "病院"
}
```

### 4.2 スケジュール管理API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST /seko/{seko_id}/schedule/` |
| **エンドポイント** | `GET/PUT/PATCH/DELETE /seko/{seko_id}/schedule/{id}/` |
| **機能** | 施行スケジュールの管理 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `SekoRelatedScheduleList`、`SekoRelatedScheduleDetail` |
| **シリアライザー** | `SekoScheduleSerializer` |

#### スケジュール作成
**リクエストボディ（POST）:**
```json
{
  "schedule": 3,
  "scheduled_datetime": "2024-06-26T14:00:00+09:00",
  "hall_name": "火葬場",
  "display_num": 3
}
```

### 4.3 アルバム管理API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST /seko/{seko_id}/albums/` |
| **エンドポイント** | `GET/PUT/PATCH/DELETE /seko/{seko_id}/albums/{id}/` |
| **機能** | 施行アルバムの管理 |
| **認証** | JWT認証必須 |
| **権限** | IsMoshu \| ReadNeedAuth（喪主または認証済みユーザー） |
| **実装クラス** | `SekoRelatedAlbumList`、`SekoRelatedAlbumDetail` |
| **シリアライザー** | `SekoRelatedAlbumSerializer` |

#### アルバム作成
**リクエストボディ（POST）:**
```json
{
  "album_title": "思い出のアルバム",
  "images": [
    {
      "image_title": "家族写真1",
      "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
      "display_num": 1
    },
    {
      "image_title": "家族写真2",
      "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
      "display_num": 2
    }
  ]
}
```

### 4.4 共有画像管理API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST /seko/{seko_id}/share_images/` |
| **エンドポイント** | `GET/PUT/PATCH/DELETE /seko/{seko_id}/share_images/{id}/` |
| **機能** | 施行共有画像の管理 |
| **認証** | JWT認証必須 |
| **権限** | IsMoshu \| ReadNeedAuth（喪主または認証済みユーザー） |
| **実装クラス** | `SekoRelatedShareImageList`、`SekoRelatedShareImageDetail` |
| **シリアライザー** | `SekoRelatedShareImageSerializer` |

### 4.5 お問い合わせ管理API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST /seko/{seko_id}/inquiries/` |
| **機能** | 施行に関するお問い合わせの管理 |
| **認証** | JWT認証必須 |
| **権限** | IsMoshu \| ReadNeedAuth（喪主または認証済みユーザー） |
| **実装クラス** | `SekoInquiryList` |
| **シリアライザー** | `SekoInquirySerializer` |

#### お問い合わせ作成
**リクエストボディ（POST）:**
```json
{
  "inquiry_content": "返礼品の配送について質問があります。",
  "inquiry_type": 1
}
```

#### お問い合わせ回答API
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST /seko/inquiries/{inquiry_id}/answers/` |
| **機能** | お問い合わせへの回答管理 |
| **実装クラス** | `SekoAnswerList` |
| **シリアライザー** | `SekoAnswerSerializer` |

**回答作成リクエスト:**
```json
{
  "answer_content": "返礼品は施行後3営業日以内に発送いたします。",
  "staff": 10
}
```

## 5. 喪主管理API

### 5.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /seko/moshu/` |
| **エンドポイント** | `GET/PUT/PATCH /seko/moshu/{id}/` |
| **機能** | 喪主一覧取得・詳細取得・更新 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `MoshuList`、`MoshuDetail` |
| **シリアライザー** | `SimpleMoshuSerializer` |

### 5.2 喪主一覧取得

**HTTPメソッド:** `GET`

**クエリパラメータ:**
| パラメータ | 型 | 必須 | 説明 | 使用例 |
|---|---|---|---|---|
| `prefecture` | string | ○ | 都道府県（部分一致） | `?prefecture=東京` |
| `address_1` | string | ○ | 住所1（部分一致） | `?address_1=千代田` |
| `af_type` | integer[] | ○ | アフタータイプ | `?af_type=1,2,3` |
| `company` | integer | ○ | 会社ID | `?company=1` |

**レスポンス例:**
```json
{
  "count": 50,
  "next": "http://localhost:8000/seko/moshu/?page=2",
  "previous": null,
  "results": [
    {
      "id": 123,
      "name": "田中 太郎",
      "kana": "タナカ タロウ",
      "kojin_moshu_relationship": "長男",
      "zip_code": "1000001",
      "prefecture": "東京都",
      "address_1": "千代田区千代田",
      "address_2": "1-1-1",
      "tel": "03-1234-5678",
      "mail_address": "<EMAIL>",
      "agree_ts": "2024-06-21T10:00:00+09:00",
      "soke_site_del_request_flg": false,
      "soke_site_del_flg": false,
      "mail_flg": true,
      "seko": {
        "id": 123,
        "soke_name": "田中家",
        "seko_style_name": "家族葬",
        "death_date": "2024-06-20",
        "seko_date": "2024-06-25T10:00:00+09:00",
        "seko_contact": {
          "name": "田中 花子",
          "kojin_relationship": "配偶者"
        }
      }
    }
  ]
}
```

### 5.3 喪主承認API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /seko/soke/{id}/approve/` |
| **機能** | 喪主の承認処理 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `MoshuApproval` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "approve": true,
  "note": "承認しました"
}
```

## 6. FDN連携API

### 6.1 FDN施行作成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /seko/fdn/` |
| **機能** | FDNシステムからの施行データ作成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `FdnSekoCreater` |
| **シリアライザー** | `FdnSekoCreateSerializer` |

**リクエストボディ:**
```json
{
  "soke_name": "田中家",
  "soke_kana": "タナカケ",
  "seko_style": 1,
  "seko_style_name": "家族葬",
  "death_date": "2024-06-20",
  "seko_date": "2024-06-25T10:00:00+09:00",
  "fuho_site_end_date": "2024-07-25",
  "henreihin_rate": 30,
  "seko_company": 1,
  "moshu": {
    "name": "田中 太郎",
    "kana": "タナカ タロウ",
    "password": "fdn_generated_password"
  },
  "kojin": [
    {
      "kojin_num": 1,
      "name": "田中 一郎",
      "kana": "タナカ イチロウ",
      "age": 75
    }
  ],
  "schedules": [
    {
      "schedule": 1,
      "scheduled_datetime": "2024-06-24T18:00:00+09:00",
      "hall_name": "セレモニーホール本館",
      "display_num": 1
    }
  ]
}
```

### 6.2 FDN施行更新API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `PUT/PATCH /seko/fdn/{id}/` |
| **機能** | FDNシステムからの施行データ更新 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `FdnSekoUpdate` |
| **シリアライザー** | `FdnSekoUpdateSerializer` |

## 7. ユーティリティAPI

### 7.1 QRコード生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /seko/{id}/qrcode/` |
| **機能** | 施行用QRコードPDF生成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `QRCodePdf` |

**レスポンス:** PDF形式のQRコード

### 7.2 メッセージPDF生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /seko/{id}/msg_pdf/` |
| **機能** | 弔文メッセージPDF生成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `MsgPDF` |

### 7.3 ファイル通知API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /seko/{id}/notice_attached_file/` |
| **機能** | 添付ファイルの通知 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `NoticeAttachedFile` |

## 8. エラーコード一覧

| ステータスコード | エラーメッセージ | 発生条件 |
|---|---|---|
| 400 | This field is required. | 必須フィールドが未入力 |
| 400 | Invalid pk "999" - object does not exist. | 存在しないIDを指定 |
| 400 | Ensure this value is less than or equal to 150. | 年齢の上限超過 |
| 400 | Enter a valid email address. | 無効なメールアドレス形式 |
| 401 | Authentication credentials were not provided. | 認証情報なし |
| 401 | No active account found with the given credentials | 喪主ログイン失敗 |
| 403 | You do not have permission to perform this action. | 権限不足 |
| 403 | Denied operation by another company's user | 他社データへのアクセス拒否 |
| 404 | Not found. | 指定されたリソースが存在しない |

## 9. セキュリティ考慮事項

### 9.1 データ保護
- **論理削除**: 物理削除ではなく論理削除でデータ保護
- **会社間分離**: 会社コードによるマルチテナント対応
- **権限制御**: スタッフと喪主の適切な権限分離

### 9.2 認証・認可
- **JWT認証**: アクセストークン（5分）とリフレッシュトークン（1日）
- **喪主認証**: 施行IDとパスワードによる認証
- **権限クラス**: IsStaff、IsMoshu、ReadNeedAuth

### 9.3 パフォーマンス最適化
- **select_related**: 外部キーの効率的な取得
- **prefetch_related**: 関連オブジェクトの効率的な取得
- **フィルタリング**: インデックスを活用した効率的な検索

### 9.4 データ整合性
- **トランザクション**: @transaction.atomicによる整合性保証
- **外部キー制約**: 関連データの存在チェック
- **バリデーション**: シリアライザーレベルでの詳細チェック
