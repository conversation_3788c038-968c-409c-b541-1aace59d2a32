# Generated by Django 3.1.2 on 2020-10-30 05:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('suppliers', '0001_initial'),
        ('items', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ItemSupplier',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'default_supplier_flg',
                    models.BooleanField(default=False, verbose_name='default supplier flg'),
                ),
                (
                    'item',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='item_supplier',
                        to='items.item',
                        verbose_name='item',
                    ),
                ),
                (
                    'supplier',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='item_supplier',
                        to='suppliers.supplier',
                        verbose_name='supplier',
                    ),
                ),
            ],
            options={
                'db_table': 'm_item_supplier',
            },
        ),
        migrations.AddField(
            model_name='item',
            name='suppliers',
            field=models.ManyToManyField(
                blank=True,
                null=True,
                related_name='items',
                through='items.ItemSupplier',
                to='suppliers.Supplier',
                verbose_name='',
            ),
        ),
    ]
