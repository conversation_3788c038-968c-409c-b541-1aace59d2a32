
<div class="container">
  <div class="inner">
    <div class="contents header">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="contents with-background-image description">
      <h2>供花・供物を送る</h2>
      <div class="pre-wrap">{{description}}</div>
      <div class="remark">
        ※当サイトでは<span class="important">各種割引サービス（会員割引、企業割引等）はご利用いただけません。</span><br>
        &nbsp;&nbsp;ご希望の場合は、直接葬儀社様へお問合せ下さい。<br>
        ※サービスの性質上、<span class="important">お申し込み完了後のキャンセルは出来ません。</span>ご注意ください。<br>
        ※領収書に関しては、ご注文完了メール内のURLからダウンロードいただき印刷が可能です。<br>
      </div>
      <div class="seko_company">
        葬儀に関するお問い合わせ<br>
        &nbsp;&nbsp;{{seko_info.fuho_contact_name}} <br>
        &nbsp;&nbsp;TEL：{{seko_info.fuho_contact_tel}} 
      </div>
      <div class="seko_company">
        操作に関するお問い合わせ<br>
        &nbsp;&nbsp;葬儀オンラインサービスこころサポートセンター <br>
        &nbsp;&nbsp;TEL：0120-691-484 <br>
        &nbsp;&nbsp;営業時間：09:00～21:00/年中無休
      </div>
      <div class="data-item">
        <div class="label">
          決済方法
        </div>
        <div class="data">
          クレジットカード決済
        </div>
      </div>
      <div class="title">ご利用できるクレジットカード</div>
      <div class="card-images">
        <div class="visa" *ngIf="utils.canUseCredit(seko_info, 1)"></div>
        <div class="master" *ngIf="utils.canUseCredit(seko_info, 2)"></div>
        <div class="jcb" *ngIf="utils.canUseCredit(seko_info, 3)"></div>
        <div class="amex" *ngIf="utils.canUseCredit(seko_info, 4)"></div>
        <div class="diners" *ngIf="utils.canUseCredit(seko_info, 5)"></div>
      </div>
      <div class="notice">
        <span class="important">【重要なお知らせ】3Dセキュア（本人認証サービス）対応のお願い<br>
このたび、クレジットカード決済の安全性向上を目的として、2025年4月1日より「3Dセキュア（本人認証サービス）」の登録が義務化されました。<br>
これにより、当サイトにてクレジットカードをご利用いただく際には、3Dセキュアに対応したカード、および本人認証の登録が必須となります。<br>
※全てのご注文で本人認証が求められるわけではなく、カード会社の判断によりワンタイムパスワード等の追加認証が行われます。<br>
【ご注意】<br>
※「3Dセキュア2.0」非対応のクレジットカードはご利用になれません。対応状況についてはカード発行会社にご確認ください。<br>
※本人認証の方法や、表示される本人認証画面の使い方は、カード会社により異なります。<br>
　詳しくはカード発行会社にご確認ください。<br>
何卒、ご理解とご協力を賜りますようお願い申し上げます。</span>
      </div>
      <div class="data-item" *ngIf="!isExpierd() || !item_list?.length">
        <div class="label">
          締切
        </div>
        <div class="data" *ngIf="service?.display_limit_ts">
          {{service.display_limit_ts}}まで
        </div>
      </div>
      <div class="message-area" *ngIf="message">
        {{message}}
      </div>
    </div>
    <ng-container *ngIf="!message && item_list?.length">
    <div class="navigation">
      <div class="item current"><span>供花・供物</span><span>選択</span></div>
      <div class="arrow"></div>
      <div class="item"><span>カート</span><span>内容確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>注文</span><span>手続き</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込み</span><span>完了</span></div>
    </div>
    <div class="contents" *ngIf="kumotsu_edit">
      <h2>供花・供物選択</h2>
      <div class="input-area">
        <div class="line">
          <div class="label required">供花・供物</div>
          <div class="input item" #itemEm [class.error]="isErrorField(itemEm)">
            <!--a class="button grey" (click)="showItemList()">供花・供物を選ぶ</a-->
            <div class="item-box" (click)="showItemList()">
              <div class="title-box" *ngIf="selected_item">
                <div class="selecting">選択中</div>
                <div class="name">{{selected_item.name}}</div>
                <div class="price">{{selected_item.item_price | number}}円(税込)</div>
              </div>
              <div class="title-box" *ngIf="!selected_item">
                <div class="selecting no-selected">未選択</div>
                <div class="name"></div>
                <div class="price"></div>
              </div>
              <div class="image-box" [class.no-image]="!selected_item?.image_file">
                <img [src]="selected_item?.image_file" *ngIf="selected_item?.image_file" (error)="imageLoadError(selected_item)">
                <ng-container *ngIf="!selected_item?.image_file">
                  <div class="no-image">
                    <i class="image icon huge"></i>
                    <div class="noimage">No image</div>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
        <div class="line">
          <div class="label required">数量</div>
          <div class="input quantity" #quantityEm [class.error]="isErrorField(quantityEm)">
            <com-dropdown class="mini" [settings]="quantityCombo" [(selectedValue)]="kumotsu_edit.quantity"  (selectedItemChange)="quantityChange($event)"></com-dropdown>
          </div>
        </div>
      </div>
      <h2>名札</h2>
      <div class="input-area">
        <div class="line">
          <div class="label">組織名</div>
          <div class="input">
            <input type="text" placeholder="てれ葬儀こころ" maxlength="25" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_company">
          </div>
        </div>
        <div class="line">
          <div class="label">役職名</div>
          <div class="input">
            <input type="text" placeholder="課長" maxlength="15" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_title">
          </div>
        </div>
        <div class="line">
          <div class="label">組織・役職カナ</div>
          <div class="input">
            <input type="text" placeholder="テレソウギココロ　カチョウ" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_company_kana">
          </div>
        </div>
        <div class="line">
          <div class="label required">氏名</div>
          <div class="input okurinushi_name" #okurinushiNameEm [class.error]="isErrorField(okurinushiNameEm)">
            <input type="text" placeholder="田中　太郎" maxlength="15" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_name">
          </div>
        </div>
        <div class="description">
          ※お名前に旧漢字・機種依存文字等が入っていると決済時にエラーが発生する場合があります。<BR>
          漢字がエラーになる場合は別の漢字やひらがな等で登録し、備考に内容（例：高⇒はしごのたかいです）をご入力ください。
        </div>
        <div class="line">
          <div class="label required">氏名カナ</div>
          <div class="input okurinushi_kana" #okurinushiKanaEm [class.error]="isErrorField(okurinushiKanaEm)">
            <input type="text" placeholder="タナカ　タロウ" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_kana">
          </div>
        </div>
        <div class="line">
          <div class="label required">電話番号</div>
          <div class="input okurinushi_tel" #okurinushiTelEm [class.error]="isErrorField(okurinushiTelEm)">
            <input type="tel" placeholder="03-1234-5678" autocomplete="off" maxlength="15" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_tel">
          </div>
        </div>
        <div class="line">
          <div class="label required">郵便番号</div>
          <div class="input okurinushi_zip_code" #okurinushiZipCodeEm [class.error]="isErrorField(okurinushiZipCodeEm)">
            <input type="tel" placeholder="1234567" autocomplete="off" maxlength="7" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_zip_code" (input)="zipcodeChange()">
          </div>
        </div>
        <div class="line">
          <div class="label required">都道府県</div>
          <div class="input okurinushi_prefecture" #okurinushiPrefectureEm [class.error]="isErrorField(okurinushiPrefectureEm)">
            <input type="text" placeholder="東京都" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_prefecture">
          </div>
        </div>
        <div class="line">
          <div class="label required">市区町村</div>
          <div class="input okurinushi_address_1" #okurinushiAddress1Em [class.error]="isErrorField(okurinushiAddress1Em)">
            <input type="text" placeholder="新宿区新宿" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_address_1">
          </div>
        </div>
        <div class="line">
          <div class="label required">所番地</div>
          <div class="input okurinushi_address_2" #okurinushiAddress2Em [class.error]="isErrorField(okurinushiAddress2Em)">
            <input type="text" placeholder="1-1-1" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_address_2">
          </div>
        </div>
        <div class="line">
          <div class="label">建屋名</div>
          <div class="input">
            <input type="text" placeholder="新宿こころビル１階" [(ngModel)]="kumotsu_edit.kumotsu.okurinushi_address_3">
          </div>
        </div>
        <div class="line">
          <div class="label">連名１</div>
          <div class="input">
            <input type="text" maxlength="15" [(ngModel)]="kumotsu_edit.kumotsu.renmei1">
          </div>
        </div>
        <div class="line">
          <div class="label">連名１カナ</div>
          <div class="input">
            <input type="text" [(ngModel)]="kumotsu_edit.kumotsu.renmei_kana1">
          </div>
        </div>
        <div class="line">
          <div class="label">連名２</div>
          <div class="input">
            <input type="text" maxlength="15" [(ngModel)]="kumotsu_edit.kumotsu.renmei2">
          </div>
        </div>
        <div class="line">
          <div class="label">連名２カナ</div>
          <div class="input">
            <input type="text" [(ngModel)]="kumotsu_edit.kumotsu.renmei_kana2">
          </div>
        </div>
        <div class="line">
          <div class="label">備考</div>
          <div class="input">
            <textarea rows="3" [(ngModel)]="kumotsu_edit.kumotsu.note"></textarea>
          </div>
        </div>
        <div class="line">
          <a class="button red center" (click)="showFudaPreview()">立札イメージ</a>
        </div>
      </div>
    </div>
    </ng-container>

    <div class="ui modal item" id="item-list">
      <div class="content scrolling">
        <ng-container *ngFor="let item of item_list">
          <div class="item-box" (click)="selectItem(item)">
            <div class="title-box">
              <div class="name">{{item.name}}</div>
              <div class="price">{{item.item_price | number}}円(税込)</div>
            </div>
            <div class="image-box" [class.selected]="item.selected" [class.no-image]="!item.image_file">
              <img [src]="item.image_file" *ngIf="item.image_file" (error)="imageLoadError(item)">
              <ng-container *ngIf="!item.image_file">
                <div class="no-image">
                  <i class="image icon huge"></i>
                  <div class="noimage">No image</div>
                </div>
              </ng-container>

            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <div class="ui modal fuda" id="fuda-preview">
      <div class="content scrolling">
        <div class="tatefuda">
          <div class="contents okurinushi_company" *ngIf="kumotsu_edit?.kumotsu?.okurinushi_company">{{kumotsu_edit.kumotsu.okurinushi_company}}</div>
          <div class="contents okurinushi_title" *ngIf="kumotsu_edit?.kumotsu?.okurinushi_title">{{kumotsu_edit.kumotsu.okurinushi_title}}</div>
          <div class="contents okurinushi_name" *ngIf="kumotsu_edit?.kumotsu?.okurinushi_name">{{kumotsu_edit.kumotsu.okurinushi_name}}</div>
          <div class="contents renmei1" *ngIf="kumotsu_edit?.kumotsu?.renmei1">{{kumotsu_edit.kumotsu.renmei1}}</div>
          <div class="contents renmei2" *ngIf="kumotsu_edit?.kumotsu?.renmei2">{{kumotsu_edit.kumotsu.renmei2}}</div>
        </div>
        <div class="description">
          ※画像はイメージです。<br>
          実物は会場などによって変化します。
        </div>
      </div>
    </div>

    <div class="button-area">
      <a class="button" (click)="back()">< TOPへ戻る</a>
      <a class="button grey" (click)="saveData()" *ngIf="!message">{{kumotsu_edit?.index>=0?'カート内容を変更する':'カートに入れる'}} ></a>
    </div>
  </div>
</div>
