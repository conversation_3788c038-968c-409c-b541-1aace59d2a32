from django.db import transaction
from drf_extra_fields.fields import Base64I<PERSON><PERSON>ield
from rest_framework import serializers
from rest_framework.validators import UniqueTogetherValidator

from advertises.serializers import AdvertiseSerializer
from bases.models import Base, Foc<PERSON>ee, SmsAccount, Tokusho
from masters.models import ChobunDaishiMaster, Service, SokeMenu
from masters.serializers import ChobunDaishiMasterSerializer, ServiceSerializer, SokeMenuSerializer
from service_reception_terms.serializers import ServiceReceptionTermSerializer
from utils.serializer_mixins import AddUserIdMixin


class TokushoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tokusho
        exclude = ['company']

    def create(self, validated_data):
        validated_data['company'] = self.context.get('base')
        return super().create(validated_data)


class FocFeeSerializer(AddUserIdMixin, serializers.ModelSerializer):
    class Meta:
        model = FocFee
        exclude = ['company']
        read_only_fields = ['created_user_id', 'created_at', 'update_user_id', 'updated_at']

    def create(self, validated_data):
        validated_data['company'] = self.context.get('base')
        return super().create(validated_data)

    def update(self, instance, validated_data):
        validated_data['company'] = self.context.get('base')
        return super().update(instance, validated_data)


class SmsAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = SmsAccount
        exclude = ['company']

    def create(self, validated_data):
        validated_data['company'] = self.context.get('base')
        return super().create(validated_data)

    def update(self, instance, validated_data):
        validated_data['company'] = self.context.get('base')
        return super().update(instance, validated_data)


class BaseSerializer(AddUserIdMixin, serializers.ModelSerializer):
    company_logo_file = Base64ImageField(required=False, allow_null=True)
    company_map_file = Base64ImageField(required=False, allow_null=True)
    create_user_id = serializers.IntegerField(required=False)
    update_user_id = serializers.IntegerField(required=False)
    chobun_daishi_master_ids = serializers.PrimaryKeyRelatedField(
        required=False,
        many=True,
        queryset=ChobunDaishiMaster.objects.all(),
        source='chobun_daishi_masters',
    )
    service_ids = serializers.PrimaryKeyRelatedField(
        required=False,
        many=True,
        queryset=Service.objects.all(),
        source='services',
    )
    soke_menu_ids = serializers.PrimaryKeyRelatedField(
        required=False,
        many=True,
        queryset=SokeMenu.objects.all(),
        source='soke_menus',
    )
    service_reception_terms = ServiceReceptionTermSerializer(
        many=True, required=False, allow_null=True
    )

    class Meta:
        model = Base
        fields = [
            'id',
            'base_type',
            'company_code',
            'base_name',
            'zip_code',
            'prefecture',
            'address_1',
            'address_2',
            'address_3',
            'tel',
            'fax',
            'company_logo_file',
            'company_map_file',
            'calc_type',
            'display_num',
            'del_flg',
            'created_at',
            'create_user_id',
            'updated_at',
            'update_user_id',
            'parent',
            'chobun_daishi_master_ids',
            'service_ids',
            'soke_menu_ids',
            'service_reception_terms',
            'fdn_code',
            'order_mail_address',
        ]
        read_only_fields = ['created_user_id', 'created_at', 'update_user_id', 'updated_at']
        validators = [
            UniqueTogetherValidator(
                queryset=Base.objects.filter(del_flg=False, fdn_code__isnull=False).all(),
                fields=['company_code', 'fdn_code'],
            )
        ]

    @transaction.atomic
    def create(self, validated_data):
        update_reception_terms = 'service_reception_terms' in validated_data
        service_reception_terms = validated_data.pop('service_reception_terms', [])
        instance = super().create(validated_data)
        if not self.partial or update_reception_terms:
            self.update_service_reception_terms(instance, service_reception_terms)

        return instance

    @transaction.atomic
    def update(self, instance, validated_data):
        if not self.partial:
            validated_data['company_logo_file'] = validated_data.get('company_logo_file')
            validated_data['company_map_file'] = validated_data.get('company_map_file')

        update_reception_terms = 'service_reception_terms' in validated_data
        service_reception_terms = validated_data.pop('service_reception_terms', [])
        instance = super().update(instance, validated_data)
        if not self.partial or update_reception_terms:
            self.update_service_reception_terms(instance, service_reception_terms)

        return instance

    def update_service_reception_terms(self, instance, service_reception_terms):
        if service_reception_terms is None:
            service_reception_terms = []

        olds = {
            service_reception_term.pk: service_reception_term
            for service_reception_term in instance.service_reception_terms.all()
        }

        for service_reception_term in service_reception_terms:
            service_reception_term['department'] = instance.pk
            service_reception_term['service'] = service_reception_term['service'].pk
            service_reception_term['schedule'] = service_reception_term['schedule'].pk

            id = service_reception_term.get('id', None)
            if id in olds:
                serializer = ServiceReceptionTermSerializer(
                    olds[id], data=service_reception_term, context=self.context
                )
                del olds[id]
            else:
                serializer = ServiceReceptionTermSerializer(
                    data=service_reception_term, context=self.context
                )

            serializer.is_valid(raise_exception=True)
            serializer.save()

        for old in olds.values():
            old.delete()


class BaseTokuFeeSerializer(BaseSerializer):
    tokusho = TokushoSerializer()
    focfee = FocFeeSerializer()
    chobun_daishi_masters = ChobunDaishiMasterSerializer(many=True, read_only=True)
    soke_menus = SokeMenuSerializer(many=True, read_only=True)
    advertises = AdvertiseSerializer(many=True)

    class Meta(BaseSerializer.Meta):
        fields = BaseSerializer.Meta.fields + [
            'tokusho',
            'focfee',
            'chobun_daishi_masters',
            'soke_menus',
            'advertises',
        ]


class BaseDownwardSerializer(serializers.ModelSerializer):
    tokusho = TokushoSerializer()
    focfee = FocFeeSerializer()
    sms_account = SmsAccountSerializer()
    services = ServiceSerializer(many=True, read_only=True)
    soke_menus = SokeMenuSerializer(many=True, read_only=True)
    unprinted_chobun_count = serializers.ReadOnlyField(source='get_unprinted_chobun_count')
    kumotsu_count = serializers.ReadOnlyField(source='get_kumotsu_count')
    henrei_kumotsu_count = serializers.ReadOnlyField(source='get_henrei_kumotsu_count')
    henrei_koden_count = serializers.ReadOnlyField(source='get_henrei_koden_count')
    seko_inquiries_count = serializers.ReadOnlyField(source='seko_inquiries')

    class Meta:
        model = Base
        fields = [
            'id',
            'base_type',
            'company_code',
            'base_name',
            'zip_code',
            'prefecture',
            'address_1',
            'address_2',
            'address_3',
            'tel',
            'fax',
            'company_logo_file',
            'company_map_file',
            'calc_type',
            'display_num',
            'del_flg',
            'created_at',
            'create_user_id',
            'updated_at',
            'update_user_id',
            'tokusho',
            'focfee',
            'sms_account',
            'parent',
            'children',
            'services',
            'soke_menus',
            'unprinted_chobun_count',
            'kumotsu_count',
            'henrei_kumotsu_count',
            'henrei_koden_count',
            'seko_inquiries_count',
            'fdn_code',
            'order_mail_address',
        ]
        read_only_fields = ['created_user_id', 'created_at', 'update_user_id', 'updated_at']


BaseDownwardSerializer._declared_fields['children'] = BaseDownwardSerializer(
    source='active_children', many=True
)


class BaseUpwardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Base
        exclude = ['lft', 'rght', 'level', 'tree_id']


BaseUpwardSerializer._declared_fields['parent'] = BaseUpwardSerializer()


class BaseInquirySerializer(serializers.Serializer):
    name = serializers.CharField()
    kana = serializers.CharField()
    tel = serializers.CharField(required=False, allow_null=True)
    mail_address = serializers.EmailField()
    content = serializers.CharField()
