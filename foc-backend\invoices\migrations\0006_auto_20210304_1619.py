# Generated by Django 3.1.5 on 2021-03-04 07:19

from pathlib import Path

from django.db import migrations


def is_postgres(schema_editor) -> bool:
    return schema_editor.connection.vendor.startswith('postgres')


def forwards(apps, schema_editor):
    if not is_postgres(schema_editor):
        return

    with open(Path('invoices/migrations/salescalc_v3.sql'), encoding='utf-8') as f:
        ddl: str = f.read()
    schema_editor.execute(ddl.replace('%', '%%'), params=[])


def backwards(apps, schema_editor):
    if not is_postgres(schema_editor):
        return

    with open(Path('invoices/migrations/salescalc_v2.sql'), encoding='utf-8') as f:
        ddl: str = f.read()
    schema_editor.execute(ddl.replace('%', '%%'), params=[])


class Migration(migrations.Migration):

    dependencies = [
        ('invoices', '0005_auto_20210108_1857'),
    ]

    operations = [migrations.RunPython(forwards, backwards, atomic=True)]
