import os.path

from django.core.files.storage import default_storage
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from drf_extra_fields.fields import Base64<PERSON><PERSON><PERSON>ield
from drf_extra_fields.relations import PresentablePrimaryKeyRelatedField
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework.validators import UniqueTogetherValidator

from items.models import Item, ItemSupplier
from masters.models import Tax
from masters.serializers import ServiceSerializer, TaxSerializer
from suppliers.models import Supplier
from suppliers.serializers import SupplierSerializer
from utils.serializer_mixins import AddUserIdMixin


class ItemListSerializer(AddUserIdMixin, serializers.ListSerializer):
    @transaction.atomic
    def bulk_save(self):
        for data in self.initial_data:
            if not data['hinban']:
                raise ValidationError({'hinban': _('This field is required.')}, code='required')
            instance = Item.objects.filter(
                base_id=data['base'], hinban=data['hinban'], del_flg=False
            ).first()
            serializer = ItemSerializer(instance=instance, data=data, context=self.context)
            serializer.is_valid(raise_exception=True)
            serializer.save()


class ItemSupplierSerializer(serializers.ModelSerializer):
    supplier = SupplierSerializer(read_only=True)

    class Meta:
        model = ItemSupplier
        exclude = ['item']


class ItemSerializer(AddUserIdMixin, serializers.ModelSerializer):
    create_user_id = serializers.IntegerField(required=False)
    update_user_id = serializers.IntegerField(required=False)
    supplier_ids = serializers.PrimaryKeyRelatedField(
        required=False,
        many=True,
        queryset=Supplier.objects.all(),
        source='suppliers',
        write_only=True,
    )
    tax = PresentablePrimaryKeyRelatedField(
        queryset=Tax.objects.all(), presentation_serializer=TaxSerializer
    )
    item_suppliers = ItemSupplierSerializer(many=True, read_only=True)

    class Meta:
        model = Item
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']
        validators = [
            UniqueTogetherValidator(
                queryset=Item.objects.filter(del_flg=False, hinban__isnull=False).all(),
                fields=['base', 'hinban'],
            ),
            # UniqueTogetherValidator(
            #     queryset=Item.objects.filter(del_flg=False, fdn_code__isnull=False).all(),
            #     fields=['base', 'fdn_code'],
            # ),
        ]
        list_serializer_class = ItemListSerializer

    @transaction.atomic
    def save(self, **kwargs):
        instanse = super().save()
        default_supplier = self.initial_data.get('default_supplier')
        if default_supplier:
            item_supplier: ItemSupplier = ItemSupplier.objects.filter(
                item=instanse.pk, supplier=default_supplier
            ).get()
            item_supplier.default_supplier_flg = True
            item_supplier.save()


class ItemWithServiceSerializer(serializers.ModelSerializer):
    service = ServiceSerializer(required=False)

    class Meta:
        model = Item
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']


class ItemImportImageSerializer(serializers.Serializer):
    filename = serializers.CharField()
    file_content = Base64ImageField()

    def create(self, validated_data):
        filename = validated_data['filename']
        content = validated_data['file_content']

        filepath = os.path.join('items', str(self.context['base_id']), filename)
        if default_storage.exists(filepath):
            default_storage.delete(filepath)
        default_storage.save(filepath, content.file)
        return None
