
<div class="container">
  <div class="inner">
    <div class="contents">
      <div class="menu_title">
        <i class="award icon big"></i>
        香典受付一覧
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(companyComboEm, sekoBaseComboEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchKoden()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="form_data.company_id" (selectedItemChange)="companyChange($event, sekoBaseComboEm)"></com-dropdown>
          <label>施行拠点</label>
          <com-dropdown #sekoBaseComboEm [settings]="sekoBaseCombo" [(selectedValue)]="form_data.seko_department"></com-dropdown>
          <label>申込番号</label>
          <div class="ui input small">
            <input type="tel" [(ngModel)]="form_data.entry_id">
          </div>
          <label class="large">申込者名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.entry_name">
          </div>
          <label>申込者電話番号</label>
          <div class="ui input small">
            <input type="tel" [(ngModel)]="form_data.entry_tel">
          </div>
        </div>
        <div class="line">
          <label>葬家名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.soke_name">
          </div>
          <label>故人名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.kojin_name">
          </div>
          <label>喪主名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.moshu_name">
          </div>
          <label class="large">キャンセル状況</label>
          <div class="ui checkbox" (click)="checkClick('not_canceled', form_data.canceled)">
            <input type="checkbox" name="not_canceled" [(ngModel)]="form_data.not_canceled">
            <label>未</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('canceled', form_data.not_canceled)">
            <input type="checkbox" name="canceled" [(ngModel)]="form_data.canceled">
            <label>済</label>
          </div>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="koden_list?.length">
          全{{koden_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?koden_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="entry_id">申込番号</th>
              <th class="entry_name">申込者名</th>
              <th class="entry_tel"><p>申込者</p>電話番号</th>
              <th class="seko_date"><p>施行日</p>施行拠点</th>
              <th class="soke_name">葬家名</th>
              <th class="kojin_name"><p>故人名</p>喪主名</th>
              <th class="koden_price">香典額</th>
              <th class="henreihin_name">返礼品名</th>
              <th class="henreihin_price"><p>返礼品</p>金額</th>
              <th class="henreihin_selected"><p>返礼品</p>選択</th>
              <th class="entry_ts">申込日時</th>
              <th class="is_cancel">キャンセル</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let koden of koden_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
              <td class="center aligned entry_id" title="{{koden.entry_detail.entry.id}}">{{koden.entry_detail.entry.id}}</td>
              <td class="entry_name" title="{{koden.entry_detail.entry.entry_name}}">{{koden.entry_detail.entry.entry_name}}</td>
              <td class="entry_tel" title="{{koden.entry_detail.entry.entry_tel}}">{{koden.entry_detail.entry.entry_tel}}</td>
              <td class="center aligned seko_date">
                <div title="{{koden.entry_detail.entry.seko.seko_date | date: 'yyyy/MM/dd'}}">{{koden.entry_detail.entry.seko.seko_date | date: 'yyyy/MM/dd'}}</div>
                <div title="{{koden.entry_detail.entry.seko.seko_department_name}}">{{koden.entry_detail.entry.seko.seko_department_name}}</div>
              </td>
              <td class="soke_name" title="{{koden.entry_detail.entry.seko.soke_name}}">{{koden.entry_detail.entry.seko.soke_name}}</td>
              <td class="kojin_name">
                <div title="{{koden.entry_detail.entry.seko.kojin[0].name}}">{{koden.entry_detail.entry.seko.kojin[0].name}}</div>
                <div title="{{koden.entry_detail.entry.seko.moshu.name}}">{{koden.entry_detail.entry.seko.moshu.name}}</div>
              </td>
              <td class="right aligned koden_price" title="{{koden.entry_detail.item_unit_price | number}}">
                {{koden.entry_detail.item_unit_price | number}}</td>
              <td class="henreihin_name" title="{{koden.henrei_koden?.henreihin_name}}">{{koden.henrei_koden?.henreihin_name}}</td>
              <td class="right aligned henreihin_price" title="{{koden.henrei_koden?.henreihin_price | number}}">
                {{koden.henrei_koden?.henreihin_price | number}}</td>
              <td class="center aligned henreihin_selected" title="{{koden.henreihin_fuyo_flg?'辞退':koden.henrei_koden.customer_self_select_flg?'顧客':'喪主'}}">
                {{koden.henreihin_fuyo_flg?'辞退':koden.henrei_koden.customer_self_select_flg?'顧客':'喪主'}}
              </td>
              <td class="center aligned entry_ts" title="{{koden.entry_detail.entry.entry_ts | date: 'MM/dd H:mm'}}">
                {{koden.entry_detail.entry.entry_ts | date: 'MM/dd H:mm'}}</td>
              <td class="center aligned is_cancel" title="{{koden.entry_detail.entry.cancel_ts?'キャンセル済':''}}">
                {{koden.entry_detail.entry.cancel_ts?'キャンセル済':''}}
              </td>
              
              <td class="center aligned button operation">
                <i class="large ban icon" title="キャンセル" *ngIf="(login_company.base_type === Const.BASE_TYPE_COMPANY|| login_company.base_type === Const.BASE_TYPE_ADMIN) && !koden.entry_detail.entry.cancel_ts" (click)="showOrderCancel(koden.entry_detail.entry)"></i>
                <i class="large linkify icon" title="領収書URL" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN" (click)="showUrl(koden.entry_detail.entry.id)"></i>
              </td>
            </tr>
            </ng-container>
            <tr *ngIf="!koden_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="12">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal mini" id="receipt-url">
        <div class="header ui"><i class="linkify icon large"></i>
          領収書URL
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <div class="ui input fluid">
                <input type="text" autocomplete="off" value="{{receipt_url}}" readonly (focus)="onFocus($event)">
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="ui modal big" id="order-cancel">
        <div class="header ui"><i class="ban icon large"></i>
          申込キャンセル
        </div>
        <div class="content" *ngIf="selected_entry">
          <app-com-order-cancel [entry_id]="selected_entry.id"></app-com-order-cancel>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="cancelOrder()">
            <i class="ban icon"></i>キャンセル実行
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeCancelOrder()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

		</div>
  </div>
</div>
