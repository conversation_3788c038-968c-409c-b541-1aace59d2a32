import time
from typing import Any, Dict, List, Set

from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory
from staffs.models import Staff
from staffs.tests.factories import DEFAULT_PASSWORD, StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


class StaffsLoginViewTest(TestCase):
    def setUp(self):
        super().setUp()

        company_base: Base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.staff: Staff = StaffFactory(base=company_base)

        admin_base: Base = BaseFactory(base_type=Base.OrgType.ADMIN)
        self.superuser: Staff = StaffFactory(base=admin_base)

        self.api_client = APIClient()

    def basic_params(self) -> Dict[str, Any]:
        return {
            'company_code': self.staff.company_code,
            'login_id': self.staff.login_id,
            'password': DEFAULT_PASSWORD,
        }

    def test_staff_login_succeed(self):
        """非管理者担当者でログインできる"""
        self.assertIsNone(self.staff.last_login)

        params = self.basic_params()
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        record = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(record.get('access'))
        self.assertIsNotNone(record.get('refresh'))

        self.staff.refresh_from_db()
        self.assertIsNotNone(self.staff.last_login)

    def test_superuser_login_succeed(self):
        """運営管理者でログインできる"""
        params = {
            'company_code': self.superuser.company_code,
            'login_id': self.superuser.login_id,
            'password': DEFAULT_PASSWORD,
        }
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        record = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(record.get('access'))
        self.assertIsNotNone(record.get('refresh'))

    def test_less_parameters(self):
        """不正なパスワードでログインが失敗する"""
        params = self.basic_params()
        params['password'] = 'the-incollect-password'
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record = response.json()
        self.assertTrue('detail' in record)

    def test_illegal_password(self):
        """パラメータ不足でログインが失敗する"""
        params = {}
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        params = self.basic_params().copy()
        params.pop('company_code')
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        params = self.basic_params().copy()
        params.pop('login_id')
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        params = self.basic_params().copy()
        params.pop('password')
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_login_fail_with_irregal_company_code(self):
        """会社コード未入力、不正なコードでログインが失敗する"""
        params = self.basic_params().copy()
        params['company_code'] = ''
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        params = self.basic_params().copy()
        params['company_code'] = None
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        params = self.basic_params().copy()
        params['company_code'] = 'the-incollect-company-code'
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_login_fail_by_no_match_company(self):
        """ログインAPIが担当者と一致しない会社コードを指定して失敗する"""
        another_base: Base = BaseFactory(base_type=Base.OrgType.COMPANY)
        params = self.basic_params()
        params['company_code'] = another_base.company_code
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_login_fail_with_disabled_staff(self):
        """ログインAPIが担当者が無効化されているため失敗する"""
        self.staff.disable()

        params = self.basic_params()
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_login_fail_with_retired_staff(self):
        """ログインAPIが担当者が退職済みのため失敗する"""
        self.staff.retired_flg = True
        self.staff.save()

        params = self.basic_params()
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_refresh_succeed(self):
        """トークンをリフレッシュできる"""
        params = self.basic_params()
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        record = response.json()
        access_token = record['access']
        refresh_token = record['refresh']

        time.sleep(1)

        params = {'refresh': refresh_token}
        response = self.api_client.post(
            reverse('staffs:token_refresh'), data=params, format='json'
        )
        record = response.json()
        new_token = record.get('access')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(new_token)
        self.assertNotEqual(access_token, new_token)

    def test_verify_succeed(self):
        """トークンを検証できる"""
        params = self.basic_params()
        response = self.api_client.post(
            reverse('staffs:token_obtain_pair'), data=params, format='json'
        )
        record = response.json()
        access_token = record['access']

        time.sleep(1)

        params = {'token': access_token}
        response = self.api_client.post(reverse('staffs:token_verify'), data=params, format='json')
        record = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(record, {})


class StaffMeViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company_base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.staff = StaffFactory(base=self.company_base)

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_user_me_succeed(self) -> None:
        """me APIはログインユーザの詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')
        params = {}
        response = self.api_client.get(reverse('staffs:me'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record = response.json()
        self.assertEqual(record.get('id'), self.staff.id)
        self.assertEqual(record.get('company_code'), self.staff.company_code)
        self.assertEqual(record.get('login_id'), self.staff.login_id)
        self.assertEqual(record.get('name'), self.staff.name)
        self.assertEqual(record.get('mail_address'), self.staff.mail_address)
        self.assertEqual(record.get('retired_flg'), self.staff.retired_flg)
        self.assertIsNotNone(record.get('created_at'))
        self.assertIsNotNone(record.get('updated_at'))

    def test_user_me_failed_without_auth(self) -> None:
        """me APIがAuthorizationヘッダがなくて失敗する"""
        params = {}
        response = self.api_client.get(reverse('staffs:me'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_user_me_succeed_with_ancestor_bases(self) -> None:
        """me APIで拠点の項目は祖先をさかのぼって出力する"""
        department_base = BaseFactory(base_type=Base.OrgType.DEPARTMENT, parent=self.company_base)
        hall_base = BaseFactory(base_type=Base.OrgType.HALL, parent=department_base)
        self.staff.base = hall_base
        self.staff.save()

        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')
        params = {}
        response = self.api_client.get(reverse('staffs:me'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record = response.json()
        self.assertEqual(record.get('id'), self.staff.id)
        self.assertEqual(record.get('company_code'), self.staff.company_code)
        self.assertEqual(record.get('login_id'), self.staff.login_id)
        self.assertEqual(record.get('name'), self.staff.name)
        self.assertEqual(record.get('mail_address'), self.staff.mail_address)
        self.assertEqual(record.get('retired_flg'), self.staff.retired_flg)
        self.assertIsNotNone(record.get('created_at'))
        self.assertIsNotNone(record.get('updated_at'))
        self.assertIsNotNone(record.get('base'))
        hall = record.get('base', {})
        self.assertEqual(hall.get('id'), hall_base.id)
        self.assertEqual(hall.get('base_type'), hall_base.base_type)
        self.assertEqual(hall.get('company_code'), hall_base.company_code)
        self.assertEqual(hall.get('base_name'), hall_base.base_name)
        self.assertEqual(hall.get('zip_code'), hall_base.zip_code)
        self.assertEqual(hall.get('prefecture'), hall_base.prefecture)
        self.assertEqual(hall.get('address_1'), hall_base.address_1)
        self.assertEqual(hall.get('address_2'), hall_base.address_2)
        self.assertEqual(hall.get('address_3'), hall_base.address_3)
        self.assertEqual(hall.get('tel'), hall_base.tel)
        self.assertEqual(hall.get('fax'), hall_base.fax)
        department = hall.get('parent', {})
        self.assertEqual(department.get('id'), department_base.id)
        self.assertEqual(department.get('base_type'), department_base.base_type)
        self.assertEqual(department.get('company_code'), department_base.company_code)
        self.assertEqual(department.get('base_name'), department_base.base_name)
        self.assertEqual(department.get('zip_code'), department_base.zip_code)
        self.assertEqual(department.get('prefecture'), department_base.prefecture)
        self.assertEqual(department.get('address_1'), department_base.address_1)
        self.assertEqual(department.get('address_2'), department_base.address_2)
        self.assertEqual(department.get('address_3'), department_base.address_3)
        self.assertEqual(department.get('tel'), department_base.tel)
        self.assertEqual(department.get('fax'), department_base.fax)
        company = department.get('parent', {})
        self.assertEqual(company.get('id'), self.company_base.id)
        self.assertEqual(company.get('base_type'), self.company_base.base_type)
        self.assertEqual(company.get('company_code'), self.company_base.company_code)
        self.assertEqual(company.get('base_name'), self.company_base.base_name)
        self.assertEqual(company.get('zip_code'), self.company_base.zip_code)
        self.assertEqual(company.get('prefecture'), self.company_base.prefecture)
        self.assertEqual(company.get('address_1'), self.company_base.address_1)
        self.assertEqual(company.get('address_2'), self.company_base.address_2)
        self.assertEqual(company.get('address_3'), self.company_base.address_3)
        self.assertEqual(company.get('tel'), self.company_base.tel)
        self.assertEqual(company.get('fax'), self.company_base.fax)


class StaffListLowerBasesViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company_base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.department_base_1 = BaseFactory(
            base_type=Base.OrgType.DEPARTMENT, parent=self.company_base
        )
        self.department_base_2 = BaseFactory(
            base_type=Base.OrgType.DEPARTMENT, parent=self.company_base
        )
        self.hall_base_1 = BaseFactory(base_type=Base.OrgType.HALL, parent=self.department_base_1)
        self.hall_base_2 = BaseFactory(base_type=Base.OrgType.HALL, parent=self.department_base_2)
        self.other_base_1 = BaseFactory(base_type=Base.OrgType.OTHER, parent=self.hall_base_1)
        self.other_base_2 = BaseFactory(base_type=Base.OrgType.OTHER, parent=self.hall_base_2)
        self.other_base_3 = BaseFactory(base_type=Base.OrgType.OTHER, parent=self.hall_base_2)

        self.staff_1 = StaffFactory(base=self.company_base)
        self.staff_2 = StaffFactory(base=self.department_base_1)
        self.staff_3 = StaffFactory(base=self.department_base_1)
        self.staff_4 = StaffFactory(base=self.department_base_2)
        self.staff_5 = StaffFactory(base=self.department_base_2)
        self.staff_6 = StaffFactory(base=self.hall_base_1)
        self.staff_7 = StaffFactory(base=self.hall_base_1)
        self.staff_8 = StaffFactory(base=self.hall_base_2)
        self.staff_9 = StaffFactory(base=self.hall_base_2)
        self.staff_10 = StaffFactory(base=self.other_base_1)
        self.staff_11 = StaffFactory(base=self.other_base_1)
        self.staff_12 = StaffFactory(base=self.other_base_2)
        self.staff_13 = StaffFactory(base=self.other_base_2)
        self.staff_14 = StaffFactory(base=self.other_base_3)
        self.staff_15 = StaffFactory(base=self.other_base_3)

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.staff_2)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_staff_lower_bases_succeed(self) -> None:
        """担当者一覧APIは拠点(と拠点に所属する拠点)の担当者一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # department_base_1を指定した場合はhall_base_1、other_base_1も対象
        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:list_lower_bases', kwargs={'base_id': self.department_base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: List[Dict] = response.json()
        correct_staff_ids: Set = set(
            [
                staff.pk
                for staff in [
                    self.staff_2,
                    self.staff_3,
                    self.staff_6,
                    self.staff_7,
                    self.staff_10,
                    self.staff_11,
                ]
            ]
        )
        result_staff_ids: Set = set([staff.get('id') for staff in record])
        self.assertSetEqual(result_staff_ids, correct_staff_ids)

        # hall_base_2を指定した場合はother_base_2、other_base_3も対象
        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:list_lower_bases', kwargs={'base_id': self.hall_base_2.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: List[Dict] = response.json()
        correct_staff_ids: Set = set(
            [
                staff.pk
                for staff in [
                    self.staff_8,
                    self.staff_9,
                    self.staff_12,
                    self.staff_13,
                    self.staff_14,
                    self.staff_15,
                ]
            ]
        )
        result_staff_ids: Set = set([staff.get('id') for staff in record])
        self.assertSetEqual(result_staff_ids, correct_staff_ids)

        # 担当者ごとの項目確認(項目名のみ)
        self.assertIsNotNone(record[0].get('id'))
        self.assertIsNotNone(record[0].get('company_code'))
        self.assertIsNotNone(record[0].get('login_id'))
        self.assertIsNotNone(record[0].get('name'))
        self.assertIsNotNone(record[0].get('base'))
        self.assertIsNotNone(record[0].get('base_name'))
        self.assertIsNotNone(record[0].get('mail_address'))
        self.assertIsNotNone(record[0].get('retired_flg'))
        self.assertIsNotNone(record[0].get('created_at'))
        self.assertIsNotNone(record[0].get('updated_at'))

    def test_staff_lower_bases_ignore_deleted_staff(self) -> None:
        """担当者一覧APIは無効化された担当者を除外する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # department_base_1を指定した場合はhall_base_1、other_base_1も対象
        self.staff_3.disable()
        self.staff_11.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:list_lower_bases', kwargs={'base_id': self.department_base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: List[Dict] = response.json()
        correct_staff_ids: Set = set(
            [staff.pk for staff in [self.staff_2, self.staff_6, self.staff_7, self.staff_10]]
        )
        result_staff_ids: Set = set([staff.get('id') for staff in record])
        self.assertSetEqual(result_staff_ids, correct_staff_ids)

    def test_staff_lower_bases_include_retired_staff(self) -> None:
        """担当者一覧APIは退職した担当者を除外しない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # department_base_1を指定した場合はhall_base_1、other_base_1も対象
        self.staff_3.retired_flg = True
        self.staff_3.save()
        self.staff_11.retired_flg = True
        self.staff_11.save()

        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:list_lower_bases', kwargs={'base_id': self.department_base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: List[Dict] = response.json()
        correct_staff_ids: Set = set(
            [
                staff.pk
                for staff in [
                    self.staff_2,
                    self.staff_3,
                    self.staff_6,
                    self.staff_7,
                    self.staff_10,
                    self.staff_11,
                ]
            ]
        )
        result_staff_ids: Set = set([staff.get('id') for staff in record])
        self.assertSetEqual(result_staff_ids, correct_staff_ids)

    def test_staff_lower_bases_ignore_deleted_base(self) -> None:
        """担当者一覧APIは無効化された拠点を除外する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # department_base_1を指定した場合はhall_base_1、other_base_1も対象
        self.other_base_1.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:list_lower_bases', kwargs={'base_id': self.department_base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: List[Dict] = response.json()
        correct_staff_ids: Set = set(
            [staff.pk for staff in [self.staff_2, self.staff_3, self.staff_6, self.staff_7]]
        )
        result_staff_ids: Set = set([staff.get('id') for staff in record])
        self.assertSetEqual(result_staff_ids, correct_staff_ids)

    def test_staff_lower_bases_failed_without_auth(self) -> None:
        """担当者一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('staffs:list_lower_bases', kwargs={'base_id': self.department_base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
