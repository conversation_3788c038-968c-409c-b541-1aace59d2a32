from typing import Dict, List

from dateutil.relativedelta import relativedelta
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory
from hoyo.models import HoyoSchedule, HoyoSeko
from hoyo.tests.factories import HoyoMailTargetFactory, HoyoScheduleFactory, HoyoSekoFactory
from seko.tests.factories import KojinFactory, MoshuFactory, SekoFactory, SekoScheduleFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class HoyoSekoListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.hoyo_seko_1 = HoyoSekoFactory()
        self.moshu = MoshuFactory(seko=self.hoyo_seko_1.seko)
        self.kojin_1 = KojinFactory(seko=self.hoyo_seko_1.seko, kojin_num=1)
        self.kojin_2 = KojinFactory(seko=self.hoyo_seko_1.seko, kojin_num=2)
        self.schedule_1 = SekoScheduleFactory(seko=self.hoyo_seko_1.seko, display_num=1)
        self.schedule_2 = SekoScheduleFactory(seko=self.hoyo_seko_1.seko, display_num=2)
        self.schedule_3 = SekoScheduleFactory(seko=self.hoyo_seko_1.seko, display_num=3)
        self.schedule_4 = SekoScheduleFactory(seko=self.hoyo_seko_1.seko, display_num=4)
        self.hoyo_mail_target_1 = HoyoMailTargetFactory(seko=self.hoyo_seko_1.seko)
        self.hoyo_mail_target_2 = HoyoMailTargetFactory(seko=self.hoyo_seko_1.seko)
        self.hoyo_mail_target_3 = HoyoMailTargetFactory(seko=self.hoyo_seko_1.seko)

        self.hoyo_seko_2 = HoyoSekoFactory(
            hoyo=self.hoyo_seko_1.hoyo,
            hoyo_planned_date=self.hoyo_seko_1.hoyo_planned_date + relativedelta(days=-2),
        )
        self.hoyo_seko_3 = HoyoSekoFactory(
            seko=SekoFactory(seko_department=self.hoyo_seko_1.seko.seko_department),
            hoyo_planned_date=self.hoyo_seko_1.hoyo_planned_date + relativedelta(days=1),
        )
        self.hoyo_seko_4 = HoyoSekoFactory(
            hoyo=self.hoyo_seko_1.hoyo,
            hoyo_planned_date=self.hoyo_seko_1.hoyo_planned_date + relativedelta(days=-1),
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_seko_list_succeed(self) -> None:
        """法要施行一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)
        # 順番確認 (2 > 4 > 1 > 3)
        self.assertEqual(records[0]['id'], self.hoyo_seko_2.pk)
        self.assertEqual(records[1]['id'], self.hoyo_seko_4.pk)
        self.assertEqual(records[2]['id'], self.hoyo_seko_1.pk)
        self.assertEqual(records[3]['id'], self.hoyo_seko_3.pk)

        # 項目確認
        record = records[2]
        self.assertEqual(record['id'], self.hoyo_seko_1.pk)
        self.assertEqual(record['seko']['id'], self.hoyo_seko_1.seko.pk)
        self.assertEqual(record['seko']['kojin'][0]['id'], self.kojin_1.pk)
        self.assertEqual(record['seko']['kojin'][0]['name'], self.kojin_1.name)
        self.assertEqual(record['hoyo']['id'], self.hoyo_seko_1.hoyo.pk)
        self.assertEqual(record['hoyo_name'], self.hoyo_seko_1.hoyo_name)
        self.assertEqual(
            record['hoyo_planned_date'], self.hoyo_seko_1.hoyo_planned_date.isoformat()
        )
        self.assertEqual(
            record['hoyo_activity_date'], self.hoyo_seko_1.hoyo_activity_date.isoformat()
        )
        self.assertEqual(record['begin_time'], self.hoyo_seko_1.begin_time)
        self.assertEqual(record['end_time'], self.hoyo_seko_1.end_time)
        self.assertEqual(record['hall']['id'], self.hoyo_seko_1.hall.pk)
        self.assertEqual(record['dine_flg'], self.hoyo_seko_1.dine_flg)
        self.assertEqual(record['seshu_name'], self.hoyo_seko_1.seshu_name)
        self.assertEqual(
            record['kojin_seshu_relationship'], self.hoyo_seko_1.kojin_seshu_relationship
        )
        self.assertEqual(record['zip_code'], self.hoyo_seko_1.zip_code)
        self.assertEqual(record['prefecture'], self.hoyo_seko_1.prefecture)
        self.assertEqual(record['address_1'], self.hoyo_seko_1.address_1)
        self.assertEqual(record['address_2'], self.hoyo_seko_1.address_2)
        self.assertEqual(record['address_3'], self.hoyo_seko_1.address_3)
        self.assertEqual(record['tel'], self.hoyo_seko_1.tel)
        self.assertEqual(record['hoyo_sentence'], self.hoyo_seko_1.hoyo_sentence)
        self.assertEqual(record['shishiki_name'], self.hoyo_seko_1.shishiki_name)
        self.assertEqual(record['reply_limit_date'], self.hoyo_seko_1.reply_limit_date.isoformat())
        self.assertEqual(
            record['hoyo_site_end_date'], self.hoyo_seko_1.hoyo_site_end_date.isoformat()
        )
        self.assertEqual(record['note'], self.hoyo_seko_1.note)
        self.assertEqual(record['staff']['id'], self.hoyo_seko_1.staff.pk)
        self.assertEqual(record['del_flg'], self.hoyo_seko_1.del_flg)
        self.assertEqual(
            record['created_at'], self.hoyo_seko_1.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(
            record['updated_at'], self.hoyo_seko_1.updated_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['seko']['schedules'][0]['id'], self.schedule_1.pk)
        self.assertEqual(record['seko']['schedules'][0]['hall_name'], self.schedule_1.hall_name)
        self.assertEqual(record['seko']['hoyo_mail_targets'][0]['id'], self.hoyo_mail_target_1.pk)
        self.assertEqual(
            record['seko']['hoyo_mail_targets'][0]['hoyo_mail']['hoyo'],
            self.hoyo_mail_target_1.hoyo_mail.hoyo.pk,
        )

    def test_hoyo_seko_list_ignore_deleted_seko(self) -> None:
        """法要施行一覧APIは無効化された施行を無視する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_seko_2.seko.disable()
        self.hoyo_seko_3.seko.disable()

        params: Dict = {'hoyo': self.hoyo_seko_1.hoyo.pk}
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)
        # 順番確認 (4 > 1)
        self.assertEqual(records[0]['id'], self.hoyo_seko_4.pk)
        self.assertEqual(records[1]['id'], self.hoyo_seko_1.pk)

    def test_hoyo_seko_list_filter_by_hoyo(self) -> None:
        """法要施行一覧APIで法要種別の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'hoyo': self.hoyo_seko_1.hoyo.pk}
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_hoyo_seko_list_filter_by_hoyo_planned_date(self) -> None:
        """法要施行一覧APIで法要予定日(範囲指定)の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # Fromのみ
        params: Dict = {
            'hoyo_planned_date_after': self.hoyo_seko_1.hoyo_planned_date + relativedelta(days=-1)
        }
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        # Toのみ
        params: Dict = {
            'hoyo_planned_date_before': self.hoyo_seko_1.hoyo_planned_date + relativedelta(days=-1)
        }
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        # FromとTo
        params: Dict = {
            'hoyo_planned_date_after': self.hoyo_seko_1.hoyo_planned_date + relativedelta(days=-1),
            'hoyo_planned_date_before': self.hoyo_seko_1.hoyo_planned_date,
        }
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_hoyo_seko_list_filter_by_seko_department(self) -> None:
        """法要施行一覧APIで施行拠点(部門)の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'seko_department': self.hoyo_seko_1.seko.seko_department.pk}
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_hoyo_seko_list_filter_by_seko_company(self) -> None:
        """法要施行一覧APIで施行拠点(会社)の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'seko_company': self.hoyo_seko_1.seko.seko_company.pk}
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_hoyo_seko_list_failed_without_auth(self) -> None:
        """法要施行一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_seko_list_deny_call_from_moshu(self) -> None:
        """法要施行一覧APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])


class HoyoSekoCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff = StaffFactory()
        self.hoyo_seko_data: HoyoSeko = HoyoSekoFactory.build(
            seko=SekoFactory(), hall=BaseFactory(base_type=Base.OrgType.HALL), staff=self.staff
        )
        self.hoyo_seko_schedule_data_1 = HoyoScheduleFactory.build(
            hoyo_seko=self.hoyo_seko_data,
            hall=BaseFactory(base_type=Base.OrgType.HALL),
            display_num=2,
        )
        self.hoyo_seko_schedule_data_2 = HoyoScheduleFactory.build(
            hoyo_seko=self.hoyo_seko_data,
            hall=BaseFactory(base_type=Base.OrgType.HALL),
            display_num=3,
        )
        self.hoyo_seko_schedule_data_3 = HoyoScheduleFactory.build(
            hoyo_seko=self.hoyo_seko_data,
            hall=BaseFactory(base_type=Base.OrgType.HALL),
            display_num=1,
        )

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        schedules: List[Dict] = []
        for schedule in [
            self.hoyo_seko_schedule_data_1,
            self.hoyo_seko_schedule_data_2,
            self.hoyo_seko_schedule_data_3,
        ]:
            schedules.append(
                {
                    'schedule_name': schedule.schedule_name,
                    'hall': schedule.hall.pk,
                    'hall_name': schedule.hall_name,
                    'schedule_date': schedule.schedule_date.isoformat(),
                    'begin_time': schedule.begin_time,
                    'end_time': schedule.end_time,
                    'display_num': schedule.display_num,
                }
            )

        return {
            'seko': self.hoyo_seko_data.seko.pk,
            'hoyo_name': self.hoyo_seko_data.hoyo_name,
            'hoyo_activity_date': self.hoyo_seko_data.hoyo_activity_date.isoformat(),
            'begin_time': self.hoyo_seko_data.begin_time,
            'end_time': self.hoyo_seko_data.end_time,
            'hall': self.hoyo_seko_data.hall.pk,
            'dine_flg': self.hoyo_seko_data.dine_flg,
            'seshu_name': self.hoyo_seko_data.seshu_name,
            'kojin_seshu_relationship': self.hoyo_seko_data.kojin_seshu_relationship,
            'zip_code': self.hoyo_seko_data.zip_code,
            'prefecture': self.hoyo_seko_data.prefecture,
            'address_1': self.hoyo_seko_data.address_1,
            'address_2': self.hoyo_seko_data.address_2,
            'address_3': self.hoyo_seko_data.address_3,
            'tel': self.hoyo_seko_data.tel,
            'hoyo_sentence': self.hoyo_seko_data.hoyo_sentence,
            'shishiki_name': self.hoyo_seko_data.shishiki_name,
            'reply_limit_date': self.hoyo_seko_data.reply_limit_date.isoformat(),
            'hoyo_site_end_date': self.hoyo_seko_data.hoyo_site_end_date.isoformat(),
            'note': self.hoyo_seko_data.note,
            'staff': self.hoyo_seko_data.staff.pk,
            'schedules': schedules,
        }

    def test_hoyo_seko_create_succeed(self) -> None:
        """法要施行を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['id'])
        self.assertEqual(record['seko']['id'], self.hoyo_seko_data.seko.pk)
        self.assertIsNone(record['hoyo'])
        self.assertEqual(record['hoyo_name'], self.hoyo_seko_data.hoyo_name)
        self.assertIsNone(record['hoyo_planned_date'])
        self.assertEqual(
            record['hoyo_activity_date'], self.hoyo_seko_data.hoyo_activity_date.isoformat()
        )
        self.assertEqual(record['begin_time'], self.hoyo_seko_data.begin_time)
        self.assertEqual(record['end_time'], self.hoyo_seko_data.end_time)
        hall: Dict = record['hall']
        self.assertEqual(hall['id'], self.hoyo_seko_data.hall.pk)
        self.assertEqual(hall['base_name'], self.hoyo_seko_data.hall.base_name)
        self.assertEqual(record['dine_flg'], self.hoyo_seko_data.dine_flg)
        self.assertEqual(record['seshu_name'], self.hoyo_seko_data.seshu_name)
        self.assertEqual(
            record['kojin_seshu_relationship'], self.hoyo_seko_data.kojin_seshu_relationship
        )
        self.assertEqual(record['zip_code'], self.hoyo_seko_data.zip_code)
        self.assertEqual(record['prefecture'], self.hoyo_seko_data.prefecture)
        self.assertEqual(record['address_1'], self.hoyo_seko_data.address_1)
        self.assertEqual(record['address_2'], self.hoyo_seko_data.address_2)
        self.assertEqual(record['address_3'], self.hoyo_seko_data.address_3)
        self.assertEqual(record['tel'], self.hoyo_seko_data.tel)
        self.assertEqual(record['hoyo_sentence'], self.hoyo_seko_data.hoyo_sentence)
        self.assertEqual(record['shishiki_name'], self.hoyo_seko_data.shishiki_name)
        self.assertEqual(
            record['reply_limit_date'], self.hoyo_seko_data.reply_limit_date.isoformat()
        )
        self.assertEqual(
            record['hoyo_site_end_date'], self.hoyo_seko_data.hoyo_site_end_date.isoformat()
        )
        self.assertEqual(record['note'], self.hoyo_seko_data.note)

        correct_schedules: List[HoyoSchedule] = [
            self.hoyo_seko_schedule_data_3,
            self.hoyo_seko_schedule_data_1,
            self.hoyo_seko_schedule_data_2,
        ]
        for rec_schedule, correct_schedule in zip(record['schedules'], correct_schedules):
            self.assertEqual(rec_schedule['schedule_name'], correct_schedule.schedule_name)
            self.assertEqual(rec_schedule['hall']['id'], correct_schedule.hall.pk)
            self.assertEqual(rec_schedule['hall']['base_name'], correct_schedule.hall.base_name)
            self.assertEqual(rec_schedule['hall_name'], correct_schedule.hall_name)
            self.assertEqual(
                rec_schedule['schedule_date'], correct_schedule.schedule_date.isoformat()
            )
            self.assertEqual(rec_schedule['begin_time'], correct_schedule.begin_time)
            self.assertEqual(rec_schedule['end_time'], correct_schedule.end_time)
            self.assertEqual(rec_schedule['display_num'], correct_schedule.display_num)

    def test_hoyo_seko_create_succeed_without_schedules(self) -> None:
        """法要施行追加APIは法要施行日程の項目がなくても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params.pop('schedules')
        response = self.api_client.post(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertEqual(record['schedules'], [])

    def test_hoyo_seko_create_fail_with_non_existent_seko(self) -> None:
        """法要施行追加APIは存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        non_existent_seko = SekoFactory.build()
        params['seko'] = non_existent_seko.pk
        response = self.api_client.post(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['seko'])

    def test_hoyo_seko_create_failed_without_auth(self) -> None:
        """法要施行追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_seko_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
