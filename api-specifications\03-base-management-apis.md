# 拠点管理API仕様書

## 概要

FOCシステムの拠点管理関連APIの詳細仕様です。拠点の作成、取得、更新、削除、および階層構造に基づく拠点管理機能を提供します。MPTT（Modified Preorder Tree Traversal）による効率的な階層構造処理を実装しています。

## 1. 拠点作成API

### 1.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /bases/` |
| **機能** | 新しい拠点を作成 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `BaseList` |
| **シリアライザー** | `BaseSerializer` |

### 1.2 リクエスト仕様

**HTTPメソッド:** `POST`

**Content-Type:** `application/json`

**リクエストボディ:**
```json
{
  "base_type": "integer",
  "company_code": "string",
  "base_name": "string",
  "zip_code": "string",
  "prefecture": "string",
  "address_1": "string",
  "address_2": "string",
  "address_3": "string",
  "tel": "string",
  "fax": "string",
  "company_logo_file": "string",
  "company_map_file": "string",
  "calc_type": "integer",
  "display_num": "integer",
  "parent": "integer",
  "chobun_daishi_master_ids": ["integer"],
  "service_ids": ["integer"],
  "soke_menu_ids": ["integer"],
  "fdn_code": "string",
  "order_mail_address": "string"
}
```

**パラメータ詳細:**

| フィールド | 型 | 必須 | 桁数制限 | 説明 | バリデーション |
|---|---|---|---|---|---|
| `base_type` | integer | ✓ | - | 拠点種別（0:管理者, 1:会社, 2:部門, 3:ホール, 9:その他） | 定義された選択肢のみ |
| `company_code` | string | ○ | 最大20文字 | 会社コード | - |
| `base_name` | string | ✓ | - | 拠点名 | - |
| `zip_code` | string | ✓ | 7文字 | 郵便番号 | ハイフンなし7桁 |
| `prefecture` | string | ✓ | - | 都道府県 | - |
| `address_1` | string | ✓ | - | 住所1（市区町村） | - |
| `address_2` | string | ○ | - | 住所2（番地） | - |
| `address_3` | string | ○ | - | 住所3（建物名等） | - |
| `tel` | string | ○ | - | 電話番号 | - |
| `fax` | string | ○ | - | FAX番号 | - |
| `company_logo_file` | string | ○ | - | 会社ロゴ（Base64） | Base64エンコード画像 |
| `company_map_file` | string | ○ | - | 会社地図（Base64） | Base64エンコード画像 |
| `calc_type` | integer | ○ | - | 計算種別 | - |
| `display_num` | integer | ○ | - | 表示順序 | デフォルト: 1 |
| `parent` | integer | ○ | - | 親拠点ID | 存在する拠点IDである必要がある |
| `chobun_daishi_master_ids` | array | ○ | - | 弔文台紙マスターID配列 | 存在するIDのみ |
| `service_ids` | array | ○ | - | サービスID配列 | 存在するIDのみ |
| `soke_menu_ids` | array | ○ | - | 葬家メニューID配列 | 存在するIDのみ |
| `fdn_code` | string | ○ | - | FDNコード | 会社内で一意である必要がある |
| `order_mail_address` | string | ○ | - | 注文メールアドレス | 有効なメール形式 |

### 1.3 バリデーション処理

#### 1.3.1 一意性制約
```python
# company_code + fdn_code の組み合わせが一意（fdn_codeがnullでない場合）
UniqueTogetherValidator(
    queryset=Base.objects.filter(del_flg=False, fdn_code__isnull=False).all(),
    fields=['company_code', 'fdn_code'],
)
```

#### 1.3.2 階層構造制約
- 親拠点は存在する必要がある
- 循環参照は禁止
- MPTTによる自動的な階層構造管理

### 1.4 レスポンス仕様

**成功時（201 Created）:**
```json
{
  "id": 123,
  "base_type": 2,
  "company_code": "COMPANY001",
  "base_name": "新規営業部",
  "zip_code": "1000001",
  "prefecture": "東京都",
  "address_1": "千代田区千代田",
  "address_2": "1-1-1",
  "address_3": "オフィスビル10F",
  "tel": "03-1234-5678",
  "fax": "03-1234-5679",
  "company_logo_file": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "company_map_file": null,
  "calc_type": 1,
  "display_num": 10,
  "del_flg": false,
  "created_at": "2024-06-27T10:00:00+09:00",
  "create_user_id": 1,
  "updated_at": "2024-06-27T10:00:00+09:00",
  "update_user_id": 1,
  "parent": 10,
  "chobun_daishi_master_ids": [1, 2, 3],
  "service_ids": [1, 5, 10],
  "soke_menu_ids": [2, 4],
  "service_reception_terms": [],
  "fdn_code": "FDN123",
  "order_mail_address": "<EMAIL>"
}
```

**エラー時（400 Bad Request）:**
```json
{
  "base_name": ["This field is required."],
  "zip_code": ["Ensure this field has exactly 7 characters."],
  "fdn_code": ["Base with this Company code and Fdn code already exists."]
}
```

### 1.5 使用例

**リクエスト例:**
```bash
curl -X POST http://localhost:8000/bases/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "base_type": 2,
    "company_code": "COMPANY001",
    "base_name": "新規営業部",
    "zip_code": "1000001",
    "prefecture": "東京都",
    "address_1": "千代田区千代田",
    "address_2": "1-1-1",
    "tel": "03-1234-5678",
    "parent": 10,
    "display_num": 10,
    "fdn_code": "FDN123"
  }'
```

## 2. 拠点詳細・更新・削除API

### 2.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/PUT/PATCH/DELETE /bases/{id}/` |
| **機能** | 拠点の詳細取得・更新・削除 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `BaseDetail` |
| **シリアライザー** | `BaseDownwardSerializer`（GET）、`BaseSerializer`（PUT/PATCH/DELETE） |

### 2.2 拠点詳細取得

**HTTPメソッド:** `GET`

**パスパラメータ:**

| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 拠点ID |

**レスポンス例:**
```json
{
  "id": 123,
  "base_type": 2,
  "company_code": "COMPANY001",
  "base_name": "営業部",
  "zip_code": "1000001",
  "prefecture": "東京都",
  "address_1": "千代田区千代田",
  "address_2": "1-1-1",
  "address_3": "オフィスビル10F",
  "tel": "03-1234-5678",
  "fax": "03-1234-5679",
  "company_logo_file": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "company_map_file": null,
  "calc_type": 1,
  "display_num": 10,
  "del_flg": false,
  "created_at": "2024-01-01T10:00:00+09:00",
  "create_user_id": 1,
  "updated_at": "2024-06-15T14:30:00+09:00",
  "update_user_id": 5,
  "tokusho": {
    "company": 123,
    "responsible_name": "責任者名",
    "mail_address": "<EMAIL>",
    "from_name": "送信者名",
    "url": "https://example.com",
    "kumotsu_offer_timing": "供物提供タイミング",
    "koden_offer_timing": "香典提供タイミング",
    "henrei_offer_timing": "返礼提供タイミング",
    "business_no": "事業者番号"
  },
  "focfee": {
    "base": 123,
    "fee_rate": 0.05,
    "min_fee": 1000,
    "max_fee": 50000
  },
  "sms_account": {
    "base": 123,
    "account_id": "SMS_ACCOUNT_001",
    "password": "encrypted_password"
  },
  "parent": {
    "id": 10,
    "base_name": "本社",
    "base_type": 1
  },
  "children": [
    {
      "id": 124,
      "base_name": "営業1課",
      "base_type": 3,
      "children": []
    }
  ],
  "services": [
    {
      "id": 1,
      "service_name": "基本サービス",
      "description": "基本的なサービス内容"
    }
  ],
  "soke_menus": [
    {
      "id": 2,
      "menu_name": "標準メニュー",
      "description": "標準的なメニュー内容"
    }
  ],
  "unprinted_chobun_count": 5,
  "kumotsu_count": 12,
  "henrei_kumotsu_count": 8,
  "henrei_koden_count": 15,
  "seko_inquiries_count": 3,
  "fdn_code": "FDN123",
  "order_mail_address": "<EMAIL>"
}
```

### 2.3 拠点更新

**HTTPメソッド:** `PUT` または `PATCH`

**リクエストボディ（PUT - 全項目更新）:**
```json
{
  "base_type": 2,
  "company_code": "COMPANY001",
  "base_name": "営業部（更新）",
  "zip_code": "1000002",
  "prefecture": "東京都",
  "address_1": "千代田区千代田",
  "address_2": "2-2-2",
  "tel": "03-1234-5680",
  "parent": 10,
  "display_num": 15,
  "fdn_code": "FDN123_NEW"
}
```

**リクエストボディ（PATCH - 部分更新）:**
```json
{
  "base_name": "営業部（更新）",
  "tel": "03-1234-5680",
  "display_num": 15
}
```

### 2.4 拠点削除（論理削除）

**HTTPメソッド:** `DELETE`

**処理内容:**
- 物理削除ではなく論理削除を実行
- `del_flg`をTrueに設定
- `SoftDestroyMixin`による実装

**レスポンス（204 No Content）:**
```
（レスポンスボディなし）
```

## 3. 全拠点階層構造取得API

### 3.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /bases/all/` |
| **機能** | 全拠点の階層構造を取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `BaseFullList` |
| **シリアライザー** | `BaseDownwardSerializer` |

### 3.2 リクエスト仕様

**HTTPメソッド:** `GET`

**パラメータ:** なし

### 3.3 処理ロジック

```python
def get_queryset(self, queryset=None):
    return (
        Base.objects.filter(Q(del_flg=False))
        .prefetch_related(
            'tokusho',
            'focfee', 
            'sms_account',
            Prefetch('services', queryset=Service.objects.order_by('id')),
        )
        .get_cached_trees()  # MPTT による効率的な階層構造取得
    )
```

### 3.4 レスポンス仕様

**成功時（200 OK）:**
```json
[
  {
    "id": 1,
    "base_type": 0,
    "company_code": "ADMIN",
    "base_name": "システム管理者",
    "zip_code": "1000001",
    "prefecture": "東京都",
    "address_1": "千代田区千代田",
    "address_2": "1-1-1",
    "address_3": null,
    "tel": "03-0000-0000",
    "fax": null,
    "company_logo_file": null,
    "company_map_file": null,
    "calc_type": null,
    "display_num": 1,
    "del_flg": false,
    "created_at": "2024-01-01T00:00:00+09:00",
    "create_user_id": 1,
    "updated_at": "2024-01-01T00:00:00+09:00",
    "update_user_id": 1,
    "tokusho": null,
    "focfee": null,
    "sms_account": null,
    "parent": null,
    "children": [
      {
        "id": 10,
        "base_type": 1,
        "company_code": "COMPANY001",
        "base_name": "株式会社サンプル",
        "parent": {
          "id": 1,
          "base_name": "システム管理者",
          "base_type": 0
        },
        "children": [
          {
            "id": 20,
            "base_type": 2,
            "company_code": "COMPANY001",
            "base_name": "営業部",
            "parent": {
              "id": 10,
              "base_name": "株式会社サンプル",
              "base_type": 1
            },
            "children": [
              {
                "id": 30,
                "base_type": 3,
                "company_code": "COMPANY001",
                "base_name": "営業1課",
                "parent": {
                  "id": 20,
                  "base_name": "営業部",
                  "base_type": 2
                },
                "children": []
              }
            ]
          }
        ]
      }
    ],
    "services": [],
    "soke_menus": [],
    "unprinted_chobun_count": 0,
    "kumotsu_count": 0,
    "henrei_kumotsu_count": 0,
    "henrei_koden_count": 0,
    "seko_inquiries_count": 0,
    "fdn_code": null,
    "order_mail_address": null
  }
]
```

### 3.5 使用例

**リクエスト例:**
```bash
curl -X GET http://localhost:8000/bases/all/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
```

## 4. 拠点関連設定API

### 4.1 FOC手数料設定API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/PUT/PATCH /bases/{base_id}/focfee/` |
| **機能** | 拠点のFOC手数料設定の取得・更新 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `FocFeeDetail` |
| **シリアライザー** | `FocFeeSerializer` |

#### リクエスト例（PUT）:
```json
{
  "fee_rate": 0.05,
  "min_fee": 1000,
  "max_fee": 50000
}
```

### 4.2 特商法設定API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST/PUT/PATCH /bases/{base_id}/tokusho/` |
| **機能** | 拠点の特商法設定の取得・作成・更新 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticatedOrReadOnly |
| **実装クラス** | `TokushoDetail` |
| **シリアライザー** | `TokushoSerializer` |

#### リクエスト例（POST）:
```json
{
  "responsible_name": "責任者名",
  "mail_address": "<EMAIL>",
  "from_name": "送信者名",
  "url": "https://example.com",
  "kumotsu_offer_timing": "供物提供タイミング",
  "koden_offer_timing": "香典提供タイミング",
  "henrei_offer_timing": "返礼提供タイミング",
  "business_no": "事業者番号"
}
```

### 4.3 SMS設定API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST/PUT/PATCH /bases/{base_id}/sms/` |
| **機能** | 拠点のSMS設定の取得・作成・更新 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticatedOrReadOnly |
| **実装クラス** | `SmsAccountDetail` |
| **シリアライザー** | `SmsAccountSerializer` |

#### リクエスト例（POST）:
```json
{
  "account_id": "SMS_ACCOUNT_001",
  "password": "sms_password"
}
```

### 4.4 拠点お問い合わせAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /bases/{id}/inquiry/` |
| **機能** | 拠点へのお問い合わせメール送信 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `InquiryMail` |
| **シリアライザー** | `BaseInquirySerializer` |

#### リクエスト例:
```json
{
  "name": "お問い合わせ者名",
  "kana": "オトイアワセシャメイ",
  "tel": "03-1234-5678",
  "mail_address": "<EMAIL>",
  "content": "お問い合わせ内容です。"
}
```

## 5. シリアライザー詳細

### 5.1 BaseSerializer
基本的な拠点情報の作成・更新用シリアライザー

**特徴:**
- `AddUserIdMixin`による作成者・更新者ID自動設定
- Base64画像フィールド対応
- 多対多関係のID配列による関連付け

### 5.2 BaseDownwardSerializer
拠点詳細表示用シリアライザー（子拠点含む）

**特徴:**
- 関連オブジェクト（tokusho、focfee、sms_account）の詳細情報
- 階層構造（parent、children）の再帰的表示
- 集計値（未印刷弔文数、供物数等）の計算フィールド
- サービス・葬家メニューの詳細情報

### 5.3 BaseUpwardSerializer
親拠点方向の階層構造表示用シリアライザー

**特徴:**
- 親拠点の再帰的表示
- MPTT内部フィールド（lft、rght、level、tree_id）の除外

## 6. MPTT階層構造処理

### 6.1 主要メソッド

#### get_cached_trees()
```python
# 効率的な階層構造取得
Base.objects.filter(Q(del_flg=False)).get_cached_trees()
```

#### get_descendants()
```python
# 指定拠点の配下拠点取得
specified_base.get_descendants(include_self=True)
```

#### active_children()
```python
# 有効な子拠点取得
def active_children(self) -> QuerySet:
    return self.children.filter(Q(del_flg=False)).order_by('display_num', 'base_name')
```

### 6.2 階層構造の特徴

- **効率的なクエリ**: MPTTによる単一クエリでの階層構造取得
- **表示順序制御**: `display_num`と`base_name`による並び順
- **論理削除対応**: `del_flg=False`による有効データのみ表示
- **循環参照防止**: 親子関係の整合性チェック

## 7. エラーコード一覧

| ステータスコード | エラーメッセージ | 発生条件 |
|---|---|---|
| 400 | This field is required. | 必須フィールドが未入力 |
| 400 | Base with this Company code and Fdn code already exists. | FDNコードが重複 |
| 400 | Ensure this field has exactly 7 characters. | 郵便番号の桁数エラー |
| 400 | The base already has a Tokusho/SmsAccount | 特商法・SMS設定が既に存在 |
| 401 | Authentication credentials were not provided. | 認証情報なし |
| 403 | You do not have permission to perform this action. | 権限不足 |
| 404 | Not found. | 指定されたリソースが存在しない |

## 8. セキュリティ考慮事項

### 8.1 データ保護
- **論理削除**: 物理削除ではなく論理削除でデータ保護
- **権限制御**: 認証済みユーザーのみアクセス可能
- **監査ログ**: 作成者・更新者IDの自動記録

### 8.2 パフォーマンス最適化
- **prefetch_related**: 関連オブジェクトの効率的な取得
- **select_related**: 外部キーの効率的な取得
- **MPTT**: 階層構造の効率的な処理
- **インデックス**: company_code + fdn_codeの複合インデックス

### 8.3 データ整合性
- **一意性制約**: company_code + fdn_codeの組み合わせ制約
- **外部キー制約**: 親拠点の存在チェック
- **階層構造制約**: 循環参照の防止
