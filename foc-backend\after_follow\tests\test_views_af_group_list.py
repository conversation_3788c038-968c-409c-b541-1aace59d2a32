from typing import Dict, List

from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.models import AfGroup
from after_follow.tests.factories import AfGroupFactory, AfterFollowFactory
from bases.models import Base
from bases.tests.factories import BaseFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class AfGroupListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_1 = BaseFactory(base_type=Base.OrgType.DEPARTMENT)
        self.af_group_3 = AfGroupFactory(
            department=BaseFactory(base_type=Base.OrgType.DEPARTMENT), display_num=2
        )
        self.af_group_1 = AfGroupFactory(department=self.base_1, display_num=3)
        self.af_group_2 = AfGroupFactory(department=self.base_1, display_num=1)
        self.af_group_4 = AfGroupFactory(department=self.base_1, display_num=1)

        self.after_follows = [
            AfterFollowFactory(af_group=self.af_group_3, display_num=2),
            AfterFollowFactory(af_group=self.af_group_3, display_num=1),
        ]

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_af_group_list_succeed(self) -> None:
        """AF項目グループ一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)
        # 順番確認 (2 -> 4 -> 1 -> 3)
        self.assertEqual(records[0]['id'], self.af_group_2.pk)
        self.assertEqual(records[1]['id'], self.af_group_4.pk)
        self.assertEqual(records[2]['id'], self.af_group_1.pk)
        self.assertEqual(records[3]['id'], self.af_group_3.pk)

    def test_af_group_list_succeed_by_department(self) -> None:
        """拠点で絞り込みしたAF項目グループ一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'department': self.base_1.pk,
        }
        response = self.api_client.get(
            reverse('after_follow:group_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        for record in records:
            self.assertEqual(record['department'], self.base_1.pk)

    def test_af_group_list_succeed_by_department_2(self) -> None:
        """拠点で絞り込みしたAF項目グループ一覧を返す(項目確認)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'department': self.af_group_3.department.pk,
        }
        response = self.api_client.get(
            reverse('after_follow:group_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)
        record = records[0]
        self.assertEqual(record['id'], self.af_group_3.pk)
        self.assertEqual(record['department'], self.af_group_3.department.pk)
        self.assertEqual(record['name'], self.af_group_3.name)
        self.assertEqual(record['display_num'], self.af_group_3.display_num)
        self.assertEqual(record['del_flg'], self.af_group_3.del_flg)
        self.assertEqual(
            record['created_at'], self.af_group_3.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.af_group_3.create_user_id)
        self.assertEqual(
            record['updated_at'], self.af_group_3.updated_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['update_user_id'], self.af_group_3.update_user_id)
        self.assertEqual(len(record['after_follows']), 2)
        for record_follow, self_follow in zip(
            record['after_follows'], reversed(self.after_follows)
        ):
            self.assertEqual(record_follow['id'], self_follow.pk)
            self.assertEqual(record_follow['name'], self_follow.name)
            self.assertEqual(record_follow['display_num'], self_follow.display_num)

    def test_af_group_list_ignores_deleted(self) -> None:
        """AF項目グループ一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.af_group_1.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_af_group_list_allow_from_moshu(self) -> None:
        """AF項目グループ一覧APIが喪主による呼び出しを許可する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_af_group_list_failed_without_auth(self) -> None:
        """AF項目グループ一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:group_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class AfGroupCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.af_group_data: AfGroup = AfGroupFactory.build(department=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'department': self.base.pk,
            'name': self.af_group_data.name,
            'display_num': self.af_group_data.display_num,
        }

    def test_af_group_create_succeed(self) -> None:
        """AF項目グループを追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('after_follow:group_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['id'])
        self.assertEqual(record['department'], self.base.pk)
        self.assertEqual(record['name'], self.af_group_data.name)
        self.assertEqual(record['display_num'], self.af_group_data.display_num)
        self.assertEqual(record['del_flg'], self.af_group_data.del_flg)
        self.assertIsNotNone(record['created_at'])
        self.assertEqual(record['create_user_id'], self.staff.pk)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)
        self.assertEqual(record['after_follows'], [])

    def test_af_group_create_deny_from_moshu(self) -> None:
        """AF項目グループ追加APIが喪主による呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('after_follow:group_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_af_group_create_failed_without_auth(self) -> None:
        """AF項目グループ追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = model_to_dict(self.af_group_data)
        response = self.api_client.post(
            reverse('after_follow:group_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
