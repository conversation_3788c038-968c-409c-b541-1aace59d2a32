# FOCプロジェクト API一覧

## 概要

FOCプロジェクトのバックエンドAPIエンドポイントの完全な一覧です。各APIの機能、認証要件、HTTPメソッドを整理しています。

## 1. 認証API

### 1.1 スタッフ認証
| エンドポイント | メソッド | 機能 | 認証 |
|---|---|---|---|
| `/staffs/login/` | POST | スタッフログイン（JWT取得） | 不要 |
| `/staffs/token/refresh/` | POST | アクセストークン更新 | リフレッシュトークン |
| `/staffs/token/verify/` | POST | トークン検証 | 不要 |

### 1.2 葬家認証
| エンドポイント | メソッド | 機能 | 認証 |
|---|---|---|---|
| `/seko/login/` | POST | 葬家ログイン（JWT取得） | 不要 |
| `/seko/token/refresh/` | POST | アクセストークン更新 | リフレッシュトークン |
| `/seko/token/verify/` | POST | トークン検証 | 不要 |

## 2. スタッフ管理API

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/staffs/me/` | GET | ログインユーザー情報取得 | 必要 | スタッフ |
| `/staffs/` | GET, POST | スタッフ一覧取得・作成 | 必要 | 認証済み |
| `/staffs/{id}/` | GET, PUT, PATCH, DELETE | スタッフ詳細・更新・削除 | 必要 | スタッフ |
| `/staffs/lower/{base_id}/` | GET | 指定拠点配下のスタッフ一覧 | 必要 | 認証済み |

## 3. 拠点管理API

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/bases/` | POST | 拠点作成 | 必要 | 認証済み |
| `/bases/{id}/` | GET, PUT, PATCH, DELETE | 拠点詳細・更新・削除 | 必要 | 認証済み |
| `/bases/all/` | GET | 全拠点階層構造取得 | 必要 | 認証済み |
| `/bases/{base_id}/focfee/` | GET, PUT, PATCH | FOC手数料設定 | 必要 | 認証済み |
| `/bases/{base_id}/tokusho/` | GET, PUT, PATCH | 特商法設定 | 必要 | 認証済み |
| `/bases/{base_id}/sms/` | GET, PUT, PATCH | SMS設定 | 必要 | 認証済み |
| `/bases/{id}/inquiry/` | POST | 拠点お問い合わせ | 必要 | 認証済み |

## 4. 施行管理API

### 4.1 施行基本情報
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/seko/` | GET, POST | 施行一覧取得・作成 | 必要 | 認証済み |
| `/seko/{id}/` | GET, PUT, PATCH, DELETE | 施行詳細・更新・削除 | 必要 | 認証済み |
| `/seko/{id}/fuho/` | GET, PUT, PATCH | 施行訃報情報 | 必要 | 認証済み |
| `/seko/list/` | GET | 施行一覧（NOWL用） | 必要 | 認証済み |

### 4.2 施行関連情報
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/seko/{seko_id}/kojin/` | GET, POST | 個人情報一覧・作成 | 必要 | 認証済み |
| `/seko/{seko_id}/kojin/{id}/` | GET, PUT, PATCH, DELETE | 個人情報詳細・更新・削除 | 必要 | 認証済み |
| `/seko/{seko_id}/albums/` | GET, POST | アルバム一覧・作成 | 必要 | 認証済み |
| `/seko/{seko_id}/albums/{id}/` | GET, PUT, PATCH, DELETE | アルバム詳細・更新・削除 | 必要 | 認証済み |
| `/seko/{seko_id}/share_images/` | GET, POST | 共有画像一覧・作成 | 必要 | 認証済み |
| `/seko/{seko_id}/share_images/{id}/` | GET, PUT, PATCH, DELETE | 共有画像詳細・更新・削除 | 必要 | 認証済み |

### 4.3 施行お問い合わせ
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/seko/{seko_id}/inquiries/` | GET, POST | お問い合わせ一覧・作成 | 必要 | 認証済み |
| `/seko/inquiries/{inquiry_id}/answers/` | GET, POST | 回答一覧・作成 | 必要 | 認証済み |

### 4.4 葬家管理
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/seko/moshu/` | GET, POST | 葬家一覧・作成 | 必要 | 認証済み |
| `/seko/moshu/{id}/` | GET, PUT, PATCH, DELETE | 葬家詳細・更新・削除 | 必要 | 認証済み |
| `/seko/soke/{id}/approve/` | POST | 葬家承認 | 必要 | 認証済み |

### 4.5 FDN連携
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/seko/fdn/` | POST | FDN施行作成 | 必要 | 認証済み |
| `/seko/fdn/{id}/` | PUT, PATCH | FDN施行更新 | 必要 | 認証済み |

### 4.6 PDF・ファイル出力
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/seko/{id}/homeicho/msg_pdf/` | GET | 芳名帳メッセージPDF | 必要 | 認証済み |
| `/seko/{id}/msg_pdf/` | GET | メッセージPDF | 必要 | 認証済み |
| `/seko/{id}/qrcode/` | GET | QRコードPDF | 必要 | 認証済み |
| `/seko/{id}/notice_attached_file/` | GET | 通知添付ファイル | 必要 | 認証済み |
| `/seko/{id}/notice_image_file/soke/` | GET | 葬家向け通知画像 | 必要 | 認証済み |
| `/seko/{id}/notice_image_file/company/` | GET | 企業向け通知画像 | 必要 | 認証済み |

## 5. マスターデータAPI

### 5.1 基本マスター
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/masters/schedules/` | GET | 日程マスタ一覧 | 必要 | 認証済み |
| `/masters/seko_styles/` | GET | 施行形式一覧 | 必要 | 認証済み |
| `/masters/zipcode/` | GET | 郵便番号検索 | 不要 | 読み取り専用 |
| `/masters/wareki/` | GET | 和暦一覧 | 不要 | 読み取り専用 |
| `/masters/services/` | GET | サービス一覧 | 必要 | 認証済み |
| `/masters/taxes/` | GET | 税率一覧 | 必要 | 認証済み |
| `/masters/relationship/` | GET | 関係性一覧 | 必要 | 認証済み |

### 5.2 サンプル・テンプレート
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/masters/fuho_sample/` | GET | 訃報サンプル一覧 | 必要 | 認証済み |
| `/masters/chobun_daishi/` | GET | 弔文台紙一覧 | 必要 | 認証済み |
| `/masters/chobun_sample/` | GET | 弔文サンプル一覧 | 不要 | 読み取り専用 |

### 5.3 パラメータ設定
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/masters/henrei_params/` | GET | 返礼品パラメータ | 不要 | 読み取り専用 |
| `/masters/koden_params/` | GET | 香典パラメータ | 不要 | 読み取り専用 |

### 5.4 法要関連
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/masters/hoyo_style/` | GET | 法要スタイル一覧 | 不要 | 読み取り専用 |
| `/masters/hoyo_default/` | GET | 法要初期値マスタ | 不要 | 読み取り専用 |
| `/masters/hoyo_sample/` | GET | 法要サンプルマスタ | 不要 | 読み取り専用 |

### 5.5 葬家サイト
| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/masters/soke_menu/` | GET | 葬家サイトメニュー | 不要 | 読み取り専用 |

## 6. 仕入先管理API

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/suppliers/` | GET, POST | 仕入先一覧・作成 | 必要 | 認証済み |
| `/suppliers/{id}/` | GET, PUT, PATCH, DELETE | 仕入先詳細・更新・削除 | 必要 | 認証済み |

## 7. 商品管理API

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/items/` | GET, POST | 商品一覧・作成 | 必要 | 認証済み |
| `/items/{id}/` | GET, PUT, PATCH, DELETE | 商品詳細・更新・削除 | 必要 | 認証済み |

## 8. 注文管理API

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/orders/` | GET, POST | 注文一覧・作成 | 必要 | 認証済み |
| `/orders/{id}/` | GET, PUT, PATCH, DELETE | 注文詳細・更新・削除 | 必要 | 認証済み |

## 9. 返礼品API

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/henrei/` | GET, POST | 返礼品一覧・作成 | 必要 | 認証済み |
| `/henrei/{id}/` | GET, PUT, PATCH, DELETE | 返礼品詳細・更新・削除 | 必要 | 認証済み |

## 10. 法要API

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/hoyo/` | GET, POST | 法要一覧・作成 | 必要 | 認証済み |
| `/hoyo/{id}/` | GET, PUT, PATCH, DELETE | 法要詳細・更新・削除 | 必要 | 認証済み |
| `/hoyo/samples/` | GET, POST | 法要サンプル一覧・作成 | 必要 | 認証済み |
| `/hoyo/samples/{id}/` | GET, PUT, PATCH, DELETE | 法要サンプル詳細・更新・削除 | 必要 | 認証済み |
| `/hoyo/mail_templates/` | GET, POST | 法要メールテンプレート一覧・作成 | 必要 | 認証済み |
| `/hoyo/mail_templates/{id}/` | GET, PUT, PATCH, DELETE | 法要メールテンプレート詳細・更新・削除 | 必要 | 認証済み |

## 11. アフターフォローAPI

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/after_follow/` | GET, POST | アフターフォロー一覧・作成 | 必要 | 認証済み |
| `/after_follow/{id}/` | GET, PUT, PATCH, DELETE | アフターフォロー詳細・更新・削除 | 必要 | 認証済み |

## 12. お問い合わせAPI

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/inquiry/` | GET, POST | お問い合わせ一覧・作成 | 必要 | 認証済み |
| `/inquiry/{id}/` | GET, PUT, PATCH, DELETE | お問い合わせ詳細・更新・削除 | 必要 | 認証済み |

## 13. 請求書API

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/invoices/` | GET, POST | 請求書一覧・作成 | 必要 | 認証済み |
| `/invoices/{id}/` | GET, PUT, PATCH, DELETE | 請求書詳細・更新・削除 | 必要 | 認証済み |

## 14. イベントメールAPI

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/event_mails/` | GET, POST | イベントメール一覧・作成 | 必要 | 認証済み |
| `/event_mails/{id}/` | GET, PUT, PATCH, DELETE | イベントメール詳細・更新・削除 | 必要 | 認証済み |

## 15. FAQAPI

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/faqs/` | GET, POST | FAQ一覧・作成 | 必要 | 認証済み |
| `/faqs/{id}/` | GET, PUT, PATCH, DELETE | FAQ詳細・更新・削除 | 必要 | 認証済み |

## 16. 広告API

| エンドポイント | メソッド | 機能 | 認証 | 権限 |
|---|---|---|---|---|
| `/advertises/` | GET, POST | 広告一覧・作成 | 必要 | 認証済み |
| `/advertises/{id}/` | GET, PUT, PATCH, DELETE | 広告詳細・更新・削除 | 必要 | 認証済み |

## API設計パターン

### 命名規則
- **リソース名**: 複数形（例：`/staffs/`, `/bases/`）
- **詳細取得**: `/{resource}/{id}/`
- **関連リソース**: `/{resource}/{id}/{related_resource}/`
- **アクション**: `/{resource}/{action}/`

### HTTPメソッド
- **GET**: データ取得
- **POST**: データ作成
- **PUT**: データ全体更新
- **PATCH**: データ部分更新
- **DELETE**: データ削除（論理削除）

### 認証・権限
- **JWT認証**: `Authorization: Bearer {token}`
- **権限クラス**: `IsAuthenticated`, `IsStaff`, `IsMoshu`, `ReadNeedAuth`
- **カスタム権限**: 拠点ベース、ロールベースの権限制御

### レスポンス形式
- **成功**: 200 OK, 201 Created, 204 No Content
- **エラー**: 400 Bad Request, 401 Unauthorized, 403 Forbidden, 404 Not Found, 500 Internal Server Error
- **データ形式**: JSON

このAPI一覧は、フロントエンド開発時のAPI呼び出しや、新規API設計時の参考資料として活用してください。
