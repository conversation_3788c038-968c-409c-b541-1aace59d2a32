
@import "src/assets/scss/company/setting";
.container .inner .contents {
  .menu_title {
    .icon.big {
      margin-right: 20px;
    }
    .icon.combo {
      position: absolute;
      left: 45px;
    }
  }
  .search_area .line {
    .ui.input.small {
      max-width: 150px;
    }
  }
  .ui.toggle.checkbox {
    padding: 4px 10px 0;
  }
  .table_fixed.body {
    max-height: calc(100% - 190px);
    &.no-page-nav {
      max-height: calc(100% - 160px);
    }
  }
  >.table_fixed tr {
    line-height: 1;
    td {
      >div:not(.checkbox) {
        min-height: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &.center {
          text-align: center;
        }
        &:not(:last-child) {
          min-height: 19px;
          border-bottom: dotted 1px $background-light1;
          margin: 0 -10px 5px;
          padding-left: 10px;
          padding-bottom: 5px;
        }
      }
    }
    .hoyo_name {
      width: 12%;
    }
    .select_date {
      width: 10%;
    }
    .send_date {
      width: 10%;
    }
    .mail_content {
      white-space: pre-wrap;
    }
    .note {
      width: 25%;
    }
  }
  .ui.modal {
    .input_area {
      .mail_content {
        height: auto;
        white-space: pre-wrap;
      }
      .data {
        width: 100%;
      }
    }
    tr {
      cursor: default;
      &:hover {
        background: $table-row-light;
      }
    }
    .seko_id {
      width: 9%;
    }
    .soke_name {
      width: 11%;
    }
    .kojin_name {
      width: 13%;
    }
    .moshu_name {
      width: 13%;
    }
    .death_date {
      width: 12%;
    }
    .seko_date {
      width: 12%;
    }
  }
}
