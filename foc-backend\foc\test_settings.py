from tempfile import TemporaryDirectory

from foc.settings import *  # noqa

SECRET_KEY = '2m=hd@mckqbmg9_&_$9)q5gd-8=!ays$-uv^9wr#d8hsy4a3nq'
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}
if 'STATIC_ROOT' in locals():
    del STATIC_ROOT
if 'STATICFILES_STORAGE' in locals():
    del STATICFILES_STORAGE

# FileField、ImageFieldは一時ディレクトリに保存、テスト終了時に自動削除
# _temp_media変数に一度受けて、この変数への参照がなくなると自動削除になる
_temp_media = TemporaryDirectory(prefix='djmedia-')
MEDIA_ROOT = _temp_media.name
