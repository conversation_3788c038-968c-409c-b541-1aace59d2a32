# FOCシステム セキュリティ脆弱性対策案

## 1. 問題の概要

### 1.1 現在の問題

FOCシステムでは、JWTトークンに会社コード情報が含まれておらず、フロントエンドのセッションストレージから会社コード情報を取得しています。これにより、以下の重大なセキュリティ脆弱性が存在します。

#### **主要な脆弱性**
1. **会社コード改ざん攻撃**: リクエストパラメータの会社コードを他社に変更
2. **IDベースAPI攻撃**: `/seko/{id}/`のようなAPIで他社のIDを指定
3. **セッションストレージ改ざん**: ブラウザ開発者ツールでセッション情報を改ざん
4. **権限昇格攻撃**: テナント会社がシステム管理者権限に昇格

### 1.2 攻撃対象API

#### **直接パラメータ攻撃対象**
- `GET /api/seko/?seko_company=1009` - 施行一覧API
- `GET /api/staffs/?company_id=1009` - スタッフ一覧API
- `GET /api/bases/all/` - 拠点一覧API（権限フィルタリング不備）

#### **IDベース攻撃対象**
- `GET /api/seko/{id}/` - 施行詳細API（会社コード情報含む）
- `GET /api/staffs/{id}/` - スタッフ詳細API（company_code含む）
- `GET /api/bases/{id}/` - 拠点詳細API（company_code含む）
- `GET /api/moshu/{id}/` - 喪主詳細API（関連施行の会社情報含む）
- `GET /api/suppliers/{id}/` - 発注先詳細API（company_id含む）

## 2. 被害例

### 2.1 会社コード改ざん攻撃

#### **攻撃手順**
```javascript
// 1. テナント会社A（company_code=1001）で正常ログイン
// 2. ブラウザ開発者ツールでセッション改ざん
sessionStorage.setItem('staff_login_info', JSON.stringify({
  staff: {
    id: 123,
    company_code: "1009",  // 他社（テナント会社B）に改ざん
    base: {
      base_type: 0  // システム管理者に権限昇格
    }
  }
}));

// 3. 改ざんされた情報でAPI攻撃
fetch('/api/seko/?seko_company=1009', {
  headers: { 'Authorization': 'Bearer ' + validJWTToken }
});
```

#### **被害内容**
```json
{
  "results": [
    {
      "id": 5678,
      "seko_company": {"company_code": "1009", "base_name": "B社本社"},
      "moshu_name": "山田花子",
      "moshu_tel": "090-1234-5678",
      "total_amount": 1500000,
      "payment_status": "paid"
    }
  ]
}
```
**→ 他社の個人情報・売上情報が大量漏洩**

### 2.2 IDベース攻撃

#### **攻撃手順**
```javascript
// 1. テナント会社A（company_code=1001）で正常ログイン
// 2. 他社の施行IDを推測して攻撃
fetch('/api/seko/9999/', {  // 他社の施行データ
  headers: { 'Authorization': 'Bearer ' + validJWTToken }
});
```

#### **被害内容**
```json
{
  "id": 9999,
  "seko_company": {"company_code": "1009"},
  "moshu": {
    "name": "田中太郎",
    "tel": "03-1234-5678",
    "mail_address": "<EMAIL>"
  },
  "kojin": [{"name": "田中一郎", "age": 75}]
}
```
**→ 他社の施行詳細・個人情報が漏洩**

## 3. 対策案

### 3.1 認証レベルでの包括的対策

**基本方針**: 認証段階で全ての攻撃を阻止し、ビューレベルでの実装ミスも防ぐ

#### **対策の特徴**
1. **二重チェック機構**: パラメータ攻撃 + IDベース攻撃の両方を阻止
2. **最小限の改修**: 主要ファイル2つのみの修正
3. **実装ミス耐性**: ビューの実装に依存しない根本的対策
4. **運用継続性**: 既存システムへの影響最小化

### 3.2 実装内容

#### **改修対象ファイル**
1. `utils/authentication.py` - JWT認証強化
2. `bases/views.py` - BaseFullListの権限フィルタリング

#### **主要機能**
1. **直接パラメータチェック**: リクエストパラメータの会社コード検証
2. **リソース所有者チェック**: IDベースAPIでのデータ所有者確認
3. **権限ベースフィルタリング**: base_listの適切なフィルタリング
