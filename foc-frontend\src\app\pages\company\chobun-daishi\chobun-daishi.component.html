
<div class="container">
  <div class="inner with_footer">
    <div class="contents mini">
      <div class="menu_title">
        <i class="chess board icon big"></i>
        弔文台紙
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #CompanyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" (selectedItemChange)="companyChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="ui segment">
        <div *ngFor="let item of daishi_list">
          <div class="item-box">
            <div class="image-box" [class.selected]="item.selected" (click)="selectItem(item)">
              <img [src]="item.file_name" *ngIf="item.file_name">

              <div class="ui checkbox">
                <input type="checkbox" [(ngModel)]="item.selected">
                <label></label>
              </div>
            </div>
            <div class="name">{{item.name}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" (click)="saveData()">
      <i class="save icon"></i>保存
    </button>
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
