{% load humanize %}
{% load invoice_filters %}
{% load fuhoshi_filters %}
<!DOCTYPE html>
<html>
<title>請求書</title>
<head>
</head>
<body>
<section class="break-after">
  <div class="top">
    <div class="title">請求書</div>
    <div class="page-header">
      <div>No. {{ sales.company_id }}-{{ sales.id }}</div>
      <div>発行日 {{ sales.invoice_date|date:"Y年n月j日" }}</div>
    </div>
    <div class="target-company">
      <div>〒 {{ sales.zip_code|slice:"0:3" }}-{{ sales.zip_code|slice:"3:7" }}</div>
      <div>{{ sales.address_1|default_if_none:"" }}</div>
      <div>{{ sales.address_2|default_if_none:"" }}</div>
      <div class="name">{{ sales.company_name|default_if_none:"" }}</div>
      <div class="right">御中</div>
    </div>
    <div class="sender-company">
      <div><img class="logo" src="{{ images.logo }}">株式会社エム・エス・アイ</div>
      <div>〒 160-0022</div>
      <div>東京都新宿区新宿一丁目1-7</div>
      <div>コスモ新宿御苑ビル6F</div>
      <div class="indent">電話　03-5367-5730（代）</div>
      <div class="indent">FAX　03-5367-5731</div>
    </div>
    <img class="stamp" src="{{ images.stamp }}">
    <div class="description">
      <div>毎々格別のお引き立てを賜わり厚くお礼申し上げます。</div>
      <div>下記の通り御請求申し上げます。</div>
      <div>※振込手数料は貴社にてご負担くださいますよう</div>
      <div>お願い申し上げます。</div>
    </div>
    {% with sales_price=sales.sales_price|default_if_none:0 invoice_tax=sales.invoice_tax|default_if_none:0 koden_fee_commission=sales.koden_fee_commission|default_if_none:0%}
    <div class="total-price">
      <div>{{ sales.pay_date|date:"Y年n月j日" }}までにお振込み下さい。</div>
      <div class="header">今回御請求額</div>
      <div class="data">¥{{ sales_price|add:invoice_tax|add:koden_fee_commission|intcomma }}</div>
    </div>
    <table class="list">
      <tr class="header">
        <td class="date">日付</td>
        <td class="number"></td>
        <td class="subject">件&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</td>
        <td class="quantity" colspan="2">数量</td>
        <td class="price">単&nbsp;&nbsp;価</td>
        <td class="amount">ご請求額</td>
      </tr>
      <tr class="data">
        <td>{{ sales.invoice_date|date:"n/j" }}</td><td></td><td></td>
        <td class="quantity1"></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject">FOC利用料({{sales.invoice_date|first_day|date:"Y年n月j日"}}~{{ sales.invoice_date|date:"n月j日" }})</td>
        <td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">①</td>
        <td class="subject indent">月額費用</td>
        <td>1</td>
        <td>式</td>
        <td class="price">¥{{sales.monthly_fee|default_if_none:0|intcomma}}</td>
        <td class="amount">¥{{sales.monthly_fee|default_if_none:0|intcomma}}</td>
      </tr>
      <tr class="data">
        <td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject">システム利用料({{sales.invoice_date|first_day|date:"Y年n月j日"}}~{{ sales.invoice_date|date:"n月j日" }})</td>
        <td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">①</td>
        <td class="subject indent">弔文</td>
        <td>{{sales.chobun_fee}}</td>
        <td>{{sales.chobun_fee_unit}}</td>
        <td class="price">¥{{sales.chobun_order_price|default_if_none:0|intcomma}}</td>
        <td class="amount">¥{{sales.chobun_order_fee|default_if_none:0|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">②</td>
        <td class="subject indent">供花・供物</td>
        <td>{{sales.kumotsu_fee}}</td>
        <td>{{sales.kumotsu_fee_unit}}</td>
        <td class="price">¥{{sales.kumotsu_order_price|default_if_none:0|intcomma}}</td>
        <td class="amount">¥{{sales.kumotsu_order_fee|default_if_none:0|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">③</td>
        {% with koden_order_price=sales.koden_order_price|default_if_none:0 koden_order_fee=sales.koden_order_fee|default_if_none:0 %}
        <td class="subject indent">香典　※({{koden_order_fee|intcomma}} - {{koden_order_price|add:koden_order_fee|intcomma}} × {{sales.koden_commission_pct|default_if_none:0}}%)</td>
        {% endwith %}
        <td></td>
        <td></td>
        <td></td>
        <td class="amount">¥{{koden_fee_commission|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td>
        <td class="number">④</td>
        <td class="subject indent">返礼品</td>
        <td>{{sales.henreihin_fee}}</td>
        <td>{{sales.henreihin_fee_unit}}</td>
        <td class="price">¥{{sales.henreihin_order_price|default_if_none:0|intcomma}}</td>
        <td class="amount">¥{{sales.henreihin_order_fee|default_if_none:0|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject indent">&nbsp;&nbsp;※明細は別紙に記載</td>
        <td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td></td>
        <td></td>
        <td class="subject"><span>税抜課税対象額（</span><span>¥{{sales_price|intcomma }}）</span></td>
        <td>{{sales.invoice_tax_pct|default_if_none:0}}</td>
        <td>%</td>
        <td class="subject">消費税等</td>
        <td class="amount">¥{{invoice_tax|intcomma}}</td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject"><span>税込課税対象額（</span><span>¥{{koden_fee_commission|intcomma }}）</span></td>
        <td>{{sales.invoice_tax_pct|default_if_none:0}}</td>
        <td>%</td>
        <td></td><td></td>
      </tr>
      <tr class="data">
        <td></td><td></td>
        <td class="subject"><span>内消費税（</span><span>¥{{sales.koden_fee_commission_tax|default_if_none:0|intcomma }}）</span></td>
        <td></td><td></td><td></td><td></td>
      </tr>
      <tr class="data">
        <td colspan="5" class="blank">※香典システム利用料 - (香典額 + 香典システム利用料) × {{sales.koden_commission_pct|default_if_none:0}}%</td>
        <td class="summary">合&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;計</td>
        <td class="amount">¥{{sales_price|add:invoice_tax|add:koden_fee_commission|intcomma}}</td>
      </tr>
    </table>
    {% endwith %}
    <div class="bank-title">
      <div>お振込先</div>
    </div>
    <table class="bank">
      <tr>
        <td class="name">みずほ銀行</td>
        <td class="branch">新宿西口支店</td>
        <td class="type">普通</td>
        <td class="number">2153514</td>
      </tr>
      <tr>
        <td>りそな銀行</td>
        <td>虎ノ門支店</td>
        <td>普通</td>
        <td>7722799</td>
      </tr>
      <tr>
        <td>三菱UFJ銀行</td>
        <td>四谷支店</td>
        <td>普通</td>
        <td>0118831</td>
      </tr>
      <tr>
        <td>三井住友銀行</td>
        <td>新宿西口支店</td>
        <td>普通</td>
        <td>2631630</td>
      </tr>
      <tr>
        <td></td>
        <td colspan="3">口座名義：株式会社エム・エス・アイ</td>
      </tr>
    </table>
  </div>
</section>

<section class="content" style="clear: both">
  <div class="detail">
    <div class="page-header">
      <div>No. {{ sales.company_id }}-{{ sales.id }}</div>
      <div>発行日 {{ sales.invoice_date|date:"Y年n月j日" }}</div>
    </div>
    <table class="list">
      <thead>
        <tr>
          <td>
            <div class="page-header-space"></div>
          </td>
        </tr>
        <tr class="header">
          <td class="note">詳細</td>
          <td class="number">注文番号</td>
          <td class="entry_name">申込者</td>
          <td class="quantity">数量</td>
          <td class="price">販売単価<br>（税抜）</td>
          <td class="amount">販売金額<br>（税抜）</td>
          <td class="amount">販売金額<br>（税込）</td>
          <td class="mark"></td>
        </tr>
      </thead>
      <tbody>
        {% for department in sales.sales_seko_departments.all %}
        <tr class="data">
          <td colspan="8">【{{department.base_name}}】</td>
        </tr>
        {% for detail in department.sales_details.all %}
        {% if department.sales_details.all|is_first_of_seko:forloop.counter %}
        <tr class="data">
          <td colspan="8">施行日{{detail.seko_date|date:'n/j'}}【{{detail.soke_name}}】（故人）{{detail.kojin_name}}（喪主）{{detail.moshu_name}}　葬儀番号：{{detail.seko_id}}</td>
        </tr>
        {% endif %}
        {% if department.sales_details.all|is_first_of_service:forloop.counter %}
        <tr class="data">
          <td colspan="8">【{{detail.service_name}}】</td>
        </tr>
        {% endif %}
        <tr class="data">
        {% if detail.service_id == 30 and not detail.item_name %}
          <td class="small-indent">{{ detail.service_name|default_if_none:'' }}</td>
        {% else %}
          <td class="small-indent">{{ detail.item_name|default_if_none:'' }}</td>
        {% endif %}
          <td class="center">{{ detail.entry_id|default_if_none:'' }}</td>
          <td>{{ detail.entry_name|default_if_none:'' }}</td>
          <td class="center">{{ detail.quantity|intcomma }}</td>
          {% if detail.item_name != "香典システム利用料" %}
          <td class="right">{{ detail.item_price|divide:detail.quantity|intcomma }}</td>
          <td class="right">{{ detail.item_price|default_if_none:0|intcomma }}</td>
          {% else %}
          <td class="right"></td>
          <td class="right"></td>
          {% endif %}
          <td class="right">{{ detail.item_taxed_price|multiple:detail.quantity|intcomma }}</td>
          <td class="center">{% if detail.keigen_flg %}*{% endif %}</td>
        </tr>
        {% endfor %}
        <tr class="data">
          <td colspan="8">【{{department.base_name}} ホール合計】</td>
        </tr>
        {% if department.chobun_qty %}
        <tr class="data">
          <td class="indent">【弔文】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.chobun_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ department.chobun_order_price|intcomma }}</td>
          <td class="right">{{ department.chobun_order_price|add:department.chobun_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if department.kumotsu_qty %}
        <tr class="data">
          <td class="indent">【供花・供物】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.kumotsu_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ department.kumotsu_order_price|intcomma }}</td>
          <td class="right">{{ department.kumotsu_order_price|add:department.kumotsu_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if department.koden_qty %}
        <tr class="data">
          <td class="indent">【香典】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.koden_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ department.koden_order_price|intcomma }}</td>
          <td class="right">{{ department.koden_order_price|add:department.koden_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if department.koden_order_fee %}
        <tr class="data">
          <td class="indent">【香典システム利用料】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.koden_qty|intcomma }}</td>
          <td></td>
          <td class="right"></td>
          <td class="right">{{ department.koden_order_fee|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if department.henreihin_qty %}
        <tr class="data">
          <td class="indent">【返礼品】</td>
          <td></td>
          <td></td>
          <td class="center">{{ department.henreihin_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ department.henreihin_order_price|intcomma }}</td>
          <td class="right">{{ department.henreihin_order_price|add:department.henreihin_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% endfor %}

        <tr class="data">
          <td colspan="8">【販売総合計】</td>
        </tr>
        {% if sales.chobun_qty %}
        <tr class="data">
          <td class="indent">【弔文】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.chobun_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ sales.chobun_order_price|intcomma }}</td>
          <td class="right">{{ sales.chobun_order_price|add:sales.chobun_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if sales.kumotsu_qty %}
        <tr class="data">
          <td class="indent">【供花・供物】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.kumotsu_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ sales.kumotsu_order_price|intcomma }}</td>
          <td class="right">{{ sales.kumotsu_order_price|add:sales.kumotsu_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if sales.koden_qty %}
        <tr class="data">
          <td class="indent">【香典】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.koden_qty|intcomma }}</td>
          <td></td>
          <td class="right"></td>
          <td class="right">{{ sales.koden_order_price|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if sales.koden_order_fee %}
        <tr class="data">
          <td class="indent">【香典システム利用料】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.koden_qty|intcomma }}</td>
          <td></td>
          <td class="right"></td>
          <td class="right">{{ sales.koden_order_fee|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
        {% if sales.henreihin_qty %}
        <tr class="data">
          <td class="indent">【返礼品】</td>
          <td></td>
          <td></td>
          <td class="center">{{ sales.henreihin_qty|intcomma }}</td>
          <td></td>
          <td class="right">{{ sales.henreihin_order_price|intcomma }}</td>
          <td class="right">{{ sales.henreihin_order_price|add:sales.henreihin_tax|intcomma }}</td>
          <td></td>
        </tr>
        {% endif %}
      </tbody>
      <tfoot>
        <tr>
          <td colspan="8">
            <div></div>
          </td>
        </tr>
      </tfoot>
    </table>
  </div>
</section>
</body>
</html>
