# 業務系API仕様書

## 概要

FOCシステムの業務系API（注文、返礼、アフターフォロー、法要等）の詳細仕様です。葬儀に関連する各種業務処理の管理機能を提供します。

## 1. 注文管理API

### 1.1 注文一覧・作成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/POST /orders/` |
| **機能** | 注文一覧取得・注文作成 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `EntryList` |
| **シリアライザー** | `EntrySerializer` |

**HTTPメソッド:** `GET/POST`

**クエリパラメータ（GET）:**
| パラメータ | 型 | 必須 | 説明 | 使用例 |
|---|---|---|---|---|
| `seko` | integer | ○ | 施行ID | `?seko=123` |
| `entry_from` | date | ○ | 注文日時（開始） | `?entry_from=2024-06-01` |
| `entry_to` | date | ○ | 注文日時（終了） | `?entry_to=2024-06-30` |
| `cancel_flg` | boolean | ○ | キャンセル状態 | `?cancel_flg=false` |
| `koden_henrei_from` | date | ○ | 香典返礼作成日（開始） | `?koden_henrei_from=2024-06-01` |
| `koden_henrei_to` | date | ○ | 香典返礼作成日（終了） | `?koden_henrei_to=2024-06-30` |

**レスポンス例（GET）:**
```json
{
  "count": 25,
  "next": "http://localhost:8000/orders/?page=2",
  "previous": null,
  "results": [
    {
      "id": 123,
      "seko": {
        "id": 456,
        "soke_name": "田中家",
        "seko_style_name": "家族葬"
      },
      "payment": {
        "id": 1,
        "payment_name": "クレジットカード",
        "payment_code": "CREDIT"
      },
      "entry_name": "田中 太郎",
      "entry_name_kana": "タナカ タロウ",
      "entry_zip_code": "1000001",
      "entry_prefecture": "東京都",
      "entry_address_1": "千代田区千代田",
      "entry_address_2": "1-1-1",
      "entry_address_3": "マンション101",
      "entry_tel": "03-1234-5678",
      "entry_mail_address": "<EMAIL>",
      "entry_ts": "2024-06-25T14:30:00+09:00",
      "cancel_ts": null,
      "receipt_count": 1,
      "details": [
        {
          "id": 789,
          "service": {
            "id": 1,
            "service_name": "弔文作成"
          },
          "quantity": 1,
          "unit_price": 5000,
          "total_amount": 5000
        }
      ]
    }
  ]
}
```

**リクエストボディ（POST）:**
```json
{
  "seko": 456,
  "payment": 1,
  "entry_name": "田中 太郎",
  "entry_name_kana": "タナカ タロウ",
  "entry_zip_code": "1000001",
  "entry_prefecture": "東京都",
  "entry_address_1": "千代田区千代田",
  "entry_address_2": "1-1-1",
  "entry_address_3": "マンション101",
  "entry_tel": "03-1234-5678",
  "entry_mail_address": "<EMAIL>",
  "epsilon_token": "ep_token_12345",
  "billing_amount": 5500,
  "con_count": 1,
  "order_number": 987654,
  "details": [
    {
      "service": 1,
      "quantity": 1,
      "unit_price": 5000,
      "chobun": {
        "chobun_daishi": 1,
        "chobun_content": "心からお悔やみ申し上げます。",
        "chobun_name": "田中 花子",
        "chobun_name_kana": "タナカ ハナコ"
      }
    }
  ]
}
```

**バリデーション:**
| フィールド | 制約 | エラーメッセージ |
|---|---|---|
| `entry_name` | 必須、最大100文字 | This field is required. |
| `entry_zip_code` | 必須、7桁数字 | Enter a valid zip code. |
| `entry_tel` | 必須、最大15文字 | This field is required. |
| `entry_mail_address` | 必須、メール形式 | Enter a valid email address. |
| `billing_amount` | 必須、正の整数 | Ensure this value is greater than 0. |

### 1.2 注文詳細API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/{id}/` |
| **機能** | 注文詳細取得 |
| **認証** | 不要 |
| **権限** | なし |
| **実装クラス** | `EntryDetailView` |
| **シリアライザー** | `EntryDownwardSerializer` |

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 注文ID |

**レスポンス例:**
```json
{
  "id": 123,
  "seko": {
    "id": 456,
    "soke_name": "田中家",
    "seko_style_name": "家族葬",
    "death_date": "2024-06-20",
    "seko_date": "2024-06-25T10:00:00+09:00"
  },
  "payment": {
    "id": 1,
    "payment_name": "クレジットカード",
    "payment_code": "CREDIT"
  },
  "entry_name": "田中 太郎",
  "entry_name_kana": "タナカ タロウ",
  "entry_zip_code": "1000001",
  "entry_prefecture": "東京都",
  "entry_address_1": "千代田区千代田",
  "entry_address_2": "1-1-1",
  "entry_address_3": "マンション101",
  "entry_tel": "03-1234-5678",
  "entry_mail_address": "<EMAIL>",
  "entry_ts": "2024-06-25T14:30:00+09:00",
  "cancel_ts": null,
  "receipt_count": 1,
  "details": [
    {
      "id": 789,
      "service": {
        "id": 1,
        "service_name": "弔文作成",
        "service_category": "文書作成"
      },
      "quantity": 1,
      "unit_price": 5000,
      "total_amount": 5000,
      "chobun": {
        "id": 101,
        "chobun_daishi": {
          "id": 1,
          "daishi_name": "桜の台紙",
          "daishi_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
        },
        "chobun_content": "心からお悔やみ申し上げます。",
        "chobun_name": "田中 花子",
        "chobun_name_kana": "タナカ ハナコ"
      }
    }
  ]
}
```

### 1.3 注文キャンセルAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /orders/{id}/cancel/` |
| **機能** | 注文キャンセル |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `EntryCancelView` |
| **シリアライザー** | `EntryCancelSerializer` |

**HTTPメソッド:** `POST`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 注文ID |

**リクエストボディ:**
```json
{
  "cancel_reason": "お客様都合によるキャンセル"
}
```

**レスポンス例:**
```json
{
  "id": 123,
  "cancel_ts": "2024-06-26T10:00:00+09:00",
  "cancel_reason": "お客様都合によるキャンセル",
  "message": "注文をキャンセルしました。"
}
```

### 1.4 領収書PDF生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/{id}/receipt/` |
| **機能** | 領収書PDF生成 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `ReceiptPDF` |
| **シリアライザー** | `ReceiptSerializer` |

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 注文ID |

**レスポンス:** PDF形式の領収書ファイル

**Content-Type:** `application/pdf`

## 2. 注文詳細管理API

### 2.1 弔文注文一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/chobun/` |
| **機能** | 弔文注文一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `ChobunList` |
| **シリアライザー** | `SimpleEntryDetailChobunSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 101,
    "entry_detail": {
      "id": 789,
      "entry": {
        "id": 123,
        "entry_name": "田中 太郎",
        "entry_ts": "2024-06-25T14:30:00+09:00"
      },
      "service": {
        "id": 1,
        "service_name": "弔文作成"
      },
      "quantity": 1,
      "unit_price": 5000
    },
    "chobun_daishi": {
      "id": 1,
      "daishi_name": "桜の台紙",
      "daishi_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
    },
    "chobun_content": "心からお悔やみ申し上げます。",
    "chobun_name": "田中 花子",
    "chobun_name_kana": "タナカ ハナコ",
    "created_at": "2024-06-25T14:30:00+09:00"
  }
]
```

### 2.3 香典注文一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/koden/` |
| **機能** | 香典注文一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `KodenList` |
| **シリアライザー** | `SimpleEntryDetailKodenSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 301,
    "entry_detail": {
      "id": 791,
      "entry": {
        "id": 125,
        "entry_name": "鈴木 三郎",
        "entry_ts": "2024-06-25T16:00:00+09:00"
      },
      "service": {
        "id": 3,
        "service_name": "香典記帳"
      },
      "quantity": 1,
      "unit_price": 3000
    },
    "koden_amount": 10000,
    "koden_name": "鈴木 三郎",
    "koden_name_kana": "スズキ サブロウ",
    "koden_relationship": "友人",
    "henrei_koden": {
      "id": 401,
      "henrei_amount": 3000,
      "henrei_item": {
        "id": 20,
        "item_name": "お茶セット",
        "item_category": "返礼品"
      },
      "order_status": 1,
      "created_at": "2024-06-25T16:00:00+09:00"
    },
    "created_at": "2024-06-25T16:00:00+09:00"
  }
]
```

### 2.4 メッセージ注文一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/msg/` |
| **機能** | メッセージ注文一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `MsgList` |
| **シリアライザー** | `SimpleEntryDetailMsgSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 501,
    "entry_detail": {
      "id": 792,
      "entry": {
        "id": 126,
        "entry_name": "高橋 四郎",
        "entry_ts": "2024-06-25T17:00:00+09:00"
      },
      "service": {
        "id": 4,
        "service_name": "メッセージ作成"
      },
      "quantity": 1,
      "unit_price": 2000
    },
    "msg_content": "心よりお悔やみ申し上げます。故人のご冥福をお祈りいたします。",
    "msg_name": "高橋 四郎",
    "msg_name_kana": "タカハシ シロウ",
    "created_at": "2024-06-25T17:00:00+09:00"
  }
]
```

### 2.5 供物注文ステータス更新API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /orders/kumotsu/order_status/` |
| **機能** | 供物注文ステータス一括更新 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `KumotsuUpdateStatus` |
| **シリアライザー** | `EntryDetailKumotsuUpdateStatusSerializer` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "ids": [201, 202, 203],
  "order_status": 2
}
```

**ステータス値:**
| 値 | 意味 |
|---|---|
| 1 | 注文受付 |
| 2 | 発注済み |
| 3 | 配送中 |
| 4 | 配送完了 |
| 9 | キャンセル |

**レスポンス例:**
```json
{
  "updated_count": 3,
  "updated_items": [
    {
      "id": 201,
      "order_status": 2,
      "order_ts": "2024-06-26T10:00:00+09:00",
      "order_staff": {
        "id": 10,
        "name": "管理者 太郎"
      }
    },
    {
      "id": 202,
      "order_status": 2,
      "order_ts": "2024-06-26T10:00:00+09:00",
      "order_staff": {
        "id": 10,
        "name": "管理者 太郎"
      }
    },
    {
      "id": 203,
      "order_status": 2,
      "order_ts": "2024-06-26T10:00:00+09:00",
      "order_staff": {
        "id": 10,
        "name": "管理者 太郎"
      }
    }
  ]
}
```

## 3. 返礼品管理API

### 3.1 返礼品注文一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /henrei/` |
| **機能** | 返礼品注文一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `HenreiList` |
| **シリアライザー** | `HenreiListSerializer` |

**HTTPメソッド:** `GET`

**クエリパラメータ:**
| パラメータ | 型 | 必須 | 説明 | 使用例 |
|---|---|---|---|---|
| `seko` | integer | ○ | 施行ID | `?seko=123` |
| `supplier` | integer | ○ | 仕入先ID | `?supplier=5` |
| `order_status` | integer | ○ | 注文ステータス | `?order_status=1` |
| `order_from` | date | ○ | 注文日（開始） | `?order_from=2024-06-01` |
| `order_to` | date | ○ | 注文日（終了） | `?order_to=2024-06-30` |

**レスポンス例:**
```json
{
  "count": 15,
  "next": "http://localhost:8000/henrei/?page=2",
  "previous": null,
  "results": [
    {
      "id": 601,
      "seko": {
        "id": 456,
        "soke_name": "田中家",
        "seko_style_name": "家族葬"
      },
      "supplier": {
        "id": 5,
        "supplier_name": "返礼品センター",
        "supplier_tel": "03-1111-2222"
      },
      "order_status": 1,
      "order_ts": null,
      "order_staff": null,
      "order_note": null,
      "henrei_koden": [
        {
          "id": 401,
          "koden_name": "鈴木 三郎",
          "koden_amount": 10000,
          "henrei_amount": 3000,
          "henrei_item": {
            "id": 20,
            "item_name": "お茶セット",
            "item_category": "返礼品"
          }
        }
      ],
      "henrei_kumotsu": [
        {
          "id": 501,
          "kumotsu_name": "供物返礼",
          "henrei_amount": 2000,
          "henrei_item": {
            "id": 21,
            "item_name": "タオルセット",
            "item_category": "返礼品"
          }
        }
      ],
      "created_at": "2024-06-25T16:00:00+09:00"
    }
  ]
}
```

### 3.2 返礼品注文作成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /henrei/` |
| **機能** | 返礼品注文作成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `HenreiCreate` |
| **シリアライザー** | `HenreiCreateSerializer` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "seko": 456,
  "supplier": 5
}
```

**レスポンス例:**
```json
{
  "id": 602,
  "seko": 456,
  "supplier": 5,
  "order_status": 1,
  "order_ts": null,
  "order_staff": null,
  "order_note": null,
  "created_at": "2024-06-26T10:00:00+09:00",
  "message": "返礼品注文を作成しました。"
}
```

### 3.3 返礼品注文ステータス更新API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /henrei/order_status/` |
| **機能** | 返礼品注文ステータス一括更新 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `OrderHenreiUpdateStatus` |
| **シリアライザー** | `OrderHenreiUpdateStatusSerializer` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "ids": [601, 602, 603],
  "order_status": 2
}
```

**レスポンス例:**
```json
{
  "updated_count": 3,
  "updated_orders": [
    {
      "id": 601,
      "order_status": 2,
      "order_ts": "2024-06-26T10:00:00+09:00",
      "order_staff": {
        "id": 10,
        "name": "管理者 太郎"
      }
    },
    {
      "id": 602,
      "order_status": 2,
      "order_ts": "2024-06-26T10:00:00+09:00",
      "order_staff": {
        "id": 10,
        "name": "管理者 太郎"
      }
    },
    {
      "id": 603,
      "order_status": 2,
      "order_ts": "2024-06-26T10:00:00+09:00",
      "order_staff": {
        "id": 10,
        "name": "管理者 太郎"
      }
    }
  ]
}
```

### 3.4 喪主向け返礼品注文API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /henrei/place_order/` |
| **機能** | 喪主による返礼品注文 |
| **認証** | JWT認証必須 |
| **権限** | IsMoshu（喪主のみ） |
| **実装クラス** | `PlaceHenreiOrder` |
| **シリアライザー** | `PlaceHenreiOrderSerializer` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "seko": 456
}
```

**レスポンス例:**
```json
{
  "order_id": 603,
  "seko": 456,
  "total_koden_count": 15,
  "total_kumotsu_count": 8,
  "estimated_delivery_date": "2024-07-03",
  "message": "返礼品の注文を受け付けました。",
  "result": {
    "henrei_koden_count": 15,
    "henrei_kumotsu_count": 8,
    "total_henrei_amount": 45000
  }
}
```

## 4. アフターフォローAPI

### 4.1 アフターフォロー一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /after_follow/` |
| **機能** | アフターフォロー一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `AfterFollowList` |
| **シリアライザー** | `AfterFollowListSerializer` |

**HTTPメソッド:** `GET`

**クエリパラメータ:**
| パラメータ | 型 | 必須 | 説明 | 使用例 |
|---|---|---|---|---|
| `seko` | integer | ○ | 施行ID | `?seko=123` |
| `af_type` | integer | ○ | アフタータイプ | `?af_type=1` |
| `status` | integer | ○ | ステータス | `?status=1` |
| `scheduled_from` | date | ○ | 予定日（開始） | `?scheduled_from=2024-07-01` |
| `scheduled_to` | date | ○ | 予定日（終了） | `?scheduled_to=2024-07-31` |

**レスポンス例:**
```json
{
  "count": 10,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 701,
      "seko": {
        "id": 456,
        "soke_name": "田中家",
        "seko_style_name": "家族葬",
        "death_date": "2024-06-20"
      },
      "af_type": 1,
      "af_type_name": "初七日フォロー",
      "scheduled_date": "2024-06-27",
      "completed_date": null,
      "status": 1,
      "status_name": "予定",
      "staff": {
        "id": 10,
        "name": "担当者 太郎"
      },
      "activities": [
        {
          "id": 801,
          "activity_type": "電話連絡",
          "activity_content": "初七日の準備について確認",
          "completed_flg": false,
          "scheduled_datetime": "2024-06-27T10:00:00+09:00"
        }
      ],
      "created_at": "2024-06-25T18:00:00+09:00"
    }
  ]
}
```

### 4.2 アフターフォロー作成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /after_follow/` |
| **機能** | アフターフォロー作成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `AfterFollowCreate` |
| **シリアライザー** | `AfterFollowCreateSerializer` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "seko": 456,
  "af_type": 1,
  "scheduled_date": "2024-06-27",
  "staff": 10,
  "activities": [
    {
      "activity_type": "電話連絡",
      "activity_content": "初七日の準備について確認",
      "scheduled_datetime": "2024-06-27T10:00:00+09:00"
    },
    {
      "activity_type": "メール送信",
      "activity_content": "初七日のご案内メール",
      "scheduled_datetime": "2024-06-27T09:00:00+09:00"
    }
  ]
}
```

**レスポンス例:**
```json
{
  "id": 702,
  "seko": 456,
  "af_type": 1,
  "scheduled_date": "2024-06-27",
  "status": 1,
  "staff": 10,
  "activities_count": 2,
  "created_at": "2024-06-26T10:00:00+09:00",
  "message": "アフターフォローを作成しました。"
}
```

## 5. 法要管理API

### 5.1 法要一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /hoyo/` |
| **機能** | 法要一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `HoyoList` |
| **シリアライザー** | `HoyoSerializer` |

**HTTPメソッド:** `GET`

**クエリパラメータ:**
| パラメータ | 型 | 必須 | 説明 | 使用例 |
|---|---|---|---|---|
| `seko` | integer | ○ | 施行ID | `?seko=123` |
| `hoyo_style` | integer | ○ | 法要形式 | `?hoyo_style=1` |
| `status` | integer | ○ | ステータス | `?status=1` |
| `scheduled_from` | date | ○ | 予定日（開始） | `?scheduled_from=2024-07-01` |
| `scheduled_to` | date | ○ | 予定日（終了） | `?scheduled_to=2024-07-31` |

**レスポンス例:**
```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 901,
      "seko": {
        "id": 456,
        "soke_name": "田中家",
        "kojin": {
          "name": "田中 一郎",
          "death_date": "2024-06-20"
        }
      },
      "hoyo_style": {
        "id": 1,
        "name": "一般法要",
        "wareki_use_flg": true
      },
      "hoyo_name": "初七日",
      "scheduled_date": "2024-06-27",
      "scheduled_time": "14:00:00",
      "venue_name": "自宅",
      "venue_address": "東京都千代田区千代田1-1-1",
      "participant_count": 10,
      "status": 1,
      "status_name": "予定",
      "schedules": [
        {
          "id": 1001,
          "schedule_name": "読経",
          "scheduled_datetime": "2024-06-27T14:00:00+09:00",
          "duration_minutes": 30
        },
        {
          "id": 1002,
          "schedule_name": "焼香",
          "scheduled_datetime": "2024-06-27T14:30:00+09:00",
          "duration_minutes": 15
        }
      ],
      "created_at": "2024-06-25T19:00:00+09:00"
    }
  ]
}
```

### 5.2 法要作成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /hoyo/` |
| **機能** | 法要作成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `HoyoCreate` |
| **シリアライザー** | `HoyoCreateSerializer` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "seko": 456,
  "hoyo_style": 1,
  "hoyo_name": "初七日",
  "scheduled_date": "2024-06-27",
  "scheduled_time": "14:00:00",
  "venue_name": "自宅",
  "venue_address": "東京都千代田区千代田1-1-1",
  "participant_count": 10,
  "schedules": [
    {
      "schedule_name": "読経",
      "scheduled_datetime": "2024-06-27T14:00:00+09:00",
      "duration_minutes": 30
    },
    {
      "schedule_name": "焼香",
      "scheduled_datetime": "2024-06-27T14:30:00+09:00",
      "duration_minutes": 15
    }
  ]
}
```

**レスポンス例:**
```json
{
  "id": 902,
  "seko": 456,
  "hoyo_style": 1,
  "hoyo_name": "初七日",
  "scheduled_date": "2024-06-27",
  "status": 1,
  "schedules_count": 2,
  "created_at": "2024-06-26T10:00:00+09:00",
  "message": "法要を作成しました。"
}
```

## 6. FDN連携API

### 6.1 FDN注文一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/fdn/` |
| **機能** | FDN連携注文一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `FdnOrderList` |
| **シリアライザー** | `FdnOrderListSerializer` |

**HTTPメソッド:** `GET`

**クエリパラメータ:**
| パラメータ | 型 | 必須 | 説明 | 使用例 |
|---|---|---|---|---|
| `company` | integer | ○ | 会社ID | `?company=1` |
| `seko_id` | integer | ○ | 施行ID | `?seko_id=123` |
| `entry_id` | integer | ○ | 注文ID | `?entry_id=456` |
| `service_id` | integer | ○ | サービスID | `?service_id=1` |
| `order_from` | date | ○ | 注文日（開始） | `?order_from=2024-06-01` |
| `order_to` | date | ○ | 注文日（終了） | `?order_to=2024-06-30` |

**レスポンス例:**
```json
[
  {
    "seko_id": 456,
    "soke_name": "田中家",
    "entry_id": 123,
    "entry_detail_id": 789,
    "service_id": 1,
    "service_name": "弔文作成",
    "quantity": 1,
    "unit_price": 5000,
    "total_amount": 5000,
    "order_type": "chobun",
    "order_status": 1,
    "order_ts": null,
    "created_at": "2024-06-25T14:30:00+09:00"
  },
  {
    "seko_id": 456,
    "soke_name": "田中家",
    "entry_id": 124,
    "entry_detail_id": 790,
    "service_id": 2,
    "service_name": "供物配送",
    "quantity": 1,
    "unit_price": 8000,
    "total_amount": 8000,
    "order_type": "kumotsu",
    "order_status": 2,
    "order_ts": "2024-06-26T10:00:00+09:00",
    "created_at": "2024-06-25T15:00:00+09:00"
  }
]
```

## 7. 決済関連API

### 7.1 決済結果一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/paymentresult/` |
| **機能** | 決済結果一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `PaymentResultListView` |
| **シリアライザー** | `PaymentResultSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
{
  "count": 20,
  "next": "http://localhost:8000/orders/paymentresult/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1101,
      "entry": {
        "id": 123,
        "entry_name": "田中 太郎",
        "entry_ts": "2024-06-25T14:30:00+09:00"
      },
      "payment_method": "CREDIT",
      "payment_amount": 5500,
      "payment_status": "SUCCESS",
      "transaction_id": "txn_12345678",
      "epsilon_order_number": 987654,
      "payment_ts": "2024-06-25T14:35:00+09:00",
      "created_at": "2024-06-25T14:35:00+09:00"
    }
  ]
}
```

### 7.2 Epsilon決済戻りAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /orders/epsilon/return/` |
| **機能** | Epsilon決済システムからの戻り処理 |
| **認証** | 不要 |
| **権限** | なし |
| **実装クラス** | `EpsilonReturn` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "order_number": 987654,
  "result": "1",
  "trans_code": "txn_12345678",
  "user_id": "123",
  "user_name": "田中 太郎",
  "user_email": "<EMAIL>",
  "item_code": "CHOBUN_001",
  "item_name": "弔文作成",
  "order_number": 987654,
  "st_code": "10000",
  "mission_code": "1234567890"
}
```

**レスポンス例:**
```json
{
  "status": "success",
  "message": "決済結果を正常に処理しました。",
  "entry_id": 123,
  "payment_status": "SUCCESS"
}
```

## 8. エラーコード一覧

| ステータスコード | エラーメッセージ | 発生条件 |
|---|---|---|
| 400 | This field is required. | 必須フィールドが未入力 |
| 400 | Invalid pk "999" - object does not exist. | 存在しないIDを指定 |
| 400 | Ensure this value is greater than 0. | 金額が0以下 |
| 400 | Enter a valid email address. | 無効なメールアドレス形式 |
| 400 | Seko not found | 指定された施行が存在しない |
| 400 | Supplier not found | 指定された仕入先が存在しない |
| 400 | Invalid order status | 無効な注文ステータス |
| 401 | Authentication credentials were not provided. | 認証情報なし |
| 403 | You do not have permission to perform this action. | 権限不足 |
| 403 | The seko is not yours. | 他の喪主の施行へのアクセス |
| 404 | Not found. | 指定されたリソースが存在しない |
| 409 | Order already cancelled. | 既にキャンセル済みの注文 |
| 409 | Order already completed. | 既に完了済みの注文 |

## 9. セキュリティ考慮事項

### 9.1 データ保護
- **論理削除**: 注文データは物理削除ではなく論理削除
- **個人情報保護**: 注文者情報の適切な暗号化と保護
- **決済情報**: クレジットカード情報の非保持（PCI DSS準拠）

### 9.2 認証・認可
- **JWT認証**: アクセストークンによる認証
- **権限制御**: スタッフと喪主の適切な権限分離
- **施行所有権**: 喪主は自分の施行のみアクセス可能

### 9.3 トランザクション管理
- **ACID特性**: 注文作成時の整合性保証
- **決済連携**: Epsilon決済システムとの安全な連携
- **ロールバック**: エラー時の適切なロールバック処理

### 9.4 パフォーマンス最適化
- **select_related**: 関連オブジェクトの効率的な取得
- **prefetch_related**: 多対多関係の効率的な取得
- **インデックス**: 検索対象フィールドへの適切なインデックス

## 10. 使用例

### 10.1 弔文注文の完全フロー
```javascript
// 1. 弔文注文作成
const createChobunOrder = async (orderData) => {
  const response = await fetch('/orders/', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      seko: orderData.sekoId,
      payment: orderData.paymentType,
      entry_name: orderData.customerName,
      entry_name_kana: orderData.customerKana,
      entry_zip_code: orderData.zipCode,
      entry_prefecture: orderData.prefecture,
      entry_address_1: orderData.address1,
      entry_address_2: orderData.address2,
      entry_tel: orderData.tel,
      entry_mail_address: orderData.email,
      epsilon_token: orderData.epsilonToken,
      billing_amount: orderData.totalAmount,
      con_count: 1,
      details: [{
        service: 1, // 弔文作成サービス
        quantity: 1,
        unit_price: 5000,
        chobun: {
          chobun_daishi: orderData.daishiId,
          chobun_content: orderData.message,
          chobun_name: orderData.senderName,
          chobun_name_kana: orderData.senderKana
        }
      }]
    })
  });

  return response.json();
};

// 2. 注文詳細確認
const getOrderDetail = async (orderId) => {
  const response = await fetch(`/orders/${orderId}/`);
  return response.json();
};

// 3. 領収書PDF生成
const generateReceipt = async (orderId) => {
  const response = await fetch(`/orders/${orderId}/receipt/`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });

  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `receipt_${orderId}.pdf`;
  a.click();
};
```

### 10.2 返礼品注文管理
```javascript
// 喪主による返礼品注文
const placeHenreiOrder = async (sekoId) => {
  const response = await fetch('/henrei/place_order/', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${moshuToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ seko: sekoId })
  });

  return response.json();
};

// スタッフによる返礼品注文ステータス更新
const updateHenreiStatus = async (orderIds, newStatus) => {
  const response = await fetch('/henrei/order_status/', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${staffToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      ids: orderIds,
      order_status: newStatus
    })
  });

  return response.json();
};
```

### 10.3 法要スケジュール管理
```javascript
// 法要作成（初期値マスタから自動生成）
const createHoyoFromDefaults = async (sekoId, deathDate, hoyoStyleId) => {
  // マスターデータから法要初期値取得
  const defaults = await fetch('/masters/hoyo_default/', {
    headers: { 'Authorization': `Bearer ${token}` }
  }).then(res => res.json());

  // 指定された法要形式の初期値をフィルタ
  const hoyoDefaults = defaults.filter(def => def.hoyo_style.id === hoyoStyleId);

  // 各法要の作成
  const hoyoPromises = hoyoDefaults.map(async (def) => {
    const scheduledDate = new Date(deathDate);

    // 経過時間を加算
    switch (def.unit) {
      case 1: // 日
        scheduledDate.setDate(scheduledDate.getDate() + def.elapsed_time);
        break;
      case 2: // 月
        scheduledDate.setMonth(scheduledDate.getMonth() + def.elapsed_time);
        break;
      case 3: // 年
        scheduledDate.setFullYear(scheduledDate.getFullYear() + def.elapsed_time);
        break;
    }

    return fetch('/hoyo/', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        seko: sekoId,
        hoyo_style: hoyoStyleId,
        hoyo_name: def.name,
        scheduled_date: scheduledDate.toISOString().split('T')[0],
        scheduled_time: "14:00:00",
        venue_name: "自宅",
        participant_count: 10
      })
    }).then(res => res.json());
  });

  return Promise.all(hoyoPromises);
};
```

### 10.4 注文状況ダッシュボード
```javascript
// 注文状況の統計取得
const getOrderStatistics = async (dateFrom, dateTo) => {
  const [orders, henreiOrders, afterFollows] = await Promise.all([
    fetch(`/orders/?entry_from=${dateFrom}&entry_to=${dateTo}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    }).then(res => res.json()),

    fetch(`/henrei/?order_from=${dateFrom}&order_to=${dateTo}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    }).then(res => res.json()),

    fetch(`/after_follow/?scheduled_from=${dateFrom}&scheduled_to=${dateTo}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    }).then(res => res.json())
  ]);

  return {
    totalOrders: orders.count,
    totalHenreiOrders: henreiOrders.count,
    totalAfterFollows: afterFollows.count,
    ordersByType: {
      chobun: orders.results.filter(o => o.details.some(d => d.chobun)).length,
      kumotsu: orders.results.filter(o => o.details.some(d => d.kumotsu)).length,
      koden: orders.results.filter(o => o.details.some(d => d.koden)).length
    }
  };
};
```

### 2.2 供物注文一覧API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/kumotsu/` |
| **機能** | 供物注文一覧取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `KumotsuList` |
| **シリアライザー** | `SimpleEntryDetailKumotsuSerializer` |

**HTTPメソッド:** `GET`

**レスポンス例:**
```json
[
  {
    "id": 201,
    "entry_detail": {
      "id": 790,
      "entry": {
        "id": 124,
        "entry_name": "佐藤 次郎",
        "entry_ts": "2024-06-25T15:00:00+09:00"
      },
      "service": {
        "id": 2,
        "service_name": "供物配送"
      },
      "quantity": 1,
      "unit_price": 8000
    },
    "item": {
      "id": 10,
      "item_name": "白菊の花束",
      "item_category": "生花"
    },
    "supplier": {
      "id": 5,
      "supplier_name": "花屋ABC",
      "supplier_tel": "03-9876-5432"
    },
    "order_status": 1,
    "order_ts": null,
    "delivery_ts": null,
    "order_note": null,
    "created_at": "2024-06-25T15:00:00+09:00"
  }
]
```
