import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EventGuidanceComponent } from './event-guidance.component';

describe('EventGuidanceComponent', () => {
  let component: EventGuidanceComponent;
  let fixture: ComponentFixture<EventGuidanceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EventGuidanceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EventGuidanceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
