
<div class="container">
  <div class="inner">
    <div class="contents top">
      <div class="data_area">
        <table>
          <tbody class="bigger">
            <tr>
              <td>
                <div class="label">訃報ページ</div>
                <div class="data">
                  <a target="_blank" href="/fuho/{{seko_info?.seko_company.id}}-{{seko_info?.id}}/">こちら</a>
                </div>
              </td>
            </tr>
          </tbody>
          <tbody>
            <tr>
              <td>
                <div class="label">訃報掲載期限</div>
                <div class="data">{{seko_info?.fuho_site_end_date | date:'yyyy/MM/dd'}}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="contents body">
      <h2>施行情報</h2>
      <div class="data_area">
        <table>
          <tbody *ngFor="let kojin of seko_info.kojin">
            <tr>
              <td>
                <div class="label">故人名</div>
                <div class="data">{{kojin.name}}様</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="label">ご命日</div>
                <div class="data">{{kojin.death_date | date:'yyyy/MM/dd'}} {{kojin.age_kbn}} {{kojin.age}}才
                </div>
              </td>
            </tr>
          </tbody>
          <tbody *ngFor="let schedule of seko_info.schedules">
            <tr>
              <td>
                <div class="label">{{schedule.schedule_name}}</div>
                <div class="data">{{schedule.hall_name}}
                  <p>
                    {{schedule.schedule_date|date:"yyyy/MM/dd"}}({{utils.getWeekName(schedule.schedule_date)}})
                    {{schedule.display_begin_time}} 〜 {{schedule.display_end_time}}
                  </p>
                </div>
              </td>
            </tr>
          </tbody>
          
          <ng-container *ngFor="let cnt of arrayCount(5); index as i">
            <tbody *ngIf="seko_info.moshu_display_place===i">
              <tr>
                <td>
                  <div class="label">喪主</div>
                  <div class="data">{{seko_info?.moshu?.name}}様</div>
                </td>
              </tr>
            </tbody>
            <tbody *ngIf="getData('free'+(i+1)+'_label') || getData('free'+(i+1)+'_data')">
              <tr>
                <td>
                  <div class="label">{{getData('free'+(i+1)+'_label')}}</div>
                  <div class="data">{{getData('free'+(i+1)+'_data')}}</div>
                </td>
              </tr>
            </tbody>
          </ng-container>
          <tbody>
            <tr *ngIf="seko_info.invoice_file_name">
              <td>
                <div class="label">請求書</div>
                <div class="data"><a target="_blank" [href]="seko_info.invoice_file_name">ダウンロード</a></div>
              </td>
            </tr>
          </tbody>
          <tbody>
            <tr *ngIf="seko_info.attached_file1">
              <td>
                <div class="label">{{seko_info.attached_file1_name?seko_info.attached_file1_name:'その他ファイル'}}</div>
                <div class="data"><a target="_blank" [href]="seko_info.attached_file1">ダウンロード</a></div>
              </td>
            </tr>
          </tbody>
          <tbody>
            <tr *ngIf="seko_info.attached_file2">
              <td>
                <div class="label">{{seko_info.attached_file2_name?seko_info.attached_file2_name:'その他ファイル'}}</div>
                <div class="data"><a target="_blank" [href]="seko_info.attached_file2">ダウンロード</a></div>
              </td>
            </tr>
          </tbody>
          <tbody>
            <tr *ngIf="seko_info.attached_file3">
              <td>
                <div class="label">{{seko_info.attached_file3_name?seko_info.attached_file3_name:'その他ファイル'}}</div>
                <div class="data"><a target="_blank" [href]="seko_info.attached_file3">ダウンロード</a></div>
              </td>
            </tr>
          </tbody>
          <tbody>
            <tr>
              <td>
                <div class="label">お問合せ先</div>
                <div class="data">{{seko_info?.fuho_contact_name}}
                  <p>
                    {{seko_info?.fuho_contact_tel}}
                  </p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <a class="userpolicy" routerLink="/soke/userpolicy">ご利用規約</a>
    </div>
  </div>

</div>

