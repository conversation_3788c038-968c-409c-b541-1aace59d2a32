import random
import time
from typing import Any, Dict
from unittest.mock import patch

from dateutil.relativedelta import relativedelta
from django.core import mail
from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from masters.tests.factories import WarekiFactory
from seko.models import Moshu
from seko.tests.factories import (
    DEFAULT_PASSWORD,
    KojinFactory,
    MoshuFactory,
    SekoFactory,
    SekoScheduleFactory,
)
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


class FuhoshiViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.seko = SekoFactory(del_flg=False)
        MoshuFactory(seko=self.seko)
        KojinFactory(seko=self.seko)
        KojinFactory(seko=self.seko)

        for idx in range(4):
            SekoScheduleFactory(seko=self.seko)
        WarekiFactory(
            begin_date=self.seko.death_date + relativedelta(years=-1 * random.randint(1, 20))
        )

        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_fuhoshi_succeed(self) -> None:
        """訃報紙PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:fuhoshi', kwargs={'pk': self.seko.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'訃報紙-{self.seko.pk}.pdf')

    def test_fuhoshi_failed_by_notfound(self) -> None:
        """訃報紙PDFで存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_existent_seko = SekoFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:fuhoshi', kwargs={'pk': non_existent_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class GuideMailToMoshuViewTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

        self.api_client = APIClient()

        self.seko = SekoFactory()
        MoshuFactory(seko=self.seko)

        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    @patch('seko.views.request_origin')
    def test_guide_mail_success(self, mock_origin) -> None:
        """喪主宛メール送信APIは200(本文は空)を返し、本登録用メールを送信する"""
        mock_origin.return_value = 'http://frontend:9999'

        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params = {}
        response = self.api_client.post(
            reverse('seko:moshu_mail', kwargs={'pk': self.seko.pk}), data=params, format='json'
        )
        record = response.json()

        # 200を返し、本文は空
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(record, {})

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)

    def test_guilde_mail_failed_without_auth(self) -> None:
        """喪主宛メール送信APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('seko:seko_detail', kwargs={'pk': self.seko.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class MoshuLoginViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.moshu: Moshu = MoshuFactory()

        self.api_client = APIClient()

    def basic_params(self) -> Dict[str, Any]:
        return {
            'seko': self.moshu.pk,
            'password': DEFAULT_PASSWORD,
        }

    def test_moshu_login_succeed(self):
        """喪主(葬家)でログインできる"""
        self.assertIsNone(self.moshu.last_login)

        params = self.basic_params()
        response = self.api_client.post(
            reverse('seko:moshu_token_obtain_pair'), data=params, format='json'
        )
        record = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(record.get('access'))
        self.assertIsNotNone(record.get('refresh'))

        self.moshu.refresh_from_db()
        self.assertIsNotNone(self.moshu.last_login)

    def test_less_parameters(self):
        """不正なパスワードで喪主(葬家)ログインが失敗する"""
        params = self.basic_params()
        params['password'] = 'the-incollect-password'
        response = self.api_client.post(
            reverse('seko:moshu_token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record = response.json()
        self.assertTrue('detail' in record)

    def test_illegal_password(self):
        """パラメータ不足で喪主(葬家)ログインが失敗する"""
        params = {}
        response = self.api_client.post(
            reverse('seko:moshu_token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        params = self.basic_params().copy()
        params.pop('seko')
        response = self.api_client.post(
            reverse('seko:moshu_token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        params = self.basic_params().copy()
        params.pop('password')
        response = self.api_client.post(
            reverse('seko:moshu_token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_login_fail_with_disabled_seko(self):
        """喪主(葬家)ログインAPIが喪主が属する施行が無効化されているため失敗する"""
        self.moshu.seko.disable()

        params = self.basic_params()
        response = self.api_client.post(
            reverse('seko:moshu_token_obtain_pair'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_refresh_succeed(self):
        """トークンをリフレッシュできる"""
        params = self.basic_params()
        response = self.api_client.post(
            reverse('seko:moshu_token_obtain_pair'), data=params, format='json'
        )
        record = response.json()
        access_token = record['access']
        refresh_token = record['refresh']

        time.sleep(1)

        params = {'refresh': refresh_token}
        response = self.api_client.post(reverse('seko:token_refresh'), data=params, format='json')
        record = response.json()
        new_token = record.get('access')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(new_token)
        self.assertNotEqual(access_token, new_token)

    def test_verify_succeed(self):
        """トークンを検証できる"""
        params = self.basic_params()
        response = self.api_client.post(
            reverse('seko:moshu_token_obtain_pair'), data=params, format='json'
        )
        record = response.json()
        access_token = record['access']

        time.sleep(1)

        params = {'token': access_token}
        response = self.api_client.post(reverse('seko:token_verify'), data=params, format='json')
        record = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(record, {})
