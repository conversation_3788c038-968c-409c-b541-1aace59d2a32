from datetime import date
from typing import Dict, Optional

from django.contrib.auth import authenticate
from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from drf_extra_fields.fields import Base64<PERSON>mageField
from drf_extra_fields.relations import PresentablePrimary<PERSON>eyRelated<PERSON>ield
from rest_framework import exceptions, serializers

from after_follow.serializers import SekoAfBySekoSerializer
from bases.models import Base
from bases.serializers import BaseSerializer, BaseTokuFeeSerializer
from hoyo.serializers import HoyoSekoBySekoSerializer
from items.models import Item
from items.serializers import ItemSerializer
from masters.models import Service
from masters.serializers import ScheduleSerializer, SekoStyleSerializer, ServiceSerializer
from seko.models import (
    HenreihinKakegami,
    Kojin,
    Moshu,
    Seko,
    SekoAlbum,
    SekoAnswer,
    SekoContact,
    SekoInquiry,
    SekoItem,
    SekoSchedule,
    SekoService,
    SekoShareImage,
    SekoVideo,
)
from staffs.models import Staff
from staffs.serializers import StaffSerializer
from utils.authentication import ClassableRefreshToken
from utils.display_datetime import (
    get_display_date,
    get_display_datetime,
    get_display_time,
    get_display_time1,
)
from utils.serializers import PdfBase64File


class MoshuSerializerBase(serializers.ModelSerializer):
    class Meta:
        model = Moshu

    def validate(self, attrs):
        validated_attrs = super().validate(attrs)
        # phone_keys: FrozenSet = frozenset(['tel', 'mobile_num'])
        # key_intersect: Set = set(validated_attrs.keys()) & phone_keys
        # error_message: str = _('Either tel or mobile_num is required.')
        # if key_intersect and key_intersect != phone_keys:
        #     raise serializers.ValidationError(
        # {'tel': error_message, 'mobile_num': error_message}
        # )

        # if not self.partial or set(validated_attrs.keys()) & phone_keys:
        #     if not validated_attrs.get('tel') and not validated_attrs.get('mobile_num'):
        #         raise serializers.ValidationError(
        #             {'tel': error_message, 'mobile_num': error_message}
        #         )
        return validated_attrs


class MoshuSerializer(MoshuSerializerBase):
    class Meta(MoshuSerializerBase.Meta):
        exclude = ['seko', 'password', 'salt']
        read_only_fields = ['created_at', 'updated_at']


class MoshuFuhoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Moshu
        fields = [
            'name',
            'kana',
            'kojin_moshu_relationship',
            'agree_ts',
            'soke_site_del_request_flg',
            'soke_site_del_flg',
            'mail_flg',
        ]


class SekoContactFuhoSerializer(serializers.ModelSerializer):
    class Meta:
        model = SekoContact
        fields = [
            'name',
            'kojin_relationship',
        ]


class SekoContactSerializer(serializers.ModelSerializer):
    class Meta:
        model = SekoContact
        exclude = ['seko']
        read_only_fields = ['created_at', 'updated_at']


class KojinSerializer(serializers.ModelSerializer):
    iei_file_name = Base64ImageField(required=False, allow_null=True)

    class Meta:
        model = Kojin
        exclude = ['seko']
        read_only_fields = ['del_flg', 'created_at', 'updated_at']

    def create(self, validated_data):
        validated_data['seko'] = self.context.get('seko')
        return super().create(validated_data)

    def update(self, instance, validated_data):
        validated_data['seko'] = self.context.get('seko')
        return super().update(instance, validated_data)


class SekoScheduleSerializer(serializers.ModelSerializer):
    display_begin_time = serializers.SerializerMethodField()
    display_end_time = serializers.SerializerMethodField()

    class Meta:
        model = SekoSchedule
        exclude = ['seko']
        read_only_fields = ['created_at', 'updated_at']

    def get_display_begin_time(self, obj: SekoSchedule):
        return get_display_time(obj.begin_time)

    def get_display_end_time(self, obj: SekoSchedule):
        return get_display_time(obj.end_time)


class SekoScheduleRelatedSerializer(SekoScheduleSerializer):
    schedule = ScheduleSerializer()
    hall = BaseSerializer()


class SekoVideoSerializer(serializers.ModelSerializer):
    display_live_begin_date = serializers.SerializerMethodField()
    display_live_end_date = serializers.SerializerMethodField()
    display_live_begin_time = serializers.SerializerMethodField()
    display_live_end_time = serializers.SerializerMethodField()
    display_delivery_end_ts = serializers.SerializerMethodField()

    class Meta:
        model = SekoVideo
        exclude = ['seko']
        read_only_fields = ['created_at', 'updated_at']

    def get_display_live_begin_date(self, obj: SekoVideo):
        return get_display_date(obj.live_begin_ts)

    def get_display_live_begin_time(self, obj: SekoVideo):
        return get_display_time1(obj.live_begin_ts)

    def get_display_live_end_date(self, obj: SekoVideo):
        return get_display_date(obj.live_end_ts)

    def get_display_live_end_time(self, obj: SekoVideo):
        return get_display_time1(obj.live_end_ts)

    def get_display_delivery_end_ts(self, obj: SekoVideo):
        return get_display_datetime(obj.delivery_end_ts)


class SekoRelatedAlbumSerializer(serializers.ModelSerializer):
    file_name = Base64ImageField()

    class Meta:
        model = SekoAlbum
        exclude = ['seko']
        read_only_fields = ['created_at', 'updated_at']

    def create(self, validated_data):
        validated_data['seko'] = self.context.get('seko')
        return super().create(validated_data)

    def update(self, instance, validated_data):
        validated_data['seko'] = self.context.get('seko')
        return super().update(instance, validated_data)


class SekoRelatedShareImageSerializer(serializers.ModelSerializer):
    file_name = Base64ImageField()

    class Meta:
        model = SekoShareImage
        exclude = ['seko']
        read_only_fields = ['created_at', 'updated_at']

    def create(self, validated_data):
        validated_data['seko'] = self.context.get('seko')
        return super().create(validated_data)

    def update(self, instance, validated_data):
        validated_data['seko'] = self.context.get('seko')
        return super().update(instance, validated_data)


class SekoItemSerializer(serializers.ModelSerializer):
    item = PresentablePrimaryKeyRelatedField(
        queryset=Item.objects.all(), presentation_serializer=ItemSerializer
    )

    class Meta:
        model = SekoItem
        exclude = ['seko']
        read_only_fields = ['created_at', 'updated_at']


class SekoServiceSerializer(serializers.ModelSerializer):
    display_limit_ts = serializers.SerializerMethodField()
    is_expired = serializers.SerializerMethodField()
    hall_service = PresentablePrimaryKeyRelatedField(
        queryset=Service.objects.all(), presentation_serializer=ServiceSerializer
    )

    class Meta:
        model = SekoService
        exclude = ['seko']
        read_only_fields = ['created_at', 'updated_at']

    def get_display_limit_ts(self, obj: SekoService):
        return get_display_datetime(obj.limit_ts)

    def get_is_expired(self, obj: SekoService):
        if obj.limit_ts is None:
            return False
        return obj.limit_ts < timezone.now()


class HenreihinKakegamiSerializer(serializers.ModelSerializer):
    class Meta:
        model = HenreihinKakegami
        exclude = ['seko']
        read_only_fields = ['created_at', 'updated_at']


class SekoAnswerSerializerBase(serializers.ModelSerializer):
    staff = PresentablePrimaryKeyRelatedField(
        queryset=Staff.objects.all(), presentation_serializer=StaffSerializer, required=False
    )

    class Meta:
        model = SekoAnswer
        read_only_fields = ['staff', 'del_flg', 'created_at', 'updated_at']


class SekoAnswerSerializer(SekoAnswerSerializerBase):
    class Meta(SekoAnswerSerializerBase.Meta):
        fields = '__all__'
        read_only_fields = SekoAnswerSerializerBase.Meta.read_only_fields + ['inquiry']

    def create(self, validated_data):
        validated_data['inquiry'] = self.context['inquiry']
        validated_data['staff'] = self.context['staff']
        validated_data['del_flg'] = False
        return super().create(validated_data)


class SekoAnswerBySekoInquirySerializer(SekoAnswerSerializerBase):
    class Meta(SekoAnswerSerializerBase.Meta):
        exclude = ['inquiry']


class SekoInquirySerializerBase(serializers.ModelSerializer):
    answers = SekoAnswerBySekoInquirySerializer(many=True, required=False)

    class Meta:
        model = SekoInquiry
        read_only_fields = ['answers', 'created_at', 'updated_at']


class SekoInquiryBySekoSerializer(SekoInquirySerializerBase):
    class Meta(SekoInquirySerializerBase.Meta):
        exclude = ['seko']


class SekoInquirySerializer(SekoInquirySerializerBase):
    class Meta(SekoInquirySerializerBase.Meta):
        fields = '__all__'
        read_only_fields = SekoInquirySerializerBase.Meta.read_only_fields + ['seko']

    def create(self, validated_data):
        validated_data['seko'] = self.context['seko']
        return super().create(validated_data)


class SekoRelatedSerializer(serializers.ModelSerializer):
    moshu = MoshuSerializer()
    seko_contact = SekoContactSerializer(required=False, allow_null=True)
    kojin = KojinSerializer(many=True)
    schedules = SekoScheduleRelatedSerializer(many=True)
    videos = SekoVideoSerializer(many=True, required=False)
    albums = SekoRelatedAlbumSerializer(many=True, required=False)
    share_images = SekoRelatedShareImageSerializer(many=True, required=False)
    seko_style = SekoStyleSerializer()
    invoice_file_name = PdfBase64File(required=False, allow_null=True)
    attached_file1 = PdfBase64File(required=False, allow_null=True)
    attached_file2 = PdfBase64File(required=False, allow_null=True)
    attached_file3 = PdfBase64File(required=False, allow_null=True)
    items = SekoItemSerializer(many=True, required=False)
    services = SekoServiceSerializer(many=True, required=False)
    henrei_kakegami = HenreihinKakegamiSerializer(required=False)
    seko_company = PresentablePrimaryKeyRelatedField(
        queryset=Base.objects.all(), presentation_serializer=BaseTokuFeeSerializer
    )
    seko_department = PresentablePrimaryKeyRelatedField(
        queryset=Base.objects.all(), presentation_serializer=BaseTokuFeeSerializer, required=False
    )
    hoyo_seko = HoyoSekoBySekoSerializer(many=True, required=False)
    seko_af = SekoAfBySekoSerializer(required=False)

    class Meta:
        model = Seko
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']


class SekoFuhoRelatedSerializer(serializers.ModelSerializer):
    moshu = MoshuFuhoSerializer()
    seko_contact = SekoContactFuhoSerializer(required=False, allow_null=True)
    kojin = KojinSerializer(many=True)
    schedules = SekoScheduleRelatedSerializer(many=True)
    videos = SekoVideoSerializer(many=True, required=False)
    albums = SekoRelatedAlbumSerializer(many=True, required=False)
    share_images = SekoRelatedShareImageSerializer(many=True, required=False)
    seko_style = SekoStyleSerializer()
    invoice_file_name = PdfBase64File(required=False, allow_null=True)
    attached_file1 = PdfBase64File(required=False, allow_null=True)
    attached_file2 = PdfBase64File(required=False, allow_null=True)
    attached_file3 = PdfBase64File(required=False, allow_null=True)
    items = SekoItemSerializer(many=True, required=False)
    services = SekoServiceSerializer(many=True, required=False)
    henrei_kakegami = HenreihinKakegamiSerializer(required=False)
    seko_company = PresentablePrimaryKeyRelatedField(
        queryset=Base.objects.all(), presentation_serializer=BaseTokuFeeSerializer
    )
    seko_department = PresentablePrimaryKeyRelatedField(
        queryset=Base.objects.all(), presentation_serializer=BaseTokuFeeSerializer, required=False
    )
    hoyo_seko = HoyoSekoBySekoSerializer(many=True, required=False)
    seko_af = SekoAfBySekoSerializer(required=False)

    class Meta:
        model = Seko
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']


class SekoCreateSerializer(SekoRelatedSerializer):
    seko_style = serializers.IntegerField(source='seko_style_id')
    schedules = SekoScheduleSerializer(many=True)

    @transaction.atomic
    def create(self, validated_data: Dict) -> Seko:
        moshu_data = validated_data.pop('moshu', {})
        kojin_data = validated_data.pop('kojin', [])
        schedules_data = validated_data.pop('schedules', [])
        videos_data = validated_data.pop('videos', [])
        items_data = validated_data.pop('items', [])
        services_data = validated_data.pop('services', [])
        kakegami_data = validated_data.pop('henrei_kakegami', {})
        seko_contact_data = validated_data.pop('seko_contact', {})

        instance = super().create(validated_data)
        if moshu_data:
            Moshu.objects.create(seko=instance, **moshu_data)
        for kojin_dict in kojin_data:
            Kojin.objects.create(seko=instance, **kojin_dict)
        for schedule_dict in schedules_data:
            SekoSchedule.objects.create(seko=instance, **schedule_dict)
        for video_dict in videos_data:
            SekoVideo.objects.create(seko=instance, **video_dict)
        for item_dict in items_data:
            SekoItem.objects.create(seko=instance, **item_dict)
        for service_dict in services_data:
            SekoService.objects.create(seko=instance, **service_dict)
        if kakegami_data:
            HenreihinKakegami.objects.create(seko=instance, **kakegami_data)
        if seko_contact_data:
            SekoContact.objects.create(seko=instance, **seko_contact_data)

        return instance

    @transaction.atomic
    def update(self, instance: Seko, validated_data: Dict):
        update_moshu = 'moshu' in validated_data
        update_kojin = 'kojin' in validated_data
        update_schedules = 'schedules' in validated_data
        update_videos = 'videos' in validated_data
        update_items = 'items' in validated_data
        update_services = 'services' in validated_data
        update_kakegami = 'henrei_kakegami' in validated_data
        update_seko_contact = 'seko_contact' in validated_data

        moshu_data = validated_data.pop('moshu', {})
        kojin_data = validated_data.pop('kojin', [])
        schedules_data = validated_data.pop('schedules', [])
        videos_data = validated_data.pop('videos', [])
        items_data = validated_data.pop('items', [])
        services_data = validated_data.pop('services', [])
        kakegami_data = validated_data.pop('henrei_kakegami', {})
        seko_contact_data = validated_data.pop('seko_contact', {})

        instance = super().update(instance, validated_data)
        if not self.partial or update_moshu:
            instance.update_moshu(moshu_data)
        if not self.partial or update_kojin:
            instance.update_kojin(kojin_data)
        if not self.partial or update_schedules:
            instance.update_schedules(schedules_data)
        if not self.partial or update_videos:
            instance.update_videos(videos_data)
        if not self.partial or update_items:
            instance.update_items(items_data)
        if not self.partial or update_services:
            instance.update_services(services_data)
        if not self.partial or update_kakegami:
            instance.update_kakegami(kakegami_data)
        if not self.partial or update_seko_contact:
            instance.update_seko_contact(seko_contact_data)

        return instance


class SekoListSerializer(serializers.ModelSerializer):
    order_staff_name = serializers.SerializerMethodField()
    seko_staff_name = serializers.SerializerMethodField()
    af_staff_name = serializers.SerializerMethodField()
    seko_company_name = serializers.SerializerMethodField()
    seko_department_name = serializers.SerializerMethodField()
    koden_sum = serializers.SerializerMethodField()
    moshu = MoshuSerializer()
    seko_contact = SekoContactSerializer()
    kojin = KojinSerializer(many=True)
    schedules = SekoScheduleSerializer(many=True)
    seko_af = SekoAfBySekoSerializer(required=False)
    inquiries = SekoInquiryBySekoSerializer(many=True, required=False)

    class Meta:
        model = Seko
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']

    def get_order_staff_name(self, obj: Seko) -> str:
        return obj.order_staff.name

    def get_seko_staff_name(self, obj: Seko) -> str:
        return obj.seko_staff.name if obj.seko_staff else None

    def get_af_staff_name(self, obj: Seko) -> str:
        return obj.af_staff.name if obj.af_staff else None

    def get_seko_company_name(self, obj: Seko) -> str:
        return obj.seko_company.base_name

    def get_seko_department_name(self, obj: Seko) -> str:
        return obj.seko_department.base_name if obj.seko_department else None

    def get_koden_sum(self, obj: Seko) -> int:
        sum = 0
        for entry in obj.entries.all():
            if entry.cancel_ts is not None:
                continue
            for detail in entry.details.all():
                if detail.item.service_id == 30:
                    sum += detail.item_unit_price
        return sum


class SekoSerializer(serializers.ModelSerializer):
    kojin = KojinSerializer(many=True)
    moshu = MoshuSerializer()
    seko_contact = SekoContactSerializer()
    seko_department_name = serializers.SerializerMethodField()

    class Meta:
        model = Seko
        fields = '__all__'

    def get_seko_department_name(self, obj: Seko) -> str:
        return obj.seko_department.base_name if obj.seko_department else None


class MoshuTokenObtainSerializer(serializers.Serializer):
    seko = serializers.IntegerField()
    password = serializers.CharField(write_only=True)

    def validate_seko(self, value) -> Seko:
        seko: Seko = Seko.objects.filter(Q(pk=value) & Q(del_flg=False)).first()
        if not seko:
            raise exceptions.ValidationError(f'Seko not found: ID={value}')

        return seko

    def validate(self, attrs):
        authenticate_kwargs = {
            'seko_id': attrs['seko'].pk,
            'password': attrs['password'],
        }
        authenticate_kwargs['request'] = self.context.get('request')

        self.user = authenticate(**authenticate_kwargs)

        if self.user is None:
            raise exceptions.AuthenticationFailed()

        refresh = self.get_token(self.user)
        return {'refresh': str(refresh), 'access': str(refresh.access_token)}

    @classmethod
    def get_token(cls, user) -> ClassableRefreshToken:
        return ClassableRefreshToken.for_user(user)


class SimpleSekoSerializer(serializers.ModelSerializer):
    seko_contact = SekoContactSerializer()

    class Meta:
        model = Seko
        fields = '__all__'


class SimpleMoshuSerializer(MoshuSerializerBase):
    seko = SimpleSekoSerializer(required=False)
    password = serializers.CharField(write_only=True, required=False)
    fuho_site_end_date = serializers.DateField(write_only=True, required=False)
    seko_contact = SekoContactSerializer(write_only=True, required=False, allow_null=True)

    class Meta(MoshuSerializerBase.Meta):
        exclude = ['salt']
        read_only_fields = ['seko', 'created_at', 'updated_at']

    def update(self, instance, validated_data) -> Moshu:
        update_password: bool = not self.partial or 'password' in validated_data
        update_fuho_site_end_date: bool = (
            not self.partial or 'fuho_site_end_date' in validated_data
        )
        update_soke_site_del_request_flg: bool = (
            not self.partial or 'soke_site_del_request_flg' in validated_data
        )
        need_soke_site_del_mail: bool = (
            update_soke_site_del_request_flg
            and not instance.soke_site_del_request_flg
            and validated_data['soke_site_del_request_flg']
        )
        update_seko_contact: bool = not self.partial or 'seko_contact' in validated_data
        new_password: Optional[str] = validated_data.pop('password', None)
        new_fuho_site_end_date: Optional[date] = validated_data.pop('fuho_site_end_date', None)
        seko_contact_data: Optional[date] = validated_data.pop('seko_contact', None)

        instance = super().update(instance, validated_data)
        if update_password:
            instance.set_password(new_password)
            instance.save()
        if update_fuho_site_end_date:
            instance.seko.fuho_site_end_date = new_fuho_site_end_date
            instance.seko.save()
        if update_seko_contact:
            instance.seko.update_seko_contact(seko_contact_data)

        self.context['send_soke_site_del_mail'] = need_soke_site_del_mail

        return instance


class FdnSekoCreateSerializer(SekoRelatedSerializer):
    seko_style = serializers.IntegerField(source='seko_style_id')
    schedules = SekoScheduleSerializer(many=True)

    @transaction.atomic
    def create(self, validated_data: Dict) -> Seko:
        moshu_data = validated_data.pop('moshu', {})
        kojin_data = validated_data.pop('kojin', [])
        schedules_data = validated_data.pop('schedules', [])

        instance = super().create(validated_data)
        if moshu_data:
            Moshu.objects.create(seko=instance, **moshu_data)
        for kojin_dict in kojin_data:
            Kojin.objects.create(seko=instance, **kojin_dict)
        for schedule_dict in schedules_data:
            SekoSchedule.objects.create(seko=instance, **schedule_dict)

        return instance


class FdnSekoUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Seko
        fields = ['fdn_code']


class NowlKojinSerializer(serializers.ModelSerializer):
    class Meta:
        model = Kojin
        fields = [
            'kojin_num',
            'name',
            'kana',
            'moshu_kojin_relationship',
            'age_kbn',
            'age',
            'birth_date',
            'death_date',
        ]


class NowlMoshuSerializer(serializers.ModelSerializer):
    class Meta:
        model = Moshu
        fields = [
            'name',
            'kana',
            'kojin_moshu_relationship',
            'zip_code',
            'prefecture',
            'address_1',
            'address_2',
            'address_3',
            'tel',
            'mobile_num',
            'mail_address',
        ]


class NowlSekoScheduleSerializer(serializers.ModelSerializer):
    class Meta:
        model = SekoSchedule
        fields = [
            'schedule_name',
            'hall_name',
            'hall_zip_code',
            'hall_address',
            'hall_tel',
            'schedule_date',
            'begin_time',
            'end_time',
        ]


class NowlSekoListSerializer(serializers.ModelSerializer):
    seko_id = serializers.SerializerMethodField()
    company_id = serializers.SerializerMethodField()
    kojin = NowlKojinSerializer(many=True)
    moshu = NowlMoshuSerializer()
    schedules = NowlSekoScheduleSerializer(many=True)

    class Meta:
        model = Seko
        fields = [
            'company_id',
            'seko_id',
            'soke_name',
            'soke_kana',
            'seko_style',
            'seko_style_name',
            'fuho_site_admission_ts',
            'fuho_site_end_date',
            'fuho_sentence',
            'fuho_contact_name',
            'fuho_contact_tel',
            'death_date',
            'seko_date',
            'note',
            'kojin',
            'moshu',
            'schedules',
        ]

    def get_seko_id(self, obj: Seko) -> int:
        return obj.id

    def get_company_id(self, obj: Seko) -> int:
        return obj.seko_company.id
