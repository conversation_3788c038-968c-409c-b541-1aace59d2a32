from typing import Dict, Optional
from urllib.parse import urljoin

from django.conf import settings
from django.core.mail import EmailMessage
from django.db.models import Prefetch, Q
from django.template.loader import get_template
from django.utils.translation import gettext_lazy as _
from rest_framework import generics, mixins, views
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from rest_framework.response import Response

from bases.models import Base, FocFee, SmsAccount, Tokusho
from bases.serializers import (
    BaseDownwardSerializer,
    BaseInquirySerializer,
    BaseSerializer,
    FocFeeSerializer,
    SmsAccountSerializer,
    TokushoSerializer,
)
from masters.models import Service
from utils.requests import request_origin
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class BaseFullList(generics.ListAPIView):
    """
    get: 全拠点の一覧を階層構造で取得します
    """

    serializer_class = BaseDownwardSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self, queryset=None):
        return (
            Base.objects.filter(Q(del_flg=False))
            .prefetch_related(
                'tokusho',
                'focfee',
                'sms_account',
                Prefetch('services', queryset=Service.objects.order_by('id')),
            )
            .get_cached_trees()
        )


class BaseList(AddContextMixin, generics.CreateAPIView):
    serializer_class = BaseSerializer
    permission_classes = [IsAuthenticated]


class BaseDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 拠点情報を取得します
    put: 拠点情報を更新します
    patch: 拠点情報を更新します
    delete: 拠点情報を(論理)削除します
    """

    queryset = Base.objects.filter(Q(del_flg=False)).prefetch_related(
        'tokusho', 'focfee', 'sms_account'
    )
    serializer_class = BaseDownwardSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return BaseSerializer
        return self.serializer_class


class FocFeeDetail(AddContextMixin, generics.RetrieveUpdateAPIView, mixins.CreateModelMixin):
    """
    get: FOC利用料を取得します
    post: FOC利用料を追加します
    put: FOC利用料を更新します
    patch: FOC利用料を更新します
    """

    serializer_class = FocFeeSerializer
    permission_classes = [IsAuthenticated]

    def get_base(self) -> Base:
        base_id: int = self.kwargs.get('base_id')
        base: Base = Base.objects.filter(pk=base_id).first()
        if not base:
            raise NotFound(f'No {Base._meta.object_name} matches the given query.')
        return base

    def get_object(self):
        base: Base = self.get_base()
        if not hasattr(base, 'focfee'):
            raise NotFound()
        return base.focfee

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['base'] = self.get_base()
        return context

    def post(self, request, *args, **kwargs):
        base: Base = self.get_base()
        if hasattr(base, 'focfee'):
            raise ValidationError(f'The base already has a {FocFee._meta.object_name}')
        return self.create(request, *args, **kwargs)


class TokushoDetail(generics.RetrieveUpdateAPIView, mixins.CreateModelMixin):
    """
    get:特商法を取得します
    post:特商法を追加します
    put:特商法を更新します
    patch: 特商法を更新します
    """

    serializer_class = TokushoSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_base(self) -> Base:
        base_id: int = self.kwargs.get('base_id')
        base: Base = Base.objects.filter(pk=base_id).first()
        if not base:
            raise NotFound(f'No {Base._meta.object_name} matches the given query.')
        return base

    def get_object(self):
        base: Base = self.get_base()
        if not hasattr(base, 'tokusho'):
            raise NotFound()
        return base.tokusho

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['base'] = self.get_base()
        return context

    def post(self, request, *args, **kwargs):
        base: Base = self.get_base()
        if hasattr(base, 'tokusho'):
            raise ValidationError(f'The base already has a {Tokusho._meta.object_name}')
        return self.create(request, *args, **kwargs)


class SmsAccountDetail(generics.RetrieveUpdateAPIView, mixins.CreateModelMixin):
    """
    get:SMSアカウントを取得します
    post:SMSアカウントを追加します
    put:SMSアカウントを更新します
    patch: SMSアカウントを更新します
    """

    serializer_class = SmsAccountSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_base(self) -> Base:
        base_id: int = self.kwargs.get('base_id')
        base: Base = Base.objects.filter(pk=base_id).first()
        if not base:
            raise NotFound(f'No {Base._meta.object_name} matches the given query.')
        return base

    def get_object(self):
        base: Base = self.get_base()
        if not hasattr(base, 'sms_account'):
            raise NotFound()
        return base.sms_account

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['base'] = self.get_base()
        return context

    def post(self, request, *args, **kwargs):
        base: Base = self.get_base()
        if hasattr(base, 'sms_account'):
            raise ValidationError(f'The base already has a {SmsAccount._meta.object_name}')
        return self.create(request, *args, **kwargs)


class InquiryMail(views.APIView):
    def post(self, request, pk, format=None):
        instance: Optional[Base] = Base.objects.filter(Q(pk=pk)).first()
        if not instance:
            raise NotFound(detail=_('Base not found.'))
        if not hasattr(instance, 'tokusho'):
            raise NotFound(detail=_('Tokusho not found.'))

        fuho_num = request.data.pop('fuho_num', None)
        fuho_url = None
        if fuho_num:
            fuho_url: str = urljoin(request_origin(self.request), f'fuho/{fuho_num}')
        serializer = BaseInquirySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data: Dict = serializer.validated_data

        mail_body = get_template('mail/inquiry.txt').render(
            {'object': instance, 'input_data': validated_data, 'fuho_url': fuho_url}
        )

        message = EmailMessage(
            subject='FOCこころ　お問合せ',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=[instance.tokusho.mail_address],
            bcc=settings.INQUIRY_EMAIL_BCC,
        )
        message.send(fail_silently=False)

        return Response({})
