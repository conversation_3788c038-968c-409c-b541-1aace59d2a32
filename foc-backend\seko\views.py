import functools
import itertools
from base64 import b64encode
from io import BytesIO
from typing import Dict, List
from urllib.parse import urljoin

import qrcode
from django.conf import settings
from django.contrib.staticfiles import finders
from django.core.mail import EmailMessage
from django.db import transaction
from django.db.models import Prefetch, Q
from django.http import Http404
from django.http.response import FileResponse, HttpResponse
from django.shortcuts import get_object_or_404
from django.template.loader import get_template
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_filters import rest_framework as filters
from rest_framework import generics, status, views
from rest_framework.exceptions import NotFound, PermissionDenied, ValidationError
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from weasyprint import CSS, HTML

from advertises.models import Advertise
from after_follow.models import AfActivityDetail, AfWish, SekoAf
from bases.models import Base
from hoyo.models import HoyoSchedule, HoyoSeko
from masters.models import ChobunDaishiMaster, Service, SokeMenu
from orders.models import Entry, EntryDetail
from orders.serializers import EntryDownwardSerializer
from seko.models import (
    Kojin,
    Moshu,
    Seko,
    SekoAlbum,
    SekoAnswer,
    SekoInquiry,
    SekoItem,
    SekoSchedule,
    SekoService,
    SekoShareImage,
)
from seko.serializers import (
    FdnSekoCreateSerializer,
    FdnSekoUpdateSerializer,
    KojinSerializer,
    MoshuTokenObtainSerializer,
    NowlSekoListSerializer,
    SekoAnswerSerializer,
    SekoCreateSerializer,
    SekoFuhoRelatedSerializer,
    SekoInquirySerializer,
    SekoListSerializer,
    SekoRelatedAlbumSerializer,
    SekoRelatedSerializer,
    SekoRelatedShareImageSerializer,
    SimpleMoshuSerializer,
)
from service_reception_terms.models import ServiceReceptionTerm
from staffs.models import Staff
from suppliers.models import Supplier
from utils.permissions import IsMoshu, IsStaff, ReadNeedAuth
from utils.requests import request_origin


class SekoListFilter(filters.FilterSet):
    seko_department = filters.NumberFilter(method='filter_seko_department')
    seko_style = filters.NumberFilter()
    hoyo_style = filters.NumberFilter(field_name='seko_style__hoyo_style_id', distinct=True)
    hall = filters.NumberFilter(field_name='schedules__hall_id', distinct=True)
    soke_name = filters.CharFilter(field_name='soke_name', lookup_expr='icontains')
    soke_kana = filters.CharFilter(field_name='soke_kana', lookup_expr='icontains')
    kojin_name = filters.CharFilter(
        field_name='kojin__name', lookup_expr='icontains', distinct=True
    )
    kojin_kana = filters.CharFilter(
        field_name='kojin__kana', lookup_expr='icontains', distinct=True
    )
    moshu_name = filters.CharFilter(field_name='moshu__name', lookup_expr='icontains')
    moshu_kana = filters.CharFilter(field_name='moshu__kana', lookup_expr='icontains')
    seko_date = filters.DateFromToRangeFilter()
    death_date = filters.DateFromToRangeFilter()

    class Meta:
        model = Seko
        fields = [
            'seko_company',
            'hall',
            'order_staff',
            'seko_staff',
            'af_staff',
        ]

    def filter_seko_department(self, queryset, name, value):
        departments = Base.objects.filter(pk=value).get_cached_trees()
        if not departments:
            error_message: str = _(
                'Select a valid choice. That choice is not one of the available choices.'
            )
            raise ValidationError({'seko_department': error_message})
        department_ids: List[int] = [
            base.pk for base in departments[0].get_descendants(include_self=True)
        ]
        return queryset.filter(Q(seko_department__in=department_ids))


class SekoList(generics.ListCreateAPIView):
    queryset = (
        Seko.objects.filter(Q(del_flg=False))
        .select_related(
            'order_staff',
            'seko_staff',
            'af_staff',
            'seko_company',
            'seko_department',
            'seko_style',
        )
        .prefetch_related(
            Prefetch('moshu'),
            Prefetch('kojin', queryset=Kojin.objects.order_by('kojin_num')),
            Prefetch('schedules', queryset=SekoSchedule.objects.order_by('display_num')),
            Prefetch(
                'seko_af',
                queryset=SekoAf.objects.select_related('contact_way').prefetch_related(
                    Prefetch(
                        'wishes',
                        queryset=AfWish.objects.select_related('af_type', 'af_type__af_group')
                        .prefetch_related(Prefetch('activities'))
                        .order_by('af_type__af_group__display_num', 'af_type__display_num'),
                    )
                ),
            ),
            Prefetch(
                'inquiries',
                queryset=SekoInquiry.objects.prefetch_related(
                    Prefetch(
                        'answers',
                        queryset=SekoAnswer.objects.select_related('staff', 'staff__base')
                        .prefetch_related(
                            Prefetch(
                                'staff__base__chobun_daishi_masters',
                                queryset=ChobunDaishiMaster.objects.all(),
                            ),
                            Prefetch('staff__base__services', queryset=Service.objects.all()),
                            Prefetch(
                                'staff__base__service_reception_terms',
                                queryset=ServiceReceptionTerm.objects.all(),
                            ),
                        )
                        .filter(Q(del_flg=False))
                        .order_by('pk'),
                    )
                ).order_by('query_group_id', 'pk'),
            ),
            Prefetch(
                'entries',
                queryset=Entry.objects.prefetch_related(Prefetch('details')),
            ),
        )
        .order_by('seko_date')
        .all()
    )
    serializer_class = SekoListSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = SekoListFilter
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return SekoCreateSerializer
        return self.serializer_class


class SekoDetail(generics.RetrieveUpdateDestroyAPIView):
    """
    get: 施行情報を取得します
    """

    queryset = (
        Seko.objects.select_related('seko_style', 'seko_company', 'seko_department')
        .filter(Q(del_flg=False))
        .prefetch_related(
            Prefetch(
                'kojin', queryset=Kojin.objects.filter(Q(del_flg=False)).order_by('kojin_num')
            ),
            Prefetch(
                'schedules',
                queryset=SekoSchedule.objects.select_related('schedule', 'hall')
                .prefetch_related(
                    Prefetch(
                        'hall__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
                    ),
                    Prefetch('hall__services', queryset=Service.objects.all()),
                    Prefetch(
                        'hall__service_reception_terms',
                        queryset=ServiceReceptionTerm.objects.all(),
                    ),
                )
                .order_by('display_num'),
            ),
            Prefetch('videos'),
            Prefetch('albums', queryset=SekoAlbum.objects.order_by('display_num')),
            Prefetch('share_images', queryset=SekoShareImage.objects.order_by('display_num')),
            Prefetch(
                'items',
                queryset=SekoItem.objects.select_related('item', 'item__tax')
                .prefetch_related(Prefetch('item__suppliers', queryset=Supplier.objects.all()))
                .order_by('item__service_id', 'item__display_num', 'item__id'),
            ),
            Prefetch(
                'services', queryset=SekoService.objects.select_related('hall_service').all()
            ),
            Prefetch(
                'seko_company__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
            ),
            Prefetch(
                'seko_company__soke_menus', queryset=SokeMenu.objects.all().order_by('disp_no')
            ),
            Prefetch('seko_company__advertises', queryset=Advertise.objects.filter_active()),
            Prefetch(
                'seko_department__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
            ),
            Prefetch('seko_department__advertises', queryset=Advertise.objects.filter_active()),
            Prefetch(
                'hoyo_seko',
                queryset=HoyoSeko.objects.select_related(
                    'hoyo', 'hoyo__style', 'hall', 'staff', 'staff__base'
                )
                .prefetch_related(
                    Prefetch(
                        'hall__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
                    ),
                    Prefetch('hall__services', queryset=Service.objects.all()),
                    Prefetch(
                        'hall__service_reception_terms',
                        queryset=ServiceReceptionTerm.objects.all(),
                    ),
                    Prefetch(
                        'staff__base__chobun_daishi_masters',
                        queryset=ChobunDaishiMaster.objects.all(),
                    ),
                    Prefetch('staff__base__services', queryset=Service.objects.all()),
                    Prefetch(
                        'staff__base__service_reception_terms',
                        queryset=ServiceReceptionTerm.objects.all(),
                    ),
                    Prefetch(
                        'schedules',
                        queryset=HoyoSchedule.objects.select_related('hall')
                        .prefetch_related(
                            Prefetch(
                                'hall__chobun_daishi_masters',
                                queryset=ChobunDaishiMaster.objects.all(),
                            ),
                            Prefetch('hall__services', queryset=Service.objects.all()),
                            Prefetch(
                                'hall__service_reception_terms',
                                queryset=ServiceReceptionTerm.objects.all(),
                            ),
                        )
                        .order_by('display_num', 'pk'),
                    ),
                )
                .filter(Q(del_flg=False))
                .order_by('hoyo_planned_date', 'hoyo_activity_date'),
            ),
            Prefetch(
                'seko_af',
                queryset=SekoAf.objects.select_related('contact_way')
                .prefetch_related(
                    Prefetch(
                        'wishes',
                        queryset=AfWish.objects.select_related('af_type', 'af_type__af_group')
                        .prefetch_related(
                            Prefetch(
                                'activities', queryset=AfActivityDetail.objects.order_by('pk')
                            )
                        )
                        .order_by('af_type__af_group__display_num', 'af_type__display_num'),
                    )
                )
                .all(),
            ),
        )
        .all()
    )
    serializer_class = SekoRelatedSerializer
    permission_classes = [IsAuthenticated | IsStaff | ReadNeedAuth]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return SekoCreateSerializer
        return self.serializer_class

    def get_object(self) -> Seko:
        obj: Seko = super().get_object()
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            user_bases: List[Base] = Base.objects.filter(
                pk=self.request.user.base_id
            ).get_cached_trees()
            if obj.seko_company not in user_bases[0].get_ancestors(include_self=True):
                raise PermissionDenied(_("Denied operation by another company's user"))
        return obj

    def destroy(self, request, *args, **kwargs):
        instance: Seko = self.get_object()
        instance.disable()
        return Response(status=status.HTTP_204_NO_CONTENT)


class SekoFuhoDetail(generics.RetrieveAPIView):
    """
    get: 施行情報を取得します
    """

    queryset = (
        Seko.objects.select_related('seko_style', 'seko_company', 'seko_department')
        .filter(Q(del_flg=False))
        .prefetch_related(
            Prefetch(
                'kojin', queryset=Kojin.objects.filter(Q(del_flg=False)).order_by('kojin_num')
            ),
            Prefetch(
                'schedules',
                queryset=SekoSchedule.objects.select_related('schedule', 'hall')
                .prefetch_related(
                    Prefetch(
                        'hall__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
                    ),
                    Prefetch('hall__services', queryset=Service.objects.all()),
                    Prefetch(
                        'hall__service_reception_terms',
                        queryset=ServiceReceptionTerm.objects.all(),
                    ),
                )
                .order_by('display_num'),
            ),
            Prefetch('videos'),
            Prefetch('albums', queryset=SekoAlbum.objects.order_by('display_num')),
            Prefetch('share_images', queryset=SekoShareImage.objects.order_by('display_num')),
            Prefetch(
                'items',
                queryset=SekoItem.objects.select_related('item', 'item__tax')
                .prefetch_related(Prefetch('item__suppliers', queryset=Supplier.objects.all()))
                .order_by('item__service_id', 'item__display_num', 'item__id'),
            ),
            Prefetch(
                'services', queryset=SekoService.objects.select_related('hall_service').all()
            ),
            Prefetch(
                'seko_company__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
            ),
            Prefetch(
                'seko_company__soke_menus', queryset=SokeMenu.objects.all().order_by('disp_no')
            ),
            Prefetch('seko_company__advertises', queryset=Advertise.objects.filter_active()),
            Prefetch(
                'seko_department__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
            ),
            Prefetch('seko_department__advertises', queryset=Advertise.objects.filter_active()),
            Prefetch(
                'hoyo_seko',
                queryset=HoyoSeko.objects.select_related(
                    'hoyo', 'hoyo__style', 'hall', 'staff', 'staff__base'
                )
                .prefetch_related(
                    Prefetch(
                        'hall__chobun_daishi_masters', queryset=ChobunDaishiMaster.objects.all()
                    ),
                    Prefetch('hall__services', queryset=Service.objects.all()),
                    Prefetch(
                        'hall__service_reception_terms',
                        queryset=ServiceReceptionTerm.objects.all(),
                    ),
                    Prefetch(
                        'staff__base__chobun_daishi_masters',
                        queryset=ChobunDaishiMaster.objects.all(),
                    ),
                    Prefetch('staff__base__services', queryset=Service.objects.all()),
                    Prefetch(
                        'staff__base__service_reception_terms',
                        queryset=ServiceReceptionTerm.objects.all(),
                    ),
                    Prefetch(
                        'schedules',
                        queryset=HoyoSchedule.objects.select_related('hall')
                        .prefetch_related(
                            Prefetch(
                                'hall__chobun_daishi_masters',
                                queryset=ChobunDaishiMaster.objects.all(),
                            ),
                            Prefetch('hall__services', queryset=Service.objects.all()),
                            Prefetch(
                                'hall__service_reception_terms',
                                queryset=ServiceReceptionTerm.objects.all(),
                            ),
                        )
                        .order_by('display_num', 'pk'),
                    ),
                )
                .filter(Q(del_flg=False))
                .order_by('hoyo_planned_date', 'hoyo_activity_date'),
            ),
            Prefetch(
                'seko_af',
                queryset=SekoAf.objects.select_related('contact_way')
                .prefetch_related(
                    Prefetch(
                        'wishes',
                        queryset=AfWish.objects.select_related('af_type', 'af_type__af_group')
                        .prefetch_related(
                            Prefetch(
                                'activities', queryset=AfActivityDetail.objects.order_by('pk')
                            )
                        )
                        .order_by('af_type__af_group__display_num', 'af_type__display_num'),
                    )
                )
                .all(),
            ),
        )
        .all()
    )
    serializer_class = SekoFuhoRelatedSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        if instance.fuho_site_end_date < timezone.localtime().date():
            return Response('expired')

        return Response(serializer.data)


class SekoApproval(generics.RetrieveAPIView):
    """
    get: 施行の公開を承認します
    """

    queryset = (
        Seko.objects.filter(Q(del_flg=False))
        .prefetch_related(
            Prefetch('kojin', queryset=Kojin.objects.order_by('kojin_num')),
            Prefetch('schedules', queryset=SekoSchedule.objects.order_by('display_num')),
            Prefetch('videos'),
            Prefetch('albums', queryset=SekoAlbum.objects.order_by('display_num')),
            Prefetch('share_images', queryset=SekoShareImage.objects.order_by('display_num')),
        )
        .all()
    )
    serializer_class = SekoRelatedSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]

    @transaction.atomic
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.fuho_site_admission_ts:
            raise PermissionDenied(f'{Seko._meta.object_name} is already approved.')
        instance.fuho_site_admission_ts = timezone.localtime()
        instance.save()

        HoyoSeko.generate_from_seko(seko=instance)

        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class SekoRelatedKojinList(generics.ListCreateAPIView):

    queryset = Kojin.objects.order_by('kojin_num').all()
    serializer_class = KojinSerializer
    permission_classes = [IsAuthenticated]

    def get_seko(self) -> Seko:
        seko_id: int = self.kwargs.get('seko_id')
        seko: Seko = Seko.objects.filter(pk=seko_id).first()
        if not seko:
            raise Http404(f'No {Seko._meta.object_name} matches the given query.')
        return seko

    def get_queryset(self):
        seko: Seko = self.get_seko()
        return super().get_queryset().filter(seko=seko)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['seko'] = self.get_seko()
        return context


class SekoRelatedKojinDetail(generics.RetrieveUpdateDestroyAPIView):

    queryset = Kojin.objects.all()
    serializer_class = KojinSerializer
    permission_classes = [IsAuthenticated]

    def get_seko(self) -> Seko:
        seko_id: int = self.kwargs.get('seko_id')
        seko: Seko = Seko.objects.filter(pk=seko_id).first()
        if not seko:
            raise Http404(f'No {Seko._meta.object_name} matches the given query.')
        return seko

    def get_queryset(self):
        seko: Seko = self.get_seko()
        return super().get_queryset().filter(seko=seko)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['seko'] = self.get_seko()
        return context


class SekoRelatedAlbumList(generics.ListCreateAPIView):

    queryset = SekoAlbum.objects.all()
    serializer_class = SekoRelatedAlbumSerializer
    permission_classes = [IsAuthenticated]

    def get_seko(self) -> Seko:
        seko_id: int = self.kwargs.get('seko_id')
        seko: Seko = Seko.objects.filter(pk=seko_id).first()
        if not seko:
            raise Http404(f'No {Seko._meta.object_name} matches the given query.')
        return seko

    def get_queryset(self):
        seko: Seko = self.get_seko()
        return super().get_queryset().filter(seko=seko)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['seko'] = self.get_seko()
        return context


class SekoRelatedAlbumDetail(generics.RetrieveUpdateDestroyAPIView):

    queryset = SekoAlbum.objects.all()
    serializer_class = SekoRelatedAlbumSerializer
    permission_classes = [IsAuthenticated]

    def get_seko(self) -> Seko:
        seko_id: int = self.kwargs.get('seko_id')
        seko: Seko = Seko.objects.filter(pk=seko_id).first()
        if not seko:
            raise Http404(f'No {Seko._meta.object_name} matches the given query.')
        return seko

    def get_queryset(self):
        seko: Seko = self.get_seko()
        return super().get_queryset().filter(seko=seko)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['seko'] = self.get_seko()
        return context


class SekoRelatedShareImageList(generics.ListCreateAPIView):

    queryset = SekoShareImage.objects.all()
    serializer_class = SekoRelatedShareImageSerializer
    permission_classes = [IsAuthenticated]

    def get_seko(self) -> Seko:
        seko_id: int = self.kwargs.get('seko_id')
        seko: Seko = Seko.objects.filter(pk=seko_id).first()
        if not seko:
            raise Http404(f'No {Seko._meta.object_name} matches the given query.')
        return seko

    def get_queryset(self):
        seko: Seko = self.get_seko()
        return super().get_queryset().filter(seko=seko)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['seko'] = self.get_seko()
        return context


class SekoRelatedShareImageDetail(generics.RetrieveUpdateDestroyAPIView):

    queryset = SekoShareImage.objects.all()
    serializer_class = SekoRelatedShareImageSerializer
    permission_classes = [IsAuthenticated]

    def get_seko(self) -> Seko:
        seko_id: int = self.kwargs.get('seko_id')
        seko: Seko = Seko.objects.filter(pk=seko_id).first()
        if not seko:
            raise Http404(f'No {Seko._meta.object_name} matches the given query.')
        return seko

    def get_queryset(self):
        seko: Seko = self.get_seko()
        return super().get_queryset().filter(seko=seko)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['seko'] = self.get_seko()
        return context


class FuhoshiPdf(views.APIView):

    # permission_classes = [IsAuthenticated]

    def get(self, request, pk, format=None):
        seko: Seko = (
            Seko.objects.filter(pk=pk)
            .prefetch_related(
                Prefetch('kojin', queryset=Kojin.objects.order_by('kojin_num')),
                Prefetch('schedules', queryset=SekoSchedule.objects.order_by('display_num')),
            )
            .first()
        )
        if not seko:
            raise Http404(f'No {Seko._meta.object_name} matches the given query.')

        fuho_url: str = urljoin(request_origin(self.request), 'fuho/')
        qr_url: str = urljoin(fuho_url, f'{seko.seko_company_id}-{seko.id}/')
        qr_image = qrcode.make(qr_url)

        static_images: Dict[str, str] = {'smaho': 'pdf/smaho.png', 'pc': 'pdf/pc.png'}
        image_encoded: Dict[str, str] = {}
        for key, image_name in static_images.items():
            physical_path: str = finders.find(image_name)
            with open(physical_path, 'rb') as image_file:
                encoded: str = b64encode(image_file.read()).decode('ascii')
                image_encoded[key] = f'data:image/png;base64,{encoded}'

        html_text = get_template('pdf/fuhoshi.html').render(
            {'seko': seko, 'qr_image': qr_image, 'fuho_url': fuho_url, 'images': image_encoded}
        )
        css_text = get_template('pdf/fuhoshi.css').render()

        buffer = BytesIO()
        HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
            buffer,
            stylesheets=[CSS(string=css_text, base_url=request.build_absolute_uri())],
        )
        buffer.seek(0)

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'訃報紙-{seko.pk}.pdf',
            content_type='application/pdf',
        )
        return response


class QRCodePdf(views.APIView):

    # permission_classes = [IsAuthenticated]

    def get(self, request, pk, format=None):
        seko: Seko = (
            Seko.objects.filter(pk=pk)
            .prefetch_related(
                Prefetch('kojin', queryset=Kojin.objects.order_by('kojin_num')),
                Prefetch('schedules', queryset=SekoSchedule.objects.order_by('display_num')),
            )
            .first()
        )
        if not seko:
            raise Http404(f'No {Seko._meta.object_name} matches the given query.')

        fuho_url: str = urljoin(request_origin(self.request), 'fuho/')
        qr_url: str = urljoin(fuho_url, f'{seko.seko_company_id}-{seko.id}/')
        qr_image = qrcode.make(qr_url)

        html_text = get_template('pdf/fuho_qrcode.html').render(
            {'seko': seko, 'qr_image': qr_image}
        )
        css_text = get_template('pdf/fuho_qrcode.css').render()

        buffer = BytesIO()
        HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
            buffer,
            stylesheets=[CSS(string=css_text, base_url=request.build_absolute_uri())],
        )
        buffer.seek(0)

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'QRコード-{seko.pk}.pdf',
            content_type='application/pdf',
        )
        return response


class GuideMailToMoshu(views.APIView):

    permission_classes = [IsAuthenticated]

    def post(self, request, pk, format=None):
        seko: Seko = get_object_or_404(Seko, pk=pk)

        fuho_approve_url: str = urljoin(
            request_origin(self.request), f'fuho-approve/{seko.seko_company.id}-{seko.id}'
        )
        fuho_url: str = urljoin(
            request_origin(self.request), f'fuho/{seko.seko_company.id}-{seko.id}'
        )
        soke_approve_url: str = urljoin(
            request_origin(self.request), f'soke-approve/{seko.seko_company.id}-{seko.id}'
        )
        mail_body = get_template('mail/guide_moshu.txt').render(
            {
                'seko': seko,
                'fuho_approve_url': fuho_approve_url,
                'fuho_url': fuho_url,
                'soke_approve_url': soke_approve_url,
            }
        )
        if hasattr(seko, 'seko_contact') and seko.seko_contact.mail_address:
            address_to = seko.seko_contact.mail_address
        else:
            address_to = seko.moshu.mail_address

        message = EmailMessage(
            subject='【てれ葬儀こころ】ご葬家専用ページのお申し込み',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=[address_to],
            bcc=settings.INQUIRY_EMAIL_BCC,
        )
        message.send(fail_silently=False)
        return HttpResponse(b'{}', content_type='application/json')


class HomeichoList(generics.ListAPIView):

    serializer_class = EntryDownwardSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return (
            Entry.objects.prefetch_related(
                Prefetch(
                    'details',
                    queryset=EntryDetail.objects.select_related('item', 'item__service').order_by(
                        'id'
                    ),
                ),
                'details__chobun',
                'details__koden',
                'details__kumotsu',
                'details__message',
            )
            .filter(Q(seko_id=self.kwargs['pk']) & Q(cancel_ts__isnull=True))
            .order_by('id')
        )


class HomeichoChobunPDF(generics.RetrieveAPIView):
    queryset = Seko.objects.select_related('seko_company',).prefetch_related(
        Prefetch('kojin', queryset=Kojin.objects.filter(kojin_num=1)),
        Prefetch(
            'entries',
            queryset=Entry.objects.filter(cancel_ts__isnull=True)
            .prefetch_related(
                Prefetch('details', queryset=EntryDetail.objects.filter(chobun__isnull=False))
            )
            .order_by('id'),
        ),
    )

    def retrieve(self, request, *args, **kwargs):
        seko = self.get_object()
        html_text_top = get_template('pdf/homeicho_top.html').render(
            {
                'title1': '弔 文',
                'seko': seko,
            }
        )
        html_text_detail = get_template('pdf/homeicho_chobun.html').render(
            {
                'counter': functools.partial(next, itertools.count(start=1)),
                'seko': seko,
            }
        )
        pdf_top = HTML(string=html_text_top, base_url=request.build_absolute_uri()).render()
        pdf_detail = HTML(string=html_text_detail, base_url=request.build_absolute_uri()).render()

        all_pages = []
        for doc in pdf_top, pdf_detail:
            for p in doc.pages:
                all_pages.append(p)

        buffer = BytesIO()
        pdf_top.copy(all_pages).write_pdf(buffer)

        buffer.seek(0)

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'芳名帳（弔文）-{seko.id}.pdf',
            content_type='application/pdf',
        )
        return response


class HomeichoKumotsuPDF(generics.RetrieveAPIView):
    queryset = Seko.objects.select_related('seko_company',).prefetch_related(
        Prefetch('kojin', queryset=Kojin.objects.filter(kojin_num=1)),
        Prefetch(
            'entries',
            queryset=Entry.objects.filter(cancel_ts__isnull=True)
            .prefetch_related(
                Prefetch(
                    'details',
                    queryset=EntryDetail.objects.select_related(
                        'kumotsu',
                        'kumotsu__henrei_kumotsu',
                    ).filter(kumotsu__isnull=False),
                )
            )
            .order_by('id'),
        ),
    )

    def retrieve(self, request, *args, **kwargs):
        seko = self.get_object()

        total_henrei = 0
        for entry in seko.entries.all():
            for detail in entry.details.all():
                if hasattr(detail.kumotsu, 'henrei_kumotsu'):
                    total_henrei += detail.kumotsu.henrei_kumotsu.henreihin_price

        # html_text = get_template('pdf/homeicho_kumotsu.html').render(
        #     {
        #         'counter': functools.partial(next, itertools.count(start=1)),
        #         'seko': seko,
        #         'total_henrei': total_henrei,
        #     }
        # )

        # buffer = BytesIO()
        # HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
        #     buffer,
        # )
        # buffer.seek(0)
        html_text_top = get_template('pdf/homeicho_top.html').render(
            {
                'title1': '供 花',
                'title2': '供 物',
                'seko': seko,
            }
        )
        html_text_detail = get_template('pdf/homeicho_kumotsu.html').render(
            {
                'counter': functools.partial(next, itertools.count(start=1)),
                'seko': seko,
                'total_henrei': total_henrei,
            }
        )
        pdf_top = HTML(string=html_text_top, base_url=request.build_absolute_uri()).render()
        pdf_detail = HTML(string=html_text_detail, base_url=request.build_absolute_uri()).render()

        all_pages = []
        for doc in pdf_top, pdf_detail:
            for p in doc.pages:
                all_pages.append(p)

        buffer = BytesIO()
        pdf_top.copy(all_pages).write_pdf(buffer)

        buffer.seek(0)
        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'芳名帳（供花供物）-{seko.id}.pdf',
            content_type='application/pdf',
        )
        return response


class HomeichoKodenPDF(generics.RetrieveAPIView):
    queryset = Seko.objects.select_related('seko_company',).prefetch_related(
        Prefetch('kojin', queryset=Kojin.objects.filter(kojin_num=1)),
        Prefetch(
            'entries',
            queryset=Entry.objects.filter(cancel_ts__isnull=True)
            .prefetch_related(
                Prefetch(
                    'details',
                    queryset=EntryDetail.objects.filter(koden__isnull=False).select_related(
                        'koden'
                    ),
                )
            )
            .order_by('id'),
        ),
    )

    def retrieve(self, request, *args, **kwargs):
        seko = self.get_object()

        total_henrei = 0
        total_koden = 0
        for entry in seko.entries.all():
            for detail in entry.details.all():
                total_koden += detail.item_unit_price
                if hasattr(detail.koden, 'henrei_koden'):
                    total_henrei += detail.koden.henrei_koden.henreihin_price

        # html_text = get_template('pdf/homeicho_koden.html').render(
        #     {
        #         'counter': functools.partial(next, itertools.count(start=1)),
        #         'seko': seko,
        #         'total_henrei': total_henrei,
        #     }
        # )

        # buffer = BytesIO()
        # HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
        #     buffer,
        # )
        # buffer.seek(0)
        html_text_top = get_template('pdf/homeicho_top.html').render(
            {
                'title1': 'ご 香 典',
                'seko': seko,
            }
        )
        html_text_detail = get_template('pdf/homeicho_koden.html').render(
            {
                'counter': functools.partial(next, itertools.count(start=1)),
                'seko': seko,
                'total_henrei': total_henrei,
                'total_koden': total_koden,
            }
        )
        pdf_top = HTML(string=html_text_top, base_url=request.build_absolute_uri()).render()
        pdf_detail = HTML(string=html_text_detail, base_url=request.build_absolute_uri()).render()

        all_pages = []
        for doc in pdf_top, pdf_detail:
            for p in doc.pages:
                all_pages.append(p)

        buffer = BytesIO()
        pdf_top.copy(all_pages).write_pdf(buffer)

        buffer.seek(0)
        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'芳名帳（香典）-{seko.id}.pdf',
            content_type='application/pdf',
        )
        return response


class HomeichoMsgPDF(generics.RetrieveAPIView):
    queryset = Seko.objects.select_related('seko_company',).prefetch_related(
        Prefetch('kojin', queryset=Kojin.objects.filter(kojin_num=1)),
        Prefetch(
            'entries',
            queryset=Entry.objects.filter(cancel_ts__isnull=True)
            .prefetch_related(
                Prefetch(
                    'details',
                    queryset=EntryDetail.objects.filter(message__isnull=False).select_related(
                        'message'
                    ),
                )
            )
            .order_by('id'),
        ),
    )

    def retrieve(self, request, *args, **kwargs):
        seko = self.get_object()

        # html_text = get_template('pdf/homeicho_message.html').render(
        #     {
        #         'counter': functools.partial(next, itertools.count(start=1)),
        #         'seko': seko,
        #     }
        # )

        # buffer = BytesIO()
        # HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
        #     buffer,
        # )
        # buffer.seek(0)
        html_text_top = get_template('pdf/homeicho_top.html').render(
            {
                'title1': '追 悼',
                'title2': 'メッセージ',
                'seko': seko,
            }
        )
        html_text_detail = get_template('pdf/homeicho_message.html').render(
            {
                'counter': functools.partial(next, itertools.count(start=1)),
                'seko': seko,
            }
        )
        pdf_top = HTML(string=html_text_top, base_url=request.build_absolute_uri()).render()
        pdf_detail = HTML(string=html_text_detail, base_url=request.build_absolute_uri()).render()

        all_pages = []
        for doc in pdf_top, pdf_detail:
            for p in doc.pages:
                all_pages.append(p)

        buffer = BytesIO()
        pdf_top.copy(all_pages).write_pdf(buffer)

        buffer.seek(0)
        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'芳名帳（追悼MSG）-{seko.id}.pdf',
            content_type='application/pdf',
        )
        return response


class MsgPDF(generics.RetrieveAPIView):
    queryset = Seko.objects.select_related('seko_company',).prefetch_related(
        Prefetch(
            'entries',
            queryset=Entry.objects.filter(cancel_ts__isnull=True)
            .prefetch_related(
                Prefetch(
                    'details',
                    queryset=EntryDetail.objects.filter(
                        Q(message__isnull=False) & ~Q(message__release_status=2)
                    ).select_related('message'),
                )
            )
            .order_by('id'),
        ),
    )

    def retrieve(self, request, *args, **kwargs):
        seko = self.get_object()

        html_text = get_template('pdf/message.html').render(
            {
                'counter': functools.partial(next, itertools.count(start=1)),
                'seko': seko,
            }
        )

        buffer = BytesIO()
        HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
            buffer,
        )
        buffer.seek(0)

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'追悼MSG-{seko.id}.pdf',
            content_type='application/pdf',
        )
        return response


class MoshuTokenObtainPair(TokenObtainPairView):
    serializer_class = MoshuTokenObtainSerializer


class NumberInFilter(filters.BaseInFilter, filters.NumberFilter):
    pass


class MoshuListFilter(filters.FilterSet):
    prefecture = filters.CharFilter(lookup_expr='contains')
    address_1 = filters.CharFilter(lookup_expr='contains')
    af_type = NumberInFilter(
        field_name='seko__seko_af__wishes__af_type', lookup_expr='in', distinct=True
    )
    company = filters.NumberFilter(field_name='seko__seko_company')


class MoshuList(generics.ListAPIView):

    queryset = Moshu.objects.select_related('seko').filter(Q(seko__del_flg=False)).order_by('seko')
    serializer_class = SimpleMoshuSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = MoshuListFilter
    permission_classes = [IsStaff]


class MoshuDetail(generics.RetrieveUpdateAPIView):
    """
    get: 喪主情報を取得します
    put: 喪主情報を更新します
    patch: 喪主情報を更新します
    """

    queryset = Moshu.objects.select_related('seko').filter(Q(seko__del_flg=False)).all()
    serializer_class = SimpleMoshuSerializer
    permission_classes = [IsMoshu]

    def get_object(self) -> Moshu:
        instance: Moshu = super().get_object()
        if instance != self.request.user:
            raise PermissionDenied
        return instance

    def perform_update(self, serializer):
        instance: Moshu = serializer.save()
        if serializer.context['send_soke_site_del_mail']:
            mail_body = (
                get_template('mail/soke_page_delete_request.txt')
                .render({'object': instance})
                .replace('  ', '　')
            )
            message = EmailMessage(
                subject='FOC葬家専用ページ削除依頼',
                body=mail_body,
                from_email=settings.EMAIL_FROM,
                to=[instance.seko.af_staff.mail_address],
                bcc=settings.INQUIRY_EMAIL_BCC,
            )
            message.send(fail_silently=False)


class MoshuApproval(generics.RetrieveAPIView):
    """
    get: 葬家専用ページの公開を承認し、パスワードをリセットします
    """

    queryset = Moshu.objects.select_related('seko').filter(Q(seko__del_flg=False)).all()
    serializer_class = SimpleMoshuSerializer

    def retrieve(self, request, *args, **kwargs):
        instance: Moshu = self.get_object()
        if not instance.agree_ts:
            instance.agree_ts = timezone.localtime()
            instance.save()

        seko: Seko = Seko.objects.filter(pk=instance.pk).first()
        if not seko.fuho_site_admission_ts:
            seko.fuho_site_admission_ts = instance.agree_ts
            seko.save()
            HoyoSeko.generate_from_seko(seko=seko)
        new_raw_password: str = instance.reset_password()
        soke_url: str = urljoin(request_origin(self.request), f'soke/?id={instance.pk}')
        mail_body = (
            get_template('mail/soke_page_is_ready.txt')
            .render({'object': instance, 'raw_password': new_raw_password, 'soke_url': soke_url})
            .replace('  ', '　')
        )
        if hasattr(seko, 'seko_contact') and seko.seko_contact.mail_address:
            address_to = seko.seko_contact.mail_address
        else:
            address_to = instance.mail_address
        message = EmailMessage(
            subject='【てれ葬儀こころ】ご葬家専用ページのご案内',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=[address_to],
            bcc=settings.INQUIRY_EMAIL_BCC,
        )
        message.send(fail_silently=False)

        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class SekoInquiryList(generics.ListCreateAPIView):

    serializer_class = SekoInquirySerializer
    permission_classes = [IsMoshu | ReadNeedAuth]

    def get_seko(self) -> Seko:
        seko_id: int = self.kwargs.get('seko_id')
        seko: Seko = Seko.objects.filter(pk=seko_id).first()
        if not seko:
            raise NotFound(f'No {Seko._meta.object_name} matches the given query.')
        return seko

    def get_queryset(self):
        seko: Seko = self.get_seko()
        return (
            SekoInquiry.objects.prefetch_related(
                Prefetch(
                    'answers', queryset=SekoAnswer.objects.filter(Q(del_flg=False)).order_by('pk')
                )
            )
            .filter(Q(seko=seko))
            .order_by('query_group_id', 'pk')
        )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['seko'] = self.get_seko()
        return context

    def perform_create(self, serializer):
        super().perform_create(serializer)

        instance: SekoInquiry = serializer.instance

        mail_body = get_template('mail/seko_inquiry.txt').render(
            {'inquiry': instance, 'moshu': instance.seko.moshu}
        )
        message = EmailMessage(
            subject='【FOC】ご葬家からの問合せがあります',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=[instance.seko.af_staff.mail_address],
            bcc=settings.INQUIRY_EMAIL_BCC,
        )
        message.send(fail_silently=False)
        return instance


class SekoAnswerList(generics.CreateAPIView):

    serializer_class = SekoAnswerSerializer
    permission_classes = [IsStaff]

    def get_inquiry(self) -> Seko:
        inquiry_id: int = self.kwargs.get('inquiry_id')
        inquiry: SekoInquiry = SekoInquiry.objects.filter(pk=inquiry_id).first()
        if not inquiry:
            raise NotFound(f'No {SekoInquiry._meta.object_name} matches the given query.')
        return inquiry

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['inquiry'] = self.get_inquiry()
        context['staff'] = self.request.user
        return context

    def perform_create(self, serializer):
        super().perform_create(serializer)

        instance: SekoAnswer = serializer.instance
        seko: Seko = instance.inquiry.seko
        moshu: Moshu = seko.moshu
        soke_url: str = urljoin(request_origin(self.request), f'soke/?id={seko.id}')
        mail_body = get_template('mail/seko_answer.txt').render(
            {
                'answer': instance,
                'seko': seko,
                'moshu': moshu,
                'soke_url': soke_url,
            }
        )
        if hasattr(seko, 'seko_contact') and seko.seko_contact.mail_address:
            address_to = seko.seko_contact.mail_address
        else:
            address_to = moshu.mail_address
        message = EmailMessage(
            subject='【てれ葬儀こころ】お問合せのご回答',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=[address_to],
            bcc=settings.INQUIRY_EMAIL_BCC,
        )
        message.send(fail_silently=False)
        return instance


class FdnSekoCreater(generics.CreateAPIView):

    permission_classes = [IsStaff]
    serializer = FdnSekoCreateSerializer

    def validate_data(self, request) -> str:
        company: Base = Base.objects.filter(
            Q(del_flg=False),
            Q(base_type=Base.OrgType.COMPANY),
            Q(company_code=self.request.user.company_code),
        ).first()
        if not company:
            raise ValidationError(f'Base not found: company_code={self.request.user.company_code}')
        request.data['seko_company'] = company.id

        fdn_seko_code = request.data.pop('fdn_seko_code', None)
        if not fdn_seko_code:
            raise ValidationError({'fdn_seko_code': _('This field is required.')}, code='required')

        seko: Seko = Seko.objects.filter(
            Q(del_flg=False), Q(seko_company=company.id), Q(fdn_code=fdn_seko_code)
        ).first()
        if seko:
            return {'code': 'E001', 'message': f'この施行情報(施行番号={fdn_seko_code})はすでに連携済みです。'}
        request.data['fdn_code'] = fdn_seko_code

        seko_department_code = request.data.pop('seko_department_code', None)
        if seko_department_code:
            department: Base = Base.objects.filter(
                Q(del_flg=False),
                Q(company_code=self.request.user.company_code),
                Q(fdn_code=seko_department_code),
            ).first()
            if not department:
                code_only = seko_department_code.replace('bumon.', '').replace('kaijyo.', '')
                return {'code': 'E002', 'message': f'施行部門コード({code_only})がFOCシステムに存在しません。'}
            request.data['seko_department'] = department.id

        order_staff_code = request.data.pop('order_staff_code', None)
        if not order_staff_code:
            raise ValidationError(
                {'order_staff_code': _('This field is required.')}, code='required'
            )
        order_staff: Staff = Staff.objects.filter(
            Q(del_flg=False),
            Q(company_code=self.request.user.company_code),
            Q(fdn_code=order_staff_code),
        ).first()
        if not order_staff:
            return {'code': 'E003', 'message': f'受注担当者コード({order_staff_code})がFOCシステムに存在しません。'}
        request.data['order_staff'] = order_staff.id

        seko_staff_code = request.data.pop('seko_staff_code', None)
        if seko_staff_code:
            seko_staff: Staff = Staff.objects.filter(
                Q(del_flg=False),
                Q(company_code=self.request.user.company_code),
                Q(fdn_code=seko_staff_code),
            ).first()
            if not seko_staff:
                return {'code': 'E004', 'message': f'施行担当者コード({seko_staff_code})がFOCシステムに存在しません。'}
            request.data['seko_staff'] = seko_staff.id

        af_staff_code = request.data.pop('af_staff_code', None)
        if af_staff_code:
            af_staff: Staff = Staff.objects.filter(
                Q(del_flg=False),
                Q(company_code=self.request.user.company_code),
                Q(fdn_code=af_staff_code),
            ).first()
            if not af_staff:
                return {'code': 'E005', 'message': f'AF担当者コード({af_staff_code})がFOCシステムに存在しません。'}
            request.data['af_staff'] = af_staff.id

        schedules_data = request.data.get('schedules', [])
        for schedule_dict in schedules_data:
            hall_code = schedule_dict.pop('hall_code', None)
            if hall_code:
                hall: Base = Base.objects.filter(
                    Q(del_flg=False),
                    Q(company_code=self.request.user.company_code),
                    Q(fdn_code=hall_code),
                ).first()
                if not hall:
                    code_only = hall_code.replace('bumon.', '').replace('kaijyo.', '')
                    return {'code': 'E006', 'message': f'式場コード({code_only})がFOCシステムに存在しません。'}
                schedule_dict['hall'] = hall.id

        self.serializer = FdnSekoCreateSerializer(data=request.data)
        return None

    def post(self, request, format=None):
        ret_data = {
            'status': 0,
        }
        error = self.validate_data(request)
        if error:
            ret_data['status'] = 1
            ret_data['error'] = error
            return Response(ret_data, status=status.HTTP_201_CREATED)

        if not self.serializer.is_valid():
            return Response(self.serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        super().perform_create(self.serializer)
        ret_data['data'] = {'seko_id': self.serializer.instance.id}

        return Response(ret_data, status=status.HTTP_201_CREATED)


class FdnSekoUpdate(generics.UpdateAPIView):

    permission_classes = [IsStaff]
    serializer_class = FdnSekoUpdateSerializer
    queryset = Seko.objects.filter(Q(del_flg=False)).all()

    def validate_data(self, request) -> str:
        company: Base = Base.objects.filter(
            Q(del_flg=False),
            Q(base_type=Base.OrgType.COMPANY),
            Q(company_code=self.request.user.company_code),
        ).first()
        if not company:
            raise ValidationError(f'Base not found: company_code={self.request.user.company_code}')

        fdn_seko_code = request.data.pop('fdn_seko_code', None)
        if not fdn_seko_code:
            request.data['fdn_code'] = fdn_seko_code
            return None

        seko: Seko = Seko.objects.filter(
            Q(del_flg=False), Q(seko_company=company.id), Q(fdn_code=fdn_seko_code)
        ).first()
        if seko:
            return {'code': 'E001', 'message': f'この施行情報(施行番号={fdn_seko_code})はすでに連携済みです。'}
        request.data['fdn_code'] = fdn_seko_code
        return None

    def patch(self, request, *args, **kwargs):
        ret_data = {
            'status': 0,
        }
        error = self.validate_data(request)
        if error:
            ret_data['status'] = 1
            ret_data['error'] = error
            return Response(ret_data, status=status.HTTP_200_OK)

        instance = self.get_object()
        self.serializer = self.get_serializer(instance, data=request.data, partial=True)
        if not self.serializer.is_valid():
            return Response(self.serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        self.serializer.save()

        return Response(ret_data, status=status.HTTP_200_OK)


class NoticeAttachedFile(views.APIView):

    permission_classes = [IsAuthenticated]

    def post(self, request, pk, format=None):
        seko: Seko = get_object_or_404(Seko, pk=pk)

        file_title = request.data.pop('file_title', None)
        if not file_title:
            raise ValidationError({'file_title': _('This field is required.')}, code='required')

        soke_url: str = urljoin(request_origin(self.request), f'soke/?id={seko.id}')
        mail_body = get_template('mail/seko_attached_file.txt').render(
            {
                'seko': seko,
                'soke_url': soke_url,
            }
        )
        if hasattr(seko, 'seko_contact') and seko.seko_contact.mail_address:
            address_to = seko.seko_contact.mail_address
        else:
            address_to = seko.moshu.mail_address

        message = EmailMessage(
            subject=f'【てれ葬儀こころ】{file_title}送付のご案内',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=[address_to],
        )
        message.send(fail_silently=False)
        return HttpResponse(b'{}', content_type='application/json')


class NoticeImageFileToSoke(views.APIView):

    permission_classes = [IsAuthenticated]

    def post(self, request, pk, format=None):
        seko: Seko = get_object_or_404(Seko, pk=pk)

        soke_url: str = urljoin(request_origin(self.request), f'soke/?id={seko.id}')
        mail_body = get_template('mail/seko_share_image_soke.txt').render(
            {
                'seko': seko,
                'soke_url': soke_url,
            }
        )
        if hasattr(seko, 'seko_contact') and seko.seko_contact.mail_address:
            address_to = seko.seko_contact.mail_address
        else:
            address_to = seko.moshu.mail_address

        message = EmailMessage(
            subject='【てれ葬儀こころ】画像ファイルのご案内',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=[address_to],
        )
        message.send(fail_silently=False)
        return HttpResponse(b'{}', content_type='application/json')


class NoticeImageFileToCompany(views.APIView):

    permission_classes = [IsAuthenticated]

    def post(self, request, pk, format=None):
        seko: Seko = get_object_or_404(Seko, pk=pk)

        if seko.seko_company.order_mail_address:
            mail_address = seko.seko_company.order_mail_address.split(',')
        if not mail_address:
            raise ValidationError(
                {'mail_address': _('Then mail_address is required.')}, code='required'
            )

        mail_body = get_template('mail/seko_share_image_company.txt').render({'seko': seko})
        message = EmailMessage(
            subject='【FOC】ご葬家からの画像アップロードがあります',
            body=mail_body,
            from_email=settings.EMAIL_FROM,
            to=mail_address,
        )
        message.send(fail_silently=False)

        return HttpResponse(b'{}', content_type='application/json')


class NowlSekoListFilter(filters.FilterSet):
    term_from = filters.DateTimeFilter(field_name='seko_date', lookup_expr='gte')
    term_to = filters.DateTimeFilter(field_name='seko_date', lookup_expr='lte')


class NowlSekoList(generics.ListAPIView):

    queryset = (
        Seko.objects.filter(Q(del_flg=False), Q(seko_company__company_code='01001000'))
        .select_related(
            'seko_style',
            'seko_company',
        )
        .prefetch_related(
            Prefetch('moshu'),
            Prefetch('kojin', queryset=Kojin.objects.order_by('kojin_num')),
            Prefetch('schedules', queryset=SekoSchedule.objects.order_by('display_num')),
        )
        .order_by('seko_date')
        .all()
    )
    serializer_class = NowlSekoListSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = NowlSekoListFilter
