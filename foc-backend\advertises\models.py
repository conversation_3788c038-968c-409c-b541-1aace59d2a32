from datetime import date
from typing import Optional

from django.db import models
from django.db.models import Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from bases.models import Base


class AdvertiseQuerySet(models.QuerySet):
    def filter_active(self, today: Optional[date] = None) -> models.QuerySet:
        the_date: date = today or timezone.localdate()
        return self.filter(
            Q(del_flg=False) & Q(begin_date__lte=the_date) & Q(end_date__gte=the_date)
        )


class Advertise(models.Model):
    company = models.ForeignKey(
        Base, models.PROTECT, verbose_name=_('company'), related_name='advertises'
    )
    banner_file = models.ImageField(
        _('banner file'), db_column='file_name', upload_to='adv_banner'
    )
    url = models.TextField(_('URL'), blank=True, null=True)
    link_file = models.FileField(
        _('link file'), db_column='link_file_name', upload_to='adv_link', blank=True, null=True
    )
    begin_date = models.DateField(_('begin on'))
    end_date = models.DateField(_('end on'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))

    objects = AdvertiseQuerySet.as_manager()

    class Meta:
        db_table = 'm_advertise'

    def disable(self) -> None:
        """広告を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()
