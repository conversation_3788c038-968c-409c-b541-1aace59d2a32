import multiprocessing
import os

from invoke import Collection, task

nproc: int = multiprocessing.cpu_count()


@task
def lint_isort(ctx):
    """
    isortでPythonコード内のimportをソートします
    """
    print('Sorting imports...')
    result = ctx.run(f'isort -j {nproc} .')
    if result.exited == 0:
        print('Isort sorted imports with no errors')


@task
def check_isort(ctx):
    """
    isortでPythonコード内のimportがソートされているか確認します
    """
    print('Checking imports are sorted...')
    result = ctx.run(f'isort --check -j {nproc} .')
    if result.exited == 0:
        print('Isort sorted imports with no errors')


@task
def lint_ffffff(ctx):
    """
    ffffffでコーディングスタイル設定に沿ってフォーマットします
    """
    print('Formatting code...')
    result = ctx.run('ffffff .')
    if result.exited == 0:
        print('ffffff formatted code with no errors')


@task
def check_ffffff(ctx):
    """
    ffffffでコーディングスタイル設定に沿ってフォーマットされているか確認します
    """
    print('Checking code are sorted...')
    result = ctx.run('ffffff --check .')
    if result.exited == 0:
        print('ffffff formatted code with no errors')


@task
def lint_flake8(ctx):
    """
    flake8で書式エラーなどを検知します
    """
    print('Linting your code with flake8...')
    result = ctx.run('flake8 .')
    if result.exited == 0:
        print('Flake8 linted codes with no errors.')


@task
def lint_radon_mi(ctx):
    """
    radonでメンテナンス複雑性を測定します
    """
    print('Computing Maintainability Index...')
    os.environ['RADONFILESENCODING'] = 'UTF-8'
    result = ctx.run('radon mi -n B .')
    if result.exited == 0:
        print('Maintainability Index computed with no errors')


@task
def lint_radon_cc(ctx):
    """
    radonで循環的複雑度を測定します
    """
    print('Computing Cyclomatic Complexity...')
    os.environ['RADONFILESENCODING'] = 'UTF-8'
    result = ctx.run('radon cc -n C .')
    if result.exited == 0:
        print('Cyclomatic Complexity computed with no errors')


@task(lint_isort, lint_ffffff, lint_flake8, lint_radon_mi, lint_radon_cc)
def lint_all(ctx):
    """
    すべてのLint項目を実行します
    """
    print('All lint has done. Check results above.')


@task(check_isort, check_ffffff, lint_flake8, lint_radon_mi, lint_radon_cc)
def check_all(ctx):
    """
    すべてのLint項目を検査します
    """
    print('All lint has done. Check results above.')


lints = Collection('lint')
lints.add_task(lint_isort, 'isort')
lints.add_task(lint_ffffff, 'ffffff')
lints.add_task(lint_flake8, 'flake8')
lints.add_task(lint_radon_mi, 'radon_mi')
lints.add_task(lint_radon_cc, 'radon_cc')
lints.add_task(lint_all, 'all', default=True)
lints.add_task(check_all, 'check_all')


@task
def unittest(ctx):
    """
    UnitTestを実行します
    """
    print('Starting all UnitTest...')
    result = ctx.run('python manage.py test --settings=foc.test_settings --failfast -v2')
    if result.exited == 0:
        print('UnitTest has done with no errors')


tests = Collection('test')
tests.add_task(unittest, 'unitest', default=True)


@task
def coverage_run(ctx, verbose=1):
    """
    Coverage測定付きのUnitTestを実行します
    """
    print('Starting all UnitTest with coverage...')
    commands = [
        'coverage run manage.py test --settings=foc.test_settings',
    ]
    if verbose != 1:
        commands.append(f'-v{verbose}')
    commands.append('.')
    result = ctx.run(' '.join(commands))
    if result.exited == 0:
        print('Coverage ran with no errors')


@task
def coverage_run_parallel(ctx, verbose=1):
    """
    Coverage測定付きのUnitTestを実行します
    """
    print('Starting all UnitTest with coverage...')
    commands = [
        'coverage run --parallel-mode --concurrency=multiprocessing',
        'manage.py test --settings=foc.test_settings --parallel',
    ]
    if verbose != 1:
        commands.append(f'-v{verbose}')
    result = ctx.run(' '.join(commands))
    result = ctx.run('coverage combine')
    if result.exited == 0:
        print('Coverage ran with no errors')


@task
def coverage_clean(ctx):
    """
    Coverage測定結果ファイルを消去します
    """
    ctx.run('coverage erase')


@task
def coverage_report(ctx, skip_covered=False):
    """
    Coverage測定結果を表示します

    Args:
        ctx ([type]): Context
        skip_covered (bool, optional): Defaults to False. カバー率100%以外の項目だけ表示します
    """
    commandline = 'coverage report'
    if skip_covered:
        commandline = f'{commandline} --skip-covered'
    ctx.run(commandline)


@task
def coverage_html(ctx, skip_covered=False):
    """
    Coverage測定結果をHTMLで出力します

    Args:
        ctx ([type]): Context
        skip_covered (bool, optional): Defaults to False. カバー率100%以外の項目だけ表示します
    """
    commandline = 'coverage html -d .coverage.html'
    ctx.run(commandline)


@task(coverage_run, coverage_report, coverage_html, coverage_clean)
def coverage_all(ctx):
    """
    すべてのCoverage項目を実行します
    """
    print('All coverage has done. Check results above.')


coverages = Collection('cover')
coverages.add_task(coverage_run, 'run')
coverages.add_task(coverage_run_parallel, 'prun')
coverages.add_task(coverage_report, 'report')
coverages.add_task(coverage_html, 'html')
coverages.add_task(coverage_clean, 'clean')
coverages.add_task(coverage_all, 'all', default=True)

ns = Collection()
ns.add_collection(lints)
ns.add_collection(tests)
ns.add_collection(coverages)
