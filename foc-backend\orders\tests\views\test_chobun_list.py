from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from orders.models import EntryDetailChobun
from orders.tests.factories import EntryDetailChobunFactory, EntryDetailFactory, EntryFactory
from seko.tests.factories import KojinFactory, MoshuFactory, SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class ChobunListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_1 = BaseFactory()
        self.base_2 = BaseFactory()

        self.seko_1 = SekoFactory(seko_company=self.base_1)
        self.seko_2 = SekoFactory(seko_company=self.base_2)
        self.kojin_1 = KojinFactory(kojin_num=1, seko=self.seko_1)
        self.kojin_2 = KojinFactory(kojin_num=1, seko=self.seko_2)
        self.moshu_1 = MoshuFactory(seko=self.seko_1)
        self.moshu_2 = MoshuFactory(seko=self.seko_2)
        self.entry_1 = EntryFactory(seko=self.seko_1)
        self.entry_2 = EntryFactory(seko=self.seko_2)

        entry_detail_21 = EntryDetailFactory(entry=self.entry_2)
        entry_detail_22 = EntryDetailFactory(entry=self.entry_2)
        entry_detail_11 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_12 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_13 = EntryDetailFactory(entry=self.entry_1)

        self.chobun_1 = EntryDetailChobunFactory(entry_detail=entry_detail_21)
        self.chobun_2 = EntryDetailChobunFactory(entry_detail=entry_detail_22)
        self.chobun_3 = EntryDetailChobunFactory(entry_detail=entry_detail_11)
        self.chobun_4 = EntryDetailChobunFactory(entry_detail=entry_detail_13)
        self.chobun_5 = EntryDetailChobunFactory(entry_detail=entry_detail_12)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_chobun_list_succeed(self) -> None:
        """弔文一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 5)

        response_chobun = records[0]
        db_chobun = (
            EntryDetailChobun.objects.select_related(
                'entry_detail',
                'entry_detail__entry',
                'entry_detail__entry__seko',
                'entry_detail__entry__seko__moshu',
            )
            .prefetch_related('entry_detail__entry__seko__kojin')
            .get(pk=response_chobun['entry_detail']['id'])
        )

        self.assertEqual(response_chobun['okurinushi_name'], db_chobun.okurinushi_name)
        self.assertEqual(response_chobun['entry_detail']['id'], db_chobun.entry_detail.id)
        self.assertEqual(
            response_chobun['entry_detail']['item_name'], db_chobun.entry_detail.item_name
        )
        self.assertEqual(
            response_chobun['entry_detail']['entry']['id'], db_chobun.entry_detail.entry.id
        )
        self.assertEqual(
            response_chobun['entry_detail']['entry']['entry_name'],
            db_chobun.entry_detail.entry.entry_name,
        )
        self.assertEqual(
            response_chobun['entry_detail']['entry']['seko']['id'],
            db_chobun.entry_detail.entry.seko.id,
        )
        self.assertEqual(
            response_chobun['entry_detail']['entry']['seko']['soke_name'],
            db_chobun.entry_detail.entry.seko.soke_name,
        )
        self.assertEqual(
            response_chobun['entry_detail']['entry']['seko']['seko_company'],
            db_chobun.entry_detail.entry.seko.seko_company.id,
        )
        self.assertEqual(
            response_chobun['entry_detail']['entry']['seko']['kojin'][0]['id'],
            db_chobun.entry_detail.entry.seko.kojin.get().id,
        )
        self.assertEqual(
            response_chobun['entry_detail']['entry']['seko']['moshu']['name'],
            db_chobun.entry_detail.entry.seko.moshu.name,
        )

        # 申込IDの昇順になっているか
        entry_ids = [record['entry_detail']['entry']['id'] for record in records]
        self.assertEqual(entry_ids, sorted(entry_ids))

    def test_chobun_list_ignores_deleted_seko(self) -> None:
        """削除した施行の弔文は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko_1.disable()

        params: Dict = {}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_chobun_list_ignores_when_kojin_num_is_not_equal_1(self) -> None:
        """故人番号が1ではない弔文は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.kojin_2.kojin_num = 2
        self.kojin_2.save()

        params: Dict = {}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_chobun_list_filter_by_company_id(self) -> None:
        """弔文一覧で葬儀社IDを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'company_id': self.base_1.id}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_chobun_list_filter_by_entry_id(self) -> None:
        """弔文一覧で申込IDを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'entry_id': self.entry_2.id}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_chobun_list_filter_by_okurinushi_name(self) -> None:
        """弔文一覧で送り主_氏名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.chobun_1.okurinushi_name = '1一'
        self.chobun_2.okurinushi_name = '1一2二'
        self.chobun_3.okurinushi_name = '3三1一'
        self.chobun_4.okurinushi_name = '4四'
        self.chobun_5.okurinushi_name = '5五'
        self.chobun_1.save()
        self.chobun_2.save()
        self.chobun_3.save()
        self.chobun_4.save()
        self.chobun_5.save()

        params: Dict = {'okurinushi_name': '1一'}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_chobun_list_filter_by_entry_name(self) -> None:
        """弔文一覧で申込者氏名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.entry_1.entry_name = '1一'
        self.entry_2.entry_name = '1一2二'
        self.entry_1.save()
        self.entry_2.save()

        params: Dict = {'entry_name': '2二'}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_chobun_list_filter_by_renmei(self) -> None:
        """弔文一覧で連名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.chobun_1.renmei1 = '11連名一一'
        self.chobun_1.renmei2 = '12連名一二'
        self.chobun_2.renmei1 = '21連名二一'
        self.chobun_2.renmei2 = '22連名二二11連名一一'
        self.chobun_3.renmei1 = '31連名三一'
        self.chobun_3.renmei2 = '32連名三二'
        self.chobun_4.renmei1 = '41連名四一'
        self.chobun_4.renmei2 = '42連名四二'
        self.chobun_5.renmei1 = '51連名五一11連名一一'
        self.chobun_5.renmei2 = '11連名一一52連名五二'
        self.chobun_1.save()
        self.chobun_2.save()
        self.chobun_3.save()
        self.chobun_4.save()
        self.chobun_5.save()

        params: Dict = {'renmei': '11連名一一'}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_chobun_list_filter_by_okurinushi_company(self) -> None:
        """弔文一覧で送り主_組織名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.chobun_1.okurinushi_company = '1一'
        self.chobun_2.okurinushi_company = '1一2二'
        self.chobun_3.okurinushi_company = '3三'
        self.chobun_4.okurinushi_company = '4四1一'
        self.chobun_5.okurinushi_company = '5五'
        self.chobun_1.save()
        self.chobun_2.save()
        self.chobun_3.save()
        self.chobun_4.save()
        self.chobun_5.save()

        params: Dict = {'okurinushi_company': '1一'}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_chobun_list_filter_by_okurinushi_title(self) -> None:
        """弔文一覧で送り主_役職名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.chobun_1.okurinushi_title = '1一'
        self.chobun_2.okurinushi_title = '1一2二'
        self.chobun_3.okurinushi_title = '3三'
        self.chobun_4.okurinushi_title = '4四'
        self.chobun_5.okurinushi_title = '5五1一'
        self.chobun_1.save()
        self.chobun_2.save()
        self.chobun_3.save()
        self.chobun_4.save()
        self.chobun_5.save()

        params: Dict = {'okurinushi_title': '1一'}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_chobun_list_filter_by_soke_name(self) -> None:
        """弔文一覧で葬家名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.seko_1.soke_name = '1一'
        self.seko_2.soke_name = '1一2二'
        self.seko_1.save()
        self.seko_2.save()

        params: Dict = {'soke_name': '一2'}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_chobun_list_filter_by_kojin_name(self) -> None:
        """弔文一覧で故人名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.kojin_1.name = '1一'
        self.kojin_2.name = '1一2二'
        self.kojin_1.save()
        self.kojin_2.save()

        params: Dict = {'kojin_name': '2二'}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_chobun_list_filter_by_moshu_name(self) -> None:
        """弔文一覧で喪主名を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.moshu_1.name = '1一'
        self.moshu_2.name = '2二1一'
        self.moshu_1.save()
        self.moshu_2.save()

        params: Dict = {'moshu_name': '二1'}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_chobun_list_filter_by_printed_flg(self) -> None:
        """弔文一覧で印刷済フラグを検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.chobun_1.printed_flg = True
        self.chobun_2.printed_flg = True
        self.chobun_3.printed_flg = False
        self.chobun_4.printed_flg = True
        self.chobun_5.printed_flg = False
        self.chobun_1.save()
        self.chobun_2.save()
        self.chobun_3.save()
        self.chobun_4.save()
        self.chobun_5.save()

        params: Dict = {'printed_flg': True}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_chobun_list_filter_by_is_cancel(self) -> None:
        """弔文一覧でキャンセル状況を検索する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.entry_1.cancel_ts = timezone.now() - timezone.timedelta(30)
        self.entry_1.save()

        params: Dict = {'is_cancel': True}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        params: Dict = {'is_cancel': False}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_chobun_list_failed_without_auth(self) -> None:
        """弔文一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('orders:chobun_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
