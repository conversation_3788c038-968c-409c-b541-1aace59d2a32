from typing import Optional

from django.db import models
from django.db.models import Q
from django.utils.translation import gettext_lazy as _

from bases.models import Base
from masters.models import Service, Tax
from suppliers.models import Supplier


class Item(models.Model):
    service = models.ForeignKey(
        Service, models.DO_NOTHING, verbose_name=_('service'), related_name='items'
    )
    base = models.ForeignKey(
        Base, models.DO_NOTHING, verbose_name=_('base company'), related_name='items'
    )
    hinban = models.TextField(_('hinban'), blank=True, null=True)
    name = models.TextField(_('name'))
    item_price = models.IntegerField(_('item price'))
    tax = models.ForeignKey(Tax, models.DO_NOTHING, verbose_name=_('tax'), related_name='items')
    begin_date = models.DateField(_('begin date'))
    end_date = models.DateField(_('end date'))
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))
    suppliers = models.ManyToManyField(
        'suppliers.Supplier',
        through='items.ItemSupplier',
        verbose_name='suppliers',
        related_name='items',
    )
    fdn_code = models.TextField(_('fdn code'), blank=True, null=True)
    display_num = models.IntegerField(_('display order'))

    class Meta:
        db_table = 'm_item'

    def disable(self) -> None:
        """商品を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()

    def default_supplier(self) -> Optional[Supplier]:
        """デフォルト設定されている発注先を返します"""
        return (
            self.suppliers.filter(Q(supplier_items__default_supplier_flg=True))
            .order_by('id')
            .first()
        )


class ItemSupplier(models.Model):
    item = models.ForeignKey(
        Item, models.CASCADE, verbose_name=_('item'), related_name='item_suppliers'
    )
    supplier = models.ForeignKey(
        Supplier, models.CASCADE, verbose_name=_('supplier'), related_name='supplier_items'
    )
    default_supplier_flg = models.BooleanField(_('default supplier flg'), default=False)

    class Meta:
        db_table = 'm_item_supplier'
