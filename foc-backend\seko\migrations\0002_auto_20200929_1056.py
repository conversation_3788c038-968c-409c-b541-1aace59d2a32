# Generated by Django 3.1.1 on 2020-09-29 01:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('seko', '0001_initial'),
        ('masters', '0003_zipcode'),
        ('bases', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='seko',
            name='af_staff',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='af_seko',
                to=settings.AUTH_USER_MODEL,
                verbose_name='af_staff',
            ),
        ),
        migrations.AddField(
            model_name='seko',
            name='order_staff',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='ordered_seko',
                to=settings.AUTH_USER_MODEL,
                verbose_name='order staff',
            ),
        ),
        migrations.AddField(
            model_name='seko',
            name='seko_company',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='seko_by_company',
                to='bases.base',
                verbose_name='seko company',
            ),
        ),
        migrations.AddField(
            model_name='seko',
            name='seko_department',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='seko',
                to='bases.base',
                verbose_name='seko department',
            ),
        ),
        migrations.AddField(
            model_name='seko',
            name='seko_staff',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='staffed_seko',
                to=settings.AUTH_USER_MODEL,
                verbose_name='seko staff',
            ),
        ),
        migrations.AddField(
            model_name='seko',
            name='seko_style',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='seko',
                to='masters.sekostyle',
                verbose_name='seko style',
            ),
        ),
        migrations.AddField(
            model_name='kojin',
            name='seko',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='kojin',
                to='seko.seko',
                verbose_name='seko',
            ),
        ),
    ]
