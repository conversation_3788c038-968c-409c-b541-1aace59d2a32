from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from henrei.tests.factories import HenreihinKodenFactory, HenreihinKumotsuFactory
from orders.tests.factories import (
    EntryDetailChobunFactory,
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryDetailMsgFactory,
    EntryFactory,
)
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

tz = timezone.get_current_timezone()
jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class EntryDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.entry = EntryFactory()
        entry_detail_1 = EntryDetailFactory(entry=self.entry)
        EntryDetailChobunFactory(entry_detail=entry_detail_1)

        entry_detail_2 = EntryDetailFactory(entry=self.entry)
        detail_kumotsu_1 = EntryDetailKumotsuFactory(entry_detail=entry_detail_2)
        HenreihinKumotsuFactory(detail_kumotsu=detail_kumotsu_1, henreihin=entry_detail_2.item)

        entry_detail_3 = EntryDetailFactory(entry=self.entry)
        detail_koden_1 = EntryDetailKodenFactory(entry_detail=entry_detail_3)
        HenreihinKodenFactory(detail_koden=detail_koden_1, henreihin=entry_detail_3.item)

        entry_detail_4 = EntryDetailFactory(entry=self.entry)
        EntryDetailMsgFactory(entry_detail=entry_detail_4)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_entry_detail_succeed(self) -> None:
        """申込詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:entry_detail', kwargs={'pk': self.entry.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['id'], self.entry.pk)
        self.assertEqual(record['entry_ts'], self.entry.entry_ts.astimezone(tz).isoformat())
        self.assertEqual(record['entry_name'], self.entry.entry_name)
        self.assertEqual(record['entry_tel'], self.entry.entry_tel)
        self.assertEqual(record['entry_mail_address'], self.entry.entry_mail_address)
        self.assertEqual(record['entry_prefecture'], self.entry.entry_prefecture)
        self.assertEqual(record['entry_address_1'], self.entry.entry_address_1)
        self.assertEqual(record['entry_address_2'], self.entry.entry_address_2)
        self.assertEqual(record['entry_address_3'], self.entry.entry_address_3)

        for record_detail, db_detail in zip(record['details'], self.entry.details.order_by('id')):
            self.assertEqual(record_detail['item']['service']['name'], db_detail.item.service.name)
            self.assertEqual(record_detail['item_name'], db_detail.item_name)
            self.assertEqual(record_detail['item_unit_price'], db_detail.item_unit_price)
            self.assertEqual(record_detail['quantity'], db_detail.quantity)
            if record_koden := record_detail['koden']:
                self.assertEqual(
                    record_koden['henrei_koden']['henreihin_name'],
                    db_detail.koden.henrei_koden.henreihin_name,
                )
                self.assertEqual(
                    record_koden['henrei_koden']['order_status'],
                    db_detail.koden.henrei_koden.order_status,
                )
                self.assertEqual(
                    record_koden['henrei_koden']['supplier']['name'],
                    db_detail.koden.henrei_koden.supplier.name,
                )
            if record_kumotsu := record_detail['kumotsu']:
                self.assertEqual(
                    record_kumotsu['henrei_kumotsu']['henreihin_name'],
                    db_detail.kumotsu.henrei_kumotsu.henreihin_name,
                )
                self.assertEqual(
                    record_kumotsu['henrei_kumotsu']['order_status'],
                    db_detail.kumotsu.henrei_kumotsu.order_status,
                )
                self.assertEqual(
                    record_kumotsu['henrei_kumotsu']['supplier']['name'],
                    db_detail.kumotsu.henrei_kumotsu.supplier.name,
                )

    # def test_entry_detail_fail_without_auth(self) -> None:
    #     """申込詳細APIがAuthorizationヘッダがなくて失敗する"""
    #     params: Dict = {}
    #     response = self.api_client.get(
    #         reverse('orders:entry_detail', kwargs={'pk': self.entry.pk}),
    #         data=params,
    #         format='json',
    #     )

    #     self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    #     record: Dict = response.json()
    #     self.assertIsNotNone(record['detail'])

    def test_entry_detail_failed_by_notfound(self) -> None:
        """申込詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        non_saved_entry = EntryFactory.build()
        response = self.api_client.get(
            reverse('orders:entry_detail', kwargs={'pk': non_saved_entry.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class EntryCancelViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.entry = EntryFactory()
        entry_detail_1 = EntryDetailFactory(entry=self.entry)
        EntryDetailChobunFactory(entry_detail=entry_detail_1)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_entry_cancel_succeed(self) -> None:
        """申込をキャンセルする"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.post(
            reverse('orders:entry_cancel', kwargs={'pk': self.entry.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['id'], self.entry.pk)
        self.assertIsNotNone(record['cancel_ts'])
        self.assertEqual(record['entry_ts'], self.entry.entry_ts.astimezone(tz).isoformat())
        self.assertEqual(record['entry_name'], self.entry.entry_name)
        self.assertEqual(record['entry_tel'], self.entry.entry_tel)
        self.assertEqual(record['entry_mail_address'], self.entry.entry_mail_address)
        self.assertEqual(record['entry_prefecture'], self.entry.entry_prefecture)
        self.assertEqual(record['entry_address_1'], self.entry.entry_address_1)
        self.assertEqual(record['entry_address_2'], self.entry.entry_address_2)
        self.assertEqual(record['entry_address_3'], self.entry.entry_address_3)

        self.entry.refresh_from_db()
        self.assertEqual(record['cancel_ts'], self.entry.cancel_ts.astimezone(tz).isoformat())

    def test_entry_cancel_failed_by_notfound(self) -> None:
        """申込キャンセルAPIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        non_saved_entry = EntryFactory.build()
        response = self.api_client.post(
            reverse('orders:entry_cancel', kwargs={'pk': non_saved_entry.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_entry_cancel_fail_without_auth(self) -> None:
        """申込キャンセルAPIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('orders:entry_cancel', kwargs={'pk': self.entry.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
