from base64 import b64encode
from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from advertises.models import Advertise
from advertises.tests.factories import AdvertiseFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
tz = timezone.get_current_timezone()


class AdvertiseDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.advertise: Advertise = AdvertiseFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_advertise_detail_succeed(self) -> None:
        """広告詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.advertise.pk)
        self.assertEqual(record['company'], self.advertise.company.pk)
        self.assertTrue(record['banner_file'].endswith(self.advertise.banner_file.url))
        self.assertEqual(record['url'], self.advertise.url)
        self.assertTrue(record['link_file'].endswith(self.advertise.link_file.url))
        self.assertEqual(record['begin_date'], self.advertise.begin_date.isoformat())
        self.assertEqual(record['end_date'], self.advertise.end_date.isoformat())
        self.assertEqual(record['del_flg'], self.advertise.del_flg)
        self.assertEqual(
            record['created_at'], self.advertise.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.advertise.create_user_id)
        self.assertEqual(
            record['updated_at'], self.advertise.updated_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['update_user_id'], self.advertise.update_user_id)

    def test_advertise_detail_failed_by_notfound(self) -> None:
        """広告詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_advertise: Advertise = AdvertiseFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('advertises:advertise_detail', kwargs={'pk': non_saved_advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_advertise_detail_failed_by_already_deleted(self) -> None:
        """広告詳細APIは無効になった広告を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.advertise.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_advertise_detail_failed_without_auth(self) -> None:
        """広告詳細APIはAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_advertise_detail_allow_from_moshu(self) -> None:
        """広告詳細APIが喪主からの呼び出しを許可する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class AdvertiseUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.advertise: Advertise = AdvertiseFactory()
        self.new_advertise_data: Advertise = AdvertiseFactory.build()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.advertise.company.pk,
            'banner_file': b64encode(self.new_advertise_data.banner_file.read()),
            'url': self.new_advertise_data.url,
            'link_file': b64encode(self.new_advertise_data.link_file.read()),
            'begin_date': self.new_advertise_data.begin_date,
            'end_date': self.new_advertise_data.end_date,
        }

    def test_advertise_update_succeed(self) -> None:
        """広告を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['company'], self.advertise.company.pk)
        self.assertIsNotNone(record['banner_file'])
        self.assertEqual(record['url'], self.new_advertise_data.url)
        self.assertIsNotNone(record['link_file'])
        self.assertEqual(record['begin_date'], self.new_advertise_data.begin_date.isoformat())
        self.assertEqual(record['end_date'], self.new_advertise_data.end_date.isoformat())
        self.assertEqual(record['del_flg'], self.advertise.del_flg)
        self.assertEqual(record['create_user_id'], self.advertise.create_user_id)
        self.assertEqual(
            record['created_at'], self.advertise.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['update_user_id'], self.staff.pk)
        self.assertIsNotNone(record['updated_at'])

    # def test_advertise_update_ignore_some_fields(self) -> None:
    #     """広告更新APIは特定のフィールドへの更新を無視する"""
    #     self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

    #     params: Dict = self.basic_params()
    #     params['company'] = BaseFactory().pk
    #     params['del_flg'] = True
    #     response = self.api_client.put(
    #         reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
    #         data=params,
    #         format='json',
    #     )
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)

    #     record: Dict = response.json()
    #     self.assertEqual(record['company'], self.advertise.company.pk)
    #     self.assertFalse(record['del_flg'])
    #     self.assertEqual(record['create_user_id'], self.advertise.create_user_id)
    #     self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_advertise_update_failed_by_notfound(self) -> None:
        """広告更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_advertise: Advertise = AdvertiseFactory.build()

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('advertises:advertise_detail', kwargs={'pk': non_saved_advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_advertise_update_failed_by_already_deleted(self) -> None:
        """広告更新APIが論理削除済みの広告を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.advertise.disable()

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_advertise_update_failed_without_auth(self) -> None:
        """広告更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_advertise_update_deny_from_moshu(self) -> None:
        """広告更新APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])


class AdvertiseDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.advertise: Advertise = AdvertiseFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_advertise_delete_succeed(self) -> None:
        """広告を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 広告のdel_flgがTrueになる
        db_advertise: Advertise = Advertise.objects.get(pk=self.advertise.pk)
        self.assertTrue(db_advertise.del_flg)
        self.assertEqual(db_advertise.create_user_id, self.advertise.create_user_id)
        self.assertEqual(db_advertise.update_user_id, self.staff.pk)

    def test_advertise_delete_failed_by_notfound(self) -> None:
        """広告削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_advertise: Advertise = AdvertiseFactory.build()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('advertises:advertise_detail', kwargs={'pk': non_saved_advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_advertise_delete_failed_by_already_deleted(self) -> None:
        """広告削除APIが論理削除済みの広告を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.advertise.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_advertise_delete_failed_without_auth(self) -> None:
        """広告削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_advertise_delete_deny_from_moshu(self) -> None:
        """広告削除APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('advertises:advertise_detail', kwargs={'pk': self.advertise.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
