import random
from typing import Dict

from dateutil.relativedelta import relativedelta
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory
from hoyo.tests.factories import HoyoSekoFactory
from masters.tests.factories import WarekiFactory
from seko.tests.factories import SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


class HoyoPdfViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff = StaffFactory()
        self.hoyo_seko_data = HoyoSekoFactory(
            seko=SekoFactory(),
            hall=BaseFactory(base_type=Base.OrgType.HALL),
            staff=self.staff,
            hoyo_activity_date=timezone.now(),
        )
        WarekiFactory(
            begin_date=self.hoyo_seko_data.hoyo_activity_date
            + relativedelta(years=-1 * random.randint(1, 20))
        )

        self.api_client = APIClient()

        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_pdf_succeed(self) -> None:
        """法要案内状PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_pdf', kwargs={'pk': self.hoyo_seko_data.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'法要案内状-{self.hoyo_seko_data.pk}.pdf')

    def test_hoyo_pdf_failed_by_notfound(self) -> None:
        """法要案内状PDFで存在しない法要施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_existent_data = HoyoSekoFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_pdf', kwargs={'pk': non_existent_data.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
