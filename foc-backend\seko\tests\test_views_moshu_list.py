from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.models import AfterFollow, SekoAf
from after_follow.tests.factories import AfterFollowFactory, AfWishFactory, SekoAfFactory
from bases.models import Base
from bases.tests.factories import BaseFactory
from seko.models import Moshu
from seko.tests.factories import MoshuFactory, SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class MoshuListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        seko_company: Base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.moshu_1: Moshu = MoshuFactory.build(
            seko=SekoFactory(seko_company=seko_company), prefecture='東京都', address_1='東京都'
        )
        self.moshu_2: Moshu = MoshuFactory.build(
            seko=SekoFactory(), prefecture='京都府', address_1='京都府'
        )
        self.moshu_3: Moshu = MoshuFactory.build(
            seko=SekoFactory(seko_company=seko_company), prefecture='北海道', address_1='西東京市'
        )
        self.moshu_2.save()
        self.moshu_3.save()
        self.moshu_1.save()

        self.af_type_1: AfterFollow = AfterFollowFactory()
        self.af_type_2: AfterFollow = AfterFollowFactory()
        self.af_type_3: AfterFollow = AfterFollowFactory()
        seko_af_1: SekoAf = SekoAfFactory(seko=self.moshu_1.seko)
        AfWishFactory(seko_af=seko_af_1, af_type=self.af_type_1)
        AfWishFactory(seko_af=seko_af_1, af_type=self.af_type_2)
        seko_af_2: SekoAf = SekoAfFactory(seko=self.moshu_2.seko)
        AfWishFactory(seko_af=seko_af_2, af_type=self.af_type_1)
        seko_af_3: SekoAf = SekoAfFactory(seko=self.moshu_3.seko)
        AfWishFactory(seko_af=seko_af_3, af_type=self.af_type_2)
        AfWishFactory(seko_af=seko_af_3, af_type=self.af_type_3)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_moshu_list_succeed(self) -> None:
        """喪主一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        for rec_moshu, correct_moshu in zip(records, [self.moshu_1, self.moshu_2, self.moshu_3]):
            self.assertEqual(rec_moshu['seko']['id'], correct_moshu.pk)
            self.assertEqual(rec_moshu['name'], correct_moshu.name)
            self.assertEqual(rec_moshu['mobile_num'], correct_moshu.mobile_num)
            self.assertEqual(rec_moshu['prefecture'], correct_moshu.prefecture)
            self.assertEqual(rec_moshu['address_1'], correct_moshu.address_1)
            self.assertEqual(rec_moshu['address_2'], correct_moshu.address_2)
            self.assertEqual(rec_moshu['address_3'], correct_moshu.address_3)
            self.assertEqual(rec_moshu['mail_flg'], correct_moshu.mail_flg)

    def test_moshu_list_ignores_disabled_seko(self) -> None:
        """喪主一覧APIは無効化した施行の喪主は返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.moshu_3.seko.disable()

        params: Dict = {}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_moshu_list_filter_by_prefecture(self) -> None:
        """喪主一覧APIで都道府県(部分一致)の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'prefecture': '東京都'}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        params: Dict = {'prefecture': '京都'}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        params: Dict = {'prefecture': '海'}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_moshu_list_filter_by_address_1(self) -> None:
        """喪主一覧APIで市区町村(部分一致)の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'address_1': '東京'}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        params: Dict = {'address_1': '東京市'}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        params: Dict = {'address_1': '京都'}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_moshu_list_filter_by_af_type(self) -> None:
        """喪主一覧APIでAF項目(IN)の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'af_type': ','.join([str(x) for x in [self.af_type_1.pk]])}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(
            set([rec['seko']['id'] for rec in records]), set([self.moshu_1.pk, self.moshu_2.pk])
        )

        params: Dict = {
            'af_type': ','.join([str(x) for x in [self.af_type_2.pk, self.af_type_3.pk]])
        }
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(
            set([rec['seko']['id'] for rec in records]), set([self.moshu_1.pk, self.moshu_3.pk])
        )

        params: Dict = {'af_type': ','.join([str(x) for x in [self.af_type_3.pk]])}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(set([rec['seko']['id'] for rec in records]), set([self.moshu_3.pk]))

    def test_moshu_list_filter_by_company(self) -> None:
        """喪主一覧APIで施行会社の絞り込みを行う"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'company': self.moshu_1.seko.seko_company.pk}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        params: Dict = {'company': self.moshu_2.seko.seko_company.pk}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_moshu_list_failed_without_auth(self) -> None:
        """喪主一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_moshu_list_deny_from_moshu(self) -> None:
        """喪主一覧APIが喪主による呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('seko:moshu_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
