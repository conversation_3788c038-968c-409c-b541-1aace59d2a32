name: Test application then build a docker image

on: push

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      SECRET_KEY: dummy-secret-key
    steps:
      - uses: actions/checkout@v2

      - name: Set up Python 3.8
        uses: actions/setup-python@v2
        with:
          python-version: 3.8

      - name: Use pip and poetry cache
        uses: actions/cache@v2
        with:
          path: |
            ~/.cache/pip
            ~/.cache/pypoetry
          key: ${{ runner.os }}-pip-${{ hashFiles('poetry.lock') }}
          restore-keys: |
            $${{ runner.os }}-pip-
            $${{ runner.os }}-

      - name: Install dependencies
        run: |
          python -m pip install poetry
          poetry install

      - name: Run lint
        run: |
          poetry run inv lint.check-all

      # - name: Run test
      #   run: |
      #     poetry run inv cover.prun
      #     poetry run inv cover.report
      #     poetry run inv cover.clean
