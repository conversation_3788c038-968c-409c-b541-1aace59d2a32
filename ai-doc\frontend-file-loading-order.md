# FOCフロントエンド ファイル読み込み順序

## 概要

FOCフロントエンドでのファイル読み込み順序を詳細に説明します。Angularアプリケーションの起動からコンポーネント表示まで、どのファイルがどの順序で読み込まれるかを理解できます。

## 1. アプリケーション起動フロー

### 1.1 ブートストラップ段階

```
1. index.html
   ↓
2. main.ts (エントリーポイント)
   ↓
3. app.module.ts (ルートモジュール)
   ↓
4. app.component.ts (ルートコンポーネント)
```

#### 詳細説明

**1. index.html**
- ブラウザが最初に読み込むHTMLファイル
- `<app-root></app-root>` タグでAngularアプリケーションをマウント

**2. main.ts**
```typescript
import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { environment } from './environments/environment';

if (environment.production) {
  enableProdMode();
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
```
- Angularアプリケーションのエントリーポイント
- 環境設定の読み込み
- AppModuleのブートストラップ

**3. app.module.ts**
- ルートモジュールの定義
- 全コンポーネント、サービス、ガードの宣言・登録

**4. app.component.ts**
- ルートコンポーネント
- アプリケーション全体のベースとなるコンポーネント

### 1.2 モジュール読み込み順序

```typescript
// app.module.ts での読み込み順序

// 1. Angular Core Modules
import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// 2. Third-party Modules
import { MatDialogModule } from '@angular/material/dialog';
import { HttpClientModule } from '@angular/common/http';
import { RuntimeConfigLoaderModule } from 'runtime-config-loader';

// 3. App Routing Module
import { AppRoutingModule } from './app-routing.module';

// 4. Guards (認証・権限)
import { AuthFGuard } from './guard/authF.guard';
import { AdminGuard } from './guard/admin.guard';
import { OrderGuard } from './guard/order.guard';
import { AuthSGuard } from './guard/authS.guard';
import { StaffOnlyGuard } from './guard/staff.guard';

// 5. Services (サービス)
import { RouterService } from './service/router.service';
import { HttpClientService } from './service/http-client.service';
import { LoaderService } from './service/loader.service';
import { SessionService } from './service/session.service';
import { CacheService } from './service/cache.service';

// 6. Common Components (共通コンポーネント)
import { DialogComponent } from './pages/common/dialog/dialog.component';
import { LoadingModalComponent } from './pages/common/loading-modal/loading-modal.component';

// 7. Frame Components (フレームコンポーネント)
import { CompanyFrameComponent } from './pages/company/frame.component';
import { CustomerFrameComponent } from './pages/customer/frame.component';
import { FamilyFrameComponent } from './pages/family/frame.component';

// 8. Page Components (各ページコンポーネント)
import { LoginComponent } from './pages/company/login/login.component';
import { TopComponent } from './pages/company/top/top.component';
// ... 他のコンポーネント
```

## 2. ルーティング処理フロー

### 2.1 URL アクセス時の読み込み順序

```
1. app-routing.module.ts (ルート定義の確認)
   ↓
2. Guard実行 (認証・権限チェック)
   ↓
3. Frame Component読み込み
   ↓
4. 対象Page Component読み込み
```

#### 例: `/foc/seko` アクセス時

```typescript
// 1. app-routing.module.ts
{ path: 'foc/seko', component: CompanyFrameComponent, canActivate: [AuthFGuard] }

// 2. AuthFGuard実行
export class AuthFGuard implements CanActivate {
  canActivate(): boolean {
    // 認証チェック処理
  }
}

// 3. CompanyFrameComponent読み込み
export class CompanyFrameComponent implements OnInit {
  ngOnInit() {
    // フレーム初期化処理
  }
}

// 4. SekoComponent読み込み (フレーム内で動的に決定)
export class SekoComponent implements OnInit {
  ngOnInit() {
    // 施行一覧の初期化処理
  }
}
```

### 2.2 フレームコンポーネント内での動的読み込み

```typescript
// CompanyFrameComponent内での処理
ngOnInit() {
  const url = this.router.url;
  
  // URLに基づいて対象コンポーネントを決定
  if (url.includes('/foc/seko')) {
    // SekoComponent を動的読み込み
  } else if (url.includes('/foc/top')) {
    // TopComponent を動的読み込み
  }
  // ...
}
```

## 3. サービス読み込み順序

### 3.1 依存関係に基づく読み込み

```
1. SessionService (基盤サービス)
   ↓
2. HttpClientService (SessionServiceに依存)
   ↓
3. CacheService (HttpClientServiceに依存)
   ↓
4. 各種業務サービス (BaseService, StaffService等)
```

#### 詳細な依存関係

```typescript
// SessionService (最初に読み込まれる)
export class SessionService {
  // ローカルストレージ管理
}

// HttpClientService (SessionServiceに依存)
export class HttpClientService {
  constructor(private sessionSvc: SessionService) {}
}

// CacheService (HttpClientServiceに依存)
export class CacheService {
  constructor(private httpClientSvc: HttpClientService) {}
}

// BaseService (複数サービスに依存)
export class BaseService {
  constructor(
    private httpClientSvc: HttpClientService,
    private cacheSvc: CacheService
  ) {}
}
```

## 4. コンポーネント読み込み詳細フロー

### 4.1 企業向け画面 (`/foc/*`) アクセス時

```
1. app-routing.module.ts
   ↓ ルート: { path: 'foc/seko', component: CompanyFrameComponent, canActivate: [AuthFGuard] }
2. AuthFGuard.canActivate()
   ↓ 認証チェック
3. CompanyFrameComponent
   ↓ フレーム初期化
4. pages/company/frame.component.ts
   ↓ URL解析・コンポーネント決定
5. 対象コンポーネント (例: SekoComponent)
   ↓ ページ固有の初期化
6. HttpClientService
   ↓ API呼び出し
7. データ取得・画面表示
```

### 4.2 顧客向け画面 (`/fuho`, `/hoyo`等) アクセス時

```
1. app-routing.module.ts
   ↓ ルート: { path: 'fuho', component: CustomerFrameComponent }
2. CustomerFrameComponent
   ↓ フレーム初期化 (認証不要)
3. pages/customer/frame.component.ts
   ↓ URL解析・コンポーネント決定
4. 対象コンポーネント (例: HuhoComponent)
   ↓ ページ固有の初期化
5. データ取得・画面表示
```

### 4.3 家族向け画面 (`/soke/*`) アクセス時

```
1. app-routing.module.ts
   ↓ ルート: { path: 'soke/login', component: FamilyFrameComponent }
2. AuthSGuard.canActivate() (認証が必要な場合)
   ↓ 葬家認証チェック
3. FamilyFrameComponent
   ↓ フレーム初期化
4. pages/family/frame.component.ts
   ↓ URL解析・コンポーネント決定
5. 対象コンポーネント (例: SokeLoginComponent)
   ↓ ページ固有の初期化
6. データ取得・画面表示
```

## 5. 具体的なファイル読み込み例

### 例: ユーザーが `/foc/seko` にアクセスした場合

```
1. index.html
2. main.ts
3. app.module.ts
4. app.component.ts
5. app-routing.module.ts (ルート解析)
6. guard/authF.guard.ts (認証チェック)
7. service/session.service.ts (認証情報取得)
8. pages/company/frame.component.ts (フレーム表示)
9. pages/company/seko/seko.component.ts (施行一覧コンポーネント)
10. service/http-client.service.ts (API呼び出し)
11. service/cache.service.ts (キャッシュ確認)
12. seko.component.html (テンプレート表示)
13. seko.component.scss (スタイル適用)
```

## 6. 遅延読み込み (Lazy Loading) について

FOCプロジェクトでは現在、全コンポーネントがapp.module.tsで一括読み込みされています。
パフォーマンス向上のため、将来的には以下のような遅延読み込みの導入が推奨されます：

```typescript
// 遅延読み込みの例 (将来的な改善案)
const routes: Routes = [
  {
    path: 'foc',
    loadChildren: () => import('./pages/company/company.module').then(m => m.CompanyModule)
  },
  {
    path: 'customer',
    loadChildren: () => import('./pages/customer/customer.module').then(m => m.CustomerModule)
  }
];
```

## 7. 読み込み最適化のポイント

### 7.1 現在の課題
- 全コンポーネントの一括読み込みによる初期読み込み時間の増加
- 未使用コンポーネントも含めた全体のバンドルサイズ増加

### 7.2 改善提案
1. **機能別モジュール分割**: Company, Customer, Family別のモジュール作成
2. **遅延読み込み導入**: 必要な時にのみコンポーネントを読み込み
3. **共通コンポーネントの最適化**: 使用頻度の高いコンポーネントの事前読み込み

この読み込み順序を理解することで、デバッグ時の問題特定や、パフォーマンス最適化の検討に役立てることができます。
