class AddUserIdMixin:
    def create(self, validated_data):
        if 'staff' in self.context:
            validated_data['create_user_id'] = self.context['staff'].id
            validated_data['update_user_id'] = self.context['staff'].id
        return super().create(validated_data)

    def update(self, instance, validated_data):
        if 'staff' in self.context:
            validated_data['update_user_id'] = self.context['staff'].id
        return super().update(instance, validated_data)
