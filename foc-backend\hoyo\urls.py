from django.urls import path

from hoyo import views

app_name = 'hoyo'
urlpatterns = [
    path('', views.HoyoList.as_view(), name='hoyo_list'),
    path('<int:pk>/', views.HoyoDetail.as_view(), name='hoyo_detail'),
    path('samples/', views.HoyoSampleList.as_view(), name='hoyo_sample_list'),
    path('samples/<int:pk>/', views.HoyoSampleDetail.as_view(), name='hoyo_sample_detail'),
    path('mail_templates/', views.HoyoMailTemplateList.as_view(), name='hoyo_mail_template_list'),
    path(
        'mail_templates/<int:pk>/',
        views.HoyoMailTemplateDetail.as_view(),
        name='hoyo_mail_template_detail',
    ),
    path('seko/', views.HoyoSekoList.as_view(), name='hoyo_seko_list'),
    path('seko/<int:pk>/', views.HoyoSekoDetail.as_view(), name='hoyo_seko_detail'),
    path('seko/<int:pk>/pdf/', views.HoyoPdf.as_view(), name='hoyo_pdf'),
    path('mails/', views.HoyoMailList.as_view(), name='hoyo_mail_list'),
]
