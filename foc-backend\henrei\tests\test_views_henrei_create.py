from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from henrei.tests.factories import HenreihinKodenFactory, HenreihinKumotsuFactory
from orders.models import EntryDetail
from orders.tests.factories import (
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryFactory,
)
from seko.models import Seko
from seko.tests.factories import SekoFactory
from staffs.tests.factories import StaffFactory
from suppliers.models import Supplier
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class HenreiCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        entry_1 = EntryFactory(seko=self.seko)
        entry_2 = EntryFactory(seko=self.seko)

        self.entry_detail_21 = EntryDetailFactory(entry=entry_2)
        self.entry_detail_22 = EntryDetailFactory(entry=entry_2)
        self.entry_detail_11 = EntryDetailFactory(entry=entry_1)

        koden_1 = EntryDetailKodenFactory(entry_detail=self.entry_detail_21)
        koden_2 = EntryDetailKodenFactory(entry_detail=self.entry_detail_22)
        koden_3 = EntryDetailKodenFactory(entry_detail=self.entry_detail_11)

        self.henrei_koden_1 = HenreihinKodenFactory(detail_koden=koden_1, henrei_order=None)
        self.henrei_koden_2 = HenreihinKodenFactory(detail_koden=koden_2, henrei_order=None)
        self.henrei_koden_3 = HenreihinKodenFactory(detail_koden=koden_3, henrei_order=None)

        kumotsu_1 = EntryDetailKumotsuFactory(entry_detail=self.entry_detail_21)
        kumotsu_2 = EntryDetailKumotsuFactory(entry_detail=self.entry_detail_22)
        kumotsu_3 = EntryDetailKumotsuFactory(entry_detail=self.entry_detail_11)

        self.henrei_kumotsu_1 = HenreihinKumotsuFactory(
            detail_kumotsu=kumotsu_1, henrei_order=None
        )
        self.henrei_kumotsu_2 = HenreihinKumotsuFactory(
            detail_kumotsu=kumotsu_2, henrei_order=None
        )
        self.henrei_kumotsu_3 = HenreihinKumotsuFactory(
            detail_kumotsu=kumotsu_3, henrei_order=None
        )

        self.supplier = SupplierFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'entry_details': [
                self.entry_detail_11.pk,
                self.entry_detail_21.pk,
                self.entry_detail_22.pk,
            ],
            'seko': self.seko.pk,
            'supplier': self.supplier.pk,
        }

    def test_henrei_create_succeed(self) -> None:
        """返礼品発注を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_create'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record['seko'], self.seko.pk)
        self.assertEqual(type(record['supplier']), dict)
        self.assertEqual(record['supplier']['id'], self.supplier.pk)
        self.assertIsNone(record['order_ts'])
        self.assertIsNone(record['order_staff'])
        self.assertEqual(record['order_status'], 1)
        self.assertIsNone(record['order_note'])
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

        order_henrei_id: int = record['id']
        self.assertEqual(len(record['henrei_koden']), 3)
        for record_henrei_koden in record['henrei_koden']:
            self.assertEqual(record_henrei_koden['henrei_order'], order_henrei_id)
            self.assertEqual(record_henrei_koden['order_status'], 1)
        self.assertEqual(len(record['henrei_kumotsu']), 3)
        for record_henrei_kumotsu in record['henrei_kumotsu']:
            self.assertEqual(record_henrei_kumotsu['henrei_order'], order_henrei_id)
            self.assertEqual(record_henrei_kumotsu['order_status'], 1)

    def test_henrei_create_succeed_less_entry_details(self) -> None:
        """返礼品発注を追加する(申込明細を少なく指定)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['entry_details'] = [self.entry_detail_11.pk, self.entry_detail_21.pk]
        response = self.api_client.post(
            reverse('henrei:henrei_create'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertEqual(record['seko'], self.seko.pk)
        self.assertEqual(type(record['supplier']), dict)
        self.assertEqual(record['supplier']['id'], self.supplier.pk)
        self.assertIsNone(record['order_ts'])
        self.assertIsNone(record['order_staff'])
        self.assertEqual(record['order_status'], 1)
        self.assertIsNone(record['order_note'])
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

        order_henrei_id: int = record['id']
        self.assertEqual(len(record['henrei_koden']), 2)
        for record_henrei_koden in record['henrei_koden']:
            self.assertEqual(record_henrei_koden['henrei_order'], order_henrei_id)
            self.assertEqual(record_henrei_koden['order_status'], 1)
        self.assertEqual(len(record['henrei_kumotsu']), 2)
        for record_henrei_kumotsu in record['henrei_kumotsu']:
            self.assertEqual(record_henrei_kumotsu['henrei_order'], order_henrei_id)
            self.assertEqual(record_henrei_kumotsu['order_status'], 1)

    def test_henrei_create_failed_entry_detail_notfound(self) -> None:
        """返礼品香発注追加APIは存在しない申込明細を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_entry_detail: EntryDetail = EntryDetailFactory.build()
        params: Dict = self.basic_params()
        params['entry_details'] = [
            self.entry_detail_11.pk,
            self.entry_detail_21.pk,
            non_saved_entry_detail.pk,
        ]
        response = self.api_client.post(
            reverse('henrei:henrei_create'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('entry_details'))

    def test_henrei_create_failed_seko_notfound(self) -> None:
        """返礼品香発注追加APIは存在しない施行を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_seko: Seko = SekoFactory.build()
        params: Dict = self.basic_params()
        params['seko'] = non_saved_seko.pk
        response = self.api_client.post(
            reverse('henrei:henrei_create'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('seko'))

    def test_henrei_create_failed_supplier_notfound(self) -> None:
        """返礼品香発注追加APIは存在しない発注先を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_supplier: Supplier = SupplierFactory.build()
        params: Dict = self.basic_params()
        params['supplier'] = non_saved_supplier.pk
        response = self.api_client.post(
            reverse('henrei:henrei_create'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('supplier'))

    def test_henrei_crate_failed_without_auth(self) -> None:
        """返礼品発注追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_create'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
