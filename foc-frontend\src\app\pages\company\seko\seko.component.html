
<div class="container">
  <div class="inner">
    <div class="contents">
      <div class="menu_title">
        <i class="address card icon big"></i>
        施行一覧
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(departComboEm, hallComboEm, orderStaffComboEm, sekoStaffComboEm, afStaffComboEm, sekoDateFromEm, sekoDateToEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchSeko()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="form_data.company_id" (selectedItemChange)="companyChange($event, departComboEm, hallComboEm, orderStaffComboEm, sekoStaffComboEm, afStaffComboEm)"></com-dropdown>
          <label>部門</label>
          <com-dropdown #departComboEm [settings]="departCombo" [(selectedValue)]="form_data.seko_department_id" (selectedItemChange)="departChange($event, hallComboEm)"></com-dropdown>
          <label>式場</label>
          <com-dropdown #hallComboEm [settings]="hallCombo" [(selectedValue)]="form_data.hall_id"></com-dropdown>
        </div>
        <div class="line">
          <label>葬家名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.soke_name">
          </div>
          <label>葬家名カナ</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.soke_kana">
          </div>
          <label>故人名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.kojin_name">
          </div>
          <label>故人名カナ</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.kojin_kana">
          </div>
        </div>
        <div class="line">
          <label>喪主名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.moshu_name">
          </div>
          <label>喪主名カナ</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.moshu_kana">
          </div>
          <label>施行日</label>
          <com-calendar #sekoDateFromEm [settings]="seko_date_from_config" id="seko_date_from" [(value)]="form_data.seko_date_from"></com-calendar>
          <label class="plane">～</label>
          <com-calendar #sekoDateToEm [settings]="seko_date_to_config" id="seko_date_to" [(value)]="form_data.seko_date_to"></com-calendar>
        </div>
        <div class="line">
          <label>受注担当者</label>
          <com-dropdown #orderStaffComboEm [settings]="orderStaffCombo" [(selectedValue)]="form_data.order_staff_id"></com-dropdown>
          <label>施行担当者</label>
          <com-dropdown #sekoStaffComboEm [settings]="sekoStaffCombo" [(selectedValue)]="form_data.seko_staff_id"></com-dropdown>
          <label>AF担当者</label>
          <com-dropdown #afStaffComboEm [settings]="afStaffCombo" [(selectedValue)]="form_data.af_staff_id"></com-dropdown>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="seko_list?.length">
          全{{seko_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?seko_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="id">ID</th>
              <th class="mourner_family">葬家名</th>
              <th class="deceased_name">故人名</th>
              <th class="mourner_name">喪主名</th>
              <th class="exec_date">施行日</th>
              <th class="company">葬儀社</th>
              <th class="depart">施行拠点</th>
              <th class="place">式場</th>
              <th class="order_employee">受注担当者</th>
              <th class="exec_employee">施行担当者</th>
              <th class="af_employee">AF担当者</th>
              <th class="agree_date">開設日</th>
              <!--
              <th class="function"></th>
              -->
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let seko of seko_list; index as i">
            <tr (click)="selectData(seko, $event)" *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
              <td class="center aligned id" title="{{seko.id}}">{{seko.id}}</td>
              <td class="mourner_family" title="{{seko.soke_name}}">{{seko.soke_name}}</td>
              <td class="deceased_name" title="{{seko.kojin[0].name}}">{{seko.kojin[0].name}}</td>
              <td class="mourner_name" title="{{seko.moshu.name}}">{{seko.moshu.name}}</td>
              <td class="exec_date center aligned" title="{{seko.seko_date | date: 'yyyy/MM/dd'}}">{{seko.seko_date | date: 'yyyy/MM/dd'}}</td>
              <td class="company" title="{{seko.seko_company_name}}">{{seko.seko_company_name}}</td>
              <td class="depart" title="{{seko.seko_department_name}}">{{seko.seko_department_name}}</td>
              <td class="place" title="{{getPlace(seko)}}">{{getPlace(seko)}}</td>
              <td class="order_employee" title="{{seko.order_staff_name}}">{{seko.order_staff_name}}</td>
              <td class="exec_employee" title="{{seko.seko_staff_name}}">{{seko.seko_staff_name}}</td>
              <td class="af_employee" title="{{seko.af_staff_name}}">{{seko.af_staff_name}}</td>
              <td class="agree_date center aligned" title="{{seko.moshu.agree_ts | date: 'yyyy/MM/dd'}}">{{seko.moshu.agree_ts | date: 'yyyy/MM/dd'}}</td>
              <!--
              <td class="center aligned button">
                <i class="large comment icon circle" title="MSGチェック"></i>
                <i class="large boxes icon" title="法要情報"></i>
                <i class="large envelope icon" title="メール送信"></i>
              </td>
              -->
            </tr>
            </ng-container>
            <tr *ngIf="!seko_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="11">対象データがありません。</td>
              <!--
              <td class="center aligned button">
                <i class="large comment icon circle" title="MSGチェック"></i>
                <i class="large boxes icon" title="法要情報"></i>
                <i class="large envelope icon" title="メール送信"></i>
                <i class="large file alternate icon" title="受付情報"></i>
              </td>
              -->
            </tr>
          </tbody>
        </table>
      </div>
		</div>
  </div>
</div>
