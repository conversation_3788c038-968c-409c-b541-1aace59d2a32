from django.test import RequestFactory, TestCase, override_settings

from utils.requests import request_origin


class RequestOriginTest(TestCase):
    FRONTEND_URL = 'http://example.com/'
    ORIGIN_URL = 'http://origin.dummy.com'

    def setUp(self):
        super().setUp()

        self.request_factory = RequestFactory()

    def test_request_origin(self):
        """Webアプリのroot URL取得"""
        # CORS_ORIGIN_ALLOW_ALLがTrueでHTTP_ORIGINが指定されている場合はHTTP_ORIGINの値そのまま
        with override_settings(FRONTEND_HOSTNAME=self.FRONTEND_URL, CORS_ORIGIN_ALLOW_ALL=True):
            request = self.request_factory.get(
                '/nonexistent', data={}, secure=False, HTTP_ORIGIN=self.ORIGIN_URL
            )
            result: str = request_origin(request)
            self.assertEqual(result, self.ORIGIN_URL)

            # HTTP_ORIGINが指定されていない場合はFRONTEND_HOSTNAMEの値
            request = self.request_factory.get('/nonexistent', data={}, secure=False)
            result: str = request_origin(request)
            self.assertEqual(result, self.FRONTEND_URL)

        # さらにFRONTEND_URLも指定されていない場合はバックエンドのROOT URL
        with override_settings(FRONTEND_HOSTNAME=None, CORS_ORIGIN_ALLOW_ALL=True):
            request = self.request_factory.get('/nonexistent', data={}, secure=False)
            result: str = request_origin(request)
            self.assertEqual(result, request.build_absolute_uri('/'))

        # CORS_ORIGIN_ALLOW_ALLがFalseの場合はFRONTEND_HOSTNAMEを返す(HTTP_ORIGINは影響しない)
        with override_settings(FRONTEND_HOSTNAME=self.FRONTEND_URL, CORS_ORIGIN_ALLOW_ALL=False):
            request = self.request_factory.get(
                '/nonexistent', data={}, secure=False, HTTP_ORIGIN=self.ORIGIN_URL
            )
            result: str = request_origin(request)
            self.assertEqual(result, self.FRONTEND_URL)

            request = self.request_factory.get('/nonexistent', data={}, secure=False)
            result: str = request_origin(request)
            self.assertEqual(result, self.FRONTEND_URL)

        # さらにFRONTEND_URLも指定されていない場合はバックエンドのROOT URL
        with override_settings(FRONTEND_HOSTNAME=None, CORS_ORIGIN_ALLOW_ALL=False):
            request = self.request_factory.get('/nonexistent', data={}, secure=False)
            result: str = request_origin(request)
            self.assertEqual(result, request.build_absolute_uri('/'))
