@import "src/assets/scss/company/setting";
.search_area {
  margin-top: 0!important;
  .line {
    >label {
      min-width: 120px!important;
    }
    >div {
      padding: 9px;
      background-color: #ffffff;
      line-height: 1;
      font-size: 1em;
      min-width: 150px;
      &:last-child {
        width: 100%;
      }
    }
  }
}
.detail {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  .entry-detail {
    width: 58%;
    .service {
      width: 25%;
    }
    .item_unit_price {
      width: 17%;
    }
    .quantity {
      width: 13%;
    }
  }
  .henreihin {
    width: 40%;
    .supplier {
      width: 35%;
    }
    .order_status {
      width: 20%;
    }
  }
  .table_fixed.body {
    max-height: 300px!important;
    tr {
      cursor: default;
      &:hover {
        background: $table-row-light;
      }
    }
  }

}