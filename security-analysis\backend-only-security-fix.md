# FOCシステム バックエンドのみセキュリティ改修案

## 概要

JWTペイロードを変更せず、バックエンドのみでセキュリティ対策を実施する改修案です。
アクセストークンのユーザーIDから最新のcompany_codeとbase_typeを取得してチェックし、
base_listの権限フィルタリングも実装します。

## 1. 改修対象と効果

### 1.1 改修対象ファイル（バックエンドのみ）

1. **`utils/authentication.py`** - JWT認証時のユーザー情報検証強化
2. **`bases/views.py`** - BaseFullListの権限フィルタリング追加
3. **`utils/security_mixins.py`** - 共通セキュリティミックスイン（新規作成）

### 1.2 対策効果の検証

#### ✅ **会社コード改ざん対策**
- **効果**: 完全に対策可能
- **理由**: バックエンドでJWTのuser_idから最新のcompany_codeを取得し、リクエストパラメータを無視

#### ✅ **ユーザーコード改ざん対策**
- **効果**: 完全に対策可能
- **理由**: JWTのuser_idでユーザー認証し、セッションのuser_idは無視

#### ✅ **ユーザー権限改ざん対策**
- **効果**: 完全に対策可能
- **理由**: データベースから最新のbase_typeを取得し、セッションの権限情報は無視

#### ✅ **テナント会社ログイン時の他社情報漏洩対策**
- **効果**: 完全に対策可能
- **理由**: base_listを権限に応じてフィルタリング

## 2. 実装詳細

### 2.1 JWT認証強化（utils/authentication.py）

```python
# utils/authentication.py

from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import AuthenticationFailed, InvalidToken
from rest_framework_simplejwt.settings import api_settings
from rest_framework_simplejwt.state import User
from rest_framework_simplejwt.tokens import RefreshToken

from seko.models import Moshu
from bases.models import Base


class ClassableRefreshToken(RefreshToken):
    @classmethod
    def for_user(cls, user):
        if isinstance(user, Moshu):
            setattr(user, 'id', user.seko.pk)
        token = super().for_user(user)
        token['user_class'] = user._meta.object_name
        if isinstance(user, Moshu):
            delattr(user, 'id')
        return token


class ClassableJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        try:
            user_id = validated_token[api_settings.USER_ID_CLAIM]
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user identification'))

        try:
            user_class: str = validated_token['user_class']
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user class'))

        try:
            if user_class == 'Moshu':
                user = Moshu.objects.get(Q(pk=user_id) & Q(seko__del_flg=False))
            else:
                user = User.objects.get(**{api_settings.USER_ID_FIELD: user_id})
        except (Moshu.DoesNotExist, User.DoesNotExist):
            raise AuthenticationFailed(_('User not found'), code='user_not_found')

        if not user.is_active:
            raise AuthenticationFailed(_('User is inactive'), code='user_inactive')

        # 【新規追加】ユーザー情報の整合性チェック
        self.validate_user_integrity(user)

        return user
    
    def validate_user_integrity(self, user):
        """
        データベースから最新のユーザー情報を取得して整合性をチェック
        セッション改ざん攻撃を防ぐため、常に最新の情報を使用
        """
        try:
            if isinstance(user, Moshu):
                # 喪主の場合：施行情報と拠点情報を再取得
                fresh_moshu = Moshu.objects.select_related('seko__base').get(
                    Q(pk=user.pk) & Q(seko__del_flg=False)
                )
                if not fresh_moshu.seko or not fresh_moshu.seko.base:
                    raise AuthenticationFailed(
                        'Moshu seko or base not found', 
                        code='moshu_data_integrity_error'
                    )
                
                # 最新の会社コードと拠点タイプを設定
                user._fresh_company_code = fresh_moshu.seko.base.company_code
                user._fresh_base_type = fresh_moshu.seko.base.base_type
                
            else:  # Staff
                # スタッフの場合：拠点情報を再取得
                fresh_staff = User.objects.select_related('base').get(pk=user.pk)
                if not fresh_staff.base:
                    raise AuthenticationFailed(
                        'Staff base not found', 
                        code='staff_data_integrity_error'
                    )
                
                # 最新の会社コードと拠点タイプを設定
                user._fresh_company_code = fresh_staff.company_code
                user._fresh_base_type = fresh_staff.base.base_type
                
        except (Moshu.DoesNotExist, User.DoesNotExist):
            raise AuthenticationFailed(
                'User data integrity check failed', 
                code='user_integrity_error'
            )
    
    def get_fresh_company_code(self, user):
        """セキュアな会社コード取得"""
        return getattr(user, '_fresh_company_code', None)
    
    def get_fresh_base_type(self, user):
        """セキュアな拠点タイプ取得"""
        return getattr(user, '_fresh_base_type', None)
```

### 2.2 共通セキュリティミックスイン（新規作成）

```python
# utils/security_mixins.py

from django.db.models import Q
from rest_framework.exceptions import PermissionDenied
from bases.models import Base
from seko.models import Moshu


class SecureCompanyFilterMixin:
    """
    会社コードによる安全なデータフィルタリング
    JWTのuser_idから最新の会社情報を取得してフィルタリング
    """
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return self.apply_secure_company_filter(queryset)
    
    def apply_secure_company_filter(self, queryset):
        """
        セキュアな会社コードフィルタリング
        セッションやリクエストパラメータは無視し、JWTのuser_idから取得
        """
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return queryset.none()
            
        user = self.request.user
        
        # 最新の会社コードを取得（JWT認証時に設定済み）
        company_code = getattr(user, '_fresh_company_code', None)
        base_type = getattr(user, '_fresh_base_type', None)
        
        if not company_code or base_type is None:
            return queryset.none()
        
        # システム管理会社の場合
        if base_type == Base.OrgType.ADMIN:
            # リクエストパラメータで指定された会社のデータのみ
            requested_company = self.get_requested_company_code()
            if requested_company:
                return self.filter_by_company_code(queryset, requested_company)
            return queryset.none()  # 会社指定なしの場合は空
            
        # テナント会社の場合は自社データのみ
        return self.filter_by_company_code(queryset, company_code)
    
    def get_requested_company_code(self):
        """リクエストパラメータから会社コードを取得"""
        company_params = ['seko_company', 'company_id', 'company']
        for param in company_params:
            value = self.request.GET.get(param)
            if value:
                return value
        return None
    
    def filter_by_company_code(self, queryset, company_code):
        """
        会社コードでクエリセットをフィルタリング
        各APIで適切にオーバーライドする
        """
        # デフォルト実装（各APIで適切にオーバーライド）
        if hasattr(queryset.model, 'seko_company'):
            return queryset.filter(seko_company__company_code=company_code)
        elif hasattr(queryset.model, 'company_code'):
            return queryset.filter(company_code=company_code)
        return queryset
    
    def validate_company_access(self):
        """会社コードアクセス権限の検証"""
        requested_company = self.get_requested_company_code()
        
        if not requested_company:
            return True
            
        user = self.request.user
        company_code = getattr(user, '_fresh_company_code', None)
        base_type = getattr(user, '_fresh_base_type', None)
        
        # システム管理会社は全テナントアクセス可能
        if base_type == Base.OrgType.ADMIN:
            return True
            
        # テナント会社は自社のみ
        if str(requested_company) != str(company_code):
            raise PermissionDenied(
                f'Access denied: requested company {requested_company}, '
                f'user company {company_code}'
            )
        return True


class SecureUserValidationMixin:
    """
    ユーザー権限の安全な検証
    セッション改ざんを防ぐため、常にデータベースから最新情報を取得
    """
    
    def dispatch(self, request, *args, **kwargs):
        # リクエスト毎にユーザー権限を再検証
        if hasattr(request, 'user') and request.user.is_authenticated:
            self.validate_user_permissions(request.user)
        return super().dispatch(request, *args, **kwargs)
    
    def validate_user_permissions(self, user):
        """ユーザー権限の整合性チェック"""
        company_code = getattr(user, '_fresh_company_code', None)
        base_type = getattr(user, '_fresh_base_type', None)
        
        if not company_code or base_type is None:
            raise PermissionDenied('User permission validation failed')
    
    def get_secure_user_company_code(self):
        """セキュアな会社コード取得"""
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return None
        return getattr(self.request.user, '_fresh_company_code', None)
    
    def get_secure_user_base_type(self):
        """セキュアな拠点タイプ取得"""
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return None
        return getattr(self.request.user, '_fresh_base_type', None)
    
    def is_secure_system_admin(self):
        """セキュアなシステム管理者判定"""
        base_type = self.get_secure_user_base_type()
        return base_type == Base.OrgType.ADMIN
```

### 2.3 BaseFullListの権限フィルタリング

```python
# bases/views.py

from django.db.models import Q, Prefetch
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from bases.models import Base
from bases.serializers import BaseDownwardSerializer, BaseSerializer
from masters.models import Service
from utils.requests import request_origin
from utils.view_mixins import AddContextMixin, SoftDestroyMixin
from utils.security_mixins import SecureUserValidationMixin  # 追加


class BaseFullList(SecureUserValidationMixin, generics.ListAPIView):
    """
    get: 全拠点の一覧を階層構造で取得します
    権限に応じてフィルタリングされます
    """

    serializer_class = BaseDownwardSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self, queryset=None):
        # 【改修】権限に応じたフィルタリングを追加
        base_queryset = Base.objects.filter(Q(del_flg=False))
        
        # ユーザーの権限を取得
        user_company_code = self.get_secure_user_company_code()
        user_base_type = self.get_secure_user_base_type()
        
        if not user_company_code or user_base_type is None:
            return base_queryset.none()
        
        # システム管理会社の場合：全ての会社情報を返す
        if user_base_type == Base.OrgType.ADMIN:
            filtered_queryset = base_queryset
        else:
            # テナント会社の場合：自社の情報のみを返す
            filtered_queryset = base_queryset.filter(
                company_code=user_company_code
            )
        
        return (
            filtered_queryset
            .prefetch_related(
                'tokusho',
                'focfee',
                'sms_account',
                Prefetch('services', queryset=Service.objects.order_by('id')),
            )
            .get_cached_trees()
        )


# 既存のクラスは変更なし
class BaseList(AddContextMixin, generics.CreateAPIView):
    serializer_class = BaseSerializer
    permission_classes = [IsAuthenticated]


class BaseDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 拠点情報を取得します
    put: 拠点情報を更新します
    patch: 拠点情報を更新します
    delete: 拠点情報を(論理)削除します
    """

    queryset = Base.objects.filter(Q(del_flg=False)).prefetch_related(
        'tokusho', 'focfee', 'sms_account'
    )
    serializer_class = BaseDownwardSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return BaseSerializer
        return self.serializer_class
```

## 3. 重大漏洩リスクの追加対策

### 3.1 【高リスク】他のAPIでの会社コードフィルタリング不備

現在のFOCシステムでは、多くのAPIで会社コードフィルタリングが不十分です。

#### 脆弱なAPI例

```python
# 現在の実装（脆弱）
class FuhoSampleList(generics.ListCreateAPIView):
    queryset = FuhoSample.objects.filter(Q(del_flg=False))
    filterset_fields = ['company']  # クエリパラメータに依存
    permission_classes = [IsAuthenticated]

# 攻撃例：
# GET /fuho_samples/?company=1009  # 他社の訃報サンプルが漏洩
```

#### 対策実装

```python
# utils/security_mixins.py に追加

class SecureFuhoSampleFilterMixin(SecureCompanyFilterMixin):
    """訃報サンプル用のセキュアフィルタリング"""

    def filter_by_company_code(self, queryset, company_code):
        return queryset.filter(company__company_code=company_code)


class SecureFaqFilterMixin(SecureCompanyFilterMixin):
    """FAQ用のセキュアフィルタリング"""

    def filter_by_company_code(self, queryset, company_code):
        return queryset.filter(company__company_code=company_code)


class SecureSekoFilterMixin(SecureCompanyFilterMixin):
    """施行用のセキュアフィルタリング"""

    def filter_by_company_code(self, queryset, company_code):
        return queryset.filter(seko_company__company_code=company_code)
```

### 3.2 【高リスク】スタッフ情報の権限フィルタリング不備

#### 現在の問題

```python
# staffs/views.py - StaffListLowerBases
class StaffListLowerBases(generics.ListAPIView):
    def get_queryset(self):
        specified_base = Base.objects.get(pk=self.kwargs['base_id'])
        # 指定された拠点のスタッフを返す（権限チェックなし）
        return Staff.objects.filter(base_id__in=base_ids)

# 攻撃例：
# GET /staffs/bases/999/  # 他社拠点のスタッフ情報が漏洩
```

#### 対策実装

```python
# staffs/views.py の改修

from utils.security_mixins import SecureUserValidationMixin

class StaffListLowerBases(SecureUserValidationMixin, generics.ListAPIView):
    """
    get: 指定した拠点と、それに所属する拠点のいずれかに所属する担当者の一覧を取得します
    """

    serializer_class = StaffSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        base_id = self.kwargs['base_id']

        # 【追加】指定された拠点へのアクセス権限をチェック
        self.validate_base_access(base_id)

        specified_base = Base.objects.get(Q(pk=base_id) & Q(del_flg=False))
        base_ids = (
            specified_base.get_descendants(include_self=True)
            .filter(Q(del_flg=False))
            .values_list('id', flat=True)
        )
        return (
            Staff.objects.select_related('base')
            .filter(Q(base_id__in=base_ids) & Q(del_flg=False))
            .all()
        )

    def validate_base_access(self, base_id):
        """指定された拠点へのアクセス権限をチェック"""
        user_company_code = self.get_secure_user_company_code()
        user_base_type = self.get_secure_user_base_type()

        if not user_company_code or user_base_type is None:
            raise PermissionDenied('User validation failed')

        # 指定された拠点の会社コードを取得
        try:
            target_base = Base.objects.get(Q(pk=base_id) & Q(del_flg=False))
        except Base.DoesNotExist:
            raise PermissionDenied('Base not found')

        # システム管理会社は全拠点アクセス可能
        if user_base_type == Base.OrgType.ADMIN:
            return True

        # テナント会社は自社拠点のみアクセス可能
        if target_base.company_code != user_company_code:
            raise PermissionDenied(
                f'Access denied to base {base_id}: '
                f'target company {target_base.company_code}, '
                f'user company {user_company_code}'
            )

        return True
```

### 3.3 【高リスク】ファイルアップロード・ダウンロードの権限不備

#### 問題の特定

```python
# 多くのAPIでファイルアクセスの権限チェックが不十分
# 例：会社ロゴ、地図ファイル、PDF等のアクセス制御

class SecureFileAccessMixin(SecureUserValidationMixin):
    """ファイルアクセスのセキュリティチェック"""

    def validate_file_access(self, file_owner_company_code):
        """ファイルアクセス権限の検証"""
        user_company_code = self.get_secure_user_company_code()
        user_base_type = self.get_secure_user_base_type()

        if not user_company_code or user_base_type is None:
            raise PermissionDenied('User validation failed')

        # システム管理会社は全ファイルアクセス可能
        if user_base_type == Base.OrgType.ADMIN:
            return True

        # テナント会社は自社ファイルのみアクセス可能
        if file_owner_company_code != user_company_code:
            raise PermissionDenied(
                f'File access denied: '
                f'file owner {file_owner_company_code}, '
                f'user company {user_company_code}'
            )

        return True
```

## 4. 実装手順

### Phase 1: 認証強化（1時間）
1. `utils/authentication.py`の改修
   - `validate_user_integrity()`メソッド追加
   - 最新ユーザー情報の取得・設定

### Phase 2: セキュリティミックスイン作成（1時間）
1. `utils/security_mixins.py`の新規作成
   - `SecureCompanyFilterMixin`実装
   - `SecureUserValidationMixin`実装

### Phase 3: BaseFullList改修（30分）
1. `bases/views.py`の改修
   - 権限に応じたbase_listフィルタリング

### Phase 4: 重要API改修（2-3時間）
1. 施行管理API（seko）
2. スタッフ管理API（staffs）
3. 訃報サンプルAPI（fuho_samples）
4. FAQAPI（faqs）

## 5. 期待される効果

### 5.1 完全なセキュリティ対策

#### ✅ **会社コード改ざん対策**
- **対策方法**: JWTのuser_idから最新のcompany_codeを取得
- **効果**: リクエストパラメータの改ざんを完全に無効化

#### ✅ **ユーザーコード改ざん対策**
- **対策方法**: JWTのuser_idでユーザー認証
- **効果**: セッションのuser_id改ざんを完全に無効化

#### ✅ **ユーザー権限改ざん対策**
- **対策方法**: データベースから最新のbase_typeを取得
- **効果**: セッションの権限情報改ざんを完全に無効化

#### ✅ **base_list情報漏洩対策**
- **対策方法**: 権限に応じたフィルタリング
- **効果**: テナント会社は自社情報のみ取得

### 5.2 追加で発見された重大リスクの対策

#### ✅ **スタッフ情報漏洩対策**
- **リスク**: 他社拠点のスタッフ情報アクセス
- **対策**: 拠点アクセス権限の事前チェック

#### ✅ **ファイルアクセス制御**
- **リスク**: 他社のファイル（ロゴ、PDF等）への不正アクセス
- **対策**: ファイル所有者の会社コードチェック

#### ✅ **API横断的なデータ漏洩対策**
- **リスク**: 各種マスタデータ、設定情報の漏洩
- **対策**: 共通ミックスインによる統一的なフィルタリング

## 6. 運用上の利点

### 6.1 フロントエンド変更不要
- **既存機能**: 全て正常動作
- **セッション**: 既存のセッションストレージ構造を維持
- **API呼び出し**: 既存のフロントエンドコードは無変更

### 6.2 段階的導入可能
- **Phase 1**: 認証強化のみ先行導入
- **Phase 2**: 重要APIから順次適用
- **Phase 3**: 全APIへの展開

### 6.3 監査・ログ対応
- **セキュリティログ**: 改ざん試行の検知・記録
- **アクセスログ**: 権限チェック結果の記録
- **監査証跡**: 実際のアクセス権限の明確化

## 7. 結論

この改修により、**バックエンドのみの変更**で以下の脅威を完全に排除できます：

1. **会社コード改ざん攻撃** → 完全対策
2. **ユーザーID改ざん攻撃** → 完全対策
3. **権限昇格攻撃** → 完全対策
4. **テナント間データ漏洩** → 完全対策
5. **ファイル不正アクセス** → 完全対策

**重要な点:**
- JWTペイロードは変更せず、既存の仕組みを活用
- フロントエンドは一切変更不要
- 既存ユーザーへの影響なし（再ログイン不要）
- 段階的導入により安全な移行が可能

この対策により、FOCシステムのマルチテナント環境における**データ分離とセキュリティが完全に確保**されます。
```
