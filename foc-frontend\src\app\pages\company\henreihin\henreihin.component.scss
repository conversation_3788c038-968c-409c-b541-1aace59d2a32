
@import "src/assets/scss/company/setting";
.contents {
  min-width: 1360px!important;
  .search_area .ui.checkbox {
    padding: 8px 10px 0;
    label {
      cursor: pointer;
    }
  }
  >.table_fixed tr {
    line-height: 1;
    td {
      >div:not(.checkbox) {
        min-height: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &.center {
          text-align: center;
        }
        &:not(:last-child) {
          min-height: 19px;
          border-bottom: dotted 1px $background-light1;
          margin: 0 -10px 5px;
          padding-left: 10px;
          padding-bottom: 5px;
        }
      }
    }
    .check {
      width: 50px;
      padding-left: 15px;
    }
    .seko_id {
      width: 6%;
    }
    .seko_date {
      width: 7%;
    }
    .department_name {
      width: 12%;
    }
    .hall_name {
      width: 12%;
    }
    .soke_name {
      width: 7%;
    }
    .kojin_name {
      width: 7%;
    }
    .moshu_name {
      width: 7%;
    }
    .hen<PERSON><PERSON>in_price {
      width: 6%;
    }
    .supplier {
      width: 9%;
    }
    .order_status {
      width: 5%;
    }
    .is_cancel {
      width: 6%;
    }
    .operation {
      width: 80px;
    }
  }
  .ui.modal {
    &.supplier tr {
      .select {
        width: 90px
      }
      .supplier_name {
        width: 25%;
      }
      .ui.radio.checkbox {
        padding: 0;
      }
    }
  }
}
