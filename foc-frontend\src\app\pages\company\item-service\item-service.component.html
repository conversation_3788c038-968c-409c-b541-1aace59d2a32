
<div class="container">
  <div class="inner with_footer">
    <div class="contents tiny">
      <div class="menu_title">
        <i class="gifts icon big"></i>
        サービス・商品
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #CompanyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" (selectedItemChange)="companyChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination_base.pages.length > 1">
        <div class="count" *ngIf="base_list?.length">
          全{{base_list?.length}}件中
          {{(pagination_base.current-1)*page_per_count+1}}件
          ～
          {{pagination_base.current===pagination_base.pages.length?base_list?.length:pagination_base.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination_base.current===1" (click)="pageTo(pagination_base, pagination_base.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination_base.pages">
          <a class="item" [class.current]="page===pagination_base.current" (click)="pageTo(pagination_base, page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination_base.current===pagination_base.pages.length" (click)="pageTo(pagination_base, pagination_base.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!base_list?.length">
              <th class="base_type_name">拠点区分</th>
              <th class="base_name">拠点名</th>
              <th class="service">サービス</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body main">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let base of base_list; index as i">
              <tr *ngIf="i >= (pagination_base.current-1)*page_per_count && i < pagination_base.current*page_per_count" >
                <td class="base_type_name" title="{{baseTypeName(base.base_type)}}">{{baseTypeName(base.base_type)}}</td>
                <td class="base_name" title="{{base.base_name}}">{{base.base_name}}</td>
                <td class="service" title="{{servicesName(base.services)}}">{{servicesName(base.services)}}</td>
                <td class="right aligned button operation">
                  <i class="large server icon" title="サービス" (click)="showService(base)" *ngIf="base.base_type===Const.BASE_TYPE_DEPART"></i>
                  <i class="large calendar times outline icon" title="サービス申込期限" (click)="showServiceReceptionTerm(base)" *ngIf="base.base_type===Const.BASE_TYPE_DEPART"></i>
                  <i class="large gift icon" title="商品" (click)="showItemList(base)"></i>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>

      <div class="ui modal huge item list" id="item-list">
        <div class="header ui"><span><i class="gift icon large"></i>商品</span>
          <span class="title" *ngIf="select_base_id">【拠点:<span class="data">{{select_base_name}}</span>】</span>
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="ui attached tabular menu">
            <div class="item" data-tab="chobun" (click)="chanageItemTab(1)"><i class="map icon"></i>弔文</div>
            <div class="item" data-tab="kumotsu" (click)="chanageItemTab(2)"><i class="spa icon"></i>供花・供物</div>
            <div class="item" data-tab="henreihin" (click)="chanageItemTab(3)"><i class="gift icon"></i>返礼品</div>
          </div>
          <div class="ui attached tab segment chobun" data-tab="chobun">
            <div class="table_fixed head">
              <table class="ui celled structured unstackable table">
                <thead>
                  <tr class="center aligned" [class.no-data]="!item_list_chobun?.length">
                    <th class="hinban">品番</th>
                    <th class="name">商品名</th>
                    <th class="item_price">税込価格</th>
                    <th class="tax_pct">税区分</th>
                    <th class="begin_date">運用開始日</th>
                    <th class="end_date">運用終了日</th>
                    <th class="image">画像</th>
                    <th class="fdn_code">FDNコード</th>
                    <th class="display_num">表示順</th>
                    <th class="operation"></th>
                  </tr>
                </thead>
              </table>
            </div>
            <div class="table_fixed body">
              <table class="ui celled structured unstackable table">
                <tbody>
                  <ng-container *ngFor="let item of item_list_chobun">
                    <tr (click)="showItemEdit($event, item)">
                      <td class="hinban" title="{{item.hinban}}">{{item.hinban}}</td>
                      <td class="name" title="{{item.name}}">{{item.name}}</td>
                      <td class="right aligned item_price" title="¥{{item.item_price | number}}">¥{{item.item_price | number}}</td>
                      <td class="center aligned tax_pct" title="{{item.tax.tax_pct}}%">{{item.tax.tax_pct}}%</td>
                      <td class="center aligned begin_date" title="{{item.begin_date | date: 'yyyy/MM/dd'}}">{{item.begin_date | date: 'yyyy/MM/dd'}}</td>
                      <td class="center aligned end_date" title="{{item.end_date | date: 'yyyy/MM/dd'}}">{{item.end_date | date: 'yyyy/MM/dd'}}</td>
                      <td class="center aligned image data"><div><img [class.enabled]="!!item.image_can_load" src="{{item.image_file}}" (error)="imageLoadError(item)" (load)="imageLoaded(item)"></div></td>
                      <td class="fdn_code" title="{{item.fdn_code}}">{{item.fdn_code}}</td>
                      <td class="center aligned display_num" title="{{item.display_num}}">{{item.display_num}}</td>
                      <td class="center aligned button operation">
                        <i class="large trash alternate icon" title="削除" (click)="deleteItem(item)"></i>
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
            <div class="table_fixed body">
              <table class="ui celled structured unstackable table">
                <tbody>
                  <tr title="新規追加" (click)="showItemEdit($event, null, Const.SERVICE_ID_CHOBUN)">
                    <td colspan="7" class="center aligned"><i class="large add icon"></i></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="ui attached tab segment kumotsu" data-tab="kumotsu">
            <div class="table_fixed head">
              <table class="ui celled structured unstackable table">
                <thead>
                  <tr class="center aligned" [class.no-data]="!item_list_kumotsu?.length">
                    <th class="hinban">品番</th>
                    <th class="name">商品名</th>
                    <th class="item_price">税込価格</th>
                    <th class="tax_pct">税区分</th>
                    <th class="begin_date">運用開始日</th>
                    <th class="end_date">運用終了日</th>
                    <th class="image">画像</th>
                    <th class="supplier">発注先</th>
                    <th class="fdn_code">FDNコード</th>
                    <th class="display_num">表示順</th>
                    <th class="operation2"></th>
                  </tr>
                </thead>
              </table>
            </div>
            <div class="table_fixed body">
              <table class="ui celled structured unstackable table">
                <tbody>
                  <ng-container *ngFor="let item of item_list_kumotsu">
                    <tr (click)="showItemEdit($event, item)">
                      <td class="hinban" title="{{item.hinban}}">{{item.hinban}}</td>
                      <td class="name" title="{{item.name}}">{{item.name}}</td>
                      <td class="right aligned item_price" title="¥{{item.item_price | number}}">¥{{item.item_price | number}}</td>
                      <td class="center aligned tax_pct" title="{{item.tax.tax_pct}}%">{{item.tax.tax_pct}}%</td>
                      <td class="center aligned begin_date" title="{{item.begin_date | date: 'yyyy/MM/dd'}}">{{item.begin_date | date: 'yyyy/MM/dd'}}</td>
                      <td class="center aligned end_date" title="{{item.end_date | date: 'yyyy/MM/dd'}}">{{item.end_date | date: 'yyyy/MM/dd'}}</td>
                      <td class="center aligned image data"><div><img [class.enabled]="!!item.image_can_load" src="{{item.image_file}}" (error)="imageLoadError(item)" (load)="imageLoaded(item)"></div></td>
                      <td class="center aligned supplier" title="{{item.item_suppliers.length}}">{{item.item_suppliers.length}}</td>
                      <td class="fdn_code" title="{{item.fdn_code}}">{{item.fdn_code}}</td>
                      <td class="center aligned display_num" title="{{item.display_num}}">{{item.display_num}}</td>
                      <td class="center aligned button operation operation2">
                        <i class="large hands helping icon" title="発注先" (click)="showItemSupplier(item)"></i>
                        <i class="large trash alternate icon" title="削除" (click)="deleteItem(item)"></i>
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
            <div class="table_fixed body">
              <table class="ui celled structured unstackable table">
                <tbody>
                  <tr title="新規追加" (click)="showItemEdit($event, null, Const.SERVICE_ID_KUMOTSU)">
                    <td colspan="9" class="center aligned"><i class="large add icon"></i></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="ui attached tab segment henreihin" data-tab="henreihin">
            <div class="table_fixed head">
              <table class="ui celled structured unstackable table">
                <thead>
                  <tr class="center aligned" [class.no-data]="!item_list_henreihin?.length">
                    <th class="hinban">品番</th>
                    <th class="name">商品名</th>
                    <th class="item_price">税込価格</th>
                    <th class="tax_pct">税区分</th>
                    <th class="begin_date">運用開始日</th>
                    <th class="end_date">運用終了日</th>
                    <th class="image">画像</th>
                    <th class="supplier">発注先</th>
                    <th class="fdn_code">FDNコード</th>
                    <th class="display_num">表示順</th>
                    <th class="operation2"></th>
                  </tr>
                </thead>
              </table>
            </div>
            <div class="table_fixed body">
              <table class="ui celled structured unstackable table">
                <tbody>
                  <ng-container *ngFor="let item of item_list_henreihin">
                    <tr (click)="showItemEdit($event, item)">
                      <td class="hinban" title="{{item.hinban}}">{{item.hinban}}</td>
                      <td class="name" title="{{item.name}}">{{item.name}}</td>
                      <td class="right aligned item_price" title="¥{{item.item_price | number}}">¥{{item.item_price | number}}</td>
                      <td class="center aligned tax_pct" title="{{item.tax.tax_pct}}%">{{item.tax.tax_pct}}%</td>
                      <td class="center aligned begin_date" title="{{item.begin_date | date: 'yyyy/MM/dd'}}">{{item.begin_date | date: 'yyyy/MM/dd'}}</td>
                      <td class="center aligned end_date" title="{{item.end_date | date: 'yyyy/MM/dd'}}">{{item.end_date | date: 'yyyy/MM/dd'}}</td>
                      <td class="center aligned image data"><div><img [class.enabled]="!!item.image_can_load" src="{{item.image_file}}" (error)="imageLoadError(item)" (load)="imageLoaded(item)"></div></td>
                      <td class="center aligned supplier" title="{{item.suppliers.length}}">{{item.suppliers.length}}</td>
                      <td class="fdn_code" title="{{item.fdn_code}}">{{item.fdn_code}}</td>
                      <td class="center aligned display_num" title="{{item.display_num}}">{{item.display_num}}</td>
                      <td class="center aligned button operation operation2">
                        <i class="large hands helping icon" title="発注先" (click)="showItemSupplier(item)"></i>
                        <i class="large trash alternate icon" title="削除" (click)="deleteItem(item)"></i>
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
            <div class="table_fixed body">
              <table class="ui celled structured unstackable table">
                <tbody>
                  <tr title="新規追加" (click)="showItemEdit($event, null, Const.SERVICE_ID_HENREIHIN)">
                    <td colspan="9" class="center aligned"><i class="large add icon"></i></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="exportItem()">
            <i class="download icon"></i>CSV出力
          </button>
          <button class="ui labeled icon button mini" (click)="importItem()">
            <i class="upload icon"></i>CSVインポート
          </button>
          <button class="ui labeled icon button mini remark" (click)="uploadItemImage()">
            <i class="upload icon"></i><div>画像アップロード</div><div class="under">(JPEGのみ/1Mまで)</div>
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
      <div class="ui modal small item edit" id="item-edit">
        <div class="header ui"><span><i class="gift icon large"></i>商品{{item_edit_type===1?'登録':'編集'}}</span>
          <span class="title" *ngIf="select_base_id">【拠点:<span class="data">{{select_base_name}}</span>】</span>
        </div>
        <div class="messageArea">
          <div class="ui message {{message_sub?.type}}" [class.visible]="!!message_sub" [class.hidden]="!message_sub" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message_sub?.type==='info'?'info circle':message_sub?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message_sub?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message_sub?.msg">
              {{message_sub?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="item_edit">
            <div class="line">
              <label class="required">品番</label>
              <div class="ui input">
                <input type="tel" autocomplete="off" id="item_hinban" [(ngModel)]="item_edit.hinban">
              </div>
              <label class="required">商品名</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="item_name" [(ngModel)]="item_edit.name">
              </div>
            </div>
            <div class="line">
              <label class="required">税込価格</label>
              <div class="ui input">
                <input type="number" min="0" autocomplete="off" [(ngModel)]="item_edit.item_price">
              </div>
              <label class="required">税区分</label>
              <com-dropdown #comTaxEm class="mini" [settings]="taxCombo" [(selectedValue)]="item_edit.tax.id" *ngIf="item_edit.tax"></com-dropdown>
              <label class="plane">%</label>
            </div>
            <div class="line">
              <label class="required">適用開始日</label>
              <com-calendar #dateBeginEm [settings]="calendarOptionDate" [(value)]="item_edit.begin_date"></com-calendar>
              <label class="required">適用終了日</label>
              <com-calendar #dateEndEm [settings]="calendarOptionDate" [(value)]="item_edit.end_date"></com-calendar>
            </div>
            <div class="line">
              <label>FDNコード</label>
              <div class="ui input">
                <input type="tel" autocomplete="off" id="fdn_code" [(ngModel)]="item_edit.fdn_code">
              </div>
              <label class="required">表示順</label>
              <div class="ui input tiny">
                <input type="number" min="1" [(ngModel)]="item_edit.display_num">
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveItem()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeItemEdit()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal item supplier" id="item-supplier">
        <div class="header ui"><span><i class="hands helping icon large"></i>発注先</span>
          <span class="title">【{{select_service_name}}:<span class="data">{{select_item_name}}</span>】</span>
        </div>
        <div class="messageArea">
          <div class="ui message {{message_sub?.type}}" [class.visible]="!!message_sub" [class.hidden]="!message_sub" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message_sub?.type==='info'?'info circle':message_sub?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message_sub?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message_sub?.msg">
              {{message_sub?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!supplier_list?.length">
                  <th class="select">選択</th>
                  <th class="default">デフォルト</th>
                  <th class="supplier_name">発注先名</th>
                  <th class="supplier_address">住所</th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngFor="let supplier of supplier_list">
                  <tr>
                    <td class="center aligned select" (click)="selectSupplier($event, supplier)">
                      <div class="ui checkbox">
                        <input type="checkbox" [(ngModel)]="supplier.selected" [disabled]="supplier.default">
                        <label></label>
                      </div>
                    </td>
                    <td class="center aligned default" (click)="selectSupplierDefault($event, supplier)">
                      <div class="ui radio checkbox">
                        <input type="radio" name="supplier_default" [checked]="supplier.default">
                        <label></label>
                      </div>
                    </td>
                    <td class="supplier_name" title="{{supplier.name}}" (click)="selectSupplier($event, supplier)">{{supplier.name}}</td>
                    <td class="supplier_address" title="{{supplier.address}}" (click)="selectSupplier($event, supplier)">{{supplier.address}}</td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveItemSupplier()" [disabled]="!supplier_list?.length">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
      <div class="ui modal tiny service" id="service-edit">
        <div class="header ui"><span><i class="server icon large"></i>サービス選択</span>
          <span class="title" *ngIf="select_base_id">【部門:<span class="data">{{select_base_name}}</span>】</span>
          <span class="title" *ngIf="!select_base_id">【初期サービス一括登録】</span>
        </div>

        <div class="content">
          <div class="item" *ngFor="let service of service_list" [class.selected]="service.selected" (click)="selectService(service)">
            <i class="icon huge {{service.icon}}"></i>
            <div class="subtitle">{{service.name}}</div>
            <div class="ui checkbox">
              <input type="checkbox"  [(ngModel)]="service.selected">
              <label></label>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveService()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal small service-limit" id="service-limit-edit">
        <div class="header ui"><span><i class="calendar times outline icon large"></i>サービス申込期限</span>
          <span class="title" *ngIf="select_base_id">【部門:<span class="data">{{select_base_name}}</span>】</span>
          <span class="title" *ngIf="!select_base_id">【サービス申込期限一括更新】</span>
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <label class="plane schedule">基準区分</label>
              <label class="plane limit_hour">時間</label>
              <label class="plane unit">単位</label>
              <label class="plane limit_time">締切時刻</label>
            </div>
            <div class="line" *ngFor="let service_reception_term of service_reception_term_edit">
              <label class="required">{{service_reception_term.name}}</label>
              <com-dropdown class="mini" [settings]="scheduleCombo" [(selectedValue)]="service_reception_term.schedule"></com-dropdown>
              <label class="plane">の</label>
              <div class="ui input tiny">
                <input type="number" autocomplete="off" id="limit_hour" [(ngModel)]="service_reception_term.limit_hour">
              </div>
              <com-dropdown class="tiny divided" [settings]="unitCombo" [(selectedValue)]="service_reception_term.unit"></com-dropdown>
              <label class="plane">前</label>
              <com-dropdown class="tiny" [settings]="limitTimeCombo" [disabled]="service_reception_term.unit===2" [(selectedValue)]="service_reception_term.limit_time"></com-dropdown>
              <label class="plane">時</label>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveServiceReceptionTerm()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
      <input type="file" #fileUpload id="fileUpload" name="fileUpload" style="display:none;" />

    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" [disabled]="!canInitServices()" (click)="showService(null)">
      <i class="layer group icon"></i>初期サービス一括登録
    </button>
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
