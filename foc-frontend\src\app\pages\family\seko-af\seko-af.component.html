
<div class="container">
  <div class="inner" *ngIf="!is_loading">
    <div class="contents">
      <h2>ご相談窓口</h2>
      <div class="description">当社へご相談ご希望のものを下記より選択し、保存してください。改めて担当よりご連絡致します。</div>
      <ng-container *ngFor="let af_group of af_group_list">
        <div class="af-group">{{af_group.name}}</div>
        <div class="af-check">
          <ng-container *ngFor="let after_follow of af_group.after_follows">
            <div class="ui checkbox">
              <input type="checkbox" [(ngModel)]="after_follow.selected">
              <label>{{after_follow.name}}</label>
            </div>
          </ng-container>
        </div>
      </ng-container>
      <div class="input-area">
        <div class="line">
          <div class="label required">ご希望の連絡方法</div>
          <div class="input af_contact">
            <ng-container *ngFor="let af_contact of af_contact_list">
              <div class="ui radio checkbox">
                <input type="radio" name="af_contact" [value]="af_contact.id" [(ngModel)]="af_contact_checked_id" [checked]="af_contact_checked_id===af_contact.id">
                <label>{{af_contact.contact_way}}</label>
              </div>
            </ng-container>
          </div>
        </div>
        <div class="line">
          <div class="label">備考</div>
          <div class="input">
            <textarea rows="3" [(ngModel)]="seko_af_edit.note" placeholder="事前にお伝えたいことがございましたらご入力ください。"></textarea>
          </div>
        </div>
      </div>
    </div>
    <div class="button-area">
      <a class="button pink" [class.disabled]="haveActivity()" (click)="saveData()">保存</a>
    </div>
    <div class="ui modal confirm" id="message-popup">
      <div class="content red">
        {{message}}
      </div>
      <div class="button-area">
        <a class="button grey" (click)="closePopup()">閉じる</a>
      </div>
    </div>
  </div>
</div>
