import factory
import faker
from django.utils import timezone
from factory.django import DjangoModelFactory, FileField, ImageField
from factory.fuzzy import FuzzyInteger

from advertises.models import Advertise
from bases.tests.factories import BaseFactory

tz = timezone.get_current_timezone()
fake_provider = faker.Faker('ja_JP')


class AdvertiseFactory(DjangoModelFactory):
    class Meta:
        model = Advertise

    id = factory.Sequence(lambda n: n)
    company = factory.SubFactory(BaseFactory)
    banner_file = ImageField()
    url = factory.Faker('paragraph', locale='ja_JP')
    link_file = FileField(filename='dummy_link_file.pdf', data=fake_provider.binary())
    begin_date = factory.Faker('past_date', locale='ja_JP')
    end_date = factory.Faker('future_date', locale='ja_JP')
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = FuzzyInteger(1)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = FuzzyInteger(1)
