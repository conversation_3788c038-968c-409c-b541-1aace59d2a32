from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory
from staffs.tests.factories import StaffFactory
from suppliers.models import Supplier
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class SupplierDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.supplier = SupplierFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_supplier_detail_succeed(self) -> None:
        """発注先詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('suppliers:supplier_detail', kwargs={'pk': self.supplier.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('id'), self.supplier.id)

    def test_supplier_detail_failed_with_notfound(self) -> None:
        """発注先詳細は無効になった発注先を返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.supplier.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('suppliers:supplier_detail', kwargs={'pk': self.supplier.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_supplier_detail_failed_without_auth(self) -> None:
        """発注先詳細取得APIはAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('suppliers:supplier_detail', kwargs={'pk': self.supplier.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SupplierUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company = BaseFactory(base_type=Base.OrgType.COMPANY)
        supplier_department = BaseFactory(base_type=Base.OrgType.DEPARTMENT, parent=self.company)
        self.supplier = SupplierFactory(company=self.company)

        comnapy2 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.new_supplier_data = SupplierFactory.build(company=comnapy2)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=supplier_department)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.new_supplier_data.company.id,
            'name': self.new_supplier_data.name,
            'tel': self.new_supplier_data.tel,
            'fax': self.new_supplier_data.fax,
            'zip_code': self.new_supplier_data.zip_code,
            'address': self.new_supplier_data.address,
            'del_flg': self.new_supplier_data.del_flg,
        }

    def test_supplier_update_succeed(self) -> None:
        """発注先を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('suppliers:supplier_detail', kwargs={'pk': self.supplier.id}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        for attr, value in params.items():
            self.assertEqual(value, record[attr])
        self.assertEqual(record.get('create_user_id'), self.supplier.create_user_id)
        self.assertEqual(record.get('update_user_id'), self.staff.id)

    def test_supplier_update_failed_without_auth(self) -> None:
        """発注先更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('suppliers:supplier_detail', kwargs={'pk': self.supplier.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class SupplierDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company = BaseFactory(base_type=Base.OrgType.COMPANY)
        supplier_department = BaseFactory(base_type=Base.OrgType.DEPARTMENT, parent=self.company)

        self.supplier = SupplierFactory(company=self.company)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=supplier_department)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_supplier_delete_succeed(self) -> None:
        """発注先を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('suppliers:supplier_detail', kwargs={'pk': self.supplier.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 発注先のdel_flgがTrueになる
        db_supplier: Supplier = Supplier.objects.get(pk=self.supplier.id)
        self.assertTrue(db_supplier.del_flg)
        self.assertEqual(db_supplier.create_user_id, self.supplier.create_user_id)
        self.assertEqual(db_supplier.update_user_id, self.staff.id)

    def test_supplier_delete_failed_without_auth(self) -> None:
        """発注先削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('suppliers:supplier_detail', kwargs={'pk': self.supplier.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_supplier_delete_failed_by_notfound(self) -> None:
        """発注先削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_supplier: Supplier = SupplierFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('suppliers:supplier_detail', kwargs={'pk': non_saved_supplier.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_supplier_delete_failed_by_already_deleted(self) -> None:
        """発注先削除APIが論理削除済みの発注先を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.supplier.del_flg = True
        self.supplier.save()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('suppliers:supplier_detail', kwargs={'pk': self.supplier.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
