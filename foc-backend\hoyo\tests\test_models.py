from datetime import date

from dateutil.relativedelta import relativedelta
from django.test import TestCase

from hoyo.models import Hoyo, HoyoMailTemplate, HoyoSample, HoyoSeko
from hoyo.tests.factories import (
    HoyoFactory,
    HoyoMailTemplateFactory,
    HoyoSampleFactory,
    HoyoSekoFactory,
)
from masters.models import TermUnitType
from seko.models import Seko
from seko.tests.factories import MoshuFactory, SekoFactory


class HoyoModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.hoyo: Hoyo = HoyoFactory()

    def test_hoyo_disable(self) -> None:
        """法要を無効化(論理削除)する"""
        self.hoyo.disable()
        self.assertTrue(self.hoyo.del_flg)

    def elapsed_timedelta(self) -> None:
        """法要のelapsed_timeとunitを合成したrelativedeltaを返す"""
        hoyo_1: Hoyo = HoyoFactory(elapsed_time=3, unit=TermUnitType.DAYS)
        self.assertEqual(hoyo_1.elapsed_timedelta, relativedelta(days=3))
        hoyo_2: Hoyo = HoyoFactory(elapsed_time=4, unit=TermUnitType.MONTHS)
        self.assertEqual(hoyo_2.elapsed_timedelta, relativedelta(months=4))
        hoyo_3: Hoyo = HoyoFactory(elapsed_time=2, unit=TermUnitType.YEARS)
        self.assertEqual(hoyo_3.elapsed_timedelta, relativedelta(years=2))


class HoyoSampleModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.hoyo_sample: HoyoSample = HoyoSampleFactory()

    def test_hoyo_sample_disable(self) -> None:
        """法要案内文サンプルを無効化(論理削除)する"""
        self.hoyo_sample.disable()
        self.assertTrue(self.hoyo_sample.del_flg)


class HoyoMailTemplateModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.hoyo_mail_template: HoyoMailTemplate = HoyoMailTemplateFactory()

    def test_hoyo_mail_template_disable(self) -> None:
        """法要メールテンプレートを無効化(論理削除)する"""
        self.hoyo_mail_template.disable()
        self.assertTrue(self.hoyo_mail_template.del_flg)


class HoyoSekoModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.hoyo_seko: HoyoSeko = HoyoSekoFactory()

    def test_hoyo_seko_disable(self) -> None:
        """法要施行を無効化(論理削除)する"""
        self.hoyo_seko.disable()
        self.assertTrue(self.hoyo_seko.del_flg)

    def test_generate_from_seko(self) -> None:
        """施行(+喪主)と法要から法要施行インスタンスを生成する"""
        seko: Seko = SekoFactory(death_date=date(2021, 2, 22))
        MoshuFactory(seko=seko)

        with self.assertRaises(ValueError):
            HoyoSeko.generate_from_seko(seko=seko)

        hoyo_1: Hoyo = HoyoFactory(
            company=seko.seko_company,
            style=seko.seko_style.hoyo_style,
            elapsed_time=3,
            unit=TermUnitType.DAYS,
        )
        HoyoFactory(
            company=seko.seko_company,
            # style=seko.seko_style.hoyo_style,
            elapsed_time=1,
            unit=TermUnitType.MONTHS,
        )
        hoyo_2: Hoyo = HoyoFactory(
            company=seko.seko_company,
            style=seko.seko_style.hoyo_style,
            elapsed_time=4,
            unit=TermUnitType.MONTHS,
        )
        hoyo_3: Hoyo = HoyoFactory(
            company=seko.seko_company,
            style=seko.seko_style.hoyo_style,
            elapsed_time=2,
            unit=TermUnitType.YEARS,
        )

        hoyoseko_list = HoyoSeko.generate_from_seko(seko)
        self.assertEqual(len(hoyoseko_list), 3)
        for (hoyoseko, hoyo) in zip(hoyoseko_list, [hoyo_1, hoyo_2, hoyo_3]):
            self.assertEqual(hoyoseko.seko, seko)
            self.assertEqual(hoyoseko.hoyo, hoyo)
            self.assertEqual(hoyoseko.hoyo_name, hoyo.name)
            if hoyo == hoyo_1:
                self.assertEqual(hoyoseko.hoyo_planned_date, date(2021, 2, 25))
            elif hoyo == hoyo_2:
                self.assertEqual(hoyoseko.hoyo_planned_date, date(2021, 6, 22))
            elif hoyo == hoyo_3:
                self.assertEqual(hoyoseko.hoyo_planned_date, date(2023, 2, 22))
            self.assertIsNone(hoyoseko.hoyo_activity_date)
            self.assertIsNone(hoyoseko.begin_time)
            self.assertIsNone(hoyoseko.end_time)
            self.assertIsNone(hoyoseko.hall)
            self.assertTrue(hoyoseko.dine_flg)
            self.assertEqual(hoyoseko.seshu_name, seko.moshu.name)
            self.assertEqual(
                hoyoseko.kojin_seshu_relationship, seko.moshu.kojin_moshu_relationship
            )
            self.assertEqual(hoyoseko.zip_code, seko.moshu.zip_code)
            self.assertEqual(hoyoseko.prefecture, seko.moshu.prefecture)
            self.assertEqual(hoyoseko.address_1, seko.moshu.address_1)
            self.assertEqual(hoyoseko.address_2, seko.moshu.address_2)
            self.assertEqual(hoyoseko.address_3, seko.moshu.address_3)
            self.assertEqual(hoyoseko.tel, seko.moshu.tel)
            self.assertIsNone(hoyoseko.hoyo_sentence)
            self.assertIsNone(hoyoseko.shishiki_name)
            self.assertEqual(hoyoseko.reply_limit_date, date(2999, 12, 31))
            self.assertEqual(hoyoseko.hoyo_site_end_date, date(2999, 12, 31))
            self.assertIsNone(hoyoseko.note)
            self.assertIsNone(hoyoseko.staff)
            self.assertFalse(hoyoseko.del_flg)
