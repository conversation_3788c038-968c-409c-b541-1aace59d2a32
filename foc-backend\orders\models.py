from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, F, Integer<PERSON>ield, Q, Sum, Value, When
from django.db.models.functions import Coalesce
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from items.models import Item
from masters.models import ChobunD<PERSON>hiMaster, PaymentType
from seko.models import Seko
from staffs.models import Staff
from suppliers.models import Supplier


class Entry(models.Model):
    seko = models.ForeignKey(Seko, models.CASCADE, verbose_name=_('seko'), related_name='entries')
    payment = models.ForeignKey(
        PaymentType, models.PROTECT, verbose_name=_('payment'), blank=True, null=True
    )
    entry_name = models.TextField(_('name'))
    entry_name_kana = models.TextField(_('kana'))
    entry_zip_code = models.CharField(_('zipcode'), max_length=7)
    entry_prefecture = models.TextField(_('prefecture'))
    entry_address_1 = models.TextField(_('address1'))
    entry_address_2 = models.TextField(_('address2'))
    entry_address_3 = models.TextField(_('address3'), blank=True, null=True)
    entry_tel = models.CharField(_('tel no'), max_length=15)
    entry_mail_address = models.TextField(_('mail address'))
    entry_ts = models.DateTimeField(_('entried at'), auto_now_add=True)
    cancel_ts = models.DateTimeField(_('cancelled at'), blank=True, null=True)
    receipt_count = models.IntegerField(_('receipt count'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_entry'

    def increase_receipt_count(self):
        self.receipt_count += 1
        self.save()
        return self.receipt_count

    @classmethod
    def get_receipt_data(cls, pk):
        qs = Entry.objects
        qs = qs.filter(
            pk=pk,
            cancel_ts=None,
        )

        qs = qs.annotate(
            calc_price=Sum(F('details__item_unit_price') * F('details__quantity')),
            calc_tax=Sum(F('details__item_tax') + F('details__tax_adjust')),
            calc_price_koden=Sum('details__koden__koden_commission'),
            calc_tax_koden=Sum(
                F('details__koden__koden_commission_tax') + F('details__koden__tax_adjust')
            ),
        )

        result = qs.aggregate(
            price_8=Coalesce(
                Sum(
                    Case(
                        When(
                            Q(details__item_tax_pct=8) & Q(details__item__service__in=[10, 20]),
                            then='calc_price',
                        ),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
            tax_8=Coalesce(
                Sum(
                    Case(
                        When(
                            Q(details__item_tax_pct=8) & Q(details__item__service__in=[10, 20]),
                            then='calc_tax',
                        ),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
            price_10=Coalesce(
                Sum(
                    Case(
                        When(
                            Q(details__item_tax_pct=10) & Q(details__item__service__in=[10, 20]),
                            then='calc_price',
                        ),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
            tax_10=Coalesce(
                Sum(
                    Case(
                        When(
                            Q(details__item_tax_pct=10) & Q(details__item__service__in=[10, 20]),
                            then='calc_tax',
                        ),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
            price_koden=Coalesce(
                Sum(
                    Case(
                        When(details__item__service=30, then='calc_price_koden'),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
            tax_koden=Coalesce(
                Sum(
                    Case(
                        When(details__item__service=30, then='calc_tax_koden'),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
        )

        return result


class EntryDetail(models.Model):
    entry = models.ForeignKey(
        Entry, models.CASCADE, verbose_name=_('entry'), related_name='details'
    )
    item = models.ForeignKey(Item, models.PROTECT, verbose_name=_('item'))
    item_hinban = models.CharField(_('item hinban'), max_length=20, blank=True, null=True)
    item_name = models.TextField(_('item name'))
    item_unit_price = models.IntegerField(_('unit price'))
    item_tax = models.IntegerField(_('tax'))
    item_tax_pct = models.IntegerField(_('tax percentage'))
    keigen_flg = models.BooleanField(_('tax reduced'))
    quantity = models.IntegerField(_('quantity'))
    tax_adjust = models.IntegerField(_('tax adjusted'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_entry_detail'


class FdnCommonListManager(models.Manager):
    def get_queryset(self, company_id):
        return (
            super()
            .get_queryset()
            .select_related(
                'entry_detail',
                'entry_detail__entry',
                'entry_detail__item',
                'entry_detail__item__service',
                'entry_detail__entry__seko',
                'entry_detail__entry__seko__moshu',
                'entry_detail__entry__seko__kojin',
            )
            .filter(
                Q(entry_detail__entry__seko__seko_company=company_id)
                & Q(entry_detail__entry__seko__del_flg=False)
                & Q(entry_detail__entry__seko__kojin__kojin_num=1)
            )
            .annotate(
                seko_id=F('entry_detail__entry__seko_id'),
                term=F('entry_detail__entry__updated_at'),
                fdn_seko_code=F('entry_detail__entry__seko__fdn_code'),
                soke_name=F('entry_detail__entry__seko__soke_name'),
                kojin_name=F('entry_detail__entry__seko__kojin__name'),
                kojin_birth_date=F('entry_detail__entry__seko__kojin__birth_date'),
                kojin_death_date=F('entry_detail__entry__seko__kojin__death_date'),
                moshu_name=F('entry_detail__entry__seko__moshu__name'),
                moshu_tel=F('entry_detail__entry__seko__moshu__tel'),
                moshu_mobile=F('entry_detail__entry__seko__moshu__mobile_num'),
                entry_id=F('entry_detail__entry'),
                entry_name=F('entry_detail__entry__entry_name'),
                entry_name_kana=F('entry_detail__entry__entry_name_kana'),
                entry_zip_code=F('entry_detail__entry__entry_zip_code'),
                entry_prefecture=F('entry_detail__entry__entry_prefecture'),
                entry_address_1=F('entry_detail__entry__entry_address_1'),
                entry_address_2=F('entry_detail__entry__entry_address_2'),
                entry_address_3=F('entry_detail__entry__entry_address_3'),
                entry_tel=F('entry_detail__entry__entry_tel'),
                entry_mail_address=F('entry_detail__entry__entry_mail_address'),
                entry_ts=F('entry_detail__entry__entry_ts'),
                cancel_ts=F('entry_detail__entry__cancel_ts'),
                service_id=F('entry_detail__item__service'),
                service_name=F('entry_detail__item__service__name'),
                item_id=F('entry_detail__item'),
                fdn_item_code=F('entry_detail__item__fdn_code'),
                item_hinban=F('entry_detail__item_hinban'),
                item_name=F('entry_detail__item_name'),
                item_unit_price=F('entry_detail__item_unit_price'),
                quantity=F('entry_detail__quantity'),
                item_tax=F('entry_detail__item_tax'),
                item_tax_adjust=F('entry_detail__tax_adjust'),
                item_tax_pct=F('entry_detail__item_tax_pct'),
                keigen_flg=F('entry_detail__keigen_flg'),
                update_ts=F('entry_detail__entry__updated_at'),
            )
        )


class EntryDetailChobun(models.Model):
    entry_detail = models.OneToOneField(
        EntryDetail,
        models.CASCADE,
        verbose_name=_('entry detail'),
        related_name='chobun',
        db_column='id',
        primary_key=True,
    )
    okurinushi_company = models.TextField(_('sender company name'), blank=True, null=True)
    okurinushi_title = models.TextField(_('sender title'), blank=True, null=True)
    okurinushi_company_kana = models.TextField(_('sender company kana'), blank=True, null=True)
    okurinushi_name = models.TextField(_('sender name'))
    okurinushi_kana = models.TextField(_('sender kana'))
    okurinushi_zip_code = models.CharField(_('sender zip code'), max_length=7, default='')
    okurinushi_prefecture = models.TextField(_('sender prefecture'), default='')
    okurinushi_address_1 = models.TextField(_('sender address_1'), default='')
    okurinushi_address_2 = models.TextField(_('sender address_2'), default='')
    okurinushi_address_3 = models.TextField(
        _('sender address_3'), blank=True, null=True, default=''
    )
    okurinushi_tel = models.CharField(_('sender tel'), max_length=15, default='')
    renmei1 = models.TextField(_('joint name 1'), blank=True, null=True)
    renmei_kana1 = models.TextField(_('joint kana 1'), blank=True, null=True)
    renmei2 = models.TextField(_('joint name 2'), blank=True, null=True)
    renmei_kana2 = models.TextField(_('joint kana 2'), blank=True, null=True)
    atena = models.TextField(_('recipient'))
    honbun = models.TextField(_('body'))
    daishi = models.ForeignKey(
        ChobunDaishiMaster, models.SET_NULL, verbose_name=_('daishi'), blank=True, null=True
    )
    note = models.TextField(_('note'), blank=True, null=True)
    printed_flg = models.BooleanField(_('printed'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    objects = models.Manager()
    fdn_order_list = FdnCommonListManager()

    class Meta:
        db_table = 'tr_entry_detail_chobun'

    def set_printed(self):
        self.printed_flg = True
        self.save()


class FdnKodenListManager(models.Manager):
    def get_queryset(self, company_id):
        return (
            super()
            .get_queryset()
            .select_related(
                'entry_detail',
                'entry_detail__entry',
                'entry_detail__item',
                'entry_detail__entry__seko',
                'entry_detail__entry__seko__moshu',
                'entry_detail__entry__seko__kojin',
            )
            .filter(
                Q(entry_detail__entry__seko__seko_company=company_id)
                & Q(entry_detail__entry__seko__del_flg=False)
                & Q(entry_detail__entry__seko__kojin__kojin_num=1)
            )
            .annotate(
                seko_id=F('entry_detail__entry__seko_id'),
                term=F('entry_detail__entry__updated_at'),
                fdn_seko_code=F('entry_detail__entry__seko__fdn_code'),
                soke_name=F('entry_detail__entry__seko__soke_name'),
                kojin_name=F('entry_detail__entry__seko__kojin__name'),
                kojin_birth_date=F('entry_detail__entry__seko__kojin__birth_date'),
                kojin_death_date=F('entry_detail__entry__seko__kojin__death_date'),
                moshu_name=F('entry_detail__entry__seko__moshu__name'),
                moshu_tel=F('entry_detail__entry__seko__moshu__tel'),
                moshu_mobile=F('entry_detail__entry__seko__moshu__mobile_num'),
                entry_id=F('entry_detail__entry'),
                entry_name=F('entry_detail__entry__entry_name'),
                entry_name_kana=F('entry_detail__entry__entry_name_kana'),
                entry_zip_code=F('entry_detail__entry__entry_zip_code'),
                entry_prefecture=F('entry_detail__entry__entry_prefecture'),
                entry_address_1=F('entry_detail__entry__entry_address_1'),
                entry_address_2=F('entry_detail__entry__entry_address_2'),
                entry_address_3=F('entry_detail__entry__entry_address_3'),
                entry_tel=F('entry_detail__entry__entry_tel'),
                entry_mail_address=F('entry_detail__entry__entry_mail_address'),
                entry_ts=F('entry_detail__entry__entry_ts'),
                cancel_ts=F('entry_detail__entry__cancel_ts'),
                service_id=Value(30, IntegerField()),
                service_name=Value('香典', CharField()),
                item_id=F('entry_detail__item'),
                fdn_item_code=F('entry_detail__item__fdn_code'),
                item_hinban=F('entry_detail__item_hinban'),
                item_name=F('entry_detail__item_name'),
                item_unit_price=F('entry_detail__item_unit_price'),
                quantity=F('entry_detail__quantity'),
                item_tax=F('entry_detail__item_tax'),
                item_tax_adjust=F('entry_detail__tax_adjust'),
                item_tax_pct=F('entry_detail__item_tax_pct'),
                keigen_flg=F('entry_detail__keigen_flg'),
                okurinushi_company=Value(None, CharField()),
                okurinushi_company_kana=Value(None, CharField()),
                okurinushi_title=Value(None, CharField()),
                okurinushi_name=Value(None, CharField()),
                okurinushi_zip_code=Value(None, CharField()),
                okurinushi_prefecture=Value(None, CharField()),
                okurinushi_address_1=Value(None, CharField()),
                okurinushi_address_2=Value(None, CharField()),
                okurinushi_address_3=Value(None, CharField()),
                okurinushi_tel=Value(None, CharField()),
                renmei1=Value(None, CharField()),
                renmei2=Value(None, CharField()),
                update_ts=F('entry_detail__entry__updated_at'),
            )
        )


class FdnKodenCommissionListManager(models.Manager):
    def get_queryset(self, company_id):
        return (
            super()
            .get_queryset()
            .select_related(
                'entry_detail',
                'entry_detail__entry',
                'entry_detail__item',
                'entry_detail__entry__seko',
                'entry_detail__entry__seko__moshu',
                'entry_detail__entry__seko__kojin',
            )
            .filter(
                Q(entry_detail__entry__seko__seko_company=company_id)
                & Q(entry_detail__entry__seko__del_flg=False)
                & Q(entry_detail__entry__seko__kojin__kojin_num=1)
            )
            .annotate(
                seko_id=F('entry_detail__entry__seko_id'),
                term=F('entry_detail__entry__updated_at'),
                fdn_seko_code=F('entry_detail__entry__seko__fdn_code'),
                soke_name=F('entry_detail__entry__seko__soke_name'),
                kojin_name=F('entry_detail__entry__seko__kojin__name'),
                kojin_birth_date=F('entry_detail__entry__seko__kojin__birth_date'),
                kojin_death_date=F('entry_detail__entry__seko__kojin__death_date'),
                moshu_name=F('entry_detail__entry__seko__moshu__name'),
                moshu_tel=F('entry_detail__entry__seko__moshu__tel'),
                moshu_mobile=F('entry_detail__entry__seko__moshu__mobile_num'),
                entry_id=F('entry_detail__entry'),
                entry_name=F('entry_detail__entry__entry_name'),
                entry_name_kana=F('entry_detail__entry__entry_name_kana'),
                entry_zip_code=F('entry_detail__entry__entry_zip_code'),
                entry_prefecture=F('entry_detail__entry__entry_prefecture'),
                entry_address_1=F('entry_detail__entry__entry_address_1'),
                entry_address_2=F('entry_detail__entry__entry_address_2'),
                entry_address_3=F('entry_detail__entry__entry_address_3'),
                entry_tel=F('entry_detail__entry__entry_tel'),
                entry_mail_address=F('entry_detail__entry__entry_mail_address'),
                entry_ts=F('entry_detail__entry__entry_ts'),
                cancel_ts=F('entry_detail__entry__cancel_ts'),
                service_id=Value(31, IntegerField()),
                service_name=Value('香典システム利用料', CharField()),
                item_id=Value(None, IntegerField()),
                fdn_item_code=Value(None, CharField()),
                item_hinban=Value(None, CharField()),
                item_name=Value('香典システム利用料', CharField()),
                item_unit_price=F('koden_commission'),
                quantity=Value(1, IntegerField()),
                item_tax=F('koden_commission_tax'),
                item_tax_adjust=F('tax_adjust'),
                item_tax_pct=F('koden_commission_tax_pct'),
                keigen_flg=Value(False, BooleanField()),
                okurinushi_company=Value(None, CharField()),
                okurinushi_company_kana=Value(None, CharField()),
                okurinushi_title=Value(None, CharField()),
                okurinushi_name=Value(None, CharField()),
                okurinushi_zip_code=Value(None, CharField()),
                okurinushi_prefecture=Value(None, CharField()),
                okurinushi_address_1=Value(None, CharField()),
                okurinushi_address_2=Value(None, CharField()),
                okurinushi_address_3=Value(None, CharField()),
                okurinushi_tel=Value(None, CharField()),
                renmei1=Value(None, CharField()),
                renmei2=Value(None, CharField()),
                update_ts=F('entry_detail__entry__updated_at'),
            )
        )


class EntryDetailKoden(models.Model):
    entry_detail = models.OneToOneField(
        EntryDetail,
        models.CASCADE,
        verbose_name=_('entry detail'),
        related_name='koden',
        db_column='id',
        primary_key=True,
    )
    koden_commission = models.IntegerField(_('commission price'))
    koden_commission_tax = models.IntegerField(_('commission tax'))
    koden_commission_tax_pct = models.IntegerField(_('commission tax percentage'))
    tax_adjust = models.IntegerField(_('tax adjusted'))
    henreihin_fuyo_flg = models.BooleanField(_('heirei fuyo'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    objects = models.Manager()
    fdn_order_list = FdnKodenListManager()
    fdn_commission_list = FdnKodenCommissionListManager()

    class Meta:
        db_table = 'tr_entry_detail_koden'


class EntryDetailKumotsu(models.Model):
    entry_detail = models.OneToOneField(
        EntryDetail,
        models.CASCADE,
        verbose_name=_('entry detail'),
        related_name='kumotsu',
        db_column='id',
        primary_key=True,
    )
    okurinushi_company = models.TextField(_('sender company name'), blank=True, null=True)
    okurinushi_title = models.TextField(_('sender title'), blank=True, null=True)
    okurinushi_company_kana = models.TextField(_('sender company kana'), blank=True, null=True)
    okurinushi_name = models.TextField(_('sender name'))
    okurinushi_kana = models.TextField(_('sender kana'))
    okurinushi_zip_code = models.CharField(_('sender zip code'), max_length=7, default='')
    okurinushi_prefecture = models.TextField(_('sender prefecture'), default='')
    okurinushi_address_1 = models.TextField(_('sender address_1'), default='')
    okurinushi_address_2 = models.TextField(_('sender address_2'), default='')
    okurinushi_address_3 = models.TextField(
        _('sender address_3'), blank=True, null=True, default=''
    )
    okurinushi_tel = models.CharField(_('sender tel'), max_length=15, default='')
    renmei1 = models.TextField(_('joint name 1'), blank=True, null=True)
    renmei_kana1 = models.TextField(_('joint kana 1'), blank=True, null=True)
    renmei2 = models.TextField(_('joint name 2'), blank=True, null=True)
    renmei_kana2 = models.TextField(_('joint kana 2'), blank=True, null=True)
    note = models.TextField(_('note'), blank=True, null=True)
    supplier = models.ForeignKey(
        Supplier, models.PROTECT, verbose_name=_('supplier'), blank=True, null=True
    )
    order_status = models.IntegerField(_('order status'))
    order_ts = models.DateTimeField(_('ordered at'), blank=True, null=True)
    delivery_ts = models.DateTimeField(_('delivered at'), blank=True, null=True)
    order_staff = models.ForeignKey(
        Staff, models.PROTECT, verbose_name=_('order staff'), blank=True, null=True
    )
    order_note = models.TextField(_('order note'), blank=True, null=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    objects = models.Manager()
    fdn_order_list = FdnCommonListManager()

    class Meta:
        db_table = 'tr_entry_detail_kumotsu'

    def set_order_status(self, new_state: int, staff: Staff) -> None:
        self.order_status = new_state
        if new_state == 2 and self.order_ts is None:
            self.order_ts = timezone.localtime()
            self.order_staff = staff
        self.save()


class EntryDetailMsg(models.Model):
    entry_detail = models.OneToOneField(
        EntryDetail,
        models.CASCADE,
        verbose_name=_('entry detail'),
        related_name='message',
        db_column='id',
        primary_key=True,
    )
    relation_ship = models.TextField(_('relationshop'))
    honbun = models.TextField(_('body'))
    release_status = models.IntegerField(_('release status'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_entry_detail_msg'


class PaymentResult(models.Model):
    contract_code = models.CharField(_('contract code'), max_length=8)
    order_number = models.IntegerField(_('ordernumber'))
    result_code = models.CharField(_('result code'), max_length=1)
    err_code = models.TextField(_('error code'), blank=True, null=True)
    err_detail = models.TextField(_('error detail'), blank=True, null=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_payment_result'
