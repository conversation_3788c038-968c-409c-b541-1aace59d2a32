import base64
from datetime import datetime

from django.contrib.auth.hashers import UNUSABLE_PASSWORD_PREFIX
from django.test import TestCase

import staffs.hash as hash
from bases.models import Base
from bases.tests.factories import BaseFactory
from staffs.models import Staff
from staffs.tests.factories import StaffFactory


class StaffModelTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff: Staff = StaffFactory()

    def test_password_hashing(self) -> None:
        """パスワードのハッシュ化が正しいか"""
        new_password = hash.make_password(
            raw_password='ThisIsDummyAccount', salt='vDULHg5oZRK9HXHTiX3VTc1TcP/gafuxbEuO5HDLt3s='
        )
        self.assertEqual(
            new_password,
            (
                'ikEtoGVVNkKN1Yf4v8xSszmeFdH9e9ZMRCAI7ln8oImT4kf9z7JBSRidQ1Nt9nQ+zYkbe4X1tiDgbsYOB'
                '7S51w=='
            ),
        )

    def test_set_and_check_password(self) -> None:
        """パスワードが再設定、確認できるか"""
        NEW_DUMMY_PASSWORD = 'ThisIsNewDummyPassword'
        self.staff.salt = 'RdyAC+0XH84KTqTdJ/lh9f4bxhPcqXOJeAo8CQug90E='
        self.staff.set_password(NEW_DUMMY_PASSWORD)
        self.staff.save()
        self.assertEqual(
            self.staff.password,
            (
                '2qru4fe59qGqxJQ9ztFEoWQ/P3jJs3GlnV0W3cyi5Q9NPxS2LrHMFnrnI0mCHRoWzqoTAH0FOu9yQ/BEg'
                'EOayg=='
            ),
        )
        self.assertFalse(self.staff.check_password('ThisIsIncollectPassword'))
        self.assertTrue(self.staff.check_password(NEW_DUMMY_PASSWORD))
        self.assertFalse(self.staff.check_password(None))

        self.staff.salt = None
        with self.assertRaises(ValueError):
            self.staff.check_password(NEW_DUMMY_PASSWORD)

    def test_set_unusable_password(self) -> None:
        """Noneのパスワードをセットすると無効なパスワードになる"""
        self.staff.set_password(None)
        self.assertTrue(self.staff.password.startswith(UNUSABLE_PASSWORD_PREFIX))

    def test_generate_salt(self) -> None:
        """Salt用文字列の生成"""
        new_salt = hash.generate_salt()
        new_salt_bytes = base64.b64decode(new_salt.encode('ascii'))
        self.assertEqual(32, len(new_salt_bytes))

    def test_is_active(self):
        """担当者は登録された直後はアクティブ"""
        self.assertTrue(self.staff.is_active)

    def test_deleted_staff_is_inactive(self):
        """無効化(論理削除)した担当者は非アクティブ"""
        self.staff.disable()
        self.assertFalse(self.staff.is_active)

    def test_retired_staff_is_inactive(self):
        """退職した担当者は非アクティブ"""
        self.staff.retired_flg = True
        self.staff.save()
        self.assertFalse(self.staff.is_active)

    def test_is_staff(self) -> None:
        """拠点タイプが管理者の担当者は管理者用サイトを表示できる"""
        base: Base = self.staff.base
        base.base_type = Base.OrgType.ADMIN
        base.save()
        self.assertTrue(self.staff.is_staff)

        base.base_type = Base.OrgType.COMPANY
        base.save()
        self.assertFalse(self.staff.is_staff)

        base.base_type = Base.OrgType.DEPARTMENT
        base.save()
        self.assertFalse(self.staff.is_staff)

        base.base_type = Base.OrgType.HALL
        base.save()
        self.assertFalse(self.staff.is_staff)

        base.base_type = Base.OrgType.OTHER
        base.save()
        self.assertFalse(self.staff.is_staff)

    def test_is_superuser(self) -> None:
        """拠点タイプが管理者の担当者は管理者権限を持つ"""
        base: Base = self.staff.base
        base.base_type = Base.OrgType.ADMIN
        base.save()
        self.assertTrue(self.staff.is_superuser)

        base.base_type = Base.OrgType.COMPANY
        base.save()
        self.assertFalse(self.staff.is_superuser)

        base.base_type = Base.OrgType.DEPARTMENT
        base.save()
        self.assertFalse(self.staff.is_superuser)

        base.base_type = Base.OrgType.HALL
        base.save()
        self.assertFalse(self.staff.is_superuser)

        base.base_type = Base.OrgType.OTHER
        base.save()
        self.assertFalse(self.staff.is_superuser)

    def test_generate_raw_password_succeed(self):
        """担当者の初期パスワードがURLSafeな文字で生成される"""
        for _ in range(1000):
            new_password = Staff.generate_raw_password()
            self.assertRegex(new_password, '[a-zA-Z0-9-_=]{17,}')

    def test_reset_password_succeed(self):
        """担当者のパスワードをリセットする"""
        old_password = self.staff.password  # 中身はHash化されている
        old_salt = self.staff.salt

        # リセットの場合はSaltも変える
        self.staff.reset_password()
        self.assertNotEqual(old_password, self.staff.password)
        self.assertNotEqual(old_salt, self.staff.salt)
        self.assertTrue(self.staff.check_password(self.staff._password))

    def test_create_superuser_succeed(self):
        """管理者ユーザを作成できる"""
        admin_base: Base = BaseFactory(base_type=Base.OrgType.ADMIN)
        new_account_vars = StaffFactory.build(base=admin_base)
        superuser = Staff.objects.create_superuser(
            base_id=admin_base.pk,
            login_id=new_account_vars.login_id,
            password=new_account_vars._password,
        )
        self.assertIsNotNone(superuser)
        self.assertEqual(superuser.company_code, admin_base.company_code)

    def test_staff_unique_together(self):
        """拠点が異なれば同じlogin_idを持つ担当者を作れる"""
        another_base: Base = BaseFactory()
        another_staff: Staff = StaffFactory.build(base=another_base, login_id=self.staff.login_id)
        another_staff.save()

        self.assertNotEqual(another_staff.pk, self.staff.pk)
        self.assertNotEqual(another_staff.base.pk, self.staff.base.pk)
        self.assertEqual(another_staff.login_id, self.staff.login_id)

    def test_staff_disable(self) -> None:
        """担当者を無効化(論理削除)する"""
        self.staff.disable()
        self.assertTrue(self.staff.del_flg)

    def test_update_last_login(self) -> None:
        """担当者の最終ログイン日時を更新する"""
        self.assertIsNone(self.staff.last_login)
        prev_updated_at: datetime = self.staff.updated_at

        self.staff.update_last_login()
        self.assertIsNotNone(self.staff.last_login)
        # 最終ログイン日時は更新されるが更新日時は変わらない
        self.assertNotEqual(self.staff.last_login, self.staff.updated_at)
        self.assertEqual(prev_updated_at, self.staff.updated_at)
