
<ng-container *ngIf="appView?.type===3">
<!--div id="debug-area" style="position:fixed;width:500px;height:500px;background-color:white;z-index:100;border:solid 1px black;">
</div-->
<header>
  <div class="main-header">
    <div class="logo" (click)="gotoTop()"></div>
    <div class="cart" [class.show-count]="entry?.details?.length" routerLink="/cart" *ngIf="seko_info?.services?.length && !is_hoyo_page">
      <div class="count" *ngIf="entry?.details?.length">{{entry.details.length}}</div>
    </div>
  </div>
</header>

<div class="routerArea">
<app-cus-huho *ngIf="appView.tag==='app-cus-huho'" class="contents"></app-cus-huho>
<app-cus-hoyo *ngIf="appView.tag==='app-cus-hoyo'" class="contents"></app-cus-hoyo>
<app-cus-huho-approve *ngIf="appView.tag==='app-cus-huho-approve'" class="contents"></app-cus-huho-approve>
<app-cus-chobun1 *ngIf="appView.tag==='app-cus-chobun1'" class="contents"></app-cus-chobun1>
<app-cus-chobun2 *ngIf="appView.tag==='app-cus-chobun2'" class="contents"></app-cus-chobun2>
<app-cus-kumotsu *ngIf="appView.tag==='app-cus-kumotsu'" class="contents"></app-cus-kumotsu>
<app-cus-koden1 *ngIf="appView.tag==='app-cus-koden1'" class="contents"></app-cus-koden1>
<app-cus-koden2 *ngIf="appView.tag==='app-cus-koden2'" class="contents"></app-cus-koden2>
<app-cus-message *ngIf="appView.tag==='app-cus-message'" class="contents"></app-cus-message>
<app-cus-cart *ngIf="appView.tag==='app-cus-cart'" class="contents"></app-cus-cart>
<app-cus-order-entry *ngIf="appView.tag==='app-cus-order-entry'" class="contents"></app-cus-order-entry>
<app-cus-order-confirm *ngIf="appView.tag==='app-cus-order-confirm'" class="contents"></app-cus-order-confirm>
<app-cus-order-payment *ngIf="appView.tag==='app-cus-order-payment'" class="contents"></app-cus-order-payment>
<app-cus-order-complete *ngIf="appView.tag==='app-cus-order-complete'" class="contents"></app-cus-order-complete>
<app-cus-receipt *ngIf="appView.tag==='app-cus-receipt'" class="contents"></app-cus-receipt>
<app-cus-inquiry *ngIf="appView.tag==='app-cus-inquiry'" class="contents"></app-cus-inquiry>
<app-cus-sitepolicy *ngIf="appView.tag==='app-cus-sitepolicy'" class="contents"></app-cus-sitepolicy>
<app-cus-userpolicy *ngIf="appView.tag==='app-cus-userpolicy'" class="contents"></app-cus-userpolicy>
<app-cus-privacy-policy *ngIf="appView.tag==='app-cus-privacy-policy'" class="contents"></app-cus-privacy-policy>
<app-cus-tokushoho *ngIf="appView.tag==='app-cus-tokushoho'" class="contents"></app-cus-tokushoho>
<app-cus-howto-share *ngIf="appView.tag==='app-cus-howto-share'" class="contents"></app-cus-howto-share>
</div>

<div class="pagetop"><a href="#"><img src="../../../assets/img/customer/pagetop.png">TOP</a></div>

<!--footer-->
<footer>
  <!--
  <div class="contents share" *ngIf="is_fuho_page || is_hoyo_page">
    <a class="line" href="https://social-plugins.line.me/lineit/share?url={{page_uri}}" target="_blank" alt="LINE"></a>
    <a class="facebook" href="https://www.facebook.com/sharer/sharer.php?u={{page_uri}}" target="_blank" alt="facebook"></a>
    <a class="twitter" href="https://twitter.com/share?url={{page_uri}}" target="_blank" alt="twitter"></a>
    <a class="mail" href="mailto:?body={{page_uri}}" target="_blank" alt="mail"></a>
  </div>
  -->
  <div class="contents">
    <ul>
      <li><a routerLink="/sitepolicy">このサイトについて</a></li>
      <li><a routerLink="/userpolicy">ご利用規約</a></li>
      <li><a routerLink="/privacypolicy">プライバシーポリシー</a></li>
      <li *ngIf="companyFeneral()"><a routerLink="/tokushoho">特定商取引法に基づく表記</a></li>
      <li *ngIf="companyFeneral()"><a routerLink="/inquiry/funeral">葬儀に関するお問い合わせ</a></li>
      <li><a routerLink="/inquiry">葬儀オンラインサービスこころに関するお問い合わせ</a></li>
    </ul>
  </div>
</footer>
</ng-container>
