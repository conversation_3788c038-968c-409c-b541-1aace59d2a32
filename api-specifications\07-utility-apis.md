# ユーティリティAPI仕様書

## 概要

FOCシステムのユーティリティAPI（ファイルアップロード、PDF生成、メール送信、SMS送信等）の詳細仕様です。システム全体で使用される共通機能を提供します。

## 1. ファイルアップロード関連API

### 1.1 画像ファイルアップロードAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /items/{base_id}/import_image/` |
| **機能** | 商品画像のBase64アップロード |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `ItemImportImageView` |
| **シリアライザー** | `ItemImportImageSerializer` |

**HTTPメソッド:** `POST`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `base_id` | integer | ✓ | 拠点ID |

**リクエストボディ:**
```json
{
  "filename": "sample_item.jpg",
  "file_content": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
}
```

**バリデーション:**
| フィールド | 制約 | エラーメッセージ |
|---|---|---|
| `filename` | 必須、文字列 | This field is required. |
| `file_content` | 必須、Base64画像形式 | Invalid image format. |

**レスポンス例:**
```json
{
  "message": "画像ファイルをアップロードしました。",
  "filepath": "items/123/sample_item.jpg"
}
```

### 1.2 共有画像アップロードAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /seko/{id}/share_image/` |
| **機能** | 施行共有画像アップロード |
| **認証** | JWT認証必須 |
| **権限** | IsMoshu（喪主のみ） |
| **実装クラス** | `ShareImageCreate` |
| **シリアライザー** | `ShareImageCreateSerializer` |

**HTTPメソッド:** `POST`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 施行ID |

**リクエストボディ:**
```json
{
  "file_name": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
  "display_num": 1,
  "mode": 1
}
```

**レスポンス例:**
```json
{
  "id": 101,
  "file_name": "share_image_101.jpg",
  "display_num": 1,
  "mode": 1,
  "created_at": "2024-06-26T10:00:00+09:00",
  "message": "共有画像をアップロードしました。"
}
```

### 1.3 広告ファイルアップロードAPI

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /advertises/` |
| **機能** | 広告バナー・PDFファイルアップロード |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `AdvertiseCreate` |
| **シリアライザー** | `AdvertiseSerializer` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "title": "新サービスのご案内",
  "banner_file": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
  "link_file": "data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsO8w6...",
  "display_order": 1,
  "active_flg": true
}
```

**バリデーション:**
| フィールド | 制約 | エラーメッセージ |
|---|---|---|
| `banner_file` | Base64画像形式 | Invalid image format. |
| `link_file` | Base64 PDF形式 | Invalid PDF format. |
| `title` | 必須、最大200文字 | This field is required. |

**レスポンス例:**
```json
{
  "id": 201,
  "title": "新サービスのご案内",
  "banner_file": "/media/advertises/banner_201.jpg",
  "link_file": "/media/advertises/link_201.pdf",
  "display_order": 1,
  "active_flg": true,
  "created_at": "2024-06-26T10:00:00+09:00"
}
```

## 2. PDF生成API

### 2.1 弔文PDF生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/chobun/{id}/pdf/` |
| **機能** | 弔文PDF生成・ダウンロード |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `ChobunPDF` |
| **シリアライザー** | なし |

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 注文詳細ID |

**レスポンス:** PDF形式の弔文ファイル

**Content-Type:** `application/pdf`

**ファイル名:** `弔文-{注文詳細ID}.pdf`

### 2.2 供物発注書PDF生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /orders/kumotsu/{id}/pdf/` |
| **機能** | 供物発注書PDF生成・ダウンロード |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `KumotsuPDF` |
| **シリアライザー** | なし |

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 供物注文詳細ID |

**レスポンス:** PDF形式の供物発注書ファイル

**Content-Type:** `application/pdf`

**ファイル名:** `供花・供物発注書-{供物注文詳細ID}.pdf`

### 2.3 返礼品発注書PDF生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /henrei/{id}/pdf/` |
| **機能** | 返礼品発注書PDF生成・ダウンロード |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `OrderHenreiPDF` |
| **シリアライザー** | なし |

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 返礼品注文ID |

**レスポンス:** PDF形式の返礼品発注書ファイル

**Content-Type:** `application/pdf`

**ファイル名:** `返礼品発注書-{返礼品注文ID}.pdf`

### 2.4 訃報PDF生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /seko/{id}/pdf/` |
| **機能** | 訃報PDF生成・ダウンロード |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `FuhoPDF` |
| **シリアライザー** | なし |

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 施行ID |

**レスポンス:** PDF形式の訃報ファイル

**Content-Type:** `application/pdf`

**ファイル名:** `訃報-{施行ID}.pdf`

**特徴:**
- QRコード自動生成（喪主サイトURL）
- 静的画像（スマホ・PC画像）の埋め込み
- Base64エンコードによる画像処理

### 2.5 芳名帳PDF生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /seko/{id}/homeicholist_pdf/` |
| **機能** | 芳名帳（弔文）PDF生成・ダウンロード |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `HomeichoListPDF` |
| **シリアライザー** | なし |

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 施行ID |

**レスポンス:** PDF形式の芳名帳ファイル

**Content-Type:** `application/pdf`

**ファイル名:** `芳名帳（弔文）-{施行ID}.pdf`

### 2.6 供物芳名帳PDF生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /seko/{id}/homeicholist_kumotsu_pdf/` |
| **機能** | 供物芳名帳PDF生成・ダウンロード |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `HomeichoKumotsuPDF` |
| **シリアライザー** | なし |

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 施行ID |

**レスポンス:** PDF形式の供物芳名帳ファイル

**Content-Type:** `application/pdf`

**ファイル名:** `芳名帳（供物）-{施行ID}.pdf`

## 3. メール送信API

### 3.1 注文完了メール送信API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | 注文作成時に自動実行 |
| **機能** | 注文完了メール自動送信 |
| **認証** | 内部処理 |
| **権限** | 内部処理 |
| **実装クラス** | `EntryList.perform_create` |
| **テンプレート** | `mail/entry_complete.txt` |

**送信タイミング:** 注文作成完了時

**送信先:**
- 注文者メールアドレス
- BCC: 拠点スタッフメールアドレス

**メール内容:**
```
件名: てれ葬儀こころ【{家名}家】：お申込みを受け付けました

{注文者名} 様

この度は、てれ葬儀こころをご利用いただき、誠にありがとうございます。
下記の内容でお申込みを受け付けました。

【注文内容】
注文番号: {注文番号}
注文日時: {注文日時}
サービス: {サービス名}
金額: {金額}円（税込）

ご不明な点がございましたら、お気軽にお問い合わせください。
```

### 3.2 施行問い合わせメール送信API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | 施行問い合わせ作成時に自動実行 |
| **機能** | 施行問い合わせメール自動送信 |
| **認証** | 内部処理 |
| **権限** | 内部処理 |
| **実装クラス** | `SekoInquiryCreate.perform_create` |
| **テンプレート** | `mail/seko_inquiry.txt` |

**送信タイミング:** 施行問い合わせ作成時

**送信先:**
- 担当スタッフメールアドレス
- BCC: 問い合わせ管理メールアドレス

**メール内容:**
```
件名: 【FOC】ご葬家からの問合せがあります

{担当スタッフ名} 様

ご葬家から問い合わせがありました。

【問い合わせ内容】
施行: {家名}家
喪主: {喪主名}
問い合わせ内容: {問い合わせ内容}
問い合わせ日時: {問い合わせ日時}

速やかにご対応をお願いいたします。
```

### 3.3 画像共有通知メール送信API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /seko/{id}/notice_image_file/company/` |
| **機能** | 画像共有通知メール送信 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `NoticeImageFileToCompany` |
| **テンプレート** | `mail/seko_share_image_company.txt` |

**HTTPメソッド:** `POST`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 施行ID |

**送信先:**
- 会社注文メールアドレス（複数可）

**レスポンス例:**
```json
{
  "message": "画像共有通知メールを送信しました。"
}
```

### 3.4 供物発注メール・FAX送信API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /orders/kumotsu/{id}/fax/` |
| **機能** | 供物発注書メール・FAX送信 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `KumotsuPDFFax` |
| **テンプレート** | `mail/order.txt` |

**HTTPメソッド:** `POST`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 供物注文詳細ID |

**送信方式:**
1. **メール送信**: 仕入先がメール送信対応の場合
   - 送信先: 仕入先メールアドレス
   - BCC: 注文担当スタッフ
   - 添付: 供物発注書PDF

2. **FAX送信**: 仕入先がFAX送信の場合
   - 送信先: `{FAX番号}@cl1.faximo.jp`
   - 添付: 供物発注書PDF

**レスポンス例:**
```json
{
  "message": "供物発注書を送信しました。",
  "send_method": "email",
  "recipient": "<EMAIL>"
}
```

### 3.5 返礼品発注メール・FAX送信API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /henrei/{id}/fax/` |
| **機能** | 返礼品発注書メール・FAX送信 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `OrderHenreiPDFFax` |
| **テンプレート** | `mail/order.txt` |

**HTTPメソッド:** `POST`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 返礼品注文ID |

**送信方式:** 供物発注と同様（メール・FAX自動判定）

**レスポンス例:**
```json
{
  "message": "返礼品発注書を送信しました。",
  "send_method": "fax",
  "recipient": "<EMAIL>"
}
```

### 3.6 一般問い合わせメール送信API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /inquiry/` |
| **機能** | 一般問い合わせメール送信 |
| **認証** | 不要 |
| **権限** | なし |
| **実装クラス** | `InquiryCreate` |
| **テンプレート** | `mail/inquiry.txt` |

**HTTPメソッド:** `POST`

**リクエストボディ:**
```json
{
  "name": "問い合わせ者名",
  "email": "<EMAIL>",
  "subject": "サービスについて",
  "message": "サービス内容について詳しく教えてください。",
  "recipient_address": "<EMAIL>"
}
```

**レスポンス例:**
```json
{
  "message": "お問い合わせを受け付けました。"
}
```

## 4. QRコード生成API

### 4.1 施行QRコードPDF生成API

#### 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /seko/{id}/qrcode/` |
| **機能** | 施行用QRコードPDF生成 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `QRCodePdf` |
| **ライブラリ** | `qrcode` |

**HTTPメソッド:** `GET`

**パスパラメータ:**
| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | 施行ID |

**QRコード内容:** `{ベースURL}/fuho/{会社ID}-{施行ID}/`

**レスポンス:** PDF形式のQRコードファイル

**Content-Type:** `application/pdf`

**特徴:**
- 喪主サイトへの直接アクセス用QRコード
- 静的画像（スマホ・PC画像）の埋め込み
- Base64エンコードによる画像処理

## 5. 画像処理API

### 5.1 Base64画像フィールド

#### 基本情報
| 項目 | 値 |
|---|---|
| **実装クラス** | `Base64ImageField` |
| **ライブラリ** | `drf-extra-fields` |
| **対応形式** | JPEG, PNG, GIF |
| **最大サイズ** | 設定による |

**使用例:**
```python
class ItemSerializer(serializers.ModelSerializer):
    image_file = Base64ImageField(required=False)

    class Meta:
        model = Item
        fields = ['id', 'name', 'image_file']
```

**リクエスト形式:**
```json
{
  "name": "商品名",
  "image_file": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
}
```

### 5.2 Base64 PDFフィールド

#### 基本情報
| 項目 | 値 |
|---|---|
| **実装クラス** | `PdfBase64File` |
| **継承元** | `Base64FileField` |
| **対応形式** | PDF |
| **拡張子** | `.pdf` |

**実装詳細:**
```python
class PdfBase64File(Base64FileField):
    ALLOWED_TYPES = ['pdf']

    def get_file_extension(self, filename, decoded_file):
        return 'pdf'
```

**使用例:**
```python
class AdvertiseSerializer(serializers.ModelSerializer):
    link_file = PdfBase64File(required=False, allow_null=True)

    class Meta:
        model = Advertise
        fields = ['id', 'title', 'link_file']
```

## 6. ファイルストレージ管理

### 6.1 ファイル保存処理

#### 基本情報
| 項目 | 値 |
|---|---|
| **ストレージ** | `default_storage` |
| **保存パス** | `{アプリ名}/{ID}/{ファイル名}` |
| **重複処理** | 既存ファイル削除後に新規保存 |

**実装例:**
```python
def create(self, validated_data):
    filename = validated_data['filename']
    content = validated_data['file_content']

    filepath = os.path.join('items', str(self.context['base_id']), filename)
    if default_storage.exists(filepath):
        default_storage.delete(filepath)
    default_storage.save(filepath, content.file)
    return None
```

### 6.2 ファイルパス構成

| アプリ | パス形式 | 例 |
|---|---|---|
| **商品画像** | `items/{拠点ID}/{ファイル名}` | `items/123/sample.jpg` |
| **広告バナー** | `advertises/banner_{ID}.{拡張子}` | `advertises/banner_201.jpg` |
| **広告PDF** | `advertises/link_{ID}.pdf` | `advertises/link_201.pdf` |
| **共有画像** | `share_images/share_image_{ID}.{拡張子}` | `share_images/share_image_101.jpg` |

## 7. エラーコード一覧

| ステータスコード | エラーメッセージ | 発生条件 |
|---|---|---|
| 400 | Invalid image format. | 無効な画像形式 |
| 400 | Invalid PDF format. | 無効なPDF形式 |
| 400 | File size too large. | ファイルサイズ超過 |
| 400 | Then mail_address is required. | メールアドレス未設定 |
| 404 | No EntryDetailChobun matches the given query. | 弔文データが存在しない |
| 404 | Supplier not found. | 仕入先が存在しない |
| 500 | Email send failed. | メール送信失敗 |
| 500 | PDF generation failed. | PDF生成失敗 |

## 8. セキュリティ考慮事項

### 8.1 ファイルアップロード
- **ファイル形式検証**: 許可された形式のみアップロード可能
- **ファイルサイズ制限**: 適切なサイズ制限の設定
- **ファイル名サニタイズ**: 危険な文字の除去

### 8.2 メール送信
- **送信先検証**: 有効なメールアドレスのみ送信
- **送信頻度制限**: スパム防止のための制限
- **テンプレート検証**: XSS攻撃防止

### 8.3 PDF生成
- **入力値検証**: HTMLインジェクション防止
- **リソース制限**: メモリ使用量の制限
- **一時ファイル管理**: 適切な一時ファイル削除

## 9. パフォーマンス最適化

### 9.1 ファイル処理
- **非同期処理**: 大容量ファイルの非同期処理
- **キャッシュ活用**: 生成済みPDFのキャッシュ
- **ストリーミング**: 大容量ファイルのストリーミング配信

### 9.2 メール送信
- **バッチ処理**: 複数メールの一括送信
- **キュー処理**: 非同期メール送信
- **失敗時リトライ**: 送信失敗時の自動リトライ

## 10. 使用例

### 10.1 画像アップロード完全フロー
```javascript
// Base64画像エンコード
const encodeImageToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

// 商品画像アップロード
const uploadItemImage = async (baseId, imageFile) => {
  const base64Image = await encodeImageToBase64(imageFile);

  const response = await fetch(`/items/${baseId}/import_image/`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      filename: imageFile.name,
      file_content: base64Image
    })
  });

  return response.json();
};

// 共有画像アップロード
const uploadShareImage = async (sekoId, imageFile) => {
  const base64Image = await encodeImageToBase64(imageFile);

  const response = await fetch(`/seko/${sekoId}/share_image/`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${moshuToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      file_name: base64Image,
      display_num: 1,
      mode: 1
    })
  });

  return response.json();
};
```

### 10.2 PDF生成・ダウンロード
```javascript
// 弔文PDF生成・ダウンロード
const downloadChobunPdf = async (entryDetailId) => {
  const response = await fetch(`/orders/chobun/${entryDetailId}/pdf/`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });

  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `弔文-${entryDetailId}.pdf`;
  a.click();
  window.URL.revokeObjectURL(url);
};

// 複数PDF一括ダウンロード
const downloadMultiplePdfs = async (orderIds) => {
  const downloadPromises = orderIds.map(async (id) => {
    const response = await fetch(`/orders/${id}/receipt/`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const blob = await response.blob();
    return { id, blob };
  });

  const results = await Promise.all(downloadPromises);

  results.forEach(({ id, blob }) => {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `領収書-${id}.pdf`;
    a.click();
    window.URL.revokeObjectURL(url);
  });
};
```

### 10.3 メール送信・通知
```javascript
// 画像共有通知送信
const notifyImageShare = async (sekoId) => {
  const response = await fetch(`/seko/${sekoId}/notice_image_file/company/`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.json();
};

// 発注書送信
const sendOrderPdf = async (kumotsuId) => {
  const response = await fetch(`/orders/kumotsu/${kumotsuId}/fax/`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.json();
};

// 一般問い合わせ送信
const sendInquiry = async (inquiryData) => {
  const response = await fetch('/inquiry/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: inquiryData.name,
      email: inquiryData.email,
      subject: inquiryData.subject,
      message: inquiryData.message,
      recipient_address: '<EMAIL>'
    })
  });

  return response.json();
};
```
