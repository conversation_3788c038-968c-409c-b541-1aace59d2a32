
<div class="container">
  <div class="inner with_footer">
    <div class="contents tiny">
      <div class="menu_title">
        <i class="hand holding medical icon big"></i>
        <i class="stream icon combo"></i>
        AF項目グループ
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" [(selectedName)]="company_name" (selectedItemChange)="companyChange($event, departComboEm)"></com-dropdown>
          <label>部門</label>
          <com-dropdown #departComboEm [settings]="departCombo" [(selectedValue)]="depart_id" (selectedItemChange)="departChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="af_group_list?.length">
          全{{af_group_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?af_group_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination, pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(pagination, page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination, pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!af_group_list?.length">
              <th class="base_name">所属拠点</th>
              <th class="name">AF項目グループ名</th>
              <th class="display_num">表示順</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let af_group of af_group_list; index as i">
              <tr (click)="showData($event, af_group)" *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
                <td class="base_name" title="{{af_group.base_name}}">{{af_group.base_name}}</td>
                <td class="name" title="{{af_group.name}}">{{af_group.name}}</td>
                <td class="center aligned display_num" title="{{af_group.display_num}}">{{af_group.display_num}}</td>
                <td class="center aligned button operation">
                  <i class="large hand holding medical icon" title="AF項目" (click)="showAfList(af_group)"></i>
                  <i class="large trash alternate icon" title="削除" (click)="deleteData(af_group)"></i>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <tr title="新規追加" (click)="showData($event)">
              <td colspan="4" class="center aligned"><i class="large add icon"></i></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal tiny" id="af-group-edit">
        <div class="header ui">
          <span>
            <i class="hand holding medical icon large"></i>
            <i class="stream icon mini combo"></i>AF項目グループ{{edit_type===1?'登録':'編集'}} 
          </span>
          <!--span class="title" *ngIf="depart_name">【部門:<span class="data">{{depart_name}}</span>】</span-->
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="af_group_edit">
            <div class="line">
              <label class="required">所属拠点(葬儀社)</label>
              <label class="plane">{{company_name}}</label>
            </div>
            <div class="line">
              <label>所属拠点(部門)</label>
              <com-dropdown class="divided full" #inputDepartComboEm [settings]="inputDepartCombo" [(selectedValue)]="af_group_edit.department"></com-dropdown>
            </div>
            <div class="line">
              <label class="required">グループ名</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="name" [(ngModel)]="af_group_edit.name">
              </div>
            </div>
            <div class="line">
              <label class="required">表示順</label>
              <div class="ui input tiny">
                <input type="number" min="1" [(ngModel)]="af_group_edit.display_num">
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeAfGroupEdit()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal tiny af list" id="af-list">
        <div class="header ui"><span><i class="hand holding medical icon large"></i>AF項目</span>
          <span class="title" *ngIf="select_af_group">【グループ:<span class="data">{{select_af_group.name}}</span>】</span>
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!af_list?.length">
                  <th class="name">AF項目名</th>
                  <th class="display_num">表示順</th>
                  <th class="operation"></th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngFor="let af of af_list">
                  <tr (click)="showAfEdit($event, af)">
                    <td class="name" title="{{af.name}}">{{af.name}}</td>
                    <td class="center aligned display_num" title="{{af.display_num}}">{{af.display_num}}</td>
                    <td class="center aligned button operation">
                      <i class="large trash alternate icon" title="削除" (click)="deleteAf(af)"></i>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table">
              <tbody>
                <tr title="新規追加" (click)="showAfEdit($event)">
                  <td colspan="3" class="center aligned"><i class="large add icon"></i></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
      <div class="ui modal mini af edit" id="af-edit">
        <div class="header ui"><i class="hand holding medical icon large"></i>AF項目{{af_edit_type===1?'登録':'編集'}}</div>
        <div class="messageArea">
          <div class="ui message {{message_sub?.type}}" [class.visible]="!!message_sub" [class.hidden]="!message_sub" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message_sub?.type==='info'?'info circle':message_sub?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message_sub?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message_sub?.msg">
              {{message_sub?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="af_edit">
            <div class="line">
              <label class="required">項目名</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="name" [(ngModel)]="af_edit.name">
              </div>
            </div>
            <div class="line">
              <label class="required">表示順</label>
              <div class="ui input tiny">
                <input type="number" min="1" [(ngModel)]="af_edit.display_num">
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveAf()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeAfEdit()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
