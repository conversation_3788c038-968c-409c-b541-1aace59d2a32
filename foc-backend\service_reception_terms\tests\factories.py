import factory
import factory.fuzzy as fuzzy
from django.utils import timezone
from factory.django import DjangoModelFactory

from bases.tests.factories import BaseFactory
from masters.tests.factories import ScheduleFactory, ServiceFactory
from service_reception_terms.models import ServiceReceptionTerm

tz = timezone.get_current_timezone()


class ServiceReceptionTermFactory(DjangoModelFactory):
    class Meta:
        model = ServiceReceptionTerm

    id = factory.Sequence(lambda n: n)
    department = factory.SubFactory(BaseFactory)
    service = factory.SubFactory(ServiceFactory)
    schedule = factory.SubFactory(ScheduleFactory)
    limit_time = fuzzy.FuzzyInteger(-24, 48)
    unit = fuzzy.FuzzyChoice(ServiceReceptionTerm.UnitType)
    limit_hour = fuzzy.FuzzyInteger(1, 24)
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0
