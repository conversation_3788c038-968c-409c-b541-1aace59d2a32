# Generated by Django 3.1.3 on 2020-11-26 07:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('masters', '0010_paymenttype'),
        ('suppliers', '0001_initial'),
        ('orders', '0002_auto_20201117_1738'),
    ]

    operations = [
        migrations.AlterField(
            model_name='entrydetailchobun',
            name='daishi',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='masters.chobundaishimaster',
                verbose_name='daishi',
            ),
        ),
        migrations.AlterField(
            model_name='entrydetailchobun',
            name='okurinushi_address_3',
            field=models.TextField(
                blank=True, default='', null=True, verbose_name='sender address_3'
            ),
        ),
        migrations.AlterField(
            model_name='entrydetailkumotsu',
            name='okurinushi_address_3',
            field=models.TextField(
                blank=True, default='', null=True, verbose_name='sender address_3'
            ),
        ),
        migrations.AlterField(
            model_name='entrydetailkumotsu',
            name='supplier',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to='suppliers.supplier',
                verbose_name='supplier',
            ),
        ),
    ]
