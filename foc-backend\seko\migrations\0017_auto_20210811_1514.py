# Generated by Django 3.1.7 on 2021-08-11 06:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('bases', '0017_auto_20210412_0843'),
        ('seko', '0016_auto_20210603_0957'),
    ]

    operations = [
        migrations.AlterField(
            model_name='seko',
            name='seko_department',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='seko',
                to='bases.base',
                verbose_name='seko department',
            ),
        ),
        migrations.AlterField(
            model_name='seko',
            name='seko_staff',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='staffed_seko',
                to=settings.AUTH_USER_MODEL,
                verbose_name='seko staff',
            ),
        ),
    ]
