from typing import Dict
from unittest.mock import patch

from django.core import mail
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from seko.models import Moshu
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake_provider = Faker(locale='ja_JP')


class MoshuDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.moshu = MoshuFactory()

        self.api_client = APIClient()
        self.moshu = MoshuFactory()
        refresh = RefreshToken.for_user(self.moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_moshu_detail_succeed(self) -> None:
        """喪主詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['seko']['id'], self.moshu.pk)
        self.assertEqual(record['zip_code'], self.moshu.zip_code)
        self.assertEqual(record['prefecture'], self.moshu.prefecture)
        self.assertEqual(record['address_1'], self.moshu.address_1)
        self.assertEqual(record['address_2'], self.moshu.address_2)
        self.assertEqual(record['address_3'], self.moshu.address_3)
        self.assertEqual(record['mail_address'], self.moshu.mail_address)
        self.assertEqual(record['tel'], self.moshu.tel)
        self.assertEqual(record['mobile_num'], self.moshu.mobile_num)
        self.assertEqual(
            record['seko']['fuho_site_end_date'], self.moshu.seko.fuho_site_end_date.isoformat()
        )
        self.assertEqual(record['soke_site_del_flg'], self.moshu.soke_site_del_flg)
        self.assertEqual(record['mail_flg'], self.moshu.mail_flg)

    def test_moshu_detail_fail_by_seko_disabled(self) -> None:
        """喪主詳細APIが喪主の所属する施行が無効になっているため失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.moshu.seko.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_moshu_detail_deny_from_another_moshu(self) -> None:
        """喪主詳細APIが他の喪主からの呼び出しを拒否する"""
        another_moshu: Moshu = MoshuFactory()
        refresh = RefreshToken.for_user(another_moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_moshu_detail_deny_from_staff(self) -> None:
        """喪主詳細APIが担当者からの呼び出しを拒否する"""
        staff = StaffFactory()
        refresh = RefreshToken.for_user(staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_moshu_detail_fail_without_auth(self) -> None:
        """喪主詳細APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class MoshuUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.moshu: Moshu = MoshuFactory()
        self.new_password = fake_provider.word()
        self.new_fuho_site_end_date = fake_provider.future_date()

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        new_moshu_data = MoshuFactory.build()
        return {
            'name': new_moshu_data.name,
            'kana': new_moshu_data.kana,
            'kojin_moshu_relationship': new_moshu_data.kojin_moshu_relationship,
            'zip_code': new_moshu_data.zip_code,
            'prefecture': new_moshu_data.prefecture,
            'address_1': new_moshu_data.address_1,
            'address_2': new_moshu_data.address_2,
            'address_3': new_moshu_data.address_3,
            'tel': new_moshu_data.tel,
            'mobile_num': new_moshu_data.mobile_num,
            'mail_address': new_moshu_data.mail_address,
            'agree_ts': new_moshu_data.agree_ts,
            'soke_site_del_request_flg': new_moshu_data.soke_site_del_request_flg,
            'soke_site_del_flg': new_moshu_data.soke_site_del_flg,
            'mail_flg': new_moshu_data.mail_flg,
            'password': self.new_password,
            'fuho_site_end_date': self.new_fuho_site_end_date,
        }

    def test_moshu_update_succeed(self) -> None:
        """喪主を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['agree_ts'] = timezone.now()
        params['soke_site_del_request_flg'] = False
        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['seko']['id'], self.moshu.pk)
        self.assertEqual(record['zip_code'], params['zip_code'])
        self.assertEqual(record['prefecture'], params['prefecture'])
        self.assertEqual(record['address_1'], params['address_1'])
        self.assertEqual(record['address_2'], params['address_2'])
        self.assertEqual(record['address_3'], params['address_3'])
        self.assertEqual(record['mail_address'], params['mail_address'])
        self.assertEqual(record['tel'], params['tel'])
        self.assertEqual(record['mobile_num'], params['mobile_num'])
        self.assertEqual(
            record['seko']['fuho_site_end_date'], self.new_fuho_site_end_date.isoformat()
        )
        self.assertEqual(record['soke_site_del_flg'], params['soke_site_del_flg'])
        self.assertEqual(record['mail_flg'], params['mail_flg'])

        # パスワードは出力項目には含めない
        self.moshu.refresh_from_db()
        self.assertTrue(self.moshu.check_password(self.new_password))

        # 葬家専用ページ削除依頼メールは送らない(False→False)
        self.assertEqual(len(mail.outbox), 0)

    def test_moshu_update_tel_or_mobile_num_is_required(self) -> None:
        """喪主更新APIでは喪主の電話番号か携帯電話番号のいずれかは有効な文字列が必要"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # 電話番号が空文字でもOK
        params: Dict = self.basic_params()
        params['tel'] = ''

        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['tel'], '')
        self.assertEqual(record['mobile_num'], params['mobile_num'])

        # 携帯電話番号が空文字でもOK
        params: Dict = self.basic_params()
        params['mobile_num'] = ''

        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['tel'], params['tel'])
        self.assertEqual(record['mobile_num'], '')

        # 電話番号と携帯電話番号の両方が空文字だとNG
        # params: Dict = self.basic_params()
        # params['tel'] = ''
        # params['mobile_num'] = ''

        # response = self.api_client.put(
        #     reverse(
        # 'seko:moshu_detail', kwargs={'pk': self.moshu.pk}
        # ), data=params, format='json'
        # )
        # self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # record: Dict = response.json()
        # self.assertIsNotNone(record.get('tel'))

    def test_moshu_update_partial_tel_or_mobile_num_is_required(self) -> None:
        """喪主更新API(PATCH)では喪主の電話番号か携帯電話番号のどちらかだけ入力は不可"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # # 電話番号あり、携帯電話番号なしでNG
        # params: Dict = self.basic_params()
        # del params['tel']

        # response = self.api_client.patch(
        #     reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}),
        # data=params, format='json'
        # )
        # self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # record: Dict = response.json()
        # self.assertIsNotNone(record.get('tel'))
        # self.assertIsNotNone(record.get('mobile_num'))

        # # 電話番号なし、携帯電話番号ありでNG
        # params: Dict = self.basic_params()
        # del params['mobile_num']

        # response = self.api_client.patch(
        #     reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}),
        # data=params, format='json'
        # )
        # self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # record: Dict = response.json()
        # self.assertIsNotNone(record.get('tel'))
        # self.assertIsNotNone(record.get('mobile_num'))

        # 電話番号と携帯電話番号の両方ともなしはOK
        params: Dict = self.basic_params()
        del params['tel']
        del params['mobile_num']

        response = self.api_client.patch(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_moshu_update_send_soke_page_del_request_mail(self) -> None:
        """喪主更新APIはsoke_site_del_request_flgがFalseからTrueになる場合はメールを送信する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # False→True
        params: Dict = self.basic_params()
        params['soke_site_del_request_flg'] = True
        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['seko']['id'], self.moshu.pk)

        # 葬家専用ページ削除依頼メールを1件送信する
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, 'FOC葬家専用ページ削除依頼')
        self.assertEqual(sent_mail.to, [self.moshu.seko.af_staff.mail_address])

    def test_moshu_update_dont_send_soke_page_del_request_mail(self) -> None:
        """喪主更新APIはsoke_site_del_request_flgがFalseからTrueになる場合以外はメールを送信しない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # True→True
        self.moshu.soke_site_del_request_flg = True
        self.moshu.save()

        params: Dict = self.basic_params()
        params['soke_site_del_request_flg'] = True
        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['seko']['id'], self.moshu.pk)

        # 葬家専用ページ削除依頼メールは送らない
        self.assertEqual(len(mail.outbox), 0)

        # True→False
        params: Dict = self.basic_params()
        params['soke_site_del_request_flg'] = False
        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['seko']['id'], self.moshu.pk)

        # 葬家専用ページ削除依頼メールは送らない
        self.assertEqual(len(mail.outbox), 0)

    def test_moshu_update_change_password_succeed(self) -> None:
        """喪主更新API(PATCH)でパスワードだけ変更する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'password': self.new_password}
        response = self.api_client.patch(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['seko']['id'], self.moshu.pk)

        # パスワードは出力項目には含めない
        self.moshu.refresh_from_db()
        self.assertTrue(self.moshu.check_password(self.new_password))

        # 葬家専用ページ削除依頼メールは送らない
        self.assertEqual(len(mail.outbox), 0)

    def test_moshu_update_fail_by_seko_disabled(self) -> None:
        """喪主更新APIが喪主の所属する施行が無効になっているため失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.moshu.seko.disable()

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_moshu_update_deny_from_another_moshu(self) -> None:
        """喪主更新APIが他の喪主からの呼び出しを拒否する"""
        another_moshu: Moshu = MoshuFactory()
        refresh = RefreshToken.for_user(another_moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_moshu_update_deny_from_staff(self) -> None:
        """喪主更新APIが担当者からの呼び出しを拒否する"""
        staff = StaffFactory()
        refresh = RefreshToken.for_user(staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_moshu_update_fail_without_auth(self) -> None:
        """喪主更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('seko:moshu_detail', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class MoshuApproveViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.moshu: Moshu = MoshuFactory(password='', salt='')

        self.api_client = APIClient()

    @patch('seko.views.request_origin')
    def test_moshu_approve_succeed(self, mock_origin) -> None:
        """葬家専用ページ公開を承認する"""
        mock_origin.return_value = 'http://frontend:9999'

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:soke_approve', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['seko']['id'], self.moshu.pk)
        self.assertIsNotNone(record['agree_ts'])

        # パスワードは出力項目には含めない
        self.moshu.refresh_from_db()
        self.assertNotEqual(self.moshu.salt, '')
        self.assertNotEqual(self.moshu.password, '')

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, '【てれ葬儀こころ】ご葬家専用ページのご案内')
        self.assertEqual(sent_mail.to, [self.moshu.mail_address])

    def test_moshu_approve_with_already_approved(self) -> None:
        """葬家専用ページ公開承認APIが承認済みの喪主のパスワードをリセットする"""
        self.moshu.agree_ts = timezone.localtime()
        self.moshu.save()
        prev_password_hash: str = self.moshu.password
        prev_salt: str = self.moshu.salt

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:soke_approve', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['seko']['id'], self.moshu.pk)
        self.assertIsNotNone(record['agree_ts'])

        # パスワードは出力項目には含めないが、変わっている
        self.moshu.refresh_from_db()
        self.assertNotEqual(self.moshu.salt, prev_password_hash)
        self.assertNotEqual(self.moshu.password, prev_salt)

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, '【てれ葬儀こころ】ご葬家専用ページのご案内')
        self.assertEqual(sent_mail.to, [self.moshu.mail_address])

    def test_moshu_approve_fail_by_seko_disabled(self) -> None:
        """葬家専用ページ公開承認APIが喪主の所属する施行が無効になっているため失敗する"""
        self.moshu.seko.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:soke_approve', kwargs={'pk': self.moshu.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
