@import "src/assets/scss/customer/setting";
.container .inner .contents {
  padding-top: 50px;
  padding-bottom: 50px;
  @media screen and (max-width: 560px) {
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .input-area {
    max-width: 350px;
    padding: 20px;
    @media screen and (max-width: 560px) {
      .line {
        display: flex;
        .label {
          width: 30%;
          min-width: 100px;
        }
        .input {
          width: 70%;
        }
        
      }
    }
  }
  .button-area {
    margin-top: 20px;
  }
  .message{
    padding-top: 30px;
    color: $text-red;
    font-size: 1.1rem;
    text-align: center;
  }
  a.reset {
    text-decoration: underline;
    font-size: 0.8rem;
    cursor: pointer;
  }
}
