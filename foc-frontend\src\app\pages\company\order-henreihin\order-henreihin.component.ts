import { Component, OnInit, Inject, LOCALE_ID, ViewChild } from '@angular/core';
import { CalendarOptionsDate } from 'src/app/components/com-calendar/com-calendar.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from 'src/app/pages/common/confirm-dialog/confirm-dialog.component';
import { SessionService } from 'src/app/service/session.service';
import { HttpClientService } from 'src/app/service/http-client.service';
import { LoaderService } from 'src/app/service/loader.service';
import {
  OrderHenreiGetRequest, OrderHenreiGetResponse, OrderHenreiOrderStatusPostRequest, OrderHenreiPutRequest
} from 'src/app/interfaces/henreihin';
import { SupplierGetRequest } from 'src/app/interfaces/supplier';
import { Utils } from 'src/app/const/func-util';
import { CommonConstants } from 'src/app/const/const';
import { formatDate } from '@angular/common';
import { ComDropdownComponent } from 'src/app/components/com-dropdown/com-dropdown.component';
declare var $;


@Component({
  selector: 'app-com-order-henreihin',
  templateUrl: './order-henreihin.component.html',
  styleUrls: ['./order-henreihin.component.scss']
})
export class OrderHenreihinComponent implements OnInit {
  @ViewChild('supplierComboEm', { static: false }) supplierComboEm: ComDropdownComponent;
  @ViewChild('companyComboEm', { static: false }) companyComboEm: ComDropdownComponent;
  @ViewChild('pdfViewerEm') public pdfViewerEm;
  Const = CommonConstants;
  Utils = Utils;
  calendarOptionDate: CalendarOptionsDate = new CalendarOptionsDate();

  host_url = window.location.origin;
  login_info = this.sessionSvc.get('staff_login_info');
  company_list = Utils.getCompanyList();
  login_company = Utils.getLoginCompany();
  companyCombo = { values: [], clearable: false, showOnFocus: false, disableSearch: true };
  supplierCombo = { values: [], clearable: true };
  sekoBaseCombo = { values: [], clearable: true };
  form_data = {
    company_id: null,
    seko_department: null,
    supplier_id: null,
    order_henrei_id: null,
    order_ts_from: null,
    order_ts_to: null,
    unorder: true,
    ordered: false,
    confirmed: false,
  };
  searched = false;
  order_henrei_list: Array<OrderHenreiGetResponse> = null;

  page_per_count = 20;
  pagination = {
    pages: new Array(),
    current: 0
  };
  receipt_url = null;
  all_check = false;

  selected_order_henrei: OrderHenreiGetResponse = null;
  message = null;
  processing = false;
  fax_pdf_loading = false;
  constructor(
    private httpClientService: HttpClientService,
    private sessionSvc: SessionService,
    private loaderSvc: LoaderService,
    private dialog: MatDialog,
    @Inject(LOCALE_ID) private locale: string,
  ) { }


  ngOnInit() {
    this.initControl();
    this.getSupplier(this.form_data.company_id);
    setTimeout(() => {
      $('.checkbox.all_check').checkbox();
    }, 100);
  }

  initControl() {
    if (this.company_list) {
      this.companyCombo.values = this.company_list.map(value => ({ name: value.base_name, value: value.id, data: value }));
      if (this.login_company) {
        if (this.login_company.base_type === CommonConstants.BASE_TYPE_COMPANY) {
          this.form_data.company_id = this.login_company.id;
          this.sekoBaseCombo.values = Utils.getSekoBaseList(this.login_company);
          if (this.login_info.staff.base.base_type !== CommonConstants.BASE_TYPE_COMPANY) {
            this.form_data.seko_department = this.login_info.staff.base.id;
          }
        } else {
          this.companyCombo.disableSearch = false;
          this.form_data.company_id = this.company_list[0].id;
          this.sekoBaseCombo.values = Utils.getSekoBaseList(this.company_list[0]);
        }
      }
    }
  }

  checkClick(flg_name, ...params: any[]) {

    for (const param of params) {
      if (param) {
        this.form_data[flg_name] = !this.form_data[flg_name];
        return;
      }
    }
  }

  async getSupplier(company_id) {
    this.supplierCombo.values = [];
    const request = new SupplierGetRequest();
    request.company_id = company_id;
    await this.httpClientService.getSupplierList(request).then((response) => {
      Utils.log(response);
      this.supplierCombo.values = response.map(value => ({ name: value.name, value: value.id }));

    }).catch(error => {
    });
    this.form_data.supplier_id = null;
    this.supplierComboEm.initComponent();
  }

  companyChange(event, sekoBaseComboEm) {
    this.sekoBaseCombo.values = [];
    this.form_data.seko_department = null;
    if (event && event.data) {
      this.sekoBaseCombo.values = Utils.getSekoBaseList(event.data);
      this.getSupplier(event.data.id);
    }
    sekoBaseComboEm.clear();
    sekoBaseComboEm.initComponent();
  }


  checkItem(event, order_henrei: OrderHenreiGetResponse) {
    if (!order_henrei) {
      return;
    }
    if (event.target.classList.contains('icon')) {
      return;
    }
    order_henrei.selected = !order_henrei.selected;
    for (const item of this.order_henrei_list) {
      if (!item.selected) {
        this.all_check = false;
        return;
      }
    }
    this.all_check = true;
  }
  checkAllItem() {
    for (const order_henrei of this.order_henrei_list) {
      order_henrei.selected = this.all_check;
    }
  }

  searchOrderHenrei() {
    this.sessionSvc.clear('message');
    const request = this.getRequest();
    if (!this.loaderSvc.isLoading) {
      this.loaderSvc.call();
    }
    this.pagination.pages = new Array();
    this.pagination.current = 0;
    this.httpClientService.getOrderHenreiList(request).then((response) => {
      Utils.log(response);
      this.searched = true;
      this.order_henrei_list = response;

      if (this.order_henrei_list && this.order_henrei_list.length) {
        for (const order_henrei of this.order_henrei_list) {
          if (order_henrei.seko_date) {
            order_henrei.seko_date = new Date(order_henrei.seko_date);
          }
        }
      }
      this.calcPagination();
      this.loaderSvc.dismiss();

    }).catch(error => {
      this.loaderSvc.dismiss();
    });
  }

  getRequest(): OrderHenreiGetRequest {
    const request = new OrderHenreiGetRequest();
    request.company_id = this.form_data.company_id;
    request.seko_department = this.form_data.seko_department;
    request.supplier_id = this.form_data.supplier_id;
    request.order_henrei_id = this.form_data.order_henrei_id;
    if (this.form_data.order_ts_from) {
      request.order_ts_from = formatDate(this.form_data.order_ts_from, 'yyyy-MM-dd', this.locale);
    }
    if (this.form_data.order_ts_to) {
      request.order_ts_to = formatDate(this.form_data.order_ts_to, 'yyyy-MM-dd', this.locale);
    }
    request.order_status = new Array();
    if (!!this.form_data.unorder) {
      request.order_status.push(this.Const.ORDER_STATUS_ID_UNORDER);
    }
    if (!!this.form_data.ordered) {
      request.order_status.push(this.Const.ORDER_STATUS_ID_ORDERED);
    }
    if (!!this.form_data.confirmed) {
      request.order_status.push(this.Const.ORDER_STATUS_ID_CONFIRMED);
    }
    return request;

  }

  calcPagination() {
    if (!this.order_henrei_list || !this.order_henrei_list.length) {
      return;
    }
    const count = Math.ceil(this.order_henrei_list.length / this.page_per_count);
    for (let i = 1; i <= count; i++) {
      this.pagination.pages.push(i);
    }
    this.pagination.current = 1;
  }

  pageTo(page_num) {
    this.pagination.current = page_num;
  }

  clearForm(sekoBaseComboEm, ...comboEms: any[]) {

    this.form_data = {
      company_id: null,
      seko_department: null,
      supplier_id: null,
      order_henrei_id: null,
      order_ts_from: null,
      order_ts_to: null,
      unorder: true,
      ordered: false,
      confirmed: false,
    };
    if (this.login_company) {
      if (this.login_company.base_type === CommonConstants.BASE_TYPE_COMPANY) {
        this.form_data.company_id = this.login_company.id;
        if (this.login_info.staff.base.base_type === CommonConstants.BASE_TYPE_COMPANY) {
          sekoBaseComboEm.clear();
        } else {
          this.form_data.seko_department = this.login_info.staff.base.id;
          sekoBaseComboEm.setValue(this.form_data.seko_department);
        }
      } else {
        this.form_data.company_id = this.company_list[0].id;
        this.sekoBaseCombo.values = Utils.getSekoBaseList(this.company_list[0]);
        sekoBaseComboEm.clear();
        sekoBaseComboEm.initComponent();
      }
      this.companyComboEm.setValue(this.form_data.company_id);
    }
    for (const comboEm of comboEms) {
      comboEm.clear();
      if ('initComponent' in comboEm) {
        comboEm.initComponent();
      }
    }

  }

  downloadPDF(id) {
    this.sessionSvc.clear('message');
    if (id) {
      this.loaderSvc.call();
      this.httpClientService.downloadOrderHenreiPdf(id)
        .then((response) => {
          this.localStreamDownload(response, id);
          this.sessionSvc.save('message', { type: 'info', title: '発注書PDFを出力しました。' });
          this.loaderSvc.dismiss();
        }).catch(error => {
          this.sessionSvc.save('message', { type: 'error', title: '発注書PDFの出力が失敗しました。' });
          this.loaderSvc.dismiss();
        });
    }
  }

  localStreamDownload(stream, id) {
    const blob = new Blob([stream]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    const file_name = '返礼品発注書-' + id + '.pdf';

    if (window.navigator.msSaveBlob) {
      window.navigator.msSaveOrOpenBlob(blob, file_name);
    } else {
      link.href = url;
      link.download = file_name;
      link.click();
    }
  }
  showDetail(order_henrei: OrderHenreiGetResponse) {
    this.message = null;
    if (!order_henrei) {
      return;
    }
    this.selected_order_henrei = order_henrei;
    $('#detail').modal({
      centered: false,
      closable: false,
      detachable: false,
      transition: 'fade'
    }).modal('show');
  }

  saveDetail() {
    this.message = null;
    this.sessionSvc.clear('message');
    if (this.processing) {
      return;
    }
    this.processing = true;
    const postData = new OrderHenreiPutRequest();
    postData.order_note = this.selected_order_henrei.order_note;
    this.loaderSvc.call();
    this.httpClientService.updateOrderHenrei(this.selected_order_henrei.id, postData)
      .then(async (response) => {
        Utils.log(response);
        await this.searchOrderHenrei();
        this.sessionSvc.save('message', { type: 'info', title: '発注備考を更新しました。' });
        this.processing = false;
      })
      .catch(error => {
        this.sessionSvc.save('message', { type: 'error', title: '発注備考の更新が失敗しました。' });
        this.processing = false;
        this.loaderSvc.dismiss();
      });
    $('#detail').modal('hide');
    setTimeout(() => {
      this.selected_order_henrei = null;
    }, 500);
  }

  closeDialog() {
    setTimeout(() => {
      this.selected_order_henrei = null;
    }, 500);
  }

  showUrl(entry_id) {
    if (!entry_id) {
      return;
    }
    this.receipt_url = this.host_url + '/order/' + entry_id + '/receipt';
    $('#receipt-url').modal({
      centered: false,
      closable: true,
      detachable: false,
      transition: 'fade'
    }).modal('show');
  }

  saveOrderStatus(order_status) {
    this.sessionSvc.clear('message');
    if (!order_status) {
      return;
    }
    if (this.processing) {
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        msg1: '発注状況を更新します、よろしいですか？',
      },
      width: '100%',
      maxWidth: '450px',
      maxHeight: '90%'
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result === true) {
        const postData = new OrderHenreiOrderStatusPostRequest();
        postData.ids = new Array();
        for (const order_henrei of this.order_henrei_list) {
          if (order_henrei.selected) {
            postData.ids.push(order_henrei.id);
          }
        }
        if (!postData.ids.length) {
          return;
        }
        postData.order_status = order_status;

        this.processing = true;
        this.loaderSvc.call();
        const chk = await this.checkOrderStatus(postData.ids);
        if (!chk) {
          this.sessionSvc.save('message', {
            type: 'warning', title: 'データが更新されております。',
            msg: '最新情報を取得し直してから、再度処理を行ってください。'
          });
          this.processing = false;
          this.loaderSvc.dismiss();
          return;
        }
        await this.httpClientService.updateOrderHenreiStatus(postData)
          .then(async (response) => {
            Utils.log(response);
            await this.searchOrderHenrei();
            this.sessionSvc.save('message', { type: 'info', title: '発注状況を更新しました。' });
            this.processing = false;
          })
          .catch(error => {
            this.sessionSvc.save('message', { type: 'error', title: '発注状況の更新が失敗しました。' });
            this.processing = false;
            this.loaderSvc.dismiss();
          });
      }
    });
  }

  async checkOrderStatus(ids: Array<number>): Promise<boolean> {
    let ret = true;
    const request = this.getRequest();
    await this.httpClientService.getOrderHenreiList(request).then((response) => {
      Utils.log(response);
      for (const id of ids) {
        const order_henrei = response.find(v => v.id === id);
        const order_henrei_old = this.order_henrei_list.find(v => v.id === id);
        if (!order_henrei || !order_henrei_old ||
          order_henrei.order_status !== order_henrei_old.order_status) {
          ret = false;
          break;
        }
      }

    }).catch(error => {
      ret = false;
    });
    return ret;
  }
  baseCheck(order_henrei: OrderHenreiGetResponse) {
    if (!order_henrei) {
      return false;
    }
    if (this.login_company.base_type === this.Const.BASE_TYPE_ADMIN) {
      return false;
    }
    return true;

  }

  canOrder(order_henrei: OrderHenreiGetResponse) {
    if (!this.baseCheck(order_henrei)) {
      return false;
    }
    if (order_henrei.order_status !== this.Const.ORDER_STATUS_ID_UNORDER) {
      return false;
    }
    return true;

  }

  buttonDisabled(order_status) {
    if (!this.order_henrei_list || !this.order_henrei_list.length) {
      return true;
    }
    let count = 0;
    for (const order_henrei of this.order_henrei_list) {
      if (order_henrei.selected) {
        count++;
        if (!this.baseCheck(order_henrei)) {
          return true;
        }
        if (order_henrei.order_status !== order_status) {
          return true;
        }
      }
    }
    if (!count) {
      return true;
    }
    return false;

  }


  showSendFax(order_henrei: OrderHenreiGetResponse) {
    if (!order_henrei) {
      return;
    }
    this.selected_order_henrei = JSON.parse(JSON.stringify(order_henrei));
    this.pdfViewerEm.pdfSrc = '';
    this.pdfViewerEm.refresh();
    this.fax_pdf_loading = true;
    this.httpClientService.downloadOrderHenreiPdf(this.selected_order_henrei.id)
      .then((response) => {
        this.pdfViewerEm.pdfSrc = response;
        this.pdfViewerEm.refresh();
        this.fax_pdf_loading = false;
      }).catch(error => {
        this.fax_pdf_loading = false;
      });
    $('#send-fax').modal({
      centered: false,
      closable: false,
      detachable: false,
      transition: 'fade'
    }).modal('show');
  }

  faxPDF() {
    this.sessionSvc.clear('message');
    if (!this.selected_order_henrei) {
      return;
    }
    if (this.processing) {
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        msg1: '発注書を送信します、よろしいですか？',
      },
      width: '100%',
      maxWidth: '450px',
      maxHeight: '90%'
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result === true) {

        $('#send-fax').modal('hide');
        this.processing = true;
        this.loaderSvc.call();
        const chk = await this.checkOrderStatus([this.selected_order_henrei.id]);
        if (!chk) {
          this.sessionSvc.save('message', {
            type: 'warning', title: 'データが更新されております。',
            msg: '最新情報を取得し直してから、再度処理を行ってください。'
          });
          setTimeout(() => {
            this.selected_order_henrei = null;
          }, 500);
          this.processing = false;
          this.loaderSvc.dismiss();
          return;
        }
        this.httpClientService.faxOrderHenreiPdf(this.selected_order_henrei.id)
          .then(async (response) => {
            Utils.log(response);
            await this.searchOrderHenrei();
            setTimeout(() => {
              this.selected_order_henrei = null;
            }, 500);
            this.sessionSvc.save('message', { type: 'info', title: '発注書を送信しました。' });
            this.processing = false;
          })
          .catch(error => {
            setTimeout(() => {
              this.searched = null;
            }, 500);
            this.sessionSvc.save('message', { type: 'error', title: '発注書の送信が失敗しました。' });
            this.processing = false;
            this.loaderSvc.dismiss();
          });
      }
    });
  }


  faxPDF1(id) {
    this.sessionSvc.clear('message');
    if (!id) {
      return;
    }
    if (this.processing) {
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        msg1: '発注書を送信します、よろしいですか？',
      },
      width: '100%',
      maxWidth: '450px',
      maxHeight: '90%'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {

        this.processing = true;
        this.loaderSvc.call();
        this.httpClientService.faxOrderHenreiPdf(id)
          .then(async (response) => {
            Utils.log(response);
            await this.searchOrderHenrei();
            this.sessionSvc.save('message', { type: 'info', title: '発注書を送信しました。' });
            this.processing = false;
          })
          .catch(error => {
            this.sessionSvc.save('message', { type: 'error', title: '発注書の送信が失敗しました。' });
            this.processing = false;
            this.loaderSvc.dismiss();
          });
      }
    });
  }
}
