import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ComTimeInputComponent } from './com-time-input.component';

describe('ComTimeInputComponent', () => {
  let component: ComTimeInputComponent;
  let fixture: ComponentFixture<ComTimeInputComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ComTimeInputComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ComTimeInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
