import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SitepolicyComponent } from './sitepolicy.component';

describe('SitepolicyComponent', () => {
  let component: SitepolicyComponent;
  let fixture: ComponentFixture<SitepolicyComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SitepolicyComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SitepolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
