
@import "src/assets/scss/company/setting";

.open-window-dialog {
  .mat-dialog-container {
    .cancelButtonWrapper {
      .cancelButton {
        background: $lable-text-light;
        &::before {
          background: $lable-text-light;
        }
      }
    }
  }
}
.cancelButtonWrapper {
  height: 20px;
  width: 20px;
  margin: -25px 2px 10px auto;
  cursor: pointer;
  .cancelButton {
    display: inline-block;
    position: relative;
    margin: 0 20px 0 7px;
    padding: 0;
    width: 2px;
    height: 20px;
    background: $lable-text-light;
    transform: rotate(45deg);
    &::before {
      display: block;
      content: "";
      position: absolute;
      top: 50%;
      left: -9px;
      width: 20px;
      height: 2px;
      margin-top: -1px;
      background: $lable-text-light;
    }
  }
  &:hover {
    opacity: .8;
  }
}
