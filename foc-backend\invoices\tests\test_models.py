from django.test import TestCase

from invoices.models import SalesCompany
from invoices.tests.factories import SalesCompanyFactory
from staffs.models import Staff
from staffs.tests.factories import StaffFactory


class SalesCompanyModelTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

        self.sales_company: SalesCompany = SalesCompanyFactory(
            confirm_ts=None, confirm_staff_id=None, confirm_staff_name=None
        )

    def test_fix_invoice(self) -> None:
        """売上集計を確定する"""
        staff: Staff = StaffFactory()

        self.sales_company.fix_invoice(staff)
        self.sales_company.refresh_from_db()
        self.assertIsNotNone(self.sales_company.confirm_ts)
        self.assertEqual(self.sales_company.confirm_staff_id, staff.pk)
        self.assertEqual(self.sales_company.confirm_staff_name, staff.name)

        # 確定済みのものには再確定できない
        with self.assertRaises(ValueError):
            self.sales_company.fix_invoice(staff)
