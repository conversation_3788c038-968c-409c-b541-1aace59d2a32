import os
from tempfile import NamedTemporaryFile

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from django.test import TestCase

import utils.jwt_keys as jwt_keys


def rsa_to_bytes(private_key: rsa.RSAPrivateKeyWithSerialization) -> bytes:
    """RSA秘密鍵をバイト文字列に変換します

    Args:
        private_key (rsa.RSAPrivateKeyWithSerialization): RSA秘密鍵インスタンス

    Returns:
        bytes: バイト文字列に変換したRSA秘密鍵
    """
    return private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.TraditionalOpenSSL,
        encryption_algorithm=serialization.NoEncryption(),
    )


class JwtKeysTest(TestCase):
    def assertEqualRSAPrivateKey(self, first, second):
        self.assertEqual(rsa_to_bytes(first), rsa_to_bytes(second))

    def test_generate_private_key(self):
        """RSA秘密鍵を自動生成する"""
        priv_key: rsa.RSAPrivateKeyWithSerialization = jwt_keys._generate_private_key()
        self.assertIsInstance(priv_key, rsa.RSAPrivateKey)
        public_key: rsa.RSAPublicKey = priv_key.public_key()
        self.assertIsInstance(public_key, rsa.RSAPublicKey)

    def test_fetch_private_key(self):
        """秘密鍵ファイルを取得する"""
        with self.assertLogs():
            generated_key = jwt_keys.fetch_private_key(path_or_name=None)
            self.assertIsInstance(generated_key, rsa.RSAPrivateKeyWithSerialization)

        key_file = NamedTemporaryFile(delete=False)
        key_file.write(rsa_to_bytes(generated_key))
        key_file.close()
        from_file_key = jwt_keys.fetch_private_key(path_or_name=key_file.name)
        os.remove(key_file.name)
        self.assertEqualRSAPrivateKey(from_file_key, generated_key)
