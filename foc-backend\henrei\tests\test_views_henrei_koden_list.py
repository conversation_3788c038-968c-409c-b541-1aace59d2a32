from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from henrei.tests.factories import HenreihinKodenFactory
from items.tests.factories import ItemFactory
from orders.models import EntryDetailKoden
from orders.tests.factories import EntryDetailKodenFactory
from seko.models import Moshu
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class HenreiKodenCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.henrei_koden_data = HenreihinKodenFactory.build(
            detail_koden=EntryDetailKodenFactory(),
            henreihin=ItemFactory(),
            supplier=SupplierFactory(),
            select_status=1,
        )
        self.moshu = MoshuFactory(seko=self.henrei_koden_data.detail_koden.entry_detail.entry.seko)

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'detail_koden': self.henrei_koden_data.detail_koden.pk,
            'henreihin': self.henrei_koden_data.henreihin.pk,
            'henreihin_hinban': self.henrei_koden_data.henreihin_hinban,
            'henreihin_name': self.henrei_koden_data.henreihin_name,
            'henreihin_price': self.henrei_koden_data.henreihin_price,
            'henreihin_tax': self.henrei_koden_data.henreihin_tax,
            'henreihin_tax_pct': self.henrei_koden_data.henreihin_tax_pct,
            'keigen_flg': self.henrei_koden_data.keigen_flg,
            'supplier': self.henrei_koden_data.supplier.pk,
        }

    def test_henrei_koden_create_succeed(self) -> None:
        """返礼品香典を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_koden_list'),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertEqual(record['henreihin'], self.henrei_koden_data.henreihin.pk)
        self.assertEqual(record['henreihin_hinban'], self.henrei_koden_data.henreihin_hinban)
        self.assertEqual(record['henreihin_name'], self.henrei_koden_data.henreihin_name)
        self.assertEqual(record['henreihin_price'], self.henrei_koden_data.henreihin_price)
        self.assertEqual(record['henreihin_tax'], self.henrei_koden_data.henreihin_tax)
        self.assertEqual(record['henreihin_tax_pct'], self.henrei_koden_data.henreihin_tax_pct)
        self.assertEqual(record['keigen_flg'], self.henrei_koden_data.keigen_flg)
        self.assertFalse(record['customer_self_select_flg'])
        self.assertEqual(record['select_status'], 1)
        self.assertIsNone(record['henrei_order'])
        self.assertEqual(record['supplier']['id'], self.henrei_koden_data.supplier.pk)
        self.assertEqual(record['order_status'], 0)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

    def test_henrei_koden_create_fail_by_notfound(self) -> None:
        """返礼品香典追加APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_detail_koden: EntryDetailKoden = EntryDetailKodenFactory.build()
        params: Dict = self.basic_params()
        params['detail_koden'] = non_saved_detail_koden.pk
        response = self.api_client.post(
            reverse('henrei:henrei_koden_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail_koden'])

    def test_henrei_koden_create_fail_by_seko_disabled(self) -> None:
        """返礼品香典追加APIが所属する施行が無効になっているため失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.henrei_koden_data.detail_koden.entry_detail.entry.seko.disable()

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_koden_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_koden_create_deny_from_another_moshu(self) -> None:
        """返礼品香典追加APIが他の喪主からの呼び出しを拒否する"""
        another_moshu: Moshu = MoshuFactory()
        refresh = RefreshToken.for_user(another_moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_koden_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_koden_create_deny_from_staff(self) -> None:
        """返礼品香典追加APIが担当者からの呼び出しを拒否する"""
        staff = StaffFactory()
        refresh = RefreshToken.for_user(staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_koden_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_henrei_koden_create_failed_without_auth(self) -> None:
        """返礼品香典追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('henrei:henrei_koden_list'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
