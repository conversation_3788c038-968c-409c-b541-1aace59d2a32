import factory
import faker
from factory.django import DjangoModelFactory, ImageField
from factory.fuzzy import Fu<PERSON>Cho<PERSON>, FuzzyInteger

from masters.models import (
    ChobunDaishiMaster,
    ChobunSample,
    FuhoSampleMaster,
    HenreihinParameter,
    HoyoDefaultMaster,
    HoyoSampleMaster,
    HoyoStyle,
    KodenParameter,
    PaymentType,
    Relationship,
    Schedule,
    SekoStyle,
    Service,
    Tax,
    TermUnitType,
    Wareki,
    ZipCode,
)

fake_provider = faker.Faker('ja_JP')


class ScheduleFactory(DjangoModelFactory):
    class Meta:
        model = Schedule
        django_get_or_create = ['id']

    id = factory.Sequence(lambda n: n)
    name = factory.Faker('word', locale='ja_JP')
    required_flg = True


class HoyoStyleFactory(DjangoModelFactory):
    class Meta:
        model = HoyoStyle

    id = factory.Sequence(lambda n: n)
    name = factory.Faker('word', locale='ja_JP')
    wareki_use_flg = FuzzyChoice([True, False])


class SekoStyleFactory(DjangoModelFactory):
    class Meta:
        model = SekoStyle

    id = factory.Sequence(lambda n: n)
    hoyo_style = factory.SubFactory(HoyoStyleFactory)
    name = factory.Faker('word', locale='ja_JP')
    wareki_use_flg = FuzzyChoice([True, False])


class ZipCodeFactory(DjangoModelFactory):
    class Meta:
        model = ZipCode

    id = factory.Sequence(lambda n: n)
    zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    prefecture = factory.Faker('prefecture', locale='ja_JP')
    address_1 = factory.Faker('city', locale='ja_JP')
    address_2 = factory.Faker('town', locale='ja_JP')


class WarekiFactory(DjangoModelFactory):
    class Meta:
        model = Wareki

    id = factory.Sequence(lambda n: n)
    name = factory.Faker('word', locale='ja_JP')
    begin_date = factory.Faker('past_date')
    end_date = factory.Faker('future_date')
    minus_years = FuzzyInteger(1, 100)


class ServiceFactory(DjangoModelFactory):
    class Meta:
        model = Service
        django_get_or_create = ['id']

    id = factory.Sequence(lambda n: n)
    name = factory.Faker('word', locale='ja_JP')
    single_item_flg = FuzzyChoice([True, False])
    cart_flg = FuzzyChoice([True, False])
    payment_flg = FuzzyChoice([True, False])


class TaxFactory(DjangoModelFactory):
    class Meta:
        model = Tax

    id = factory.Sequence(lambda n: n)
    tax_pct = FuzzyInteger(1, 100)
    keigen_flg = FuzzyChoice([True, False])


class FuhoSampleMasterFactory(DjangoModelFactory):
    class Meta:
        model = FuhoSampleMaster

    id = factory.Sequence(lambda n: n)
    sentence = factory.Faker('sentence', locale='ja_JP')


class ChobunDaishiMasterFactory(DjangoModelFactory):
    class Meta:
        model = ChobunDaishiMaster

    id = factory.Sequence(lambda n: n)
    name = factory.Faker('word', locale='ja_JP')
    file_name = ImageField()


class ChobunSampleFactory(DjangoModelFactory):
    class Meta:
        model = ChobunSample

    id = factory.Sequence(lambda n: n)
    type_id = FuzzyInteger(1, 100)
    name = factory.Faker('word', locale='ja_JP')
    sentence = factory.Faker('sentence', locale='ja_JP')


class RelationshipFactory(DjangoModelFactory):
    class Meta:
        model = Relationship

    id = factory.Sequence(lambda n: n)
    name = factory.Faker('word', locale='ja_JP')


class PaymentTypeFactory(DjangoModelFactory):
    class Meta:
        model = PaymentType

    id = factory.Sequence(lambda n: n)
    name = factory.Faker('word', locale='ja_JP')
    commission_pct = factory.Faker('pyint', min_value=0, max_value=99)


class HenreihinParameterFactory(DjangoModelFactory):
    class Meta:
        model = HenreihinParameter

    id = factory.Sequence(lambda n: n)
    henreihin_omote_default = factory.Faker('sentence', locale='ja_JP')
    henreihin_mizuhiki_default = factory.Faker('sentence', locale='ja_JP')
    henreihin_hoso_default = factory.Faker('sentence', locale='ja_JP')


class KodenParameterFactory(DjangoModelFactory):
    class Meta:
        model = KodenParameter

    id = factory.Sequence(lambda n: n)
    koden_commission_tax_pct = factory.Faker('pyint', min_value=0, max_value=20)
    koden_upper_limit = factory.Faker('pyint', min_value=0, max_value=100000)


class HoyoDefaultMasterFactory(DjangoModelFactory):
    class Meta:
        model = HoyoDefaultMaster

    id = factory.Sequence(lambda n: n)
    hoyo_style = factory.SubFactory(HoyoStyleFactory)
    name = factory.Faker('word', locale='ja_JP')
    elapsed_time = factory.Faker('pyint', min_value=0, max_value=30)
    unit = FuzzyChoice(choices=TermUnitType.values)


class HoyoSampleMasterFactory(DjangoModelFactory):
    class Meta:
        model = HoyoSampleMaster

    id = factory.Sequence(lambda n: n)
    sentence = factory.Faker('sentence', locale='ja_JP')
