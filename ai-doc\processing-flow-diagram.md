# FOCプロジェクト 処理フロー図

## 概要

このドキュメントでは、FOCプロジェクトにおけるフロントエンドからバックエンドまでの処理フローを図解します。ユーザーがURLをクリックしたり、保存操作を行った際の、ソースコードの読み込み順序と処理の流れを視覚的に理解できます。

## メイン処理フロー図

```mermaid
graph TD
    A[ユーザーがURLクリック/操作] --> B{ルーティング判定}
    B --> C[CompanyFrameComponent]
    B --> D[CustomerFrameComponent]
    B --> E[FamilyFrameComponent]
    
    C --> F[AuthFGuard認証チェック]
    F --> G{認証OK?}
    G -->|No| H[ログイン画面へリダイレクト]
    G -->|Yes| I[権限ガード実行]
    I --> J{権限OK?}
    J -->|No| K[403エラー]
    J -->|Yes| L[対象コンポーネント表示]
    
    L --> M[ngOnInit実行]
    M --> N[HttpClientService呼び出し]
    N --> O[JWT認証ヘッダー付与]
    O --> P[HTTP Request送信]
    
    P --> Q[Django URLディスパッチャー]
    Q --> R[対応するView特定]
    R --> S[ClassableJWTAuthentication]
    S --> T{JWT認証OK?}
    T -->|No| U[401 Unauthorized]
    T -->|Yes| V[権限クラス実行]
    V --> W{権限OK?}
    W -->|No| X[403 Forbidden]
    W -->|Yes| Y[ビューメソッド実行]
    
    Y --> Z[Serializer実行]
    Z --> AA[バリデーション]
    AA --> BB{バリデーションOK?}
    BB -->|No| CC[400 Bad Request]
    BB -->|Yes| DD[データベース操作]
    DD --> EE[レスポンス生成]
    
    EE --> FF[HTTP Response]
    FF --> GG[フロントエンドで受信]
    GG --> HH{成功?}
    HH -->|No| II[エラーハンドリング]
    HH -->|Yes| JJ[データ更新・画面更新]
    
    II --> KK[エラーメッセージ表示]
    JJ --> LL[ローディング終了]
    
    style A fill:#e1f5fe
    style Q fill:#fff3e0
    style DD fill:#f3e5f5
    style JJ fill:#e8f5e8
```

## 詳細フロー説明

### 1. フロントエンド層（青色エリア）

#### 1.1 ユーザー操作
- **URLクリック**: ナビゲーションリンクのクリック
- **ボタンクリック**: 保存、削除、検索等のボタン操作
- **フォーム送信**: データ入力後の送信操作

#### 1.2 ルーティング処理
```typescript
// app-routing.module.ts
const routes: Routes = [
  { path: 'foc/top', component: CompanyFrameComponent, canActivate: [AuthFGuard] },
  { path: 'fuho', component: CustomerFrameComponent },
  { path: 'soke/login', component: FamilyFrameComponent }
];
```

#### 1.3 フレームコンポーネント判定
- **CompanyFrameComponent**: 企業向け機能（`/foc/*`）
- **CustomerFrameComponent**: 顧客向け機能（`/fuho`, `/hoyo`等）
- **FamilyFrameComponent**: 家族向け機能（`/soke/*`）

### 2. 認証・権限層

#### 2.1 認証ガード
```typescript
// authF.guard.ts (スタッフ認証)
export class AuthFGuard implements CanActivate {
  canActivate(): boolean {
    const loginInfo = this.sessionSvc.getStaffLoginInfo();
    if (!loginInfo) {
      this.router.navigate(['foc/login']);
      return false;
    }
    return true;
  }
}
```

#### 2.2 権限ガード
- **AdminGuard**: 管理者権限チェック
- **StaffOnlyGuard**: スタッフ専用権限チェック
- **OrderGuard**: 注文権限チェック

### 3. コンポーネント層

#### 3.1 コンポーネント初期化
```typescript
// 例: user-profile-edit.component.ts
export class UserProfileEditComponent implements OnInit {
  async ngOnInit() {
    await this.loadUserProfile();
  }
  
  async loadUserProfile() {
    try {
      this.userProfile = await this.httpClientService.getUserProfile();
    } catch (error) {
      this.handleError(error);
    }
  }
}
```

### 4. API通信層

#### 4.1 HttpClientService
```typescript
// http-client.service.ts
export class HttpClientService {
  async getUserProfile(): Promise<UserProfileResponse> {
    await this.refreshTokenIfNeeded();
    const headers = this.getAuthHeaders();
    return this.http.get<UserProfileResponse>('/api/staffs/me/', { headers }).toPromise();
  }
  
  private getAuthHeaders(): HttpHeaders {
    const token = this.sessionSvc.getAccessToken();
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }
}
```

### 5. バックエンド層（オレンジ色エリア）

#### 5.1 Django URLディスパッチャー
```python
# foc/urls.py
urlpatterns = [
    path('staffs/', include('staffs.urls')),
    path('seko/', include('seko.urls')),
    # ...
]

# staffs/urls.py
urlpatterns = [
    path('me/', views.StaffMe.as_view(), name='me'),
    # ...
]
```

#### 5.2 認証処理
```python
# utils/authentication.py
class ClassableJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        user_class = validated_token.get('user_class')
        if user_class == 'staff':
            return Staff.objects.get(id=validated_token['user_id'])
        elif user_class == 'moshu':
            return Moshu.objects.get(id=validated_token['user_id'])
```

#### 5.3 権限チェック
```python
# utils/permissions.py
class IsStaff(BasePermission):
    def has_permission(self, request, view):
        return isinstance(request.user, Staff)
```

### 6. データ処理層（紫色エリア）

#### 6.1 ビューメソッド実行
```python
# staffs/views.py
class StaffMe(generics.RetrieveAPIView):
    serializer_class = StaffParentBasesSerializer
    permission_classes = [IsStaff]
    
    def get_object(self):
        return self.request.user
```

#### 6.2 シリアライザー処理
```python
# staffs/serializers.py
class StaffSerializer(serializers.ModelSerializer):
    class Meta:
        model = Staff
        fields = ['id', 'name', 'mail_address', 'phone']
    
    def validate_mail_address(self, value):
        if Staff.objects.filter(mail_address=value).exclude(id=self.instance.id).exists():
            raise serializers.ValidationError('このメールアドレスは既に使用されています。')
        return value
```

### 7. レスポンス処理層（緑色エリア）

#### 7.1 成功時の処理
```typescript
// フロントエンドでの成功処理
async saveUserProfile() {
  try {
    const response = await this.httpClientService.updateUserProfile(this.userProfile);
    this.showSuccessMessage('プロフィールを更新しました。');
    this.router.navigate(['/foc/top']);
  } catch (error) {
    this.handleError(error);
  }
}
```

#### 7.2 エラー処理
```typescript
// エラーハンドリング
private handleError(error: any) {
  if (error.status === 400) {
    // バリデーションエラー
    this.errors = error.error;
  } else if (error.status === 401) {
    // 認証エラー
    this.sessionSvc.clearAll();
    this.router.navigate(['foc/login']);
  } else {
    // その他のエラー
    this.showErrorMessage('エラーが発生しました。');
  }
}
```

## ファイル読み込み順序

### フロントエンド
1. `app-routing.module.ts` - ルーティング設定
2. `*.guard.ts` - 認証・権限ガード
3. `frame.component.ts` - フレームコンポーネント
4. `*.component.ts` - 対象コンポーネント
5. `http-client.service.ts` - API通信サービス
6. `session.service.ts` - セッション管理

### バックエンド
1. `foc/urls.py` - ルートURL設定
2. `{app}/urls.py` - アプリ別URL設定
3. `utils/authentication.py` - 認証クラス
4. `utils/permissions.py` - 権限クラス
5. `{app}/views.py` - ビュークラス
6. `{app}/serializers.py` - シリアライザー
7. `{app}/models.py` - モデル

## 具体的な処理例

### 例1: ユーザープロフィール編集
```mermaid
sequenceDiagram
    participant U as ユーザー
    participant C as Component
    participant H as HttpClient
    participant V as View
    participant S as Serializer
    participant D as Database
    
    U->>C: 保存ボタンクリック
    C->>H: updateUserProfile()
    H->>V: PUT /api/staffs/me/
    V->>S: validate()
    S->>D: save()
    D->>S: success
    S->>V: serialized data
    V->>H: 200 OK
    H->>C: response
    C->>U: 成功メッセージ表示
```

### 例2: 施行一覧表示
```mermaid
sequenceDiagram
    participant U as ユーザー
    participant C as Component
    participant H as HttpClient
    participant V as View
    participant D as Database
    
    U->>C: /foc/seko アクセス
    C->>H: getSekoList()
    H->>V: GET /api/seko/
    V->>D: filter & query
    D->>V: seko list
    V->>H: 200 OK + data
    H->>C: response
    C->>U: 施行一覧表示
```

この処理フロー図を参考に、新機能開発時の処理の流れを設計し、デバッグ時の問題箇所特定にご活用ください。
