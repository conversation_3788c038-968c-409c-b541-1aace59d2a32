from datetime import date
from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from henrei.tests.factories import (
    HenreihinKodenFactory,
    HenreihinKumotsuFactory,
    OrderHenreihinFactory,
)
from masters.tests.factories import ScheduleFactory
from orders.tests.factories import (
    EntryDetailFactory,
    EntryDetailKodenFactory,
    EntryDetailKumotsuFactory,
    EntryFactory,
)
from seko.tests.factories import KojinFactory, MoshuFactory, SekoFactory, SekoScheduleFactory
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

tz = timezone.get_current_timezone()
jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class OrderHenreiListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_1 = BaseFactory()
        self.base_2 = BaseFactory()

        self.seko_1 = SekoFactory(seko_company=self.base_1)
        self.seko_2 = SekoFactory(seko_company=self.base_2)
        self.kojin_1 = KojinFactory(kojin_num=1, seko=self.seko_1)
        self.kojin_2 = KojinFactory(kojin_num=1, seko=self.seko_2)
        self.moshu_1 = MoshuFactory(seko=self.seko_1)
        self.moshu_2 = MoshuFactory(seko=self.seko_2)
        soshiki_schedule = ScheduleFactory(id=2)
        self.schedule_1 = SekoScheduleFactory(seko=self.seko_1, schedule=soshiki_schedule)
        self.schedule_2 = SekoScheduleFactory(seko=self.seko_2, schedule=soshiki_schedule)
        self.entry_1 = EntryFactory(seko=self.seko_1)
        self.entry_2 = EntryFactory(seko=self.seko_2)

        entry_detail_11 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_12 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_13 = EntryDetailFactory(entry=self.entry_1)
        entry_detail_21 = EntryDetailFactory(entry=self.entry_2)
        entry_detail_22 = EntryDetailFactory(entry=self.entry_2)

        self.kumotsu_11 = EntryDetailKumotsuFactory(entry_detail=entry_detail_11)
        self.kumotsu_13 = EntryDetailKumotsuFactory(entry_detail=entry_detail_13)
        self.kumotsu_21 = EntryDetailKumotsuFactory(entry_detail=entry_detail_21)

        self.koden_12 = EntryDetailKodenFactory(entry_detail=entry_detail_12)
        self.koden_22 = EntryDetailKodenFactory(entry_detail=entry_detail_22)

        self.supplier_1 = SupplierFactory(company=self.base_1)
        self.supplier_2 = SupplierFactory(company=self.base_1)

        self.order_11 = OrderHenreihinFactory(
            seko=self.seko_1, supplier=self.supplier_1, order_status=0, order_ts=None
        )
        self.order_12 = OrderHenreihinFactory(
            seko=self.seko_1,
            supplier=self.supplier_2,
            order_status=1,
            order_ts=tz.localize(timezone.datetime(2020, 12, 10, 12, 5)),
        )
        self.order_21 = OrderHenreihinFactory(
            seko=self.seko_2, supplier=self.supplier_2, order_status=0, order_ts=None
        )
        self.order_22 = OrderHenreihinFactory(
            seko=self.seko_2,
            supplier=self.supplier_1,
            order_status=2,
            order_ts=tz.localize(timezone.datetime(2020, 12, 1, 14, 32)),
        )

        self.henrei_kumotsu_11 = HenreihinKumotsuFactory(
            detail_kumotsu=self.kumotsu_11,
            supplier=self.supplier_1,
            henrei_order=self.order_11,
            order_status=0,
        )
        self.henrei_kumotsu_13 = HenreihinKumotsuFactory(
            detail_kumotsu=self.kumotsu_13,
            supplier=self.supplier_2,
            henrei_order=self.order_12,
            order_status=1,
        )
        self.henrei_kumotsu_21 = HenreihinKumotsuFactory(
            detail_kumotsu=self.kumotsu_21,
            supplier=self.supplier_2,
            henrei_order=self.order_21,
            order_status=0,
        )

        self.henrei_koden_12 = HenreihinKodenFactory(
            detail_koden=self.koden_12,
            supplier=self.supplier_2,
            henrei_order=self.order_11,
            order_status=0,
        )
        self.henrei_koden_22 = HenreihinKodenFactory(
            detail_koden=self.koden_22,
            supplier=self.supplier_1,
            henrei_order=self.order_22,
            order_status=2,
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_order_henrei_list_succeed(self) -> None:
        """返礼品発注一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

        for record in records:
            if record['id'] == self.order_12.pk:
                self.assertEqual(record['seko'], self.seko_1.pk)
                self.assertEqual(record['seko_date'], self.seko_1.seko_date.isoformat())
                self.assertEqual(
                    record['hall_name'], self.seko_1.schedules.get(schedule_id=2).hall_name
                )
                self.assertEqual(record['soke_name'], self.seko_1.soke_name)
                self.assertEqual(record['kojin_name'], self.seko_1.kojin.get(kojin_num=1).name)
                self.assertEqual(record['moshu_name'], self.seko_1.moshu.name)
                self.assertEqual(record['order_status'], self.order_12.order_status)
                self.assertEqual(record['order_ts'], self.order_12.order_ts.isoformat())
                self.assertEqual(record['order_note'], self.order_12.order_note)
                self.assertEqual(record['supplier_name'], self.supplier_2.name)
                self.assertEqual(record['henrei_koden'], [])
                self.assertEqual(len(record['henrei_kumotsu']), 1)
                henrei_kumotsu_dict: Dict = record['henrei_kumotsu'][0]
                self.assertEqual(henrei_kumotsu_dict['detail_kumotsu'], self.henrei_kumotsu_13.pk)
                self.assertEqual(
                    henrei_kumotsu_dict['henreihin_hinban'],
                    self.henrei_kumotsu_13.henreihin_hinban,
                )
                self.assertEqual(
                    henrei_kumotsu_dict['henreihin_name'], self.henrei_kumotsu_13.henreihin_name
                )
                self.assertEqual(
                    henrei_kumotsu_dict['henreihin_price'], self.henrei_kumotsu_13.henreihin_price
                )
                self.assertEqual(
                    henrei_kumotsu_dict['henreihin_tax'], self.henrei_kumotsu_13.henreihin_tax
                )
                self.assertEqual(
                    henrei_kumotsu_dict['henreihin_tax_pct'],
                    self.henrei_kumotsu_13.henreihin_tax_pct,
                )
            if record['id'] == self.order_22.pk:
                self.assertEqual(len(record['henrei_koden']), 1)
                henrei_koden_dict: Dict = record['henrei_koden'][0]
                self.assertEqual(henrei_koden_dict['detail_koden'], self.henrei_koden_22.pk)
                self.assertEqual(
                    henrei_koden_dict['henreihin_hinban'],
                    self.henrei_koden_22.henreihin_hinban,
                )
                self.assertEqual(
                    henrei_koden_dict['henreihin_name'], self.henrei_koden_22.henreihin_name
                )
                self.assertEqual(
                    henrei_koden_dict['henreihin_price'], self.henrei_koden_22.henreihin_price
                )
                self.assertEqual(
                    henrei_koden_dict['henreihin_tax'], self.henrei_koden_22.henreihin_tax
                )
                self.assertEqual(
                    henrei_koden_dict['henreihin_tax_pct'],
                    self.henrei_koden_22.henreihin_tax_pct,
                )
                self.assertEqual(record['henrei_kumotsu'], [])

    def test_order_henrei_list_filter_by_seko_company_id(self) -> None:
        """返礼品発注一覧を葬儀社IDで絞り込む"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'seko_company_id': self.base_1.pk}
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_order_henrei_list_filter_by_supplier_id(self) -> None:
        """返礼品発注一覧を発注先IDで絞り込む"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'supplier_id': self.supplier_2.pk}
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

    def test_order_henrei_list_filter_by_pk(self) -> None:
        """返礼品発注一覧を発注先IDで絞り込む"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'order_henrei_id': self.order_11.pk}
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_order_henrei_list_filter_by_order_status(self) -> None:
        """返礼品発注一覧を発注ステータスで絞り込む"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'order_status': 2}
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

    def test_order_henrei_list_filter_by_order_ts(self) -> None:
        """返礼品発注一覧を発注日時(範囲指定)で絞り込む"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # Fromのみ
        params: Dict = {'order_ts_from': date(2020, 12, 3)}
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        # Toのみ
        params: Dict = {'order_ts_to': date(2020, 12, 7)}
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)

        # FromとTo
        params: Dict = {
            'order_ts_from': date(2020, 12, 3),
            'order_ts_to': date(2020, 12, 7),
        }
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 0)

        # JSTの午前9時以前がJSTの日付で検索できるか
        self.order_22.order_ts = tz.localize(timezone.datetime(2020, 12, 1, 8, 32))
        self.order_22.save()
        params: Dict = {
            'order_ts_from': date(2020, 12, 1),
            'order_ts_to': date(2020, 12, 1),
        }
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)
        self.assertEqual(records[0]['id'], self.order_22.pk)

    def test_order_henrei_list_failed_without_auth(self) -> None:
        """返礼品発注一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('henrei:order_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
