from typing import Dict

from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from staffs.models import Staff
from staffs.tests.factories import DEFAULT_PASSWORD, StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


class StaffCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory()
        self.staff_data = StaffFactory.build(base=base)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'base': self.staff_data.base.pk,
            'company_code': self.staff_data.company_code,
            'name': self.staff_data.name,
            'login_id': self.staff_data.login_id,
            'password': DEFAULT_PASSWORD,
            'mail_address': self.staff_data.mail_address,
            'retired_flg': self.staff_data.retired_flg,
            'fdn_code': self.staff_data.fdn_code,
        }

    def test_staff_create_succeed(self) -> None:
        """担当者を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('staffs:staff_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('id'))
        self.assertEqual(record.get('company_code'), self.staff_data.base.company_code)
        self.assertEqual(record.get('login_id'), self.staff_data.login_id)
        self.assertEqual(record.get('name'), self.staff_data.name)
        base_rec: Dict = record.get('base', {})
        self.assertEqual(base_rec.get('id'), self.staff_data.base.pk)
        self.assertEqual(base_rec.get('base_type'), self.staff_data.base.base_type)
        self.assertEqual(base_rec.get('company_code'), self.staff_data.base.company_code)
        self.assertEqual(record.get('base_name'), self.staff_data.base.base_name)
        self.assertEqual(record.get('mail_address'), self.staff_data.mail_address)
        self.assertEqual(record.get('retired_flg'), self.staff_data.retired_flg)
        self.assertEqual(record.get('create_user_id'), self.staff.pk)
        self.assertIsNotNone(record.get('created_at'))
        self.assertEqual(record.get('update_user_id'), self.staff.pk)
        self.assertIsNotNone(record.get('updated_at'))

        staff_rec: Staff = Staff.objects.get(pk=record['id'])
        self.assertTrue(staff_rec.check_password(DEFAULT_PASSWORD))

    def test_staff_create_fail_by_duplicate(self) -> None:
        """担当者追加APIがcompany_codeとlogin_idの組み合わせ重複で失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        # company_codeが同じだが別の拠点に所属
        StaffFactory(
            base=BaseFactory(company_code=self.staff_data.base.company_code),
            login_id=self.staff_data.login_id,
        )
        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('staffs:staff_list'), data=params, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['non_field_errors'])

    def test_staff_create_failed_without_auth(self) -> None:
        """担当者追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}

        response = self.api_client.post(reverse('staffs:staff_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
