/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { SekoComponent } from './seko.component';

describe('SekoComponent', () => {
  let component: SekoComponent;
  let fixture: ComponentFixture<SekoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SekoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SekoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
