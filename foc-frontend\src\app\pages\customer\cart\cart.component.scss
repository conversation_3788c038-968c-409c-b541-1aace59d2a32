@import "src/assets/scss/customer/setting";
.container .inner .contents {
  &.empty {
    padding-top: 20px;
  }
  table {
    min-width: 400px;
    margin: 0 auto;
    font-size: 1.1rem;
    border-collapse: collapse;
    tr {
      &.divided {
        border-bottom: solid 1px $border-grey;
        &:last-child {
          border-bottom-width: 2px;
        }
      }
      
    }
    td {
      vertical-align: top;
      text-align: left;
      padding-top: 6px;
      padding-bottom: 6px;
      &.name {
        min-width: 150px;
        padding-left: 15px;
        .item_name {
          font-size: 1rem;
          &.remark::after {
            margin-left: 2px;
            content: '*';            
          }
        }
        .system_name {
          font-size: 1rem;
        }
      }
      &.quantity {
        min-width: 80px;
      }
      &.price {
        min-width: 100px;
        text-align: right;
        .system_price {
          font-size: 1rem;
        }
      }
      &.operate {
        width: 100px;
        padding-left: 30px;
        &.sp {
          display: none;
        }
        .button-area {
          margin-bottom: 0;
          font-size: 0.8rem;
          justify-content: flex-end;
          .button {
            width: 60px;
            height: 25px;
            line-height: 21px;
            margin-right: 10px;
            border-radius: 30px;
            &.grey.light {
              background-color: $label-light;
              color: black;
            }
          }
        }
        @media screen and (max-width: 560px) {
          &.sp {
            display: table-cell;
          }
          &.pc {
            display: none;
          }
        }
      }
      &.expired {
        padding-left: 15px;
        font-size: 0.7rem;
        color: $text-red;
      }
    }
    &.summary {
      tr{
        border-bottom: none;
        font-size: 0.95rem;
        td.summary_name {
          position: relative;
          min-width: 200px;
          padding-left: 100px;
          &.remark::before {
            position: absolute;
            left: 90px;
            content: '*';            
          }
        }
        td.price {
          padding-right: 10px;
        }
        &.total {
          .summary_name {
            font-size: 1.2rem;
          }
          .price {
            font-size: 1.1rem;
          }
        }
      }
    }
    @media screen and (max-width: 560px) {
      min-width: 90%;
      td {
        &.name {
          min-width: 100px;
          padding-left: 15px;
          .item_name {
            font-size: 1rem;
          }
        }
        &.quantity {
          min-width: 60px;
        }
        &.price {
          min-width: 70px;
          text-align: right;
        }
        &.operate {
          width: 80px;
        }
      }
      &.summary {
        tr{
          border-bottom: none;
          font-size: 0.9rem;
          td.summary_name {
            padding-left: 50px;
            &.remark::before {
              left: 42px;       
            }
          }
          td.price {
            padding-right: 10px;
          }
          &.total {
            .summary_name {
              font-size: 1.1rem;
            }
            .price {
              font-size: 1.0rem;
            }
          }
        }
      }
    }

  }
}
.container .inner >.button-area {
  max-width: calc(100% - 40px);
  margin: auto;
  display: block;
  width: 350px;
  .button {
    width: 100%;
    margin: 0 auto 10px;
  }
}
