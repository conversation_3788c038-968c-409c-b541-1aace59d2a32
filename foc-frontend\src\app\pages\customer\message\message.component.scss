@import "src/assets/scss/customer/setting";
.container .inner .contents {
  &.with-background-image {
    background-image: url(../../../../assets/img/customer/phone.jpg);
  }
  &.description >div {
    max-width: 750px;
  }
  .remark {
    font-size: 1rem;
    line-height: 1.5;
  }
  .seko_company {
    font-size: 1rem;
    line-height: 1.5;
  }
  .input .button.grey {
    width: 180px;
  }
  .input-area {
    max-width: 600px;
    padding-top: 30px;
    .line .label {
      min-width: 168px;

    }
    .letter {
      text-align: right;
      line-height: 1;
      font-size: 0.8rem;
    }
  }
}
.container .inner {
  >.button-area .button.grey {
    width: 200px;
    @media screen and (max-width: 560px) {
      width: 180px;
    }
    @media screen and (max-width: 380px) {
      width: 150px;
    }
  }
}