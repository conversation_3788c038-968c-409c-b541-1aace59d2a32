
<div class="container">
  <div class="inner with_footer">
    <div class="contents">
      <div class="menu_title">
        <i class="calendar alternate icon big"></i>
        <i class="list icon combo"></i>
        イベント案内履歴
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(dateFromEm, dateToEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchEventMail()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>送信日</label>
          <com-calendar #dateFromEm [settings]="calendarOptionDate" id="send_date_from" [(value)]="form_data.send_date_from"></com-calendar>
          <label class="plane">～</label>
          <com-calendar #dateToEm [settings]="calendarOptionDate" id="send_date_to" [(value)]="form_data.send_date_to"></com-calendar>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="event_mail_list?.length">
          全{{event_mail_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?event_mail_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="event_name">イベント名</th>
              <th class="select_date">抽出日</th>
              <th class="send_date">送信日</th>
              <th class="mail_content">メール本文</th>
              <th class="note">備考</th>
              <th class="staff">担当</th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body" [class.no-page-nav]="pagination.pages.length===1">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let event_mail of event_mail_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" (click)="showItem($event, event_mail)">
              <td class="event_name" title="{{event_mail.event_name}}">{{event_mail.event_name}}</td>
              <td class="center aligned select_date" title="{{event_mail.select_ts | date: 'yyyy/MM/dd'}}">
                {{event_mail.select_ts | date: 'yyyy/MM/dd'}}</td>
              <td class="center aligned send_date" title="{{event_mail.send_ts | date: 'yyyy/MM/dd'}}">
                {{event_mail.send_ts | date: 'yyyy/MM/dd'}}</td>
              <td class="mail_content" title="{{event_mail.content}}">{{event_mail.content}}</td>
              <td class="note" title="{{event_mail.note}}">{{event_mail.note}}</td>
              <td class="staff" title="{{event_mail.staff?.name}}">{{event_mail.staff?.name}}</td>
            </tr>
            </ng-container>
            <tr *ngIf="!event_mail_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="6">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal" id="event-mail">
        <div class="header ui"><i class="mail bulk icon large"></i>イベント案内送信済一覧</div>
        <div class="content" *ngIf="selected_event_mail">
          <div class="input_area">
            <div class="line">
              <label class="large">イベント名</label>
              <label class="data" >{{selected_event_mail.event_name}}</label>
            </div>
            <div class="line">
              <label class="large">送信日時</label>
              <label class="data" >{{selected_event_mail.send_ts | date: 'yyyy/MM/dd H:mm'}}</label>
            </div>
            <div class="line">
              <label class="large mail_content">メール本文</label>
              <label class="data mail_content" >{{selected_event_mail.content}}</label>
            </div>
            <div class="line">
              <label class="large">備考</label>
              <label class="data note">{{selected_event_mail.note}}</label>
            </div>
          </div>
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!selected_event_mail?.inquiries?.length">
                  <th class="moshu_id">喪主ID</th>
                  <th class="moshu_name">氏名</th>
                  <th class="mobile_num">電話番号</th>
                  <th class="address">住所</th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body" *ngIf="selected_event_mail.targets">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngFor="let target of selected_event_mail.targets; index as i">
                <tr>
                  <td class="center aligned moshu_id" title="{{target.seko.id}}">{{target.seko.id}}</td>
                  <td class="moshu_name" title="{{target.seko.moshu.name}}">{{target.seko.moshu.name}}</td>
                  <td class="mobile_num" title="{{target.seko.moshu.mobile_num}}">{{target.seko.moshu.mobile_num}}</td>
                  <td class="address" title="{{target.seko.moshu.prefecture}}{{target.seko.moshu.address_1}}{{target.seko.moshu.address_2}}{{target.seko.moshu.address_3}}">
                    {{target.seko.moshu.prefecture}}{{target.seko.moshu.address_1}}{{target.seko.moshu.address_2}}{{target.seko.moshu.address_3}}
                  </td>
                </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini light cancel" (click)="closeItem()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
		</div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group">
    <div class="ui labeled icon button mini light" routerLink="/foc/event/filter" [class.disabled]="login_info.staff.base.base_type===Const.BASE_TYPE_COMPANY">
      <i class="filter icon"></i> イベント案内抽出
    </div>
  </div>
</div>
