
<div class="container">
  <div class="inner" *ngIf="!is_loading && (!hoyo_id || !hoyo_data)">
    <!--ログイン-->
    <div class="contents with-background-image top">
      <div class="hoyo_num">
        <div class="label">ご法要番号：</div>
        <div class="ui input hall-id tiny">
          <input #hallIdEm type="tel" oninput="value = value.replace(/[^0-9]+/i,'');" [(ngModel)]="hall_id">
        </div>
        <div>-</div>
        <div class="ui input hoyo-id tiny">
          <input #hoyoIdEm type="tel" oninput="value = value.replace(/[^0-9]+/i,'');" [(ngModel)]="hoyo_id">
        </div>
      </div>
      <div class="box01_02">
        <button class="ui button teal" (click)="getHoyo(hallIdEm, hoyoIdEm)">
          次へ
        </button>
      </div>
      <div class="box01_01" *ngIf="err_msg">
        <div class="error_msg">{{err_msg}}</div>
      </div>
    </div>
  </div>
  <div class="inner" *ngIf="hoyo_id && hoyo_data">
    <!--訃報-->
    <div class="contents with-background-image">
      <h2>ご案内</h2>
      <div class="box01_01">
      </div>
      <div class="box01_02">{{hoyo_data.hoyo_sentence}}</div>
    </div>

    <!--記-->
    <div class="contents with-background-image">
      <h2>記</h2>
      <div class="box02">
        <table>
          <tr class="box02_01">
            <th scope="col">日程</th>
            <td>
              {{hoyo_data.hoyo_activity_date|date:"M月d日"}}（{{utils.getWeekName(hoyo_data.hoyo_activity_date)}}）
              {{hoyo_data.display_begin_time}} 〜 {{hoyo_data.display_end_time}} 
            </td>
          </tr>
          <tr class="box02_01">
            <th scope="col">式場</th>
            <td>{{hoyo_data.hall.base_name}}</td>
          </tr>
          <tr>
            <td></td>
            <td>
              <div class="place">
                <p>〒{{zipCode(hoyo_data.hall.zip_code)}}</p>
                <p>{{hoyo_data.hall.prefecture}}{{hoyo_data.hall.address_1}}{{hoyo_data.hall.address_2}}{{hoyo_data.hall.address_3}}</p>
                <img class="map" *ngIf="hoyo_data.hall?.company_map_file" src="{{hoyo_data.hall.company_map_file}}">
                <p class="button map">
                  <a href="https://www.google.com/maps/search/?api=1&query={{hoyo_data.hall.prefecture}}{{hoyo_data.hall.address_1}}{{hoyo_data.hall.address_2}}{{hoyo_data.hall.address_3}}" target="_blank">
                    <pre>Google Mapで開く   ></pre>
                  </a>
                </p>
              </div>
            </td>
          </tr>
          <tr>
            <th scope="row">施主</th>
            <td>{{hoyo_data.seshu_name}}（{{hoyo_data.kojin_seshu_relationship}}）</td>
          </tr>
          <tr>
            <th scope="row">式次第</th>
            <td class="schedule-area" >
              <div class="schedule" *ngFor="let schedule of hoyo_data.schedules">
                <div class="schedule_name">{{schedule.schedule_name}}</div>
                <div class="schedule_date">
                  <div class="schedule_time">{{schedule.display_begin_time}} 〜 {{schedule.display_end_time}}</div>
                  <div>{{schedule.hall_name}}</div>
                </div>
              </div>

            </td>
          </tr>
        </table>
      </div>
    </div>

    <!--備考-->
    <div class="contents with-background-image" *ngIf="hoyo_data.note">
      <h2>備考</h2>
      <div class="box04">
        <p>{{hoyo_data.note}}</p>
      </div>
    </div>
    
	</div>
</div>
