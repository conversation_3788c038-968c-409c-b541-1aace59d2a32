from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from orders.tests.factories import EntryDetailFactory, EntryDetailMsgFactory, EntryFactory
from seko.tests.factories import KojinFactory, SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class MsgPDFViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.seko = SekoFactory()
        entry_1 = EntryFactory(seko=self.seko)
        KojinFactory(kojin_num=1, seko=self.seko)
        entry_2 = EntryFactory(seko=self.seko)

        entry_detail_21 = EntryDetailFactory(entry=entry_2)
        entry_detail_22 = EntryDetailFactory(entry=entry_2)
        entry_detail_11 = EntryDetailFactory(entry=entry_1)
        entry_detail_12 = EntryDetailFactory(entry=entry_1)
        entry_detail_13 = EntryDetailFactory(entry=entry_1)

        EntryDetailMsgFactory(entry_detail=entry_detail_21)
        EntryDetailMsgFactory(entry_detail=entry_detail_22, release_status=2)
        EntryDetailMsgFactory(entry_detail=entry_detail_11)
        EntryDetailMsgFactory(entry_detail=entry_detail_13)
        EntryDetailMsgFactory(entry_detail=entry_detail_12)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_msg_pdf_succeed(self) -> None:
        """追悼MSG_PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:msg_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'追悼MSG-{self.seko.pk}.pdf')

    def test_msg_pdf_succeed_without_auth(self) -> None:
        """追悼MSG_PDF取得APIはAuthorizationヘッダがなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('seko:msg_pdf', kwargs={'pk': self.seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_msg_pdf_failed_by_seko_notfound(self) -> None:
        """存在しない施行IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_seko = SekoFactory.build()
        response = self.api_client.get(
            reverse('seko:msg_pdf', kwargs={'pk': not_exist_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
