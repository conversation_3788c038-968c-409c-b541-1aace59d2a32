import factory
import factory.fuzzy as fuzzy
import faker
from django.utils import timezone
from factory.django import DjangoModelFactory

from bases.models import Base
from bases.tests.factories import BaseFactory
from hoyo.models import (
    Hoyo,
    HoyoMail,
    HoyoMailTarget,
    HoyoMailTemplate,
    HoyoSample,
    HoyoSchedule,
    HoyoSeko,
)
from masters.models import TermUnitType
from masters.tests.factories import HoyoStyleFactory
from seko.tests.factories import SekoFactory
from staffs.tests.factories import StaffFactory

tz = timezone.get_current_timezone()
fake_provider = faker.Faker('ja_JP')


class HoyoFactory(DjangoModelFactory):
    class Meta:
        model = Hoyo

    id = factory.Sequence(lambda n: n)
    company = factory.SubFactory(BaseFactory)
    style = factory.SubFactory(HoyoStyleFactory)
    name = factory.Faker('word', locale='ja_JP')
    elapsed_time = factory.Faker('pyint', min_value=0, max_value=30)
    unit = factory.Faker('random_element', elements=TermUnitType.values)
    display_num = factory.Faker('pyint', min_value=0, max_value=1000)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0


class HoyoSampleFactory(DjangoModelFactory):
    class Meta:
        model = HoyoSample

    id = factory.Sequence(lambda n: n)
    company = factory.SubFactory(BaseFactory)
    sentence = factory.Faker('sentence', locale='ja_JP')
    display_num = factory.Faker('pyint', min_value=0, max_value=1000)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0


class HoyoMailTemplateFactory(DjangoModelFactory):
    class Meta:
        model = HoyoMailTemplate

    id = factory.Sequence(lambda n: n)
    company = factory.SubFactory(BaseFactory)
    sentence = factory.Faker('sentence', locale='ja_JP')
    display_num = factory.Faker('pyint', min_value=0, max_value=1000)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    create_user_id = 0
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    update_user_id = 0


class HoyoSekoFactory(DjangoModelFactory):
    class Meta:
        model = HoyoSeko

    id = factory.Sequence(lambda n: n)
    seko = factory.SubFactory(SekoFactory)
    hoyo = factory.SubFactory(HoyoFactory)
    hoyo_name = factory.Faker('word', locale='ja_JP')
    hoyo_planned_date = factory.Faker('future_date', end_date='+3d', tzinfo=tz)
    hoyo_activity_date = factory.Faker('future_date', end_date='+3d', tzinfo=tz)
    begin_time = factory.Faker('time')
    end_time = factory.Faker('time')
    hall = factory.SubFactory(BaseFactory)
    dine_flg = fuzzy.FuzzyChoice([True, False])
    seshu_name = factory.Faker('name', locale='ja_JP')
    kojin_seshu_relationship = factory.Faker('word', locale='ja_JP')
    zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    prefecture = factory.Faker('prefecture', locale='ja_JP')
    address_1 = factory.Faker('city', locale='ja_JP')
    address_2 = factory.Faker('town', locale='ja_JP')
    address_3 = factory.Faker('chome', locale='ja_JP')
    tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    hoyo_sentence = factory.Faker('sentence', locale='ja_JP')
    shishiki_name = factory.Faker('word', locale='ja_JP')
    reply_limit_date = factory.Faker('future_date', end_date='+10d', tzinfo=tz)
    hoyo_site_end_date = factory.Faker('future_date', end_date='+10d', tzinfo=tz)
    note = factory.Faker('paragraph', locale='ja_JP')
    staff = factory.SubFactory(StaffFactory)
    del_flg = False
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class HoyoScheduleFactory(DjangoModelFactory):
    class Meta:
        model = HoyoSchedule

    id = factory.Sequence(lambda n: n)
    hoyo_seko = factory.SubFactory(HoyoSekoFactory)
    schedule_name = factory.Faker('word', locale='ja_JP')
    hall = factory.SubFactory(BaseFactory, base_type=Base.OrgType.HALL)
    hall_name = factory.Faker('company', locale='ja_JP')
    schedule_date = factory.Faker('future_date', end_date='+3d', tzinfo=tz)
    begin_time = factory.Faker('time')
    end_time = factory.Faker('time')
    display_num = factory.Faker('pyint', min_value=0, max_value=1000)
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class HoyoMailFactory(DjangoModelFactory):
    class Meta:
        model = HoyoMail

    id = factory.Sequence(lambda n: n)
    hoyo = factory.SubFactory(HoyoFactory)
    select_ts = factory.Faker('past_datetime', start_date='-10d', tzinfo=tz)
    send_ts = factory.Faker('past_datetime', start_date='-10d', tzinfo=tz)
    content = factory.Faker('paragraph', locale='ja_JP')
    staff = factory.SubFactory(StaffFactory)
    note = factory.Faker('paragraph', locale='ja_JP')
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class HoyoMailTargetFactory(DjangoModelFactory):
    class Meta:
        model = HoyoMailTarget

    id = factory.Sequence(lambda n: n)
    hoyo_mail = factory.SubFactory(HoyoMailFactory)
    seko = factory.SubFactory(SekoFactory)
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
