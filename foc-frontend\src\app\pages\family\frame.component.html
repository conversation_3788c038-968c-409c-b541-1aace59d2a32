
<ng-container *ngIf="appView?.type===2">
  <ng-container *ngIf="!is_loading">
    <div class="advertise" *ngIf="login_info && advertise_list?.length" (click)="closeMenu($event)">
      <div class="flexslider" id="ad-banner">
        <ul class="slides">
          <li *ngFor="let advertise of advertise_list">
            <ng-container *ngIf="advertise.url || advertise.link_file">
              <a target="_blank" href="{{advertise.url?advertise.url:advertise.link_file?advertise.link_file:''}}">
                <img src="{{advertise.banner_file}}">
              </a>
            </ng-container>
            <ng-container *ngIf="!advertise.url && !advertise.link_file">
              <img src="{{advertise.banner_file}}">
            </ng-container>
          </li>
        </ul>
      </div>
    </div>
    <header (click)="closeMenu($event)">
      <div class="main-header">
        <div class="logo" (click)="gotoTop()"></div>
        <div class="menu-button" *ngIf="login_info" (click)="openMenu()">
          <i class="bars icon large top-menu"></i>
          <div class="menu-name">menu</div>
        </div>
        <div class="menu-area" [class.show]="menu_show">
          <div class="close"><i class="delete icon large" (click)="closeMenu($event)"></i></div>
          <ng-container *ngFor="let soke_menu of menu_list">
            <div class="item" routerLink="{{soke_menu.url}}" [queryParams]="soke_menu.queryParam?soke_menu.queryParam:{}" *ngIf="showMenu(soke_menu)">{{getMenuName(soke_menu)}}</div>
          </ng-container>
        </div>
      </div>
    </header>
    <div class="contents header" *ngIf="login_info && seko_info" (click)="closeMenu($event)">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="routerArea" (click)="closeMenu($event)" >
      <app-fam-soke-approve *ngIf="appView.tag==='app-fam-soke-approve'" class="contents"></app-fam-soke-approve>
      <app-fam-login *ngIf="appView.tag==='app-fam-login'" class="contents"></app-fam-login>
      <app-fam-seko *ngIf="appView.tag==='app-fam-seko'" class="contents"></app-fam-seko>
      <app-fam-homei *ngIf="appView.tag==='app-fam-homei'" class="contents"></app-fam-homei>
      <app-fam-henrei *ngIf="appView.tag==='app-fam-henrei'" class="contents"></app-fam-henrei>
      <app-fam-info *ngIf="appView.tag==='app-fam-info'" class="contents"></app-fam-info>
      <app-fam-change-password *ngIf="appView.tag==='app-fam-change-password'" class="contents"></app-fam-change-password>
      <app-fam-memorial *ngIf="appView.tag==='app-fam-memorial'" class="contents"></app-fam-memorial>
      <app-fam-image-share *ngIf="appView.tag==='app-fam-image-share'" class="contents"></app-fam-image-share>
      <app-fam-soke-manual-sano *ngIf="appView.tag==='app-fam-soke-manual-sano'" class="contents"></app-fam-soke-manual-sano>
      <app-fam-hoyo *ngIf="appView.tag==='app-fam-hoyo'" class="contents"></app-fam-hoyo>
      <app-fam-seko-af *ngIf="appView.tag==='app-fam-seko-af'" class="contents"></app-fam-seko-af>
      <app-fam-inquiry *ngIf="appView.tag==='app-fam-inquiry'" class="contents"></app-fam-inquiry>
      <app-fam-faq *ngIf="appView.tag==='app-fam-faq'" class="contents"></app-fam-faq>
      <app-fam-soke-userpolicy *ngIf="appView.tag==='app-fam-soke-userpolicy'" class="contents"></app-fam-soke-userpolicy>
    </div>

    <!--footer-->
    <footer (click)="closeMenu($event)" *ngIf="login_info && seko_info">
      <div class="contents" *ngIf="seko_info.seko_company">
        <div class="logo">
          <img [src]="seko_info.seko_company.company_logo_file" *ngIf="seko_info.seko_company.company_logo_file">
        </div>
        <div class="info">
          <div class="name">{{seko_info.seko_company.base_name}}</div>
          <div class="address">{{seko_info.seko_company.prefecture}}{{seko_info.seko_company.address_1}}{{seko_info.seko_company.address_2}}{{seko_info.seko_company.address_3}}</div>
          <div class="tel">{{seko_info.seko_company.tel}}</div>
        </div>
      </div>
    </footer>
  </ng-container>

  <div class="pagetop"><a href="#"><img src="../../../assets/img/customer/pagetop.png">TOP</a></div>
</ng-container>
