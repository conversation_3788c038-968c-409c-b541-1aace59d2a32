
<div class="container">
  <div class="inner with_footer">
    <div class="contents small">
      <div class="menu_title">
        <i class="hands helping icon big"></i>
        発注先
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #CompanyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" (selectedItemChange)="companyChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="item_list?.length">
          全{{item_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?item_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination, pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(pagination, page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination, pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!item_list?.length">
              <th class="name">発注先名</th>
              <th class="tel">電話番号</th>
              <th class="fax">FAX番号</th>
              <th class="zip_code">郵便番号</th>
              <th class="address">住所</th>
              <th class="mail_address">メールアドレス</th>
              <th class="order_mail_send_flg">送信方法</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let item of item_list; index as i">
              <tr (click)="showData($event, item)" *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
                <td class="name" title="{{item.name}}">{{item.name}}</td>
                <td class="tel" title="{{item.tel}}">{{item.tel}}</td>
                <td class="fax" title="{{item.fax}}">{{item.fax}}</td>
                <td class="zip_code" title="{{item.zip_code}}">{{item.zip_code}}</td>
                <td class="address_1" title="{{item.address}}">{{item.address}}</td>
                <td class="mail_address" title="{{item.mail_address}}">{{item.mail_address}}</td>
                <td class="center aligned order_mail_send_flg" >{{item.order_mail_send_flg?'メール':'FAX'}}</td>
                <td class="center aligned button operation">
                  <i class="large trash alternate icon" title="削除" (click)="deleteData(item.id)"></i>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <tr title="新規追加" (click)="showData($event)">
              <td colspan="6" class="center aligned"><i class="large add icon"></i></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal small" id="item-edit">
        <div class="header ui"><i class="building outline icon large"></i>
          発注先{{edit_type===1?'登録':'編集'}}
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <label class="required wide">名称</label>
              <div class="ui input">
                <input type="text" [class.error]="errField.get('supplier_name')" autocomplete="off" id="supplier_name" [(ngModel)]="item_edit.name">
              </div>
            </div>
            <div class="line">
              <label class="required wide">電話番号</label>
              <div class="ui left icon input">
                <i class="phone alternate icon"></i>
                <input [class.error]="errField.get('supplier_tel')" type="tel" autocomplete="off" id="supplier_tel" maxlength="15" [(ngModel)]="item_edit.tel">
              </div>
              <label class="required">FAX番号</label>
              <div class="ui left icon input">
                <i class="fax icon"></i>
                <input [class.error]="errField.get('supplier_fax')" type="tel" autocomplete="off" id="supplier_fax" maxlength="15" [(ngModel)]="item_edit.fax">
              </div>
            </div>
            <div class="line">
              <label class="required wide">郵便番号</label>
              <div class="ui left icon input">
                <i class="tenge icon"></i>
                <input [class.error]="errField.get('supplier_zip_code')" type="tel" placeholder="ハイフンなし" autocomplete="off" id="supplier_zip_code" maxlength="7" [(ngModel)]="item_edit.zip_code" (input)="zipcodeChange()">
              </div>
            </div>
            <div class="line">
              <label class="required wide">住所</label>
              <div class="ui input wide">
                <input [class.error]="errField.get('supplier_address')" type="text" autocomplete="off" id="supplier_address" [(ngModel)]="item_edit.address">
              </div>
            </div>
            <div class="line">
              <label class="wide" [class.required]="item_edit.order_mail_send_type===2">メールアドレス</label>
              <div class="ui input wide">
                <input [class.error]="errField.get('mail_address')" type="email" autocomplete="off" id="mail_address" [(ngModel)]="item_edit.mail_address">
              </div>
            </div>
            <div class="line">
              <label class="wide">送信方法</label>
              <div class="ui radio checkbox">
                <input type="radio" name="order_mail_send_type" [value]="1" [(ngModel)]="item_edit.order_mail_send_type" [checked]="item_edit.order_mail_send_type===1">
                <label>FAX</label>
              </div>
              <div class="ui radio checkbox">
                <input type="radio" name="order_mail_send_type" [value]="2" [(ngModel)]="item_edit.order_mail_send_type" [checked]="item_edit.order_mail_send_type===2">
                <label>メール</label>
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

    </div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
