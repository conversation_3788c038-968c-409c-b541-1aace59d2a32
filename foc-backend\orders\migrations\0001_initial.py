# Generated by Django 3.1.2 on 2020-11-10 09:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('seko', '0010_moshu_agree_ts'),
        ('suppliers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('masters', '0010_paymenttype'),
        ('items', '0003_auto_20201104_1540'),
    ]

    operations = [
        migrations.CreateModel(
            name='Entry',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('entry_name', models.TextField(verbose_name='name')),
                ('entry_name_kana', models.TextField(verbose_name='kana')),
                ('entry_zip_code', models.CharField(max_length=7, verbose_name='zipcode')),
                ('entry_prefecture', models.TextField(verbose_name='prefecture')),
                ('entry_address_1', models.TextField(verbose_name='address1')),
                ('entry_address_2', models.TextField(verbose_name='address2')),
                (
                    'entry_address_3',
                    models.TextField(blank=True, null=True, verbose_name='address3'),
                ),
                ('entry_tel', models.CharField(max_length=15, verbose_name='tel no')),
                ('entry_mail_address', models.TextField(verbose_name='mail address')),
                ('entry_ts', models.DateTimeField(verbose_name='entried at')),
                (
                    'cancel_ts',
                    models.DateTimeField(blank=True, null=True, verbose_name='cancelled at'),
                ),
                ('receipt_count', models.IntegerField(verbose_name='receipt count')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'payment',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to='masters.paymenttype',
                        verbose_name='payment',
                    ),
                ),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='entries',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_entry',
            },
        ),
        migrations.CreateModel(
            name='EntryDetail',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('item_hinban', models.CharField(max_length=20, verbose_name='item hinban')),
                ('item_name', models.TextField(verbose_name='item name')),
                ('item_unit_price', models.IntegerField(verbose_name='unit price')),
                ('item_tax', models.IntegerField(verbose_name='tax')),
                ('item_tax_pct', models.IntegerField(verbose_name='tax percentage')),
                ('keigen_flg', models.BooleanField(verbose_name='tax reduced')),
                ('quantity', models.IntegerField(verbose_name='quantity')),
                ('tax_adjust', models.IntegerField(verbose_name='tax adjusted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'entry',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='details',
                        to='orders.entry',
                        verbose_name='entry',
                    ),
                ),
                (
                    'item',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='items.item',
                        verbose_name='item',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_entry_detail',
            },
        ),
        migrations.CreateModel(
            name='EntryDetailChobun',
            fields=[
                (
                    'entry_detail',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='chobun',
                        serialize=False,
                        to='orders.entrydetail',
                        verbose_name='entry detail',
                    ),
                ),
                (
                    'okurinushi_company',
                    models.TextField(blank=True, null=True, verbose_name='sender company name'),
                ),
                (
                    'okurinushi_title',
                    models.TextField(blank=True, null=True, verbose_name='sender title'),
                ),
                (
                    'okurinushi_company_kana',
                    models.TextField(blank=True, null=True, verbose_name='sender company kana'),
                ),
                ('okurinushi_name', models.TextField(verbose_name='sender name')),
                ('okurinushi_kana', models.TextField(verbose_name='sender kana')),
                ('renmei1', models.TextField(blank=True, null=True, verbose_name='joint name 1')),
                (
                    'renmei_kana1',
                    models.TextField(blank=True, null=True, verbose_name='joint kana 1'),
                ),
                ('renmei2', models.TextField(blank=True, null=True, verbose_name='joint name 2')),
                (
                    'renmei_kana2',
                    models.TextField(blank=True, null=True, verbose_name='joint kana 2'),
                ),
                ('atena', models.TextField(verbose_name='recipient')),
                ('honbun', models.TextField(verbose_name='body')),
                ('note', models.TextField(blank=True, null=True, verbose_name='note')),
                ('printed_flg', models.BooleanField(verbose_name='printed')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'db_table': 'tr_entry_detail_chobun',
            },
        ),
        migrations.CreateModel(
            name='EntryDetailKoden',
            fields=[
                (
                    'entry_detail',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='koden',
                        serialize=False,
                        to='orders.entrydetail',
                        verbose_name='entry detail',
                    ),
                ),
                ('koden_commission', models.IntegerField(verbose_name='commission price')),
                ('koden_commission_tax', models.IntegerField(verbose_name='commission tax')),
                (
                    'koden_commission_tax_pct',
                    models.IntegerField(verbose_name='commission tax percentage'),
                ),
                ('tax_adjust', models.IntegerField(verbose_name='tax adjusted')),
                ('henreihin_fuyo_flg', models.BooleanField(verbose_name='heirei fuyo')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'db_table': 'tr_entry_detail_koden',
            },
        ),
        migrations.CreateModel(
            name='EntryDetailMsg',
            fields=[
                (
                    'entry_detail',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='message',
                        serialize=False,
                        to='orders.entrydetail',
                        verbose_name='entry detail',
                    ),
                ),
                ('relation_ship', models.TextField(verbose_name='relationshop')),
                ('honbun', models.TextField(verbose_name='body')),
                ('release_status', models.IntegerField(verbose_name='release status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'db_table': 'tr_entry_detail_msg',
            },
        ),
        migrations.CreateModel(
            name='EntryDetailKumotsu',
            fields=[
                (
                    'entry_detail',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='kumotsu',
                        serialize=False,
                        to='orders.entrydetail',
                        verbose_name='entry detail',
                    ),
                ),
                (
                    'okurinushi_company',
                    models.TextField(blank=True, null=True, verbose_name='sender company name'),
                ),
                (
                    'okurinushi_title',
                    models.TextField(blank=True, null=True, verbose_name='sender title'),
                ),
                (
                    'okurinushi_company_kana',
                    models.TextField(blank=True, null=True, verbose_name='sender company kana'),
                ),
                ('okurinushi_name', models.TextField(verbose_name='sender name')),
                ('okurinushi_kana', models.TextField(verbose_name='sender kana')),
                ('renmei1', models.TextField(blank=True, null=True, verbose_name='joint name 1')),
                (
                    'renmei_kana1',
                    models.TextField(blank=True, null=True, verbose_name='joint kana 1'),
                ),
                ('renmei2', models.TextField(blank=True, null=True, verbose_name='joint name 2')),
                (
                    'renmei_kana2',
                    models.TextField(blank=True, null=True, verbose_name='joint kana 2'),
                ),
                ('note', models.TextField(blank=True, null=True, verbose_name='note')),
                ('order_status', models.IntegerField(verbose_name='order status')),
                (
                    'order_ts',
                    models.DateTimeField(blank=True, null=True, verbose_name='ordered at'),
                ),
                (
                    'delivery_ts',
                    models.DateTimeField(blank=True, null=True, verbose_name='delivered at'),
                ),
                ('order_note', models.TextField(blank=True, null=True, verbose_name='order note')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'order_staff',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='order staff',
                    ),
                ),
                (
                    'supplier',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='suppliers.supplier',
                        verbose_name='supplier',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_entry_detail_kumotsu',
            },
        ),
    ]
