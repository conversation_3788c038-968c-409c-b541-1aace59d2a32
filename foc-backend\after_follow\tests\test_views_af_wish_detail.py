from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.tests.factories import AfterFollowFactory, AfWishFactory, SekoAfFactory
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class AfWishUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.af_wish = AfWishFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:

        self.new_af_wish_data = AfWishFactory.build(af_type=AfterFollowFactory())

        return {
            'af_type': self.new_af_wish_data.af_type.pk,
            'answered_flg': self.new_af_wish_data.answered_flg,
            'proposal_status': self.new_af_wish_data.proposal_status,
            'order_status': self.new_af_wish_data.order_status,
            'order_date': self.new_af_wish_data.order_date.isoformat(),
            'order_chance': self.new_af_wish_data.order_chance,
        }

    def test_af_wish_update_succeed(self) -> None:
        """AF希望項目を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('after_follow:wish_detail', kwargs={'pk': self.af_wish.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['af_type']['name'], self.new_af_wish_data.af_type.name)
        self.assertEqual(
            record['af_type']['display_num'], self.new_af_wish_data.af_type.display_num
        )
        self.assertEqual(record['answered_flg'], self.new_af_wish_data.answered_flg)
        self.assertEqual(record['proposal_status'], self.new_af_wish_data.proposal_status)
        self.assertEqual(record['order_status'], self.new_af_wish_data.order_status)
        self.assertEqual(record['order_date'], self.new_af_wish_data.order_date.isoformat())
        self.assertEqual(record['order_chance'], self.new_af_wish_data.order_chance)

    def test_af_wish_update_ignore_seko(self) -> None:
        """AF希望項目更新APIは施行AFの更新を無視する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {'seko_af': SekoAfFactory().pk}
        response = self.api_client.put(
            reverse('after_follow:wish_detail', kwargs={'pk': self.af_wish.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['seko_af'], self.af_wish.seko_af.pk)

    def test_af_wish_update_failed_without_auth(self) -> None:
        """AF希望項目更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('after_follow:wish_detail', kwargs={'pk': self.af_wish.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])

    def test_af_wish_update_deny_call_from_moshu(self) -> None:
        """AF希望項目更新APIが喪主からの呼び出しを拒否する"""
        moshu = MoshuFactory()
        refresh = RefreshToken.for_user(moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.put(
            reverse('after_follow:wish_detail', kwargs={'pk': self.af_wish.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        record: Dict = response.json()
        self.assertIsNotNone(record['detail'])
