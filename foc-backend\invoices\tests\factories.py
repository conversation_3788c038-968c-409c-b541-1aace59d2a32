import factory
import faker
from dateutil.relativedelta import relativedelta
from django.utils import timezone
from factory.django import DjangoModelFactory

from invoices.models import SalesCompany, SalesDetail, SalesIndex, SalesSekoDepartment

fake_provider = faker.Faker('ja_JP')
tz = timezone.get_current_timezone()


class SalesIndexFactory(DjangoModelFactory):
    class Meta:
        model = SalesIndex

    id = factory.Sequence(lambda n: n)
    sales_yymm = factory.LazyAttribute(
        lambda obj: (obj.sum_ts + relativedelta(months=-1)).strftime('%Y%m')
    )
    fiscal_year = factory.LazyAttribute(
        lambda obj: (obj.sum_ts + relativedelta(months=-1)).year
        - (1 if (obj.sum_ts + relativedelta(months=-1)).month < 4 else 0)
    )
    sum_ts = factory.Faker('past_datetime', start_date='-3d', tzinfo=tz)


class SalesCompanyFactory(DjangoModelFactory):
    class Meta:
        model = SalesCompany

    id = factory.Sequence(lambda n: n)
    sales_index = factory.SubFactory(SalesIndexFactory)
    company_id = factory.Faker('pyint', min_value=1)
    company_name = factory.Faker('company', locale='ja_JP')
    zip_code = factory.LazyFunction(lambda: fake_provider.zipcode().replace('-', ''))
    address_1 = factory.Faker('city', locale='ja_JP')
    address_2 = factory.Faker('town', locale='ja_JP')
    tel = factory.LazyFunction(lambda: fake_provider.phone_number().replace('-', ''))
    invoice_date = factory.Faker('past_date', start_date='-3d', tzinfo=tz)
    pay_date = factory.Faker('future_date', end_date='+3d', tzinfo=tz)
    monthly_fee = factory.Faker('pyint', min_value=1, max_value=99999)
    chobun_fee = factory.Faker('pyint', min_value=1, max_value=99999)
    chobun_fee_unit = factory.Faker('word', locale='ja_JP')
    chobun_qty = factory.Faker('pyint', min_value=1, max_value=99999)
    chobun_order_price = factory.Faker('pyint', min_value=1, max_value=99999)
    chobun_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    chobun_order_fee = factory.Faker('pyint', min_value=1, max_value=99999)
    kumotsu_fee = factory.Faker('pyint', min_value=1, max_value=99999)
    kumotsu_fee_unit = factory.Faker('word', locale='ja_JP')
    kumotsu_qty = factory.Faker('pyint', min_value=1, max_value=99999)
    kumotsu_order_price = factory.Faker('pyint', min_value=1, max_value=99999)
    kumotsu_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    kumotsu_order_fee = factory.Faker('pyint', min_value=1, max_value=99999)
    henreihin_fee = factory.Faker('pyint', min_value=1, max_value=99999)
    henreihin_fee_unit = factory.Faker('word', locale='ja_JP')
    henreihin_qty = factory.Faker('pyint', min_value=1, max_value=99999)
    henreihin_order_price = factory.Faker('pyint', min_value=1, max_value=99999)
    henreihin_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    henreihin_order_fee = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_qty = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_order_price = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_order_fee = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_fee_commission = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_fee_commission_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_commission_pct = factory.Faker('pydecimal', left_digits=1, right_digits=2, positive=True)
    sales_price = factory.Faker('pyint', min_value=1, max_value=99999)
    invoice_tax_pct = factory.Faker('pyint', min_value=1, max_value=20)
    invoice_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    confirm_ts = None
    confirm_staff_id = None
    confirm_staff_name = None
    created_at = factory.Faker('past_datetime', start_date='-3d', tzinfo=tz)


class SalesSekoDepartmentFactory(DjangoModelFactory):
    class Meta:
        model = SalesSekoDepartment

    id = factory.Sequence(lambda n: n)
    sales_company = factory.SubFactory(SalesCompanyFactory)
    seko_department_id = factory.Faker('pyint', min_value=1)
    base_name = factory.Faker('company', locale='ja_JP')
    chobun_qty = factory.Faker('pyint', min_value=1, max_value=99999)
    chobun_order_price = factory.Faker('pyint', min_value=1, max_value=99999)
    chobun_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    kumotsu_qty = factory.Faker('pyint', min_value=1, max_value=99999)
    kumotsu_order_price = factory.Faker('pyint', min_value=1, max_value=99999)
    kumotsu_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    henreihin_qty = factory.Faker('pyint', min_value=1, max_value=99999)
    henreihin_order_price = factory.Faker('pyint', min_value=1, max_value=99999)
    henreihin_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_qty = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_order_price = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    koden_order_fee = factory.Faker('pyint', min_value=1, max_value=99999)
    created_at = factory.Faker('past_datetime', start_date='-3d', tzinfo=tz)


class SalesDetailFactory(DjangoModelFactory):
    class Meta:
        model = SalesDetail

    id = factory.Sequence(lambda n: n)
    sales_seko_department = factory.SubFactory(SalesSekoDepartmentFactory)
    seko_id = factory.Faker('pyint', min_value=1)
    seko_date = factory.Faker('past_date', start_date='-3d', tzinfo=tz)
    soke_name = factory.Faker('name', locale='ja_JP')
    kojin_name = factory.Faker('name', locale='ja_JP')
    moshu_name = factory.Faker('name', locale='ja_JP')
    entry_id = factory.Faker('pyint', min_value=1)
    entry_name = factory.Faker('name', locale='ja_JP')
    entry_ts = factory.Faker('past_datetime', start_date='-3d', tzinfo=tz)
    entry_detail_id = factory.Faker('pyint', min_value=1)
    service_id = factory.Faker('pyint', min_value=1)
    service_name = factory.Faker('word', locale='ja_JP')
    item_id = factory.Faker('pyint', min_value=1)
    item_name = factory.Faker('word', locale='ja_JP')
    quantity = factory.Faker('pyint', min_value=1, max_value=99999)
    item_price = factory.Faker('pyint', min_value=1, max_value=99999)
    item_tax = factory.Faker('pyint', min_value=1, max_value=99999)
    item_tax_pct = factory.Faker('pyint', min_value=1, max_value=20)
    item_taxed_price = factory.Faker('pyint', min_value=1, max_value=99999)
    keigen_flg = False
    created_at = factory.Faker('past_datetime', start_date='-3d', tzinfo=tz)
