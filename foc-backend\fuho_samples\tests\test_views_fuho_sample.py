from typing import Dict, List

from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from fuho_samples.models import Base, FuhoSample
from fuho_samples.tests.factories import FuhoSampleFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


class FuhoSampleListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.base_1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.fuho_sample_3 = FuhoSampleFactory(
            company=BaseFactory(base_type=Base.OrgType.COMPANY), display_num=2
        )
        self.fuho_sample_1 = FuhoSampleFactory(company=self.base_1, display_num=3)
        self.fuho_sample_2 = FuhoSampleFactory(company=self.base_1, display_num=1)
        self.fuho_sample_4 = FuhoSampleFactory(company=self.base_1, display_num=1)

        self.staff = StaffFactory(base=self.base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_fuho_sample_list_succeed(self) -> None:
        """訃報サンプル一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)

        # 順番確認 (fuho_sample_2 > fuho_sample_4 > fuho_sample_1 > fuho_sample_3)
        self.assertEqual(records[0]['id'], self.fuho_sample_2.pk)
        self.assertEqual(records[1]['id'], self.fuho_sample_4.pk)
        self.assertEqual(records[2]['id'], self.fuho_sample_1.pk)
        self.assertEqual(records[3]['id'], self.fuho_sample_3.pk)

    def test_fuho_sample_succeed_by_company(self) -> None:
        """拠点で絞り込みした訃報サンプル一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company': self.base_1.pk,
        }
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        for record in records:
            self.assertEqual(record['company'], self.base_1.pk)

    def test_fuho_sample_list_ignores_deleted(self) -> None:
        """訃報サンプル一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.fuho_sample_1.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_fuho_sample_list_failed_without_auth(self) -> None:
        """訃報サンプル一覧取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class FuhoSampleDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.fuho_sample = FuhoSampleFactory(company=base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_fuho_sample_detail_succeed(self) -> None:
        """訃報サンプル詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': self.fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        saved_fuho_sample = model_to_dict(self.fuho_sample)
        for attr, value in saved_fuho_sample.items():
            self.assertEqual(value, record[attr])

    def test_fuho_sample_detail_failed_by_notfound(self) -> None:
        """訃報サンプル詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_fuho_sample: FuhoSample = FuhoSampleFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': non_saved_fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_fuho_sample_detail_ignores_deleted(self) -> None:
        """訃報サンプル一覧は無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.fuho_sample.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': self.fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_fuho_sample_detail_failed_without_auth(self) -> None:
        """訃報サンプル詳細取得APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': self.fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class FuhoSampleCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.fuho_sample_data: FuhoSample = FuhoSampleFactory.build(company=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.fuho_sample_data.company.pk,
            'sentence': self.fuho_sample_data.sentence,
            'display_num': self.fuho_sample_data.display_num,
            'del_flg': self.fuho_sample_data.del_flg,
        }

    def test_fuho_sample_create_succeed(self) -> None:
        """訃報サンプルを追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('fuho_samples:fuho_sample_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: Dict = response.json()

        for attr, value in params.items():
            self.assertEqual(value, record[attr])

        self.assertEqual(record['company'], self.base.pk)
        self.assertEqual(record['create_user_id'], self.staff.pk)
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_fuho_sample_create_failed_without_auth(self) -> None:
        """訃報サンプル追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = model_to_dict(self.fuho_sample_data)
        response = self.api_client.post(
            reverse('fuho_samples:fuho_sample_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class FuhoSampleUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()
        self.fuho_sample = FuhoSampleFactory(company=self.base)
        self.new_fuho_sample_data = FuhoSampleFactory.build(company=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.new_fuho_sample_data.company.pk,
            'sentence': self.new_fuho_sample_data.sentence,
            'display_num': self.new_fuho_sample_data.display_num,
            'del_flg': self.new_fuho_sample_data.del_flg,
        }

    def test_fuho_sample_update_succeed(self) -> None:
        """訃報サンプルを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': self.fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        for attr, value in params.items():
            self.assertEqual(value, record[attr])

        self.assertEqual(record['create_user_id'], self.fuho_sample.create_user_id)
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_fuho_sample_update_failed_by_notfound(self) -> None:
        """訃報サンプル更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_fuho_sample: FuhoSample = FuhoSampleFactory.build()
        params: Dict = {}
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': non_saved_fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_fuho_sample_update_ignores_deleted(self) -> None:
        """訃報サンプル更新APIは無効化された訃報サンプルを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.fuho_sample.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': self.fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_fuho_sample_update_failed_without_auth(self) -> None:
        """訃報サンプル更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': self.fuho_sample.company.id}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class FuhoSampleDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.fuho_sample: FuhoSample = FuhoSampleFactory(company=self.company)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.company)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_fuho_sample_delete_succeed(self) -> None:
        """訃報サンプルを論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': self.fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 訃報のdel_flgがTrueになるだけ
        db_fuho_sample: FuhoSample = FuhoSample.objects.get(pk=self.fuho_sample.pk)
        self.assertTrue(db_fuho_sample.del_flg)
        self.assertEqual(db_fuho_sample.create_user_id, self.fuho_sample.create_user_id)
        self.assertEqual(db_fuho_sample.update_user_id, self.staff.pk)

    def test_fuho_sample_delete_failed_by_notfound(self) -> None:
        """訃報サンプル削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_fuho_sample: FuhoSample = FuhoSampleFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': non_saved_fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_fuho_sample_delete_failed_by_already_deleted(self) -> None:
        """訃報サンプル削除APIが論理削除済みの訃報サンプルを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.fuho_sample.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': self.fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_fuho_sample_delete_failed_without_auth(self) -> None:
        """訃報サンプル削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('fuho_samples:fuho_sample_detail', kwargs={'pk': self.fuho_sample.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
