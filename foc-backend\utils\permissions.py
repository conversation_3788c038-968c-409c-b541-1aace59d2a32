from rest_framework.permissions import SAFE_METHODS, BasePermission

from seko.models import Moshu
from staffs.models import Staff


class IsStaff(BasePermission):
    def has_permission(self, request, view):
        return bool(
            request.user and request.user.is_authenticated and isinstance(request.user, Staff)
        )


class IsMoshu(BasePermission):
    def has_permission(self, request, view):
        return bool(
            request.user and request.user.is_authenticated and isinstance(request.user, Moshu)
        )


class ReadNeedAuth(BasePermission):
    def has_permission(self, request, view):
        return bool(
            request.method in SAFE_METHODS and request.user and request.user.is_authenticated
        )
