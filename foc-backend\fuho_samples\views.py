from django.db.models import Q
from django_filters import rest_framework as filters
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from fuho_samples.models import FuhoSample
from fuho_samples.serializers import FuhoSampleSerializer
from utils.view_mixins import AddContextMixin, SoftDestroyMixin


class FuhoSampleList(AddContextMixin, generics.ListCreateAPIView):

    queryset = (
        FuhoSample.objects.filter(Q(del_flg=False))
        .order_by('company_id', 'display_num', 'id')
        .all()
    )
    serializer_class = FuhoSampleSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['company']
    permission_classes = [IsAuthenticated]


class FuhoSampleDetail(SoftDestroyMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    get: 訃報サンプルを取得します
    """

    queryset = FuhoSample.objects.filter(Q(del_flg=False)).all()
    serializer_class = FuhoSampleSerializer
    permission_classes = [IsAuthenticated]
