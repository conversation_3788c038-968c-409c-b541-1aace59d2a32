@import "src/assets/scss/customer/setting";
.container .inner .contents {
  .input .button.grey {
    width: 180px;
  }
  .input {
    width: 90%;
    .item-box {
      padding: 10px;
      width: 100%;
      height: 170px;
      margin: 2px;
      display: flex;
      justify-content: space-between;
      border: solid 1px $border-grey;;
      .image-box {
        width: 150px;
        height: 150px;
        background-color: #fff;
        display: flex;
        justify-content: center;
        &.no-image {
          background-color: #e9e9e9;
        }
        img {
          height: auto;
          width: auto;
          max-height: 100%;
          max-width: 100%;
          margin: auto;
          min-width: 1px;
        }
        .no-image {
          margin-top: 10px;
          opacity: .5;
          font-size: 1.2rem;
        }
      }
      .title-box {
        line-height: 1.6;
        .selecting {
          width: 100px;
          height: 30px;
          text-align: center;
          font-size: 0.9rem;
          border: solid 1px $border-dark;
          border-radius: 20px;
          background-color: #cfcfcf;
          line-height: 1.8
        }
        .name {
          margin-top: 5px;
          font-size: 1.1rem;
          font-weight: bold;
          line-height: 1.2;
        }
        .price {
          font-size: 0.9rem;
        }
      }
      @media screen and (max-width: 560px) {
        height: 150px;
        .image-box {
          width: 130px;
          height: 130px;
        }
        .title-box {
          .selecting {
            width: 80px;
            height: 25px;
          }
        }
      }
      @media screen and (max-width: 380px) {
        height: 130px;
        .image-box {
          width: 110px;
          height: 110px;
        }
        .title-box {
          .selecting {
            width: 60px;
            height: 20px;
          }
        }
      }
    }
    input[type="checkbox"] {
      margin-right: 5px;
    }
  }
  @media screen and (max-width: 560px) {
    .input .button-area, .input .item-box {
      margin-left: 10px;
    }
  }
  .button.center {
    margin-left: auto;
    margin-right: auto;
  }
}
.container .inner {
  >.button-area .button {
    @media screen and (max-width: 560px) {
      width: 130px;
    }
    @media screen and (max-width: 380px) {
      width: 110px;
    }
  }
}
.ui.modal.item {
  position: fixed !important;
  width: 90% !important;
  max-width: 810px;
  max-height: 60vh;
  min-height: 200px;
  top: calc(40vh / 2 - 10px) !important;
  >.content {
    padding: 15px !important;
    font-size: 1.4rem;
    height: 100%;
    width: 100%;
    max-height: 60vh;
    display: flex;
    flex-wrap: wrap;
    .item-box {
      padding: 10px;
      width: 380px;
      height: 170px;
      margin: 2px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      border-bottom: solid 2px $border-grey;;
      &:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,.3);
        --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
        -webkit-transform: translateY(-1px);
        transform: translateY(-1px);
      }
      .image-box {
        width: 150px;
        height: 150px;
        background-color: #fff;
        display: flex;
        justify-content: center;
        &.no-image {
          background-color: #e9e9e9;
        }
        img {
          height: auto;
          width: auto;
          max-height: 100%;
          max-width: 100%;
          margin: auto;
          min-width: 1px;
        }
        .no-image {
          margin-top: 10px;
          opacity: .5;
          font-size: 1.2rem;
        }
      }
      .title-box {
        line-height: 1.8;
        .name {
          margin-top: 5px;
          font-size: 1.1rem;
          font-weight: bold;
        }
        .price {
          font-size: 0.9rem;
        }
      }
    }
  }
  @media screen and (max-width: 560px) {
    max-width: 405px;
    top: 50px !important;
    max-height: 70vh;
    >.content {
      max-height: 70vh;
      .item-box {
        height: 150px;
        .image-box {
          width: 130px;
          height: 130px;
        }
      }
    }
  }
  @media screen and (max-width: 380px) {
    >.content {
      padding: 10px !important;
      .item-box {
        height: 130px;
        .image-box {
          width: 110px;
          height: 110px;
        }
      }
    }
  }
}
