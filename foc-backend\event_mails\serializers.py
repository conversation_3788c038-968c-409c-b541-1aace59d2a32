from typing import Dict, List

from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from rest_framework import serializers

from event_mails.models import EventMail, EventMailTarget
from seko.models import Seko
from seko.serializers import SekoSerializer
from staffs.serializers import StaffSerializer
from utils.sms import send_seko_template_sms


class EventMailTargetSerializerBase(serializers.ModelSerializer):
    class Meta:
        model = EventMailTarget
        read_only_fields = ['created_at', 'updated_at']


class EventMailTargetByEventMailSerializer(EventMailTargetSerializerBase):
    seko = SekoSerializer(required=False)

    class Meta(EventMailTargetSerializerBase.Meta):
        exclude = ['event_mail']


class EventMailSerializer(serializers.ModelSerializer):
    staff = StaffSerializer(required=False)
    targets = EventMailTargetByEventMailSerializer(many=True, required=False)
    target_seko_list = serializers.PrimaryKeyRelatedField(
        queryset=Seko.objects.filter(Q(del_flg=False)), many=True, write_only=True, required=False
    )

    class Meta:
        model = EventMail
        fields = '__all__'
        read_only_fields = ['staff', 'targets', 'created_at', 'updated_at']
        extra_kwargs = {'select_ts': {'required': False, 'allow_null': True}}

    def validate_target_seko_list(self, value: List[Seko]):
        seko_errors: List = []
        for seko in value:
            if not hasattr(seko.seko_company, 'sms_account'):
                seko_errors.append(f'Seko ID={seko.pk}: SMS Account info not found.')
            if not hasattr(seko, 'moshu'):
                seko_errors.append(f'Seko ID={seko.pk}: Moshu info not found.')
            if not hasattr(seko, 'seko_contact'):
                seko_errors.append(f'Seko ID={seko.pk}: Seko contact info not found.')
            elif not seko.seko_contact.mobile_num:
                seko_errors.append(f'Seko ID={seko.pk}: no mobile phone number.')
        if seko_errors:
            raise serializers.ValidationError(seko_errors)
        return value

    @transaction.atomic
    def create(self, validated_data: Dict) -> EventMail:
        target_seko_list = validated_data.pop('target_seko_list', [])
        for key in ['select_ts', 'send_ts']:
            if key not in validated_data or validated_data[key] is None:
                validated_data[key] = timezone.localtime()

        validated_data['staff'] = self.context['staff']
        instance = super().create(validated_data)
        for seko in target_seko_list:
            if send_seko_template_sms(seko=seko, message_template=validated_data['content']):
                EventMailTarget.objects.create(event_mail=instance, seko=seko)

        return instance
