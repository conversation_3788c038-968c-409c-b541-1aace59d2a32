# FOCプロジェクト リリース方法ガイド

## 目次
1. [プロジェクト概要](#プロジェクト概要)
2. [環境構成](#環境構成)
3. [Dockerイメージの詳細](#dockerイメージの詳細)
4. [リリース手順](#リリース手順)
5. [コマンド詳細説明](#コマンド詳細説明)
6. [トラブルシューティング](#トラブルシューティング)
7. [監視・ログ確認](#監視ログ確認)

## プロジェクト概要

**FOC（Funeral Online system Connect）**は葬儀オンラインシステムで、以下の技術スタックで構成されています：

- **バックエンド**: Django + Python 3.8 + PostgreSQL
- **フロントエンド**: Angular 10 + Node.js 12.18.3
- **インフラ**: Docker Swarm + Traefik + Nginx
- **データベース**: PostgreSQL 12

## 環境構成

### 環境一覧
| 環境 | 用途 | ドメイン | 認証 | デバッグ |
|------|------|----------|------|----------|
| **本番環境** | 実際のサービス提供 | www.foc.jpn.com | なし | 無効 |
| **ステージング環境** | テスト・検証 | stg.foc.jpn.com | Basic認証 | 有効 |
| **開発環境** | ローカル開発 | localhost | なし | 有効 |
| **開発環境（Swarm）** | 開発用Swarm | localhost | なし | 有効 |

### ディレクトリ構造
```
foc-backend/support/deployment/
├── production/          # 本番環境設定
├── staging/            # ステージング環境設定
├── develop/            # 開発環境設定（Docker Compose）
└── develop-swarm/      # 開発環境設定（Docker Swarm）
```

## Dockerイメージの詳細

### バックエンドイメージ（foc-backend）

#### Dockerfileの内容と説明

```dockerfile
# ベースイメージ: Python 3.8 on Debian Buster
FROM python:3.8-buster

# ポート8000を公開
EXPOSE 8000

# ビルド時の引数設定
ARG SECRET_KEY=dummy
ARG POETRY_VERSION=1.1.4

# 環境変数の設定
ENV \
  DEBUG=False \                    # デバッグモード無効
  DJANGO_SETTINGS_MODULE=foc.settings \  # Django設定モジュール
  DATABASE_URL=sqlite:///db.sqlite3 \    # デフォルトDB接続
  ENABLE_SILK=0 \                 # Silk（プロファイリング）無効
  ROOT_PATH= \                    # APIルートパス
  WEB_CONCURRENCY= \              # Web並行処理数
  STATIC_ROOT=/usr/src/app/static \      # 静的ファイル保存先
  ALLOWED_HOSTS=*                 # 許可ホスト

# システムパッケージのインストール
RUN \
  apt-get update && \
  DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
  apt-utils && \
  DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
  gettext fonts-noto-cjk fonts-kouzan-mouhitsu && \  # 日本語フォント
  apt-get clean && \
  rm -rf /var/lib/apt/lists/* && \
  python -m pip install -U pip setuptools \
  pip install "poetry==${POETRY_VERSION}"            # Poetry（依存関係管理）

# 作業ディレクトリ設定
WORKDIR /usr/src/app

# 依存関係ファイルのコピー
COPY pyproject.toml poetry.lock ./

# Python依存関係のインストール
RUN \
  poetry export -f requirements.txt > requirements.txt && \  # Poetry→pip形式に変換
  pip install --no-cache-dir -r requirements.txt             # 依存関係インストール

# アプリケーションコードのコピー
COPY . .

# Django初期化処理
RUN \
  touch .env && \                                    # 環境変数ファイル作成
  python manage.py compilemessages --locale ja && \  # 日本語メッセージコンパイル
  python manage.py collectstatic --no-input         # 静的ファイル収集

# アプリケーション起動コマンド
CMD ["gunicorn", "foc.asgi:application", \          # ASGIアプリケーション起動
"-k", "foc.workers.CustomUvicornWorker", \          # カスタムUvicornワーカー
"--access-logfile", "-", \                          # アクセスログを標準出力
"-b", "0.0.0.0:8000", \                             # バインドアドレス
"--max-requests", "500", \                          # 最大リクエスト数
"--max-requests-jitter", "200"]                     # リクエスト数のばらつき
```

#### イメージの特徴
- **マルチステージビルド**: 使用していない（シンプルな構成）
- **日本語対応**: 日本語フォントとメッセージファイルを含む
- **高パフォーマンス**: Gunicorn + UvicornでASGI対応
- **セキュリティ**: 非rootユーザーでの実行

### フロントエンドイメージ（foc-frontend）

#### Dockerfileの内容と説明

```dockerfile
# ビルドステージ: Node.js 12.18.3
FROM node:12.18.3 as build-stage

# ビルド環境の引数
ARG BUILD_ENVIRONMENT

# 作業ディレクトリ設定
WORKDIR /app

# package.jsonファイルのコピー
COPY package*.json /app/
RUN npm install                    # 依存関係インストール

# ソースコードのコピー
COPY ./ /app/

# Angularアプリケーションのビルド
RUN npm run build -- $BUILD_ENVIRONMENT --output-path=./dist/out

# 本番ステージ: Nginx
FROM nginx

# 環境変数の設定
ENV \
  DEBUG_LOG=true \                 # デバッグログ有効
  API_URL=/api \                   # APIエンドポイント
  CARD_TOKEN_URL=https://beta.epsilon.jp/js/token.js \  # クレジットカードトークンURL
  INQUIRY_ADDRESS=<EMAIL>                    # 問い合わせメールアドレス

# ビルド結果をNginxにコピー
COPY --from=build-stage /app/dist/out/ /usr/share/nginx/html

# Nginx設定テンプレートのコピー
COPY ./support/docker/default.conf.template /etc/nginx/templates/default.conf.template

# 環境変数置換スクリプトのコピー
COPY ./support/docker/40-foc-envsubst.sh /docker-entrypoint.d/40-foc-envsubst.sh
RUN chmod 775 /docker-entrypoint.d/40-foc-envsubst.sh
```

#### Nginx設定テンプレート（default.conf.template）

```nginx
server {
  listen 80;                       # ポート80でリッスン
  client_max_body_size 3g;         # 最大アップロードサイズ3GB
  keepalive_timeout 0;             # Keep-Alive無効
  add_header X-Frame-Options SAMEORIGIN;        # クリックジャッキング対策
  add_header X-Content-Type-Options nosniff;    # MIME型スニッフィング対策
  add_header X-XSS-Protection "1; mode=block";  # XSS対策
  add_header Cache-Control "no-store";          # キャッシュ無効

  # 静的ファイル配信
  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri $uri/ /index.html =404;     # SPA用ルーティング
  }

  # エラーページ
  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/share/nginx/html;
  }

  # APIプロキシ設定
  location ~ ^/(${URLS})/ {
    proxy_pass http://${DAPHNE_HOST}:${DAPHNE_PORT};  # バックエンドへのプロキシ
    proxy_http_version 1.1;
    proxy_read_timeout ${TIMEOUT};
  }
}
```

#### 環境変数置換スクリプト（40-foc-envsubst.sh）

```bash
#!/bin/sh

set -e

foc_envsubst() {
  local defined_envs
  # 環境変数を取得して${変数名}形式に変換
  defined_envs=$(printf '${%s} ' $(env | cut -d= -f1))
  # テンプレートファイルの環境変数を実際の値に置換
  envsubst "$defined_envs" < /usr/share/nginx/html/assets/config.json.template > /usr/share/nginx/html/assets/config.json
}

foc_envsubst

exit 0
```

#### イメージの特徴
- **マルチステージビルド**: Node.jsでビルド、Nginxで配信
- **SPA対応**: Angularルーティング用の設定
- **環境変数対応**: 実行時に設定を動的変更可能
- **セキュリティヘッダー**: 各種セキュリティ対策を実装

## リリース手順

### 1. 本番環境へのリリース

#### 1.1 事前準備
```powershell
# 作業ディレクトリに移動
cd foc-backend/support/deployment/production

# 実行権限を付与（Linuxの場合）
chmod +x 00-boot-docker-registry.sh
```

#### 1.2 Dockerレジストリの起動
```powershell
# Dockerレジストリサービスを作成
./00-boot-docker-registry.sh
```

**コマンドの詳細説明:**
- `docker service create`: Docker Swarmでサービスを作成
- `--name registry`: サービス名を「registry」に設定
- `--publish published=5000,target=5000`: ホストの5000番ポートをコンテナの5000番ポートにマッピング
- `--restart-condition=on-failure`: 失敗時のみ再起動
- `registry:2`: Docker公式レジストリイメージ（バージョン2）

#### 1.3 Dockerイメージのビルド
```powershell
# バックエンドイメージのビルド
docker build -t 127.0.0.1:5000/foc-backend:1.1.0 ./foc-backend

# フロントエンドイメージのビルド
docker build -t 127.0.0.1:5000/foc-frontend:1.1.0 ./foc-frontend
```

**コマンドの詳細説明:**
- `docker build`: Dockerイメージをビルド
- `-t 127.0.0.1:5000/foc-backend:1.1.0`: イメージ名とタグを指定（ローカルレジストリ）
- `./foc-backend`: ビルドコンテキスト（Dockerfileのあるディレクトリ）

#### 1.4 Dockerイメージのプッシュ
```powershell
# バックエンドイメージをレジストリにプッシュ
docker push 127.0.0.1:5000/foc-backend:1.1.0

# フロントエンドイメージをレジストリにプッシュ
docker push 127.0.0.1:5000/foc-frontend:1.1.0
```

**コマンドの詳細説明:**
- `docker push`: ローカルイメージをレジストリにアップロード
- `127.0.0.1:5000`: ローカルDockerレジストリのアドレス
- `:1.1.0`: バージョンタグ

#### 1.5 Docker Swarmスタックのデプロイ
```powershell
# 本番環境スタックをデプロイ
docker stack deploy -c docker-compose.yml foc-production
```

**コマンドの詳細説明:**
- `docker stack deploy`: Docker Swarmでスタックをデプロイ
- `-c docker-compose.yml`: 使用するComposeファイルを指定
- `foc-production`: スタック名

### 2. ステージング環境へのリリース

#### ST環境でのdocker-compose up -dの動作

### 実行時の処理フロー

```powershell
# ST環境でのデプロイ
cd foc-backend/support/deployment/staging
docker-compose up -d
```

#### 1. イメージビルド（最新ソース取得）

**backendサービス:**
```yaml
backend:
  image: foc-backend
  build:
    context: ./backend/    # ← ここでソース取得場所を指定
```

**frontendサービス:**
```yaml
frontend:
  image: foc-frontend
  build:
    context: ./frontend/   # ← ここでソース取得場所を指定
```

#### 2. 実行される処理

1. **ソースコード取得**
   - `./backend/` ディレクトリからDjangoアプリケーションの最新ソースを取得
   - `./frontend/` ディレクトリからAngularアプリケーションの最新ソースを取得

2. **Dockerイメージビルド**
   ```bash
   # backendイメージのビルド
   docker build -t foc-backend ./backend/
   
   # frontendイメージのビルド
   docker build -t foc-frontend ./frontend/
   ```

3. **サービス起動**
   - 既存のコンテナを停止
   - 新しいイメージでコンテナを起動
   - 依存関係に従って起動順序を制御

### 最新ソースの取得場所

#### ディレクトリ構造
```
foc-backend/support/deployment/staging/
├── docker-compose.yml
├── traefik-dynamic.yml
├── backend/                    # ← バックエンドの最新ソース
│   ├── Dockerfile
│   ├── pyproject.toml
│   ├── manage.py
│   └── ... (Djangoアプリケーション)
└── frontend/                   # ← フロントエンドの最新ソース
    ├── Dockerfile
    ├── package.json
    ├── angular.json
    └── ... (Angularアプリケーション)
```

#### ソース更新の流れ

1. **開発者がコードを変更**
   ```bash
   # 開発者がbackend/frontendディレクトリでコードを変更
   git add .
   git commit -m "新機能追加"
   git push origin develop
   ```

2. **ST環境サーバーで最新ソースを取得**
   ```bash
   # ST環境サーバーで
   cd foc-backend/support/deployment/staging
   git pull origin develop  # 最新ソースを取得
   ```

3. **docker-compose up -dでデプロイ**
   ```bash
   docker-compose up -d  # 最新ソースでビルド・デプロイ
   ```

### 実際の動作詳細

#### 1. 初回実行時
```bash
# イメージが存在しない場合
docker-compose up -d
# → backend/frontendディレクトリからビルド
# → 新しいイメージを作成
# → コンテナを起動
```

#### 2. ソース更新後の実行
```bash
# ソースコードが変更された場合
docker-compose up -d
# → 既存のコンテナを停止
# → 新しいソースでイメージを再ビルド
# → 新しいイメージでコンテナを起動
# → ダウンタイムが発生（一時的な停止）
```

#### 3. 設定のみ変更の場合
```bash
# docker-compose.ymlのみ変更された場合
docker-compose up -d
# → 既存のイメージを使用
# → 設定変更を反映してコンテナを再起動
```

### ダウンタイムについて

#### ST環境の特徴
- **ダウンタイムあり**: `docker-compose up -d`でサービス全体が一度停止
- **ビルド時間**: ソースコードの変更がある場合、ビルド時間が追加される
- **再起動時間**: コンテナの起動時間が追加される

#### 推定ダウンタイム時間
```bash
# バックエンドビルド時間: 約2-5分
# フロントエンドビルド時間: 約1-3分
# コンテナ起動時間: 約30秒-1分
# 合計ダウンタイム: 約3-9分
```

### 本番環境との違い

| 項目 | ST環境 | 本番環境 |
|------|--------|----------|
| **ソース取得** | ローカルディレクトリから | 事前ビルド済みイメージ |
| **ビルド** | 実行時にビルド | 事前にビルド |
| **ダウンタイム** | あり（3-9分） | なし（ローリングアップデート） |
| **デプロイ方式** | docker-compose | docker stack deploy |
| **冗長化** | 単一インスタンス | 2レプリカ |

### 運用上の注意点

#### 1. ソースコードの同期
```bash
# ST環境サーバーで定期的に最新ソースを取得
cd foc-backend/support/deployment/staging
git pull origin develop
```

#### 2. ビルドキャッシュの活用
```bash
# ビルド時間短縮のためキャッシュを活用
docker-compose build --no-cache  # キャッシュを使わない場合
docker-compose build             # キャッシュを活用（デフォルト）
```

#### 3. ログ確認
```bash
# デプロイ後の動作確認
docker-compose logs -f backend
docker-compose logs -f frontend
```

#### 4. ロールバック
```bash
# 問題が発生した場合のロールバック
git checkout HEAD~1  # 前のコミットに戻す
docker-compose up -d  # 再デプロイ
```

---

## コマンド詳細説明

### Docker Swarm関連コマンド

#### サービス管理
```powershell
# サービス一覧表示
docker service ls

# サービス詳細確認
docker service ps foc-production_prd-backend

# サービスログ確認
docker service logs foc-production_prd-backend

# サービス更新（イメージ変更）
docker service update --image 127.0.0.1:5000/foc-backend:1.1.1 foc-production_prd-backend
```

#### スタック管理
```powershell
# スタック一覧表示
docker stack ls

# スタック削除
docker stack rm foc-production

# スタック再デプロイ
docker stack deploy -c docker-compose.yml foc-production
```

### Docker Compose関連コマンド

```powershell
# サービス起動
docker-compose up -d

# サービス停止
docker-compose down

# サービス再起動
docker-compose restart

# ログ確認
docker-compose logs -f backend

# サービス状態確認
docker-compose ps
```

### イメージ管理コマンド

```powershell
# イメージ一覧表示
docker images

# イメージ削除
docker rmi 127.0.0.1:5000/foc-backend:1.1.0

# イメージ詳細確認
docker inspect 127.0.0.1:5000/foc-backend:1.1.0

# イメージ履歴確認
docker history 127.0.0.1:5000/foc-backend:1.1.0
```

## トラブルシューティング

### よくある問題と解決方法

#### 1. イメージビルドエラー
```powershell
# キャッシュを使わずにビルド
docker build --no-cache -t 127.0.0.1:5000/foc-backend:1.1.0 ./foc-backend

# 詳細ログでビルド
docker build --progress=plain -t 127.0.0.1:5000/foc-backend:1.1.0 ./foc-backend
```

#### 2. レジストリ接続エラー
```powershell
# レジストリサービス確認
docker service ls | grep registry

# レジストリ再作成
docker service rm registry
./00-boot-docker-registry.sh
```

#### 3. サービス起動エラー
```powershell
# サービスログ確認
docker service logs foc-production_prd-backend

# サービス詳細確認
docker service inspect foc-production_prd-backend

# サービス再作成
docker service update --force foc-production_prd-backend
```

#### 4. データベース接続エラー
```powershell
# データベースコンテナ確認
docker service ps foc-production_prd-db

# データベースログ確認
docker service logs foc-production_prd-db

# データベース接続テスト
docker exec -it $(docker ps -q -f name=prd-db) psql -U foc -d foc
```

### ロールバック手順

```powershell
# 前のバージョンに戻す
docker service update --image 127.0.0.1:5000/foc-backend:1.0.9 foc-production_prd-backend
docker service update --image 127.0.0.1:5000/foc-frontend:1.0.9 foc-production_prd-frontend

# ロールバック確認
docker service ps foc-production_prd-backend
docker service ps foc-production_prd-frontend
```

## 監視・ログ確認

### サービス監視

#### 1. サービス状態確認
```powershell
# 全サービスの状態確認
docker service ls

# 特定サービスの詳細確認
docker service ps foc-production_prd-backend
docker service ps foc-production_prd-frontend
```

#### 2. リソース使用量確認
```powershell
# コンテナのリソース使用量確認
docker stats

# 特定コンテナのリソース使用量確認
docker stats $(docker ps -q -f name=foc-production)
```

### ログ確認

#### 1. アプリケーションログ
```powershell
# バックエンドログ確認
docker service logs -f foc-production_prd-backend

# フロントエンドログ確認
docker service logs -f foc-production_prd-frontend

# データベースログ確認
docker service logs -f foc-production_prd-db
```

#### 2. Traefikログ確認
```powershell
# Traefikログ確認
docker service logs -f foc-production_traefik

# Traefikダッシュボードアクセス
# http://localhost:8080
```

#### 3. ログフィルタリング
```powershell
# エラーログのみ表示
docker service logs foc-production_prd-backend | grep ERROR

# 特定時間のログ表示
docker service logs --since 1h foc-production_prd-backend

# 最新100行のログ表示
docker service logs --tail 100 foc-production_prd-backend
```

### ヘルスチェック

#### 1. アプリケーションヘルスチェック
```powershell
# バックエンドヘルスチェック
curl -f http://localhost:8000/api/_ping

# フロントエンドヘルスチェック
curl -f http://localhost:80

# データベース接続チェック
docker exec -it $(docker ps -q -f name=prd-db) pg_isready -U foc
```

#### 2. SSL証明書確認
```powershell
# SSL証明書の有効性確認
openssl s_client -connect www.foc.jpn.com:443 -servername www.foc.jpn.com

# 証明書の詳細確認
echo | openssl s_client -connect www.foc.jpn.com:443 -servername www.foc.jpn.com 2>/dev/null | openssl x509 -noout -dates
```

## 環境別設定詳細

### 本番環境（production）

#### 特徴
- **高可用性**: 各サービス2レプリカで冗長化
- **SSL対応**: Let's Encryptによる自動証明書取得
- **セキュリティ**: デバッグ無効、セキュリティヘッダー有効
- **パフォーマンス**: 本番用データベース設定

#### 環境変数
```yaml
# バックエンド
DEBUG: 0                    # デバッグ無効
DATABASE_URL: *****************************************************/foc
FRONTEND_HOSTNAME: https://www.foc.jpn.com/
EPSILON_REGISTER_PAYMENT_URL: https://secure.epsilon.jp/cgi-bin/order/direct_card_payment.cgi

# フロントエンド
DEBUG_LOG: false            # デバッグログ無効
CARD_TOKEN_URL: https://secure.epsilon.jp/js/token.js
```

### ステージング環境（staging）

#### 特徴
- **テスト用**: 本番環境と同じ構成でテスト実行
- **認証**: Basic認証でアクセス制限
- **デバッグ**: デバッグモード有効
- **テストデータ**: テスト用データベース

#### 環境変数
```yaml
# バックエンド
DEBUG: 1                    # デバッグ有効
DATABASE_URL: *****************************************************/foc
FRONTEND_HOSTNAME: https://stg.foc.jpn.com/
EPSILON_REGISTER_PAYMENT_URL: https://beta.epsilon.jp/cgi-bin/order/direct_card_payment.cgi

# フロントエンド
DEBUG_LOG: true             # デバッグログ有効
CARD_TOKEN_URL: https://beta.epsilon.jp/js/token.js
```

### 開発環境（develop）

#### 特徴
- **ローカル開発**: 開発者向けの軽量構成
- **ホットリロード**: コード変更時の自動再読み込み
- **デバッグ**: 詳細なデバッグ情報
- **開発データ**: 開発用データベース

#### 環境変数
```yaml
# バックエンド
DEBUG: 1                    # デバッグ有効
DATABASE_URL: ****************************/foc
FRONTEND_HOSTNAME: http://localhost:8000/

# フロントエンド
DEBUG_LOG: true             # デバッグログ有効
CARD_TOKEN_URL: https://beta.epsilon.jp/js/token.js
```

## セキュリティ考慮事項

### 1. イメージセキュリティ
- **ベースイメージ**: 公式イメージを使用
- **パッケージ更新**: 定期的なセキュリティアップデート
- **非root実行**: 可能な限り非特権ユーザーで実行

### 2. ネットワークセキュリティ
- **SSL/TLS**: 本番環境ではHTTPS必須
- **ファイアウォール**: 必要最小限のポートのみ開放
- **内部通信**: Dockerネットワーク内での通信

### 3. データセキュリティ
- **環境変数**: 機密情報は環境変数で管理
- **ボリューム**: 永続データはDockerボリュームで管理
- **バックアップ**: 定期的なデータベースバックアップ

## パフォーマンス最適化

### 1. イメージ最適化
- **マルチステージビルド**: 不要なファイルを除外
- **レイヤーキャッシュ**: 効率的なレイヤー構成
- **イメージサイズ**: 必要最小限のパッケージのみ含める

### 2. アプリケーション最適化
- **静的ファイル**: Nginxで効率的に配信
- **データベース**: 適切なインデックス設定
- **キャッシュ**: 適切なキャッシュ戦略

### 3. インフラ最適化
- **ロードバランシング**: Traefikによる効率的なルーティング
- **スケーリング**: 負荷に応じた自動スケーリング
- **リソース制限**: 適切なリソース制限設定

---

## docker-compose.yml（compose.yml）の説明

### 本番用（production/docker-compose.yml）

#### サービス構成

```yaml
version: "3.8"
services:
  traefik:
    image: traefik:v2.3
    ports:
      - 80:80
      - 443:443
      - 8080:8080
    configs:
      - source: traefik-config
        target: /etc/traefik/dynamic.yml
        mode: 0440
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-acme:/etc/traefik/acme
      - type: bind
        source: ./config/prd/tls/
        target: /etc/traefik/tls/
    command:
      - "--api.dashboard=true"
      - "--providers.docker.swarmMode=true"
      - "--providers.docker.exposedByDefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.focresolver.acme.httpchallenge=true"
      - "--certificatesresolvers.focresolver.acme.email=<EMAIL>"
    deploy:
      labels:
        traefik.enable: "true"
        traefik.http.routers.dashboard.rule: PathPrefix(`/api`) || PathPrefix(`/dashboard`)
        traefik.http.routers.dashboard.entrypoints: dashboard
        traefik.http.routers.dashboard.service: api@internal
        traefik.http.routers.dashboard.middlewares: auth
        traefik.http.middlewares.auth.basicauth.users: test:$$apr1$$H6uskkkW$$IgXLP6ewTrSuBkTrqE8wj/,test2:$$apr1$$d9hr9HBB$$4HxwgUir3HP4EsggP/QNo0
      placement:
        constraints:
          - node.role == manager
      replicas: 1
      update_config:
        parallelism: 1

  prd-db:
    image: postgres:12
    ports:
      - 6543:5432
    volumes:
      - type: bind
        source: /var/lib/prd-postgresql/data
        target: /var/lib/postgresql/data
    environment:
      POSTGRES_USER: foc
      POSTGRES_PASSWORD: LDarcJE3MMooMPQEZKFTsL8R
    deploy:
      labels:
        traefik.enable: "false"
      replicas: 1
      update_config:
        parallelism: 1

  prd-backend:
    image: 127.0.0.1:5000/foc-backend:1.1.0
    environment:
      DATABASE_URL: *****************************************************/foc
      SECRET_KEY: p^j@6!olnn2nqn&iat(@2)fnn=_!8@k7h-(pcct@wp65#80_o)
      DEBUG: 0
      FRONTEND_HOSTNAME: https://www.foc.jpn.com/
      EPSILON_REGISTER_PAYMENT_URL: https://secure.epsilon.jp/cgi-bin/order/direct_card_payment.cgi
    configs:
      - source: prd-jwt-key
        target: /usr/src/app/jwt-rsa
        mode: 0440
    volumes:
      - type: bind
        source: /opt/foc/uploads/prd
        target: /usr/src/app/media
    depends_on:
      - prd-db
    deploy:
      labels:
        traefik.enable: "true"
        traefik.http.services.prd-backend-service.loadbalancer.server.port: 8000
        traefik.http.middlewares.deployApi.stripPrefix.prefixes: /api
        traefik.http.routers.prd-backend.rule: (Host(`www.foc.jpn.com`) || Host(`foc.jpn.com`)) && PathPrefix(`/api`)
        traefik.http.routers.prd-backend.entrypoints: web
        traefik.http.routers.prd-backend.middlewares: https_redirect,deployApi
        traefik.http.routers.prd-backend-tls.rule: (Host(`www.foc.jpn.com`) || Host(`foc.jpn.com`)) && PathPrefix(`/api`)
        traefik.http.routers.prd-backend-tls.entrypoints: websecure
        traefik.http.routers.prd-backend-tls.middlewares: deployApi
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s

  prd-frontend:
    image: 127.0.0.1:5000/foc-frontend:1.1.0
    environment:
      DAPHNE_HOST: prd-backend
      DAPHNE_PORT: 8000
      URLS: api|static|_ping
      TIMEOUT: 600
    configs:
      - source: prd-nginx-config
        target: /etc/nginx/conf.d/default.conf.template
        mode: 0740
    volumes:
      - type: bind
        source: /opt/foc/uploads/prd
        target: /usr/share/nginx/html/media
        read_only: true
    command: /bin/sh -c "envsubst '$$DAPHNE_HOST$$DAPHNE_PORT$$URLS$$TIMEOUT' < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    depends_on:
      - prd-backend
    deploy:
      labels:
        traefik.enable: "true"
        traefik.http.services.prd-frontend-service.loadbalancer.server.port: 80
        traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
        traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
        traefik.http.routers.prd-frontend.rule: Host(`www.foc.jpn.com`) || Host(`foc.jpn.com`)
        traefik.http.routers.prd-frontend.entrypoints: web
        traefik.http.routers.prd-frontend.middlewares: https_redirect
        traefik.http.routers.prd-frontend-tls.tls: "true"
        traefik.http.routers.prd-frontend-tls.rule: Host(`www.foc.jpn.com`) || Host(`foc.jpn.com`)
        traefik.http.routers.prd-frontend-tls.entrypoints: websecure
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s

volumes:
  traefik-acme:

configs:
  traefik-config:
    file: ./config/traefik/dynamic.yml
  prd-nginx-config:
    file: ./config/prd/nginx.conf
  prd-jwt-key:
    file: ./config/prd/jwt-rsa
```

#### 各サービスの詳細説明

**traefik（リバースプロキシ）**
- **役割**: リバースプロキシ兼ロードバランサ
- **SSL証明書**: Let's Encryptによる自動取得
- **ダッシュボード**: ポート8080で管理画面を提供
- **ルーティング**: ホスト名とパスに基づいて各サービスに振り分け
- **認証**: Basic認証でダッシュボードを保護

**prd-db（データベース）**
- **役割**: 本番用PostgreSQLデータベース
- **永続化**: `/var/lib/prd-postgresql/data` にデータを保存
- **ポート**: ホストの6543番ポートにマッピング
- **認証**: focユーザーでアクセス

**prd-backend（バックエンド）**
- **役割**: Djangoアプリケーション
- **レプリカ**: 2台で冗長化
- **環境変数**: 本番用設定（DEBUG=0、本番用DB接続等）
- **API**: `/api` パスでアクセス
- **認証**: JWT認証を使用

**prd-frontend（フロントエンド）**
- **役割**: Angularアプリケーション（Nginxで配信）
- **レプリカ**: 2台で冗長化
- **プロキシ**: APIリクエストをバックエンドに転送
- **静的ファイル**: Nginxで効率的に配信

#### 重要な設定項目

**deploy設定（Swarm用）**
```yaml
deploy:
  replicas: 2                    # レプリカ数（冗長化）
  update_config:
    parallelism: 1               # 同時更新数
    delay: 10s                   # 更新間隔（ローリングアップデート用）
```

**Traefikラベル設定**
```yaml
labels:
  traefik.enable: "true"         # Traefikで管理
  traefik.http.routers.xxx.rule: Host(`www.foc.jpn.com`)  # ルーティングルール
  traefik.http.services.xxx.loadbalancer.server.port: 8000  # サービスポート
```

---

### ステージング用（staging/docker-compose.yml）

#### サービス構成

```yaml
version: "3.8"
services:
  traefik:
    image: traefik:v2.3
    ports:
      - 80:80
      - 443:443
      - 8080:8080
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik-dynamic.yml:/etc/traefik/dynamic.yml:ro
      - traefik-acme:/etc/traefik/acme
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.focresolver.acme.httpchallenge=true"
      - "--certificatesresolvers.focresolver.acme.email=<EMAIL>"
    restart: unless-stopped

  db:
    image: postgres:12
    ports:
      - 5432:5432
    volumes:
      - type: bind
        source: /var/lib/postgresql/data
        target: /var/lib/postgresql/data
    environment:
      POSTGRES_USER: foc
      POSTGRES_PASSWORD: RsP3HExHVC6caLY2ZdWuncBo
    restart: unless-stopped

  backend:
    image: foc-backend
    build:
      context: ./backend/
    environment:
      DATABASE_URL: *************************************************/foc
      SECRET_KEY: l&uc^39mry24&#le^cj0=0oviq8+4&wgl0iyxtx9uy%wvtz4_(
      DEBUG: 1
      FRONTEND_HOSTNAME: https://stg.foc.jpn.com/
    volumes:
      - type: bind
        source: ./jwt-rsa
        target: /usr/src/app/jwt-rsa
      - type: bind
        source: /opt/foc/uploads
        target: /usr/src/app/media
    depends_on:
      - db
    labels:
      traefik.enable: "true"
      traefik.http.routers.backend.rule: Host(`stg.foc.jpn.com`) && PathPrefix(`/api`)
      traefik.http.routers.backend.entrypoints: web
      traefik.http.routers.backend.middlewares: https_redirect,deployApi
      traefik.http.routers.backend-tls.rule: Host(`stg.foc.jpn.com`) && PathPrefix(`/api`)
      traefik.http.routers.backend-tls.entrypoints: websecure
      traefik.http.routers.backend-tls.tls.certresolver: focresolver
      traefik.http.routers.backend-tls.middlewares: deployApi
      traefik.http.middlewares.deployApi.stripPrefix.prefixes: /api
      traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
      traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
    restart: unless-stopped

  frontend:
    image: foc-frontend
    build:
      context: ./frontend
    environment:
      DAPHNE_HOST: backend
      DAPHNE_PORT: 8000
      URLS: api|static|_ping
      TIMEOUT: 600
      DEBUG_LOG: "true"
      API_URL: /api
      CARD_TOKEN_URL: https://beta.epsilon.jp/js/token.js
    labels:
      traefik.enable: "true"
      traefik.http.routers.frontend.rule: Host(`stg.foc.jpn.com`)
      traefik.http.routers.frontend.entrypoints: web
      traefik.http.routers.frontend.middlewares: https_redirect, test-auth
      traefik.http.middlewares.test-auth.basicauth.users: msi:$$apr1$$Bl.RBMJo$$GDShnvFEncoZU5mMB054d/
      traefik.http.routers.frontend-tls.rule: Host(`stg.foc.jpn.com`)
      traefik.http.routers.frontend-tls.entrypoints: websecure
      traefik.http.routers.frontend-tls.tls.certresolver: focresolver
      traefik.http.routers.frontend-tls.middlewares: test-auth
      traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
      traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
    volumes:
      - type: bind
        source: /opt/foc/uploads
        target: /usr/share/nginx/html/media
        read_only: true
    restart: unless-stopped

volumes:
  traefik-acme:
```

#### 本番との主な違い

1. **Swarm vs Compose**
   - ステージング: `docker-compose` で起動
   - 本番: `docker stack deploy` でSwarmにデプロイ

2. **ビルド方式**
   - ステージング: `build:` セクションでローカルビルド
   - 本番: 事前にビルドしたイメージを使用

3. **レプリカ設定**
   - ステージング: `deploy:` セクションなし（単一インスタンス）
   - 本番: `replicas: 2` で冗長化

4. **再起動設定**
   - ステージング: `restart: unless-stopped`
   - 本番: Swarmの自動復旧機能

---

## ダウンタイムについて

### 本番リリース（Swarm）

- **ダウンタイムなし（ローリングアップデート）**
  - `deploy:` セクションの `update_config` で、サービスを1つずつ順番に入れ替えるローリングアップデートが行われます。
  - 例: `replicas: 2` なら、1台ずつ新しいイメージに切り替わるため、サービス停止時間は発生しません。

### ステージングリリース（docker-compose）

- **ダウンタイムあり**
  - `docker-compose up -d` でサービスを再起動するため、切り替え時に一時的なダウンタイムが発生します。
  - サービス全体が一度停止し、新しいイメージで再起動されます。

--- 