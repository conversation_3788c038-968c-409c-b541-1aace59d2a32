from typing import Dict

from django.test import TestCase
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from orders.tests.factories import EntryDetailMsgFactory
from seko.models import Moshu
from seko.tests.factories import MoshuFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake_provider = Faker(locale='ja_JP')


class MsgDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.detail_message = EntryDetailMsgFactory()

        self.api_client = APIClient()
        self.moshu: Moshu = MoshuFactory(seko=self.detail_message.entry_detail.entry.seko)
        refresh = RefreshToken.for_user(self.moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_msg_detail_succeed(self) -> None:
        """追悼メッセージ詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['entry_detail']['id'], self.detail_message.pk)
        self.assertEqual(record['relation_ship'], self.detail_message.relation_ship)
        self.assertEqual(record['honbun'], self.detail_message.honbun)
        self.assertEqual(record['release_status'], self.detail_message.release_status)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

    def test_msg_update_fail_by_seko_disabled(self) -> None:
        """追悼メッセージ詳細APIが所属する施行が無効になっているため失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.detail_message.entry_detail.entry.seko.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_msg_detail_deny_from_another_moshu(self) -> None:
        """追悼メッセージ詳細APIが他の喪主からの呼び出しを拒否する"""
        another_moshu: Moshu = MoshuFactory()
        refresh = RefreshToken.for_user(another_moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_msg_detail_deny_from_staff(self) -> None:
        """追悼メッセージ詳細APIが担当者からの呼び出しを拒否する"""
        staff = StaffFactory()
        refresh = RefreshToken.for_user(staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_msg_detail_fail_without_auth(self) -> None:
        """追悼メッセージ詳細APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class MsgUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.detail_message = EntryDetailMsgFactory()

        self.api_client = APIClient()
        self.moshu: Moshu = MoshuFactory(seko=self.detail_message.entry_detail.entry.seko)
        refresh = RefreshToken.for_user(self.moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        new_detail_message_data = EntryDetailMsgFactory.build()
        return {
            'relation_ship': new_detail_message_data.relation_ship,
            'honbun': new_detail_message_data.honbun,
            'release_status': new_detail_message_data.release_status,
        }

    def test_moshu_update_succeed(self) -> None:
        """追悼メッセージを更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['entry_detail']['id'], self.detail_message.pk)
        self.assertEqual(record['relation_ship'], params['relation_ship'])
        self.assertEqual(record['honbun'], params['honbun'])
        self.assertEqual(record['release_status'], params['release_status'])
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])

    def test_moshu_update_fail_by_seko_disabled(self) -> None:
        """追悼メッセージ更新APIが所属する施行が無効になっているため失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.detail_message.entry_detail.entry.seko.disable()

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_moshu_update_deny_from_another_moshu(self) -> None:
        """追悼メッセージ更新APIが他の喪主からの呼び出しを拒否する"""
        another_moshu: Moshu = MoshuFactory()
        refresh = RefreshToken.for_user(another_moshu)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_moshu_update_deny_from_staff(self) -> None:
        """追悼メッセージ更新APIが担当者からの呼び出しを拒否する"""
        staff = StaffFactory()
        refresh = RefreshToken.for_user(staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_moshu_update_fail_without_auth(self) -> None:
        """追悼メッセージ更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('orders:msg_detail', kwargs={'pk': self.detail_message.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
