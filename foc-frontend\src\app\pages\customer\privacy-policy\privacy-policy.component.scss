
body {
  font-size: 14px;
}
.contents{
  background-color: #ffffff;
  width: 90%;
  max-width: 1000px;
  margin: 0px auto 50px;
  border-radius: 10px;
  font-size: 1rem;
  padding: 25px 50px;
  color: #000000;
  @media screen and (max-width: 560px) {
    padding: 25px 20px;
    margin-top: 10px;
    
  }
  .title {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  h1 {
    font-size: 1.3em;
    letter-spacing: 0.1em;
    font-weight: bold;
    line-height: 36px;
    text-align: center;
    text-indent: 0.1em;
    margin: 0px;
    margin-right: 10px;
    @media screen and (max-width: 560px) {
      font-size: 1.2em;
      line-height: initial;
      &.sub {
        margin-top: 10px;
      }
    }
  }
  .company {
    text-align: right;
    margin: 10px;
  }
  .date {
    text-align: left;
    margin-top: 20px;
  }
  .wrapTwo {
    margin-top: 20px;
    font-size: 14px;
    @media screen and (max-width: 480px) {
      font-size: 12px;
    }
    line-height: 2;
    .top {
      font-size: 16px;
      text-indent: 10px;
      @media screen and (max-width: 480px) {
        font-size: 14px;
      }
    }
    h2 {
      font-weight: bold;
      margin: 20px 0 10px;
      font-size: 20px;
      line-height: 1.5;
      @media screen and (max-width: 480px) {
        font-size: 16px;
      }
    }
    ul {
      margin-left: 1.5rem;
      text-indent: -1rem;
      &.indent {
        margin-left: 20px;
        text-indent: -20px;
      }
      li .indent {
        margin-left: 20px;
        text-indent: 10px;
      }
    }
    p {
      text-indent: 10px;
      line-height: 2;
      margin-left: 0;
    }
    ul {
      line-height: 2;
    }
    a {
      text-decoration: underline;
    }
  }
}
