import factory
from django.utils import timezone
from factory.django import DjangoModelFactory

from bases.tests.factories import BaseFactory
from chobun_daishi.models import ChobunDaishi
from masters.tests.factories import ChobunDaishiMasterFactory

tz = timezone.get_current_timezone()


class ChobunDaishiFactory(DjangoModelFactory):
    class Meta:
        model = ChobunDaishi

    id = factory.Sequence(lambda n: n)
    company = factory.SubFactory(BaseFactory)
    daishi = factory.SubFactory(ChobunDaishiMasterFactory)
