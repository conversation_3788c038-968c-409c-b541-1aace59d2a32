from typing import Dict

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from items.tests.factories import ItemSupplierFactory
from orders.tests.factories import EntryDetailKumotsuFactory
from staffs.tests.factories import StaffFactory
from suppliers.tests.factories import SupplierFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class EntryDetailKumotsuDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.detail_kumotsu = EntryDetailKumotsuFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_detail_kumotsu_detail_succeed(self) -> None:
        """供花供物詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:kumotsu_detail', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['entry_detail']['id'], self.detail_kumotsu.pk)
        self.assertEqual(record['okurinushi_company'], self.detail_kumotsu.okurinushi_company)
        self.assertEqual(record['okurinushi_title'], self.detail_kumotsu.okurinushi_title)
        self.assertEqual(
            record['okurinushi_company_kana'], self.detail_kumotsu.okurinushi_company_kana
        )
        self.assertEqual(record['okurinushi_name'], self.detail_kumotsu.okurinushi_name)
        self.assertEqual(record['okurinushi_kana'], self.detail_kumotsu.okurinushi_kana)
        self.assertEqual(record['okurinushi_zip_code'], self.detail_kumotsu.okurinushi_zip_code)
        self.assertEqual(
            record['okurinushi_prefecture'], self.detail_kumotsu.okurinushi_prefecture
        )
        self.assertEqual(record['okurinushi_address_1'], self.detail_kumotsu.okurinushi_address_1)
        self.assertEqual(record['okurinushi_address_2'], self.detail_kumotsu.okurinushi_address_2)
        self.assertEqual(record['okurinushi_address_3'], self.detail_kumotsu.okurinushi_address_3)
        self.assertEqual(record['okurinushi_tel'], self.detail_kumotsu.okurinushi_tel)
        self.assertEqual(record['renmei1'], self.detail_kumotsu.renmei1)
        self.assertEqual(record['renmei_kana1'], self.detail_kumotsu.renmei_kana1)
        self.assertEqual(record['renmei2'], self.detail_kumotsu.renmei2)
        self.assertEqual(record['renmei_kana2'], self.detail_kumotsu.renmei_kana2)
        self.assertEqual(record['note'], self.detail_kumotsu.note)
        self.assertEqual(record['order_status'], 0)
        self.assertEqual(record['order_ts'], self.detail_kumotsu.order_ts.isoformat())
        self.assertEqual(record['delivery_ts'], self.detail_kumotsu.delivery_ts.isoformat())
        self.assertEqual(record['order_staff'], self.detail_kumotsu.order_staff.id)
        self.assertEqual(record['order_note'], self.detail_kumotsu.order_note)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])
        supplier_dict: Dict = record['supplier']
        self.assertEqual(supplier_dict['name'], self.detail_kumotsu.supplier.name)
        self.assertEqual(supplier_dict['tel'], self.detail_kumotsu.supplier.tel)
        self.assertEqual(supplier_dict['zip_code'], self.detail_kumotsu.supplier.zip_code)

    def test_detail_kumotsu_failed_without_auth(self) -> None:
        """供花供物詳細取得APIはAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:kumotsu_detail', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class EntryDetailKumotsuUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.detail_kumotsu = EntryDetailKumotsuFactory()

        new_supplier = SupplierFactory()
        ItemSupplierFactory(item=self.detail_kumotsu.entry_detail.item, supplier=new_supplier)
        self.new_detail_kumotsu_data = EntryDetailKumotsuFactory.build(
            supplier=new_supplier, order_staff=StaffFactory()
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'okurinushi_company': self.new_detail_kumotsu_data.okurinushi_company,
            'okurinushi_title': self.new_detail_kumotsu_data.okurinushi_title,
            'okurinushi_company_kana': self.new_detail_kumotsu_data.okurinushi_company_kana,
            'okurinushi_name': self.new_detail_kumotsu_data.okurinushi_name,
            'okurinushi_kana': self.new_detail_kumotsu_data.okurinushi_kana,
            'okurinushi_zip_code': self.new_detail_kumotsu_data.okurinushi_zip_code,
            'okurinushi_prefecture': self.new_detail_kumotsu_data.okurinushi_prefecture,
            'okurinushi_address_1': self.new_detail_kumotsu_data.okurinushi_address_1,
            'okurinushi_address_2': self.new_detail_kumotsu_data.okurinushi_address_2,
            'okurinushi_address_3': self.new_detail_kumotsu_data.okurinushi_address_3,
            'okurinushi_tel': self.new_detail_kumotsu_data.okurinushi_tel,
            'renmei1': self.new_detail_kumotsu_data.renmei1,
            'renmei_kana1': self.new_detail_kumotsu_data.renmei_kana1,
            'renmei2': self.new_detail_kumotsu_data.renmei2,
            'renmei_kana2': self.new_detail_kumotsu_data.renmei_kana2,
            'note': self.new_detail_kumotsu_data.note,
            'order_status': self.new_detail_kumotsu_data.order_status,
            'supplier': self.new_detail_kumotsu_data.supplier.pk,
            'order_ts': self.new_detail_kumotsu_data.order_ts,
            'delivery_ts': self.new_detail_kumotsu_data.delivery_ts,
            'order_staff': self.new_detail_kumotsu_data.order_staff.pk,
            'order_note': self.new_detail_kumotsu_data.order_note,
        }

    def test_detail_kumotsu_update_succeed(self) -> None:
        """供花供物を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('orders:kumotsu_detail', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()

        self.assertEqual(record['entry_detail']['id'], self.detail_kumotsu.pk)
        self.assertEqual(
            record['okurinushi_company'], self.new_detail_kumotsu_data.okurinushi_company
        )
        self.assertEqual(record['okurinushi_title'], self.new_detail_kumotsu_data.okurinushi_title)
        self.assertEqual(
            record['okurinushi_company_kana'], self.new_detail_kumotsu_data.okurinushi_company_kana
        )
        self.assertEqual(record['okurinushi_name'], self.new_detail_kumotsu_data.okurinushi_name)
        self.assertEqual(record['okurinushi_kana'], self.new_detail_kumotsu_data.okurinushi_kana)
        self.assertEqual(
            record['okurinushi_zip_code'], self.new_detail_kumotsu_data.okurinushi_zip_code
        )
        self.assertEqual(
            record['okurinushi_prefecture'], self.new_detail_kumotsu_data.okurinushi_prefecture
        )
        self.assertEqual(
            record['okurinushi_address_1'], self.new_detail_kumotsu_data.okurinushi_address_1
        )
        self.assertEqual(
            record['okurinushi_address_2'], self.new_detail_kumotsu_data.okurinushi_address_2
        )
        self.assertEqual(
            record['okurinushi_address_3'], self.new_detail_kumotsu_data.okurinushi_address_3
        )
        self.assertEqual(record['okurinushi_tel'], self.new_detail_kumotsu_data.okurinushi_tel)
        self.assertEqual(record['renmei1'], self.new_detail_kumotsu_data.renmei1)
        self.assertEqual(record['renmei_kana1'], self.new_detail_kumotsu_data.renmei_kana1)
        self.assertEqual(record['renmei2'], self.new_detail_kumotsu_data.renmei2)
        self.assertEqual(record['renmei_kana2'], self.new_detail_kumotsu_data.renmei_kana2)
        self.assertEqual(record['note'], self.new_detail_kumotsu_data.note)
        self.assertEqual(record['order_status'], self.new_detail_kumotsu_data.order_status)
        self.assertEqual(record['order_ts'], self.new_detail_kumotsu_data.order_ts.isoformat())
        self.assertEqual(
            record['delivery_ts'], self.new_detail_kumotsu_data.delivery_ts.isoformat()
        )
        self.assertEqual(record['order_staff'], self.new_detail_kumotsu_data.order_staff.id)
        self.assertEqual(record['order_note'], self.new_detail_kumotsu_data.order_note)
        self.assertIsNotNone(record['created_at'])
        self.assertIsNotNone(record['updated_at'])
        supplier_dict: Dict = record['supplier']
        self.assertEqual(supplier_dict['name'], self.new_detail_kumotsu_data.supplier.name)
        self.assertEqual(supplier_dict['tel'], self.new_detail_kumotsu_data.supplier.tel)
        self.assertEqual(supplier_dict['zip_code'], self.new_detail_kumotsu_data.supplier.zip_code)

    def test_detail_kumotsu_patch_supplier_succeed(self) -> None:
        """供花供物更新APIで発注先を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = dict(
            [(key, val) for key, val in self.basic_params().items() if key == 'supplier']
        )
        response = self.api_client.patch(
            reverse('orders:kumotsu_detail', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record['entry_detail']['id'], self.detail_kumotsu.pk)
        supplier_dict: Dict = record['supplier']
        self.assertEqual(supplier_dict['name'], self.new_detail_kumotsu_data.supplier.name)
        self.assertEqual(supplier_dict['tel'], self.new_detail_kumotsu_data.supplier.tel)
        self.assertEqual(supplier_dict['zip_code'], self.new_detail_kumotsu_data.supplier.zip_code)

    def test_detail_kumotsu_update_failed_without_auth(self) -> None:
        """供花供物更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.put(
            reverse('orders:kumotsu_detail', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

        response = self.api_client.patch(
            reverse('orders:kumotsu_detail', kwargs={'pk': self.detail_kumotsu.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
