from drf_extra_fields.fields import Base64<PERSON><PERSON><PERSON><PERSON>
from rest_framework import serializers

from advertises.models import Advertise
from utils.serializer_mixins import AddUserIdMixin
from utils.serializers import PdfBase64File


class AdvertiseSerializer(AddUserIdMixin, serializers.ModelSerializer):
    banner_file = Base64ImageField(required=False)
    link_file = PdfBase64File(required=False, allow_null=True)
    base_name = serializers.SerializerMethodField()

    class Meta:
        model = Advertise
        fields = '__all__'
        read_only_fields = [
            'del_flg',
            'create_user_id',
            'created_at',
            'update_user_id',
            'updated_at',
        ]

    def get_base_name(self, obj: Advertise):
        return obj.company.base_name


class AdvertiseUpdateSerializer(AdvertiseSerializer):
    class Meta(AdvertiseSerializer.Meta):
        read_only_fields = AdvertiseSerializer.Meta.read_only_fields + ['company']
        extra_kwargs = {'company': {'required': False}}
