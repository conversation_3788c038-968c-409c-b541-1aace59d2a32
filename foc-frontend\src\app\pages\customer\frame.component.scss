@import "src/assets/scss/customer/setting";
.main-header {
  position: relative;
  width: 90%;
  max-width: 1000px;
  height: 100px;
  top: 0;
  z-index: 99;
  margin: 0 auto;
  @media screen and (max-width: 560px) {
    height: 80px;
  }
  background-color: $background-light;
  // box-shadow: 0px 5px 5px -5px rgba(0,0,0,0.5);
  .logo {
    position: relative;
    width: 100%;
    height: 100px;
    margin: 0 auto;
    background-image: url(../../../assets/img/customer/logo_cocoro.png);
    background-repeat: no-repeat;
    background-size: 240px auto;
    cursor: pointer;
    &:focus {
      outline: 0;
    }
    @media screen and (max-width: 560px) {
      height: 80px;
      background-size: 200px auto;
    }
  }
  .cart {
    position: absolute;
    right: 10px;
    top: 40px;
    width: 50px;
    height: 50px;
    background-image: url(../../../assets/img/customer/cart.png);
    background-repeat: no-repeat;
    background-size: 50px auto;
    cursor: pointer;
    outline: none;
    &:hover {
      opacity: .8;
    }
    .count {
      background-color: $label-red;
      position: absolute;
      font-size: 0.65rem;
      height: 17px;
      width: 17px;
      top: 0px;
      right: 21px;
      border-radius: 50%;
      text-align: center;
      line-height: 1.6;
      color: white;

    }
    @media screen and (max-width: 560px) {
      right: 10px;
      top: 40px;
      width: 40px;
      height: 40px;
      background-size: 40px auto;
      .count {
        height: 15px;
        width: 15px;
        top: -2px;
        right: 16px;
        line-height: 1.4;
      }
    }
  }
  .menu-area {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    display: none;
    &.show {
      display: block;
    }
  }
}
.pagetop {
  display: none;
  position: fixed;
  bottom: 0;
  right: -30px;
  color: rgba(0, 0, 0, 0);
  a {
    font-size: 26px;
  }
  img {
    width: 50px;
    @media screen and (max-width: 560px) {
      width: 30px;
    }
  }
}
.routerArea {
  min-height: calc(100% - 190px);
  @media screen and (max-width: 560px) {
    min-height: calc(100% - 210px);
  }
  @media screen and (max-width: 380px) {
    min-height: calc(100% - 210px);
  }
  >.contents {
    height: 100%;
  }

}
footer {
  background-color: #413f36;
  color: #ffffff;
  padding: 10px 0 20px;
  font-size: 18px;
  line-height: 1;
  @media screen and (max-width: 560px) {
    font-size: 14px;
  }
  .contents {
    width: 90%;
    margin: 0 auto;
    padding: 20px 0 0;
  }
  .share {
    max-width: 1000px;
    display: flex;
    justify-content: flex-end;
    a {
      border-style: none;
      vertical-align: bottom;
      width: 40px;
      height: 40px;
      margin: 0 10px;
      background-position: center;
      background-size: 40px;
      background-repeat: no-repeat;
      &.line {
        background-image: url(../../../assets/img/customer/l_logo.png);
      }
      &.facebook {
        background-image: url(../../../assets/img/customer/f_logo.png);
      }
      &.twitter {
        background-image: url(../../../assets/img/customer/t_logo.png);
      }
      &.mail {
        background-image: url(../../../assets/img/customer/m_logo.png);
      }
    }
  }
  ul {
    font-size: 0.8rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    li {
      list-style: disc;
      margin: 10px 0px 10px 40px;
    }
  }
}
