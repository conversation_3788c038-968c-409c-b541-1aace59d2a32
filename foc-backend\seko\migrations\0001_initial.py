# Generated by Django 3.1.1 on 2020-09-29 01:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('masters', '0003_zipcode'),
        ('bases', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Kojin',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('kojin_num', models.IntegerField(verbose_name='numbering')),
                ('name', models.TextField(verbose_name='kojin name')),
                ('kana', models.TextField(verbose_name='kojin kana')),
                ('kaimyo', models.TextField(blank=True, null=True, verbose_name='kaimyo')),
                (
                    'iei_file_name',
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to='portraits',
                        verbose_name='portrait of deceased',
                    ),
                ),
                (
                    'moshu_kojin_relationship',
                    models.TextField(verbose_name='moshu kojin relationship'),
                ),
                ('birth_date', models.DateField(verbose_name='birthday')),
                ('death_date', models.DateField(verbose_name='date of death')),
                ('age_kbn', models.TextField(verbose_name='age type')),
                ('age', models.IntegerField(verbose_name='age')),
                ('note', models.TextField(blank=True, null=True, verbose_name='note')),
                ('del_flg', models.BooleanField(verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'db_table': 'tr_kojin',
            },
        ),
        migrations.CreateModel(
            name='Seko',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('death_date', models.DateField(verbose_name='date of death')),
                ('seko_date', models.DateField(verbose_name='date of seko')),
                ('soke_name', models.TextField(verbose_name='soke name')),
                ('soke_kana', models.TextField(verbose_name='soke kana')),
                ('seko_style_name', models.TextField(verbose_name='seko style display name')),
                ('fuho_site_end_date', models.DateField(verbose_name='fuho site open up to')),
                ('fuho_sentence', models.TextField(verbose_name='fuho sentence')),
                ('fuho_contact_name', models.TextField(verbose_name='fuho contact name')),
                (
                    'fuho_contact_tel',
                    models.CharField(max_length=15, verbose_name='fuho contact tel'),
                ),
                ('henreihin_rate', models.IntegerField(verbose_name='henreihin rate')),
                (
                    'invoice_file_name',
                    models.FileField(
                        blank=True, null=True, upload_to='invoices', verbose_name='invoice file'
                    ),
                ),
                ('note', models.TextField(blank=True, null=True, verbose_name='note')),
                ('del_flg', models.BooleanField(verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'db_table': 'tr_seko',
            },
        ),
        migrations.CreateModel(
            name='Moshu',
            fields=[
                (
                    'seko',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        primary_key=True,
                        related_name='moshu',
                        serialize=False,
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
                ('name', models.TextField(verbose_name='name')),
                ('kana', models.TextField(verbose_name='kana')),
                (
                    'kojin_moshu_relationship',
                    models.TextField(verbose_name='kojin moshu relationship'),
                ),
                ('zip_code', models.CharField(max_length=7, verbose_name='zipcode')),
                ('prefecture', models.TextField(verbose_name='prefecture')),
                ('address_1', models.TextField(verbose_name='address1')),
                ('address_2', models.TextField(blank=True, null=True, verbose_name='address2')),
                ('address_3', models.TextField(blank=True, null=True, verbose_name='address3')),
                ('tel', models.CharField(max_length=15, verbose_name='tel no')),
                (
                    'mobile_num',
                    models.CharField(
                        blank=True, max_length=15, null=True, verbose_name='mobile no'
                    ),
                ),
                ('mail_address', models.TextField(verbose_name='mail address')),
                ('password', models.TextField(verbose_name='password')),
                ('salt', models.CharField(max_length=128, verbose_name='salt')),
                (
                    'soke_site_del_request_flg',
                    models.BooleanField(default=False, verbose_name='soke site delete requested'),
                ),
                (
                    'soke_site_del_flg',
                    models.BooleanField(default=False, verbose_name='soke site deleted'),
                ),
                ('mail_flg', models.BooleanField(default=True, verbose_name='email accepted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'db_table': 'tr_moshu',
            },
        ),
        migrations.CreateModel(
            name='SekoVideo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('title', models.TextField(verbose_name='title')),
                ('live_begin_ts', models.DateTimeField(verbose_name='live begins at')),
                ('live_end_ts', models.DateTimeField(verbose_name='live ends at')),
                ('delivery_end_ts', models.DateTimeField(verbose_name='delivery ends at')),
                ('youtube_code', models.TextField(verbose_name='YouTube code')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='videos',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_video',
            },
        ),
        migrations.CreateModel(
            name='SekoSchedule',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('schedule_name', models.TextField(verbose_name='schedule name')),
                ('hall_name', models.TextField(verbose_name='hall name')),
                (
                    'hall_zip_code',
                    models.CharField(
                        blank=True, max_length=7, null=True, verbose_name='hall zipcode'
                    ),
                ),
                (
                    'hall_address',
                    models.TextField(blank=True, null=True, verbose_name='hall address'),
                ),
                (
                    'hall_tel',
                    models.CharField(
                        blank=True, max_length=15, null=True, verbose_name='hall tel no'
                    ),
                ),
                (
                    'schedule_date',
                    models.DateField(blank=True, null=True, verbose_name='schedule date'),
                ),
                ('begin_time', models.TimeField(blank=True, null=True, verbose_name='begins at')),
                ('end_time', models.TimeField(blank=True, null=True, verbose_name='ends at')),
                ('display_num', models.IntegerField(verbose_name='display order')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'hall',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='seko_schedules',
                        to='bases.base',
                        verbose_name='hall',
                    ),
                ),
                (
                    'schedule',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='seko_schedules',
                        to='masters.schedule',
                        verbose_name='schedule',
                    ),
                ),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='schedules',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_seko_schedule',
            },
        ),
        migrations.CreateModel(
            name='SekoAlbum',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('file_name', models.ImageField(upload_to='seko_album', verbose_name='file name')),
                ('display_num', models.IntegerField(verbose_name='display order')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='albums',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_album',
            },
        ),
    ]
