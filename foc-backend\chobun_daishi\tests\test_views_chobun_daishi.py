from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from chobun_daishi.models import Base
from chobun_daishi.tests.factories import ChobunDaishiFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class ChobunDaishiListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.api_client = APIClient()

        self.base_1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.chobun_daishi_1 = ChobunDaishiFactory(company=self.base_1)
        self.chobun_daishi_2 = ChobunDaishiFactory(company=self.base_1)
        self.chobun_daishi_3 = ChobunDaishiFactory(
            company=BaseFactory(base_type=Base.OrgType.COMPANY)
        )

        self.staff = StaffFactory(base=self.base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_chobun_daishi_list_succeed(self) -> None:
        """弔文台紙一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('chobun_daishi:chobun_daishi_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        param_daishi_ids = [
            self.chobun_daishi_1.daishi.id,
            self.chobun_daishi_2.daishi.id,
            self.chobun_daishi_3.daishi.id,
        ]
        record_daishi_ids = [record['daishi']['id'] for record in records]
        self.assertSetEqual(set(param_daishi_ids), set(record_daishi_ids))

    def test_chobun_daishi_list_succeed_without_auth(self) -> None:
        """弔文台紙一覧取得APIがAuthorizationヘッダがなくても成功する"""

        params: Dict = {}
        response = self.api_client.get(
            reverse('chobun_daishi:chobun_daishi_list'),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

        param_daishi_ids = [
            self.chobun_daishi_1.daishi.id,
            self.chobun_daishi_2.daishi.id,
            self.chobun_daishi_3.daishi.id,
        ]
        record_daishi_ids = [record['daishi']['id'] for record in records]
        self.assertSetEqual(set(param_daishi_ids), set(record_daishi_ids))

    def test_supplier_list_succeed_by_company(self) -> None:
        """拠点で絞り込みした弔文台紙一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company': self.base_1.pk,
        }
        response = self.api_client.get(
            reverse('chobun_daishi:chobun_daishi_list'), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        records: List[Dict] = response.json()
        self.assertEqual(len(records), 2)

        for record in records:
            self.assertEqual(record['company'], self.base_1.pk)

        param_daishi_ids = [
            self.chobun_daishi_1.daishi.id,
            self.chobun_daishi_2.daishi.id,
        ]
        record_daishi_ids = [record['daishi']['id'] for record in records]
        self.assertSetEqual(set(param_daishi_ids), set(record_daishi_ids))
