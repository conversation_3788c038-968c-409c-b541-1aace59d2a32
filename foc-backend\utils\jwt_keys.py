import logging
from pathlib import Path
from typing import Op<PERSON>, Tu<PERSON>, Union

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa

logger = logging.getLogger(__name__)


def _open_private_key(filepath: Path) -> rsa.RSAPrivateKeyWithSerialization:
    """既存の秘密鍵ファイル(PEMフォーマット)を開きます

    Args:
        filepath (Path): 秘密鍵ファイルのパス

    Returns:
        rsa.RSAPrivateKeyWithSerialization: RSA秘密鍵インスタンス
    """
    private_key = None
    with open(filepath, 'rb') as f:
        private_key = serialization.load_pem_private_key(
            f.read(), password=None, backend=default_backend()
        )
    return private_key


def _generate_private_key() -> rsa.RSAPrivateKeyWithSerialization:
    """秘密鍵を自動的に生成します

    Returns:
        rsa.RSAPrivateKeyWithSerialization: RSA秘密鍵インスタンス
    """
    return rsa.generate_private_key(
        public_exponent=65537, key_size=2048, backend=default_backend()
    )


def fetch_private_key(
    path_or_name: Optional[Union[Path, str]]
) -> rsa.RSAPrivateKeyWithSerialization:
    """秘密鍵ファイルを取得します

    path_or_nameが指定されていれば既存のファイルを開き、指定されていなければ
    自動的に生成した秘密鍵を返します

    Args:
        path_or_name (Optional[Union[Path, str]]): 秘密鍵のファイルパス

    Returns:
        rsa.RSAPrivateKeyWithSerialization: RSA秘密鍵インスタンス
    """
    if path_or_name:
        if isinstance(path_or_name, str):
            path_or_name = Path(path_or_name)
        return _open_private_key(path_or_name)
    else:
        logger.warning('RSA private key file is not specified. Using auto generated keys.')
        return _generate_private_key()


def fetch_keysets_bytes(path_or_name: Optional[Path]) -> Tuple[bytes, bytes]:
    """秘密鍵と公開鍵のPEMフォーマットバイト列を返します

    path_or_nameで指定した場所からRSA秘密鍵を取得し、公開鍵は秘密鍵から生成した
    ものを使用します

    Args:
        path_or_name (Optional[Path]): 秘密鍵ファイルのパス

    Returns:
        Tuple[bytes, bytes]: 秘密鍵PEMバイト列と公開鍵PEMバイト列のタプル
    """
    privkey: rsa.RSAPrivateKeyWithSerialization = fetch_private_key(path_or_name)
    pubkey: rsa.RSAPublicKey = privkey.public_key()
    return (
        privkey.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.TraditionalOpenSSL,
            encryption_algorithm=serialization.NoEncryption(),
        ),
        pubkey.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        ),
    )
