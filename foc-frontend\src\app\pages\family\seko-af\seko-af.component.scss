@import "src/assets/scss/customer/setting";
.container .inner .contents {
  .input-area .line {
    display: block;
    .input {
      margin-left: 5%;
      width: 90%;
      .ui.checkbox {        
        display: flex;
        margin-bottom: 10px;
      }
    }
    .input::before {
      bottom: -30px;
    }
  }
  >div {
    max-width: 700px;
  }
  padding: 15px;
  @media screen and (max-width: 360px) {
    padding: 10px;
  }
  .description {
    font-size: 1rem;
    line-height: 1.5;
    padding-bottom: 20px;
  }
  .af-group {
    font-size: 1rem;
    line-height: 1;
    padding-bottom: 10px;
  }
  .af-check {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding-bottom: 10px;
    >div {
      margin-left: 30px;
      @media screen and (max-width: 560px) {
        margin-left: 20px;
        margin-right: 20px;
      }
      @media screen and (max-width: 360px) {
        margin-left: 10px;
        margin-right: 10px;
      }
      font-size: 1rem;
      line-height: 1;
      font-weight: bold;
      display: flex;
      padding-bottom: 10px;
      input {
        margin: auto 0;
      }
      label {
        display: flex;
        font-weight: normal;
      }
    }
  }
}

.inner >.button-area {
  flex-wrap: wrap;
  .button {
    width: 180px;
    border-radius: 5px;
    margin: 5px !important;
    &.pink {
      background-color: $background-red;
      border-color: $border-red;
      color: white;
    }
    @media screen and (max-width: 560px) {
      width: 160px;
    }
    @media screen and (max-width: 380px) {
      width: 130px;
    }
  }
}
.ui.modal.confirm {
  .content.red {
    color: $text-red;
  }
}