<div class="container">
  <ng-container *ngIf="!is_loading && !err_msg">
    <app-sokepolicy></app-sokepolicy>
    <div class="approve">
      <div class="ui checkbox">
        <input type="checkbox" [(ngModel)]="approve_flg">
        <label>ご利用規約に同意する</label>
      </div>
    </div>
    <div class="approve_desc">
      上記、ご利用規約をよくお読みになり同意のうえ、お申込みください。<br>
      ご登録頂いたメールアドレス宛に葬家専用ページのログインに必要なご葬家IDとパスワードをお送りいたします。
    </div>
    <div class="button">
      <button class="ui button teal" (click)="approveSeko()" [disabled]="!approve_flg">
        申し込む
      </button>
    </div>
  </ng-container>
  <div class="contents error" *ngIf="err_msg">
    <div class="error_msg">{{err_msg}}</div>
    <div class="complete_msg" *ngIf="complete_msg">{{complete_msg}}</div>
  </div>
  <div class="button" *ngIf="complete">
    <button class="ui button grey" (click)="goLogin()">
      ログイン画面
    </button>
  </div>
</div>

