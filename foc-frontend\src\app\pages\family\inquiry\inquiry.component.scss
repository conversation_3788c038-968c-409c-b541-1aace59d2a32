
@import "src/assets/scss/customer/setting";
.container .inner .contents {
  padding: 15px;
  @media screen and (max-width: 360px) {
    padding: 10px;
  }
  >div {
    max-width: 700px;
  }
  .input-area {
    padding-bottom: 10px;
    @media screen and (max-width: 560px) {
      padding: 0 0 20px;
    }
    .line {
      display: block;
      .input {
        margin-left: 5%;
        width: 90%;
        .ui.checkbox {        
          display: flex;
          margin-bottom: 10px;
        }
      }
      @media screen and (max-width: 560px) {
        .input::before {
          bottom: -15px;
        }
      }
    }
  }
  .inquiry-list {
    font-size: 1rem;
    border-top: solid 2px #000000;
    border-bottom: solid 2px #000000;
    padding: 0 5px;
    .inquiry-data {
      position: relative;
      .updated_at {
        font-size: 0.8rem;
        line-height: 1.8;
        .staff {
          font-size: 1rem;
          margin-left: 20px;
        }
      }
      .query {
        line-height: 1.2;
        padding: 10px;
        margin: 0 0 10px 10px;
        white-space: pre-wrap;
        &.answer {
          background-color: #f0dede;
        }
      }
      .button {
        position: absolute;
        right: 0;
        top: 5px;
        margin-right: 0;
        width: 80px;
        border-radius: 5px;
        height: 23px;
        line-height: 18px;
        font-size: 0.9rem;
        @media screen and (max-width: 360px) {
          width: 60px;
        }
      }
      &:not(:first-child) {
        border-top: solid 1px $border-grey;
      }
      &.group, &.answer {
        border: 0;
      }
    }
    .no-data {
      padding: 20px 0;
    }
  }
}

.inner .contents >.button-area {
  flex-wrap: wrap;
  .button {
    width: 180px;
    border-radius: 5px;
    margin: 5px !important;
    &.pink {
      background-color: $background-red;
      border-color: $border-red;
      color: white;
    }
    @media screen and (max-width: 560px) {
      width: 160px;
    }
    @media screen and (max-width: 380px) {
      width: 130px;
    }
  }
}
.ui.modal.confirm {
  .content.red {
    color: $text-red;
  }
  &#msg-confirm, &#re-query {
    position: fixed;
    text-align: center;
    max-width: 550px;
    .title {
      display: inline-block;
      margin: 20px auto 0;
      border-bottom: solid 2px #000000;
    }
    .scrolling.content {
      text-align: left;
      white-space: pre-wrap;
      font-size: 1rem;
      max-height: 250px;
      margin: 20px 0;
      padding: 10px 30px;
      @media screen and (max-width: 560px) {
        max-height: 200px;
        padding: 0px 20px!important;
      }
    }
    .line .input {
      position: relative;
      &::before {
        position: absolute;
        content: attr(err-msg);
        color: $text-red;
        bottom: -16px;
        font-size: 0.7rem;
      }
      textarea {
        width: 100%;
        max-width: 100%;
        font-size: 1rem;
        border-radius: 5px;
        &:focus {
          outline: none;
          border-width: 2px;
        }
      }
    }
  }
}