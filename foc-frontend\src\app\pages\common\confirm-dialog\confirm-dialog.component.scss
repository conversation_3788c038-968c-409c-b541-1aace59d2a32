@import "../../common/dialog/dialog.component.scss";
@import "src/assets/scss/company/setting";

.header {
  height: 30px;
  background-color: $background-dark;
  width: 100%;
  color: $lable-text-light;
  >i {
    margin: 5px;
  }
}
.dialogList {
    .dialog {
      margin: 20px;
        .text {
            color: $lable-text-dark;
            max-width: none;
            line-height: 1.75;
            text-align: center;
        }
        .action {
            margin-top: 30px;
            text-align: center;
            .ui.button {
                width: 125px;
                margin: 0 20px;
            }
        }
    }
}

@media screen and (max-width: 480px) {
    .dialogList {
        .dialog {
            .image {
                img {
                    width: 38px;
                }
            }
            .text {
                max-width: none;
                line-height: 1.75;
            }
            .action {
                margin-top: 30px;
                text-align: center;
                .btn {
                    display: inline-block;
                    width: 180px;
                    margin: 5px 20px;
                    padding: 20px 0;
                }
            }
        }
    }
}
