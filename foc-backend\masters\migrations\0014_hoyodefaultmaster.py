# Generated by Django 3.1.5 on 2021-01-12 04:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('masters', '0013_hoyosamplemaster'),
    ]

    operations = [
        migrations.CreateModel(
            name='HoyoDefaultMaster',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('name', models.TextField(verbose_name='name')),
                ('elapsed_time', models.IntegerField(verbose_name='elapsed time')),
                (
                    'unit',
                    models.IntegerField(
                        choices=[(1, 'days'), (2, 'months'), (3, 'years')], verbose_name='unit'
                    ),
                ),
                (
                    'hoyo_style',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='defaults',
                        to='masters.hoyostyle',
                        verbose_name='hoyo default',
                    ),
                ),
            ],
            options={
                'db_table': 'mm_hoyo_default',
            },
        ),
    ]
