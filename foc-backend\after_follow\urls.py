from django.urls import path

from after_follow import views

app_name = 'after_follow'
urlpatterns = [
    path('groups/', views.AfGroupList.as_view(), name='group_list'),
    path('groups/all/', views.AfGroupListDown.as_view(), name='group_list_all'),
    path('groups/<int:pk>/', views.AfGroupDetail.as_view(), name='group_detail'),
    path('', views.AfterFollowList.as_view(), name='after_follow_list'),
    path('<int:pk>/', views.AfterFollowDetail.as_view(), name='after_follow_detail'),
    path('contact_ways/', views.AfContactWayList.as_view(), name='contact_way_list'),
    path('contact_ways/<int:pk>/', views.AfContactWayDetail.as_view(), name='contact_way_detail'),
    path('sekoaf/', views.SekoAfList.as_view(), name='seko_af_list'),
    path('sekoaf/<int:pk>/', views.SekoAfDetail.as_view(), name='seko_af_detail'),
    path('wish/<int:pk>/', views.AfWishDetail.as_view(), name='wish_detail'),
    path('activity/', views.AfActivityList.as_view(), name='activity_list'),
    path('activity/<int:pk>/', views.AfActivityDetail.as_view(), name='activity_detail'),
]
