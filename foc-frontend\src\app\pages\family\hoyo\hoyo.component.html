
<div class="container">
  <div class="inner">
    <div class="contents">
      <h2>法要情報</h2>
      <div class="kojin_area">
        <table>
          <tbody *ngFor="let kojin of seko_info.kojin">
            <tr>
              <td>
                <div class="label">故人名</div>
                <div class="data">{{kojin.name}}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="label">戒名</div>
                <div class="data">{{kojin.kaimyo}}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="label">逝去日</div>
                <div class="data">{{kojin.death_date | date:'yyyy/MM/dd'}}
                  <p>{{kojin.age_kbn}} {{kojin.age}}才</p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="title_area">
        <table>
          <tbody>
            <tr>
              <td class="hoyo_name">ご法要</td>
              <td class="hoyo_date">
                <div class="planned_date">目安日</div>
                <div class="activity_date">予定・実施日</div>
              </td>
              <td class="detail">詳細</td>
            </tr>
          </tbody>
        </table>
      </div>
      <ng-container *ngIf="hoyo_list?.length">
        <div class="data_area" *ngFor="let hoyo of hoyo_list">
          <div class="ui accordion" *ngIf="hoyo.hoyo_activity_date">
            <div class="title">
              <table>
                <tbody>
                  <tr>
                    <td class="hoyo_name">{{hoyo.hoyo_name}}</td>
                    <td class="hoyo_date">
                      <div class="planned_date" *ngIf="hoyo.hoyo_planned_date">{{hoyo.hoyo_planned_date | date: 'yyyy/MM/dd'}}({{Utils.getWeekName(hoyo.hoyo_planned_date)}})</div>
                      <div class="activity_date" *ngIf="hoyo.hoyo_activity_date">{{hoyo.hoyo_activity_date | date: 'yyyy/MM/dd'}}({{Utils.getWeekName(hoyo.hoyo_activity_date)}})</div>                      
                    </td>
                    <td class="detail">
                      <i class="chevron down icon small"></i>
                      <i class="chevron up icon small"></i>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="content">
              <table>
                <tbody>
                  <tr>
                    <td class="label">会食</td>
                    <td class="data">{{hoyo.dine_flg?'あり':'なし'}}</td>
                  </tr>
                  <tr>
                    <td class="label">返信期限日</td>
                    <td class="data">{{hoyo.reply_limit_date | date: 'yyyy/MM/dd'}}</td>
                  </tr>
                  <tr>
                    <td class="label">式次第</td>
                    <td class="data">
                      <table *ngIf="hoyo.schedules?.length">
                        <tbody>
                          <tr *ngFor="let schedule of hoyo.schedules">
                            <td class="schedule_name">{{schedule.schedule_name}}</td>
                            <td class="schedule">
                              <div class="hall_name">{{schedule.hall_name}}</div>
                              <div class="time">{{schedule.display_begin_time}}〜{{schedule.display_end_time}}</div>                      
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                  <tr>
                    <td class="label">施主</td>
                    <td class="data">{{hoyo.seshu_name}}({{hoyo.kojin_seshu_relationship}})</td>
                  </tr>
                  <tr>
                    <td class="label">司式者</td>
                    <td class="data">{{hoyo.shishiki_name}}</td>
                  </tr>
                  <tr>
                    <td class="label">担当者</td>
                    <td class="data">{{hoyo.staff?hoyo.staff.name:''}}</td>
                  </tr>
                  <tr>
                    <td class="label">法要ページ掲載終了日</td>
                    <td class="data">{{hoyo.hoyo_site_end_date | date: 'yyyy/MM/dd'}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <table *ngIf="!hoyo.hoyo_activity_date">
            <tbody>
              <tr>
                <td class="hoyo_name">{{hoyo.hoyo_name}}</td>
                <td class="hoyo_date">
                  <div class="planned_date" *ngIf="hoyo.hoyo_planned_date">{{hoyo.hoyo_planned_date | date: 'yyyy/MM/dd'}}({{Utils.getWeekName(hoyo.hoyo_planned_date)}})</div>                 
                </td>
              </tr>
            </tbody>
          </table>
          <div class="hoyo_button" *ngIf="hoyo.hoyo_activity_date">
            <a class="button pink" (click)="downloadHoyoPDF(hoyo)">案内状PDF</a>
            <a class="button grey" target="_blank" href="/hoyo/{{hoyo.hall?.id}}-{{hoyo.id}}/">法要WEB</a>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="!hoyo_list?.length">
        <div class="no-data">
          法要情報がございません。
        </div>
      </ng-container>
    </div>

  </div>
</div>

