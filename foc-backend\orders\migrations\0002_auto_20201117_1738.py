# Generated by Django 3.1.2 on 2020-11-17 08:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('masters', '0010_paymenttype'),
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='entrydetailchobun',
            name='daishi',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='masters.chobundaishimaster',
                verbose_name='daishi',
            ),
        ),
        migrations.AddField(
            model_name='entrydetailchobun',
            name='okurinushi_address_1',
            field=models.TextField(default='', verbose_name='sender address_1'),
        ),
        migrations.AddField(
            model_name='entrydetailchobun',
            name='okurinushi_address_2',
            field=models.TextField(default='', verbose_name='sender address_2'),
        ),
        migrations.AddField(
            model_name='entrydetailchobun',
            name='okurinushi_address_3',
            field=models.TextField(default='', verbose_name='sender address_3'),
        ),
        migrations.AddField(
            model_name='entrydetailchobun',
            name='okurinushi_prefecture',
            field=models.TextField(default='', verbose_name='sender prefecture'),
        ),
        migrations.AddField(
            model_name='entrydetailchobun',
            name='okurinushi_tel',
            field=models.CharField(default='', max_length=15, verbose_name='sender tel'),
        ),
        migrations.AddField(
            model_name='entrydetailchobun',
            name='okurinushi_zip_code',
            field=models.CharField(default='', max_length=7, verbose_name='sender zip code'),
        ),
        migrations.AddField(
            model_name='entrydetailkumotsu',
            name='okurinushi_address_1',
            field=models.TextField(default='', verbose_name='sender address_1'),
        ),
        migrations.AddField(
            model_name='entrydetailkumotsu',
            name='okurinushi_address_2',
            field=models.TextField(default='', verbose_name='sender address_2'),
        ),
        migrations.AddField(
            model_name='entrydetailkumotsu',
            name='okurinushi_address_3',
            field=models.TextField(default='', verbose_name='sender address_3'),
        ),
        migrations.AddField(
            model_name='entrydetailkumotsu',
            name='okurinushi_prefecture',
            field=models.TextField(default='', verbose_name='sender prefecture'),
        ),
        migrations.AddField(
            model_name='entrydetailkumotsu',
            name='okurinushi_tel',
            field=models.CharField(default='', max_length=15, verbose_name='sender tel'),
        ),
        migrations.AddField(
            model_name='entrydetailkumotsu',
            name='okurinushi_zip_code',
            field=models.CharField(default='', max_length=7, verbose_name='sender zip code'),
        ),
    ]
