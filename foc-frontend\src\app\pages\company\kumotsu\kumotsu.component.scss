
@import "src/assets/scss/company/setting";
body {
  height: 0px !important;
}
.container .inner {
  .contents >.table_fixed.body {
    max-height: calc(100% - 245px);
  }
}
.contents {
  min-width: 1450px!important;
  .search_area .ui.checkbox {
    padding: 8px 10px 0;
    label {
      cursor: pointer;
    }
  }
  >.table_fixed tr {
    line-height: 1;
    td {
      >div:not(.checkbox) {
        min-height: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &.center {
          text-align: center;
        }
        &.right {
          text-align: right;
        }
        &:not(:last-child) {
          min-height: 19px;
          border-bottom: dotted 1px $background-light1;
          margin: 0 -10px 5px;
          padding-left: 10px;
          padding-bottom: 5px;
        }
      }
    }
    .check {
      width: 50px;
      padding-left: 15px;
    }
    .entry_id {
      width: 5%;
    }
    .entry_name {
      width: 6%;
    }
    .okuri<PERSON><PERSON>_name {
      width: 6%;
    }
    .renmei {
      width: 6%;
    }
    .seko_date {
      width: 9%;
    }
    .soke_name {
      width: 5%;
    }
    .kojin_name {
      width: 6%;
    }
    .moshu_name {
      width: 6%;
    }
    .item_name {
      width: 10%;
    }
    .item_price {
      width: 5%;
    }
    .entry_ts {
      width: 105px;
    }
    .supplier {
      width: 7%;
    }
    .order_ts {
      width: 105px;
    }
    .is_cancel {
      width: 100px;
    }
    .operation {
      width: 110px;
      &.admin {
        width: 130px;
      }
      i {
        cursor: pointer;
      }
    }
  }
  .ui.modal {
    table.disabled {
      opacity: .7;
      tr {
        cursor: default;
        &:hover {
          background: $table-row-light;
        }
        .ui.checkbox input[type=radio] {
          cursor: default;
        }
      }
    }
    &.url .header, &.url .content {
      padding: 10px;
      .input_area .ui.input {
        max-width: 100%;
        >input {
          font-size: 1.2em;
        }
      }
    }
    &.item-edit .content {
      padding-top: 10px;
    }
    &.supplier tr, &.item-edit tr  {
      .select {
        width: 90px
      }
      .supplier_name {
        width: 25%;
      }
      .ui.radio.checkbox {
        padding: 0;
      }
    }
    &.fax-pdf {
      @page {
        size: A4 
      }
      position: fixed !important;
      top: 10px !important;
      height: calc(100vh - 30px);
      .content {
        height: calc(100% - 127px);
        border: 0;
        box-shadow: none;
        margin: 0;
        padding: 5px;
      }
    }
    &.kumotsu-edit .input_area label {
      min-width: 120px;
    }
  }
}