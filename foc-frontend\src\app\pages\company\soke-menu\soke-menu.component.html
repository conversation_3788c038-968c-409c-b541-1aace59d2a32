
<div class="container">
  <div class="inner">
    <div class="contents tiny">
      <div class="menu_title">
        <i class="bars icon big"></i>
        葬家専用ページメニュー
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #CompanyComboEm [settings]="companyCombo" [(selectedValue)]="company_id" (selectedItemChange)="companyChange($event)"></com-dropdown>
        </div>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned">
              <th class="display">表示</th>
              <th class="name">メニュー名</th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody *ngIf="master_loaded">
            <ng-container *ngFor="let menu of menu_list">
              <tr (click)="selectItem(menu)" [class.disabled]="menu.required_flg">
                <td class="center aligned display">
                  <div class="ui checkbox" [class.disabled]="menu.required_flg">
                    <input type="checkbox" [(ngModel)]="menu.selected">
                    <label></label>
                  </div>
                </td>
                <td class="name" title="{{menu.name}}">{{menu.name}}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>

	</div>
  </div>
</div>


<div class="footer_button_area">
  <div class="button_group right">
    <button class="ui labeled icon button mini light"  [class.disabled]="!menu_list?.length" (click)="saveData()">
      <i class="save icon"></i>保存
    </button>
    <button class="ui labeled icon button mini light" routerLink="/foc/top">
      <i class="delete icon"></i>閉じる
    </button>
  </div>
</div>
