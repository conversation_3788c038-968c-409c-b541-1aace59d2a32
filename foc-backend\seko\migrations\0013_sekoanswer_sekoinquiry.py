# Generated by Django 3.1.5 on 2021-01-29 08:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('seko', '0012_moshu_last_login'),
    ]

    operations = [
        migrations.CreateModel(
            name='SekoInquiry',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('query_group_id', models.IntegerField(verbose_name='query group id')),
                ('query', models.TextField(verbose_name='query content')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='inquiries',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_seko_query',
            },
        ),
        migrations.CreateModel(
            name='SekoAnswer',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('answer', models.TextField(verbose_name='answer content')),
                ('del_flg', models.BooleanField(verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'inquiry',
                    models.ForeignKey(
                        db_column='query_id',
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='answers',
                        to='seko.sekoinquiry',
                        verbose_name='seko inquiry',
                    ),
                ),
                (
                    'staff',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={
                'db_table': 'tr_seko_answer',
            },
        ),
    ]
