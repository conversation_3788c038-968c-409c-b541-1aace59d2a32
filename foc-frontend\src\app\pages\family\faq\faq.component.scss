
@import "src/assets/scss/customer/setting";
.container .inner .contents {
  padding: 15px;
  @media screen and (max-width: 360px) {
    padding: 10px;
  }
  >div {
    max-width: 700px;
  }
  .faq-list {
    font-size: 1rem;
    padding: 0 5px;
    .faq-data {
      position: relative;
      display: flex;
      div {
        line-height: 1.5;
        padding: 10px;
        margin: 0;
        white-space: pre-wrap;
        &.markQ {
          font-weight: bold;
          padding-right: 0;
          font-size: 1.1rem;
        }
        &.question {
          font-weight: bold;
          font-size: 1.1rem;
        }
        &.markA {
          margin-left: 5px;
          padding-right: 0;
          padding-top: 0;
        }
        &.answer {
          padding-top: 0;
        }
      }

    }
  }
}
