
<div class="container">
  <div class="inner">
    <div class="contents header">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="contents with-background-image description">
      <h2>追悼メッセージを送る</h2>
      <div class="pre-wrap">{{description}}</div>
      <div class="remark">※不適切な内容が含まれるメッセージはご遠慮下さい。</div>
      <div class="seko_company">
        葬儀に関するお問い合わせ<br>
        &nbsp;&nbsp;{{seko_info.fuho_contact_name}} <br>
        &nbsp;&nbsp;TEL：{{seko_info.fuho_contact_tel}} 
      </div>
      <div class="seko_company">
        操作に関するお問い合わせ<br>
        &nbsp;&nbsp;葬儀オンラインサービスこころサポートセンター <br>
        &nbsp;&nbsp;TEL：0120-691-484 <br>
        &nbsp;&nbsp;営業時間：09:00～21:00/年中無休
      </div>
      <div class="data-item">
        <div class="label">
          利用料
        </div>
        <div class="data">
          無料
        </div>
      </div>
      <div class="data-item">
        <div class="label">
          締切
        </div>
        <div class="data" *ngIf="service?.display_limit_ts">
          {{service.display_limit_ts}}まで
        </div>
      </div>
      <div class="message-area" *ngIf="message">
        {{message}}
      </div>
    </div>
    <ng-container *ngIf="!isExpierd() && !message && message_edit?.message">
    <div class="navigation">
      <div class="item current"><span>メッセージ</span><span>入力</span></div>
      <div class="arrow"></div>
      <div class="item"><span>カート</span><span>内容確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>注文</span><span>手続き</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込み</span><span>完了</span></div>
    </div>
    <div class="contents" *ngIf="message_edit">
      <div class="input-area">
        <div class="line">
          <div class="label required">故人との間柄</div>
          <div class="input relation_ship" #relationEm [class.error]="isErrorField(relationEm)">
            <com-dropdown class="small" #relationComboEm [settings]="relationCombo" [(selectedName)]="message_edit.message.relation_ship"></com-dropdown>
          </div>
        </div>
        <div class="line">
          <div class="label required">本文</div>
          <div class="input honbun" #honbunEm [class.error]="isErrorField(honbunEm)">
            <textarea rows="10" [maxlength]="honbun_max_length" [(ngModel)]="message_edit.message.honbun"></textarea>
          </div>
        </div>
        <div class="letter">
          {{message_edit.message.honbun?message_edit.message.honbun.length:0}}/{{ honbun_max_length }}文字
        </div>
      </div>
    </div>
    </ng-container>

    <div class="button-area">
      <a class="button" (click)="back()">< TOPへ戻る</a>
      <a class="button grey" (click)="saveData()" *ngIf="!isExpierd() && !message">{{message_edit?.index>=0?'カート内容を変更する':'カートに入れる'}} ></a>
    </div>
  </div>
</div>
