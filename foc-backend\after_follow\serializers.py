from typing import Dict

from django.db import transaction
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from drf_extra_fields.relations import PresentablePrimaryKeyRelatedField
from rest_framework import serializers

from after_follow.models import (
    AfActivityDetail,
    AfContactWay,
    AfGroup,
    AfterFollow,
    AfWish,
    SekoAf,
)
from seko.models import Seko
from utils.serializer_mixins import AddUserIdMixin


class AfterFollowSerializerBase(AddUserIdMixin, serializers.ModelSerializer):
    create_user_id = serializers.IntegerField(required=False)
    update_user_id = serializers.IntegerField(required=False)

    class Meta:
        model = AfterFollow


class AfterFollowRelatedSerializer(AfterFollowSerializerBase):
    class Meta(AfterFollowSerializerBase.Meta):
        exclude = ['af_group']
        read_only_fields = ['del_flg', 'created_at', 'updated_at']


class AfGroupSerializerBase(serializers.ModelSerializer):
    create_user_id = serializers.IntegerField(required=False)
    update_user_id = serializers.IntegerField(required=False)
    base_name = serializers.SerializerMethodField()

    class Meta:
        model = AfGroup
        fields = '__all__'
        read_only_fields = ['del_flg', 'after_follows', 'created_at', 'updated_at']

    def get_base_name(self, obj: AfGroup):
        return obj.department.base_name


class SimpleAfGroupSerializer(AfGroupSerializerBase):
    pass


class AfGroupSerializer(AddUserIdMixin, AfGroupSerializerBase):
    after_follows = AfterFollowRelatedSerializer(many=True, required=False)


class AfterFollowSerializer(AfterFollowSerializerBase):
    af_group = PresentablePrimaryKeyRelatedField(
        queryset=AfGroup.objects.all(),
        presentation_serializer=SimpleAfGroupSerializer,
        required=False,
    )

    class Meta(AfterFollowSerializerBase.Meta):
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']


class AfContactWaySerializer(AddUserIdMixin, serializers.ModelSerializer):
    create_user_id = serializers.IntegerField(required=False)
    update_user_id = serializers.IntegerField(required=False)

    class Meta:
        model = AfContactWay
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']


class AfActivityDetailSerializerBase(serializers.ModelSerializer):
    class Meta:
        model = AfActivityDetail
        read_only_fields = ['created_at', 'updated_at']


class AfActivityDetailByAfWishSerializer(AfActivityDetailSerializerBase):
    class Meta(AfActivityDetailSerializerBase.Meta):
        exclude = ['af_wish']


class AfActivityDetailSerializer(AfActivityDetailSerializerBase):
    class Meta(AfActivityDetailSerializerBase.Meta):
        fields = '__all__'

    @transaction.atomic
    def update(self, instance: AfActivityDetail, validated_data: Dict) -> AfActivityDetail:
        validated_data.pop('af_wish', None)
        return super().update(instance, validated_data)


class AfWishSerializerBase(serializers.ModelSerializer):
    af_type = PresentablePrimaryKeyRelatedField(
        queryset=AfterFollow.objects.filter(Q(del_flg=False)).all(),
        presentation_serializer=AfterFollowSerializer,
        required=False,
    )
    activities = AfActivityDetailByAfWishSerializer(many=True, required=False)

    class Meta:
        model = AfWish
        read_only_fields = ['activities', 'created_at', 'updated_at']


class AfWishBySekoAfSerializer(AfWishSerializerBase):
    class Meta(AfWishSerializerBase.Meta):
        exclude = ['seko_af']


class AfWishSerializer(AfWishSerializerBase):
    class Meta(AfWishSerializerBase.Meta):
        fields = '__all__'
        read_only_fields = AfWishSerializerBase.Meta.read_only_fields + ['seko_af']
        extra_kwargs = {
            'seko_af': {'required': False},
            'answered_flg': {'required': False},
        }


class SekoAfSerializerBase(serializers.ModelSerializer):
    contact_way = PresentablePrimaryKeyRelatedField(
        queryset=AfContactWay.objects.filter(Q(del_flg=False)).all(),
        presentation_serializer=AfContactWaySerializer,
        required=False,
    )
    wishes = AfWishBySekoAfSerializer(many=True, required=False)

    class Meta:
        model = SekoAf
        read_only_fields = ['created_at', 'updated_at']


class SekoAfBySekoSerializer(SekoAfSerializerBase):
    class Meta(SekoAfSerializerBase.Meta):
        exclude = ['seko']


class SekoAfSerializer(SekoAfSerializerBase):
    class Meta(SekoAfSerializerBase.Meta):
        fields = '__all__'

    def validate_seko(self, value: Seko):
        if AfActivityDetail.objects.filter(Q(af_wish__seko_af__seko=value)).exists():
            raise serializers.ValidationError(_('Activities for this seko already exist.'))
        return value

    @transaction.atomic
    def create(self, validated_data: Dict) -> SekoAf:
        wishes_data = validated_data.pop('wishes', [])

        instance = super().create(validated_data)
        for wishes_dict in wishes_data:
            AfWish.objects.create(seko_af=instance, **wishes_dict)

        return instance

    @transaction.atomic
    def update(self, instance: SekoAf, validated_data: Dict) -> SekoAf:
        update_wishes = 'wishes' in validated_data
        wishes_data = validated_data.pop('wishes', [])

        instance = super().update(instance, validated_data)
        if not self.partial or update_wishes:
            instance.update_wishes(wishes_data)

        return instance
