from rest_framework import serializers

from masters.models import (
    ChobunDaishiMaster,
    ChobunSample,
    FuhoSampleMaster,
    Hen<PERSON><PERSON>inParameter,
    HoyoDefaultMaster,
    HoyoSampleMaster,
    HoyoStyle,
    KodenParameter,
    PaymentType,
    Relationship,
    Schedule,
    SekoStyle,
    Service,
    SokeMenu,
    Tax,
    Wareki,
    ZipCode,
)


class ScheduleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Schedule
        fields = '__all__'


class HoyoStyleSerializer(serializers.ModelSerializer):
    class Meta:
        model = HoyoStyle
        fields = '__all__'


class SekoStyleSerializer(serializers.ModelSerializer):

    hoyo_style = HoyoStyleSerializer()

    class Meta:
        model = SekoStyle
        fields = '__all__'


class ZipCodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ZipCode
        fields = '__all__'


class WarekiSerializer(serializers.ModelSerializer):
    class Meta:
        model = Wareki
        fields = '__all__'


class FuhoSampleMasterSerializer(serializers.ModelSerializer):
    class Meta:
        model = FuhoSampleMaster
        fields = '__all__'


class ChobunDaishiMasterSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChobunDaishiMaster
        fields = '__all__'


class ChobunSampleSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChobunSample
        fields = '__all__'


class ServiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Service
        fields = '__all__'


class TaxSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tax
        fields = '__all__'


class RelationshipSerializer(serializers.ModelSerializer):
    class Meta:
        model = Relationship
        fields = '__all__'


class PaymentTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentType
        fields = '__all__'


class HenreihinParameterSerializer(serializers.ModelSerializer):
    class Meta:
        model = HenreihinParameter
        fields = '__all__'


class KodenParameterSerializer(serializers.ModelSerializer):
    class Meta:
        model = KodenParameter
        fields = '__all__'


class HoyoDefaultMasterSerializer(serializers.ModelSerializer):
    hoyo_style = HoyoStyleSerializer()

    class Meta:
        model = HoyoDefaultMaster
        fields = '__all__'


class HoyoSampleMasterSerializer(serializers.ModelSerializer):
    class Meta:
        model = HoyoSampleMaster
        fields = '__all__'


class SokeMenuSerializer(serializers.ModelSerializer):
    class Meta:
        model = SokeMenu
        fields = '__all__'
