
<div class="container">
  <div class="inner">
    <div class="contents">
      <div class="menu_title">
        <i class="map icon big"></i>
        弔文受付一覧
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(companyComboEm, sekoBaseComboEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchChobun()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="form_data.company_id" (selectedItemChange)="companyChange($event, sekoBaseComboEm)"></com-dropdown>
          <label>施行拠点</label>
          <com-dropdown #sekoBaseComboEm [settings]="sekoBaseCombo" [(selectedValue)]="form_data.seko_department"></com-dropdown>
          <label>申込番号</label>
          <div class="ui input small">
            <input type="tel" [(ngModel)]="form_data.entry_id">
          </div>
          <label>申込者名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.entry_name">
          </div>
          <label class="large">申込者電話番号</label>
          <div class="ui input small">
            <input type="tel" [(ngModel)]="form_data.entry_tel">
          </div>
        </div>
        <div class="line">
          <label>組織名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.okurinushi_company">
          </div>
          <label>役職名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.okurinushi_title">
          </div>
          <label>送り主名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.okurinushi_name">
          </div>
          <label>連名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.renmei">
          </div>
          <label class="large">キャンセル状況</label>
          <div class="ui checkbox" (click)="checkClick('not_canceled', form_data.canceled)">
            <input type="checkbox" name="not_canceled" [(ngModel)]="form_data.not_canceled">
            <label>未</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('canceled', form_data.not_canceled)">
            <input type="checkbox" name="canceled" [(ngModel)]="form_data.canceled">
            <label>済</label>
          </div>
        </div>
        <div class="line">
          <label>葬家名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.soke_name">
          </div>
          <label>故人名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.kojin_name">
          </div>
          <label>喪主名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.moshu_name">
          </div>
          <label>印刷状況</label>
          <div class="ui checkbox" (click)="checkClick('printed', form_data.not_printed)">
            <input type="checkbox" name="printed" [(ngModel)]="form_data.printed" [checked]="form_data.printed">
            <label>印刷済</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('not_printed', form_data.printed)">
            <input type="checkbox" name="not_printed" [(ngModel)]="form_data.not_printed" [checked]="form_data.not_printed">
            <label>未印刷</label>
          </div>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="chobun_list?.length">
          全{{chobun_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?chobun_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="entry_id">申込番号</th>
              <th class="entry_name"><p>申込者名</p>電話番号</th>
              <th class="okurinushi_name">送り主名</th>
              <th class="okurinushi_company"><p>組織名</p>役職名</th>
              <th class="renmei"><p>連名１</p>連名２</th>
              <th class="seko_date"><p>施行日</p>施行拠点</th>
              <th class="soke_name">葬家名</th>
              <th class="kojin_name"><p>故人名</p>喪主名</th>
              <th class="item_name">商品名</th>
              <th class="item_price">価格</th>
              <th class="entry_ts">申込日時</th>
              <th class="printed_flg"><p>印刷</p>状況</th>
              <th class="is_cancel">キャンセル</th>
              <th class="operation" [class.admin]="login_company.base_type === Const.BASE_TYPE_ADMIN"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let chobun of chobun_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
              <td class="center aligned entry_id" title="{{chobun.entry_detail.entry.id}}">{{chobun.entry_detail.entry.id}}</td>
              <td class="entry_name">
                <div title="{{chobun.entry_detail.entry.entry_name}}">{{chobun.entry_detail.entry.entry_name}}</div>
                <div title="{{chobun.entry_detail.entry.entry_tel}}">{{chobun.entry_detail.entry.entry_tel}}</div>
              </td>
              <td class="okurinushi_name" title="{{chobun.okurinushi_name}}">{{chobun.okurinushi_name}}</td>
              <td class="okurinushi_company">
                <div title="{{chobun.okurinushi_company}}">{{chobun.okurinushi_company}}</div>
                <div title="{{chobun.okurinushi_title}}">{{chobun.okurinushi_title}}</div>
              </td>
              <td class="renmei">
                <div title="{{chobun.renmei1}}">{{chobun.renmei1}}</div>
                <div title="{{chobun.renmei2}}">{{chobun.renmei2}}</div>
              </td>
              <td class="center aligned seko_date">
                <div title="{{chobun.entry_detail.entry.seko.seko_date | date: 'yyyy/MM/dd'}}">{{chobun.entry_detail.entry.seko.seko_date | date: 'yyyy/MM/dd'}}</div>
                <div title="{{chobun.entry_detail.entry.seko.seko_department_name}}">{{chobun.entry_detail.entry.seko.seko_department_name}}</div>
              </td>
              <td class="soke_name" title="{{chobun.entry_detail.entry.seko.soke_name}}">{{chobun.entry_detail.entry.seko.soke_name}}</td>
              <td class="kojin_name">
                <div title="{{chobun.entry_detail.entry.seko.kojin[0].name}}">{{chobun.entry_detail.entry.seko.kojin[0].name}}</div>
                <div title="{{chobun.entry_detail.entry.seko.moshu.name}}">{{chobun.entry_detail.entry.seko.moshu.name}}</div>
              </td>
              <td class="item_name" title="{{chobun.entry_detail.item_name}}">{{chobun.entry_detail.item_name}}</td>
              <td class="right aligned item_price" title="{{(chobun.entry_detail.item_unit_price * chobun.entry_detail.quantity) | number}}">
                {{(chobun.entry_detail.item_unit_price * chobun.entry_detail.quantity) | number}}</td>
              <td class="center aligned entry_ts" title="{{chobun.entry_detail.entry.entry_ts | date: 'MM/dd H:mm'}}">
                {{chobun.entry_detail.entry.entry_ts | date: 'MM/dd H:mm'}}</td>
              <td class="center aligned printed_flg" title="{{chobun.printed_flg?'済':'未'}}">{{chobun.printed_flg?'済':'未'}}</td>
              <td class="center aligned is_cancel" title="{{chobun.entry_detail.entry.cancel_ts?'キャンセル済':''}}">
                {{chobun.entry_detail.entry.cancel_ts?'キャンセル済':''}}
              </td>

              <td class="center aligned button operation" [class.admin]="login_company.base_type === Const.BASE_TYPE_ADMIN">
                <i class="large file pdf icon" title="弔文PDF" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN" (click)="downloadPDF(chobun.entry_detail.id)"></i>
                <i class="large ban icon" title="キャンセル" *ngIf="(login_company.base_type === Const.BASE_TYPE_COMPANY|| login_company.base_type === Const.BASE_TYPE_ADMIN) && !chobun.entry_detail.entry.cancel_ts" (click)="showOrderCancel(chobun.entry_detail.entry)"></i>
                <i class="large linkify icon" title="領収書URL" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN" (click)="showUrl(chobun.entry_detail.entry.id)"></i>
                <i class="large edit icon" title="申込情報編集" *ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN" (click)="showChobunData(chobun)"></i>
              </td>
            </tr>
            </ng-container>
            <tr *ngIf="!chobun_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="15">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal mini" id="receipt-url">
        <div class="header ui"><i class="linkify icon large"></i>
          領収書URL
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <div class="ui input fluid">
                <input type="text" autocomplete="off" value="{{receipt_url}}" readonly (focus)="onFocus($event)">
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="ui modal big" id="order-cancel">
        <div class="header ui"><i class="ban icon large"></i>
          申込キャンセル
        </div>
        <div class="content" *ngIf="selected_entry">
          <app-com-order-cancel [entry_id]="selected_entry.id"></app-com-order-cancel>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="cancelOrder()">
            <i class="ban icon"></i>キャンセル実行
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeCancelOrder()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal small" id="chobun-edit">
        <div class="header ui"><i class="edit icon large"></i>
          申込情報編集
        </div>
        <div class="messageArea">
          <div class="ui message {{message?.type}}" [class.visible]="!!message" [class.hidden]="!message" (click)="closeMessage()">
            <i class="close icon" (click)="closeMessage()"></i>
            <div class="ui center aligned header">
              <i class="{{message?.type==='info'?'info circle':message?.type==='warning'?'exclamation triangle':'times circle'}} icon"></i>
              {{message?.title}}
            </div>
            <div class="ui center aligned basic mini segment" *ngIf="message?.msg">
              {{message?.msg}}
            </div>
          </div>
        </div>
        <div class="content">
          <div class="input_area" *ngIf="chobun_edit">
            <div class="line">
              <label class="required">宛名</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="atena" maxlength="15" [(ngModel)]="chobun_edit.atena">
              </div>
            </div>
            <div class="line">
              <label>組織名</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_company" maxlength="25" [(ngModel)]="chobun_edit.okurinushi_company">
              </div>
            </div>
            <div class="line">
              <label>役職名</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_title" maxlength="15" [(ngModel)]="chobun_edit.okurinushi_title">
              </div>
            </div>
            <div class="line">
              <label>組織・役職カナ</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_company_kana" maxlength="40" [(ngModel)]="chobun_edit.okurinushi_company_kana">
              </div>
            </div>
            <div class="line">
              <label class="required">氏名</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="okurinushi_name" maxlength="15" [(ngModel)]="chobun_edit.okurinushi_name">
              </div>
              <label class="required">氏名カナ</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_kana" maxlength="15" [(ngModel)]="chobun_edit.okurinushi_kana">
              </div>
            </div>
            <div class="line">
              <label class="required">郵便番号</label>
              <div class="ui input">
                <input type="tel" autocomplete="off" id="okurinushi_zip_code" maxlength="7" [(ngModel)]="chobun_edit.okurinushi_zip_code" (input)="zipcodeChange()">
              </div>
              <label class="required">電話番号</label>
              <div class="ui input full">
                <input type="tel" autocomplete="off" id="okurinushi_tel" maxlength="15" [(ngModel)]="chobun_edit.okurinushi_tel">
              </div>
            </div>
            <div class="line">
              <label class="required">都道府県</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="okurinushi_prefecture" [(ngModel)]="chobun_edit.okurinushi_prefecture">
              </div>
              <label class="required">市区町村</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_address_1" [(ngModel)]="chobun_edit.okurinushi_address_1">
              </div>
            </div>
            <div class="line">
              <label class="required">所番地</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="okurinushi_address_2" [(ngModel)]="chobun_edit.okurinushi_address_2">
              </div>
              <label>建屋名</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="okurinushi_address_3" maxlength="26" [(ngModel)]="chobun_edit.okurinushi_address_3">
              </div>
            </div>
            <div class="line">
              <label>連名１</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="renmei1" maxlength="15" [(ngModel)]="chobun_edit.renmei1">
              </div>
              <label>連名１カナ</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="renmei_kana1" maxlength="15" [(ngModel)]="chobun_edit.renmei_kana1">
              </div>
            </div>
            <div class="line">
              <label>連名２</label>
              <div class="ui input">
                <input type="text" autocomplete="off" id="renmei2" maxlength="15" [(ngModel)]="chobun_edit.renmei2">
              </div>
              <label>連名２カナ</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="renmei_kana2" maxlength="15" [(ngModel)]="chobun_edit.renmei_kana2">
              </div>
            </div>
            <div class="line">
              <label>備考</label>
              <div class="ui input full">
                <input type="text" autocomplete="off" id="note" [(ngModel)]="chobun_edit.note">
              </div>
            </div>
              <div class="line row5">
                <label class="required">本文</label>
                <div class="ui icon input textarea">
                    <textarea id="honbun" rows="10" cols="21" [(ngModel)]="chobun_edit.honbun"></textarea>
                </div>
              </div>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveChobunData()">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeChobunEdit()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
		</div>
  </div>
</div>
