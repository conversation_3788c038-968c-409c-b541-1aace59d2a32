@import "src/assets/scss/customer/setting";
.container .inner .contents {
  .kojin_area {
    width: 100%;
    margin: auto;
    margin-bottom: 10px;
    padding: 0 5px 15px;
    table {
      font-size: 1rem;
      line-height: 1.6;
      margin: auto;
      border-collapse: collapse;
      tbody {
        &:not(:first-child) {
          border-top: 15px solid white;
        }
        tr {
          td {
            display: flex;
            .label {
              padding-left: 0px;
              min-width: 130px;
              font-weight: bold;
            }
            .data a {
              text-decoration: underline;
            }
          } 
        }
      }
    }
    @media screen and (max-width: 560px) {
      table tbody tr td {
        display: block;
        .label {
          min-width: 100%;
        }
        .data {
          padding-left: 20px;
        }
      }
    }
  }
  .no-data {
    text-align: center;
    font-size: 0.9rem;
    @media screen and (max-width: 560px) {
      font-size: 0.8rem;
    }
  }
  >div {
    max-width: 700px;
  }
  padding: 15px;
  @media screen and (max-width: 360px) {
    padding: 10px;
  }
  .title_area, .data_area .ui.accordion {
    padding: 10px;
    line-height: 1.2;
  }
  .title_area table tr td {
    padding-left: 10px;
  }
  table {
    width: 100%;
    margin: auto;
    tr td {
      &.hoyo_name {
        width: 40%;
        @media screen and (max-width: 560px) {
          width: 50%;
        }
      }
      &.hoyo_date {
        display: flex;
        .planned_date, .activity_date {
          width: 50%;
        }
        @media screen and (max-width: 560px) {
          display: block;
          .planned_date, .activity_date {
            width: 100%;
          }
        }
      }
      &.detail {
        padding-left: 0;
        text-align: center;
        width: 60px;
        @media screen and (max-width: 560px) {
          width: 40px;
        }
      }
    }
  }
  .data_area {
    .ui.accordion {
      padding: 0 !important;
      >.content table {
        margin: auto;
        tr td {
          line-height: 1.5;
          &.label {
            text-align: right;
            padding-right: 20px;
            width: 30%;
            min-width: 80px;
            vertical-align: top;
            @media screen and (max-width: 560px) {
              width: 20%;
            }
          }
          table {
            .schedule_name {
              vertical-align: top;

            }
            .schedule {
              display: flex;
              .time {
                margin-left: 10px;
              }
              @media screen and (max-width: 560px) {
                display: block;
                .time {
                  margin-left: 0;
                }
              }

            }
          }
        }
      } 
    }
    padding: 0 10px;
    font-size: 0.9rem;
    border-bottom: solid 1px $border-grey;
    &:nth-last-child(4) {
      border-bottom-width: 2px;
    }
    .activity_date {
      color: $text-red;
    }
    .hoyo_button {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 10px;
      .button {
        font-size: 0.8rem;
        margin-top: 5px;
        width: 110px;
        border-radius: 5px;
        height: 23px;
        line-height: 18px;
        color: white;
        &.pink {
          background-color: $background-red;
          border-color: $border-red;
        }
        @media screen and (max-width: 560px) {
          width: 100px;
        }
        @media screen and (max-width: 380px) {
          width: 90px;
        }
      }
    }
  }
  .title_area {
    font-size: 0.9rem;
    @media screen and (max-width: 560px) {
      font-size: 0.8rem;
    }
    border-bottom: solid 2px $border-grey;
  }
  .ui.accordion {
    padding: 5px !important;
    .title {
      .chevron.up.icon {
        display: none;
      }
      &.active {
        .chevron.down.icon {
          display: none;
        }
        .chevron.up.icon {
          display: inline;
        }
      }
    }
    .content {
      padding-top: 0px !important;
      padding-bottom: 10px;
      .ui.radio.checkbox {
        margin-right: 10px;
      }
      .button {
        font-size: 0.8rem;
        margin-top: 5px;
        width: 110px;
        border-radius: 5px;
        height: 23px;
        line-height: 18px;
        @media screen and (max-width: 560px) {
          width: 100px;
        }
        @media screen and (max-width: 380px) {
          width: 90px;
        }
      }
    }
  }
}

