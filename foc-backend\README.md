# Funeral Online system Connect (FOC)

## 開発環境整備

1. Python 3.8をインストールします。
2. poetryをインストールします。
3. このリポジトリをgit cloneします。
4. clone先のディレクトリでpoetryを使用し、依存するパッケージをインストールします。
5. 同ディレクトリに.envファイルを設置し、データベース接続先などを記載します。
6. (optional)Djangoのmigrationを使用し、データベースにテーブルなどを追加します。
7. Djangoのrunserverで動作するか確認します。

(Windowsでの例)
```bat
(2.)
Powershellの場合
> (Invoke-WebRequest -Uri https://raw.githubusercontent.com/python-poetry/poetry/master/get-poetry.py -UseBasicParsing).Content | py -3   (-3.8などでも可能)
WSLの場合
> curl -sSL https://raw.githubusercontent.com/python-poetry/poetry/master/get-poetry.py | py -3
コマンドプロンプトの場合
まず https://raw.githubusercontent.com/python-poetry/poetry/master/get-poetry.py をダウンロードします。
> py -3 get-poetry.py
```

※get-poetry.pyはpoetryを`%USERPROFILE%\.poetry\`以下にインストールし、ユーザ環境変数のPATHに`%USERPROFILE%\.poetry\bin`を登録します。<br>PATHの変更は次に開いたアプリから適用されるので、現在起動しているPowershellなどは一旦閉じて新たに開き直します。

```bat
(4.)
> cd <プロジェクトフォルダ>
> poetry config virtualenvs.in-project true
(poetryがPython仮想環境を <プロジェクトフォルダ>/.venv に作る設定にします)
> poetry install
(実行、開発用Pythonパッケージの両方がインストールされます)

(5.)
> copy .env.sample .env

続けて.envファイルをテキストエディタで開いて内容を環境に合わせて編集します。

.envファイルの内容の例

DATABASE_URL=postgresql://<USERNAME>:<PASSWORD>@<HOSTNAME>[:<PORT>]/<DBNAME>
DATABASE_URL=sqlite:///db.sqlite3

上記を参考に適宜記載してください

(6.)
> cd <プロジェクトフォルダ>
> poetry run python manage.py migrate

(7.)
> cd <プロジェクトフォルダ>
> poetry run python manage.py runserver
```

## Dockerで起動する方法

Dockerでは開発用のrunserverではなく、uvicornで起動します。`docker`コマンドを使った操作方法は下記のとおりです。

### 起動

```bat
> cd <Dockerfileがあるディレクトリ>
> docker build -t <イメージ名>
(Dockerイメージをビルドするメッセージが表示されます)
> docker run --rm -d --name=<コンテナ名> -p 8000:8000 -e "DATABASE_URL=<データベースへの接続文字列>" <イメージ名>
(起動したDockerコンテナのIDが表示されます)
```

このうちコンテナ名とデータベースへの接続文字列は下記のように指定します。

- `イメージ名`: ビルドするDockerイメージ名です。特に決まりはありません。
- `コンテナ名`: コンテナを停止する際に識別する名前です。特に決まりはありませんので`backend_1`といった適当なものをおいてください。
- `データベースへの接続文字列`: 開発環境整備の`.envファイルの内容の例`に記載した`DATABASE_URL`と同等です。

### Djangoの操作

この状態でfoc-backendは起動していますが、migrateは自動では行わないので必要に応じて下記のようにコンテナ内のDjangoコマンドを発行します。(下記は`python manage.py migrate`を行う例です)

```bat
> docker exec <コンテナ名> poetry run python manage.py migrate
```

`<コンテナ名>`の箇所は、コンテナID(左から数桁だけで可)でも同じ動作になります。

### 停止

コンテナを停止するには下記のようにしてください。(docker runで`--rm`を指定しているのでコンテナが停止したら自動的に削除されます)

```bat
> docker stop <コンテナ名>
```

## Djangoインスペクション

リクエストを受けてレスポンスを返すまでに、Django内でどのようなSQLが発行されているかを確認するためにSilkを使用することができます。Django Debug ToolbarはREST Frameworkでは使用できる場面が限られるため非推奨です。

`.env`ファイルや環境変数で`ENABLE_SILK=True`に設定し、`poetry run python manage.py migrate silk`を実行することでインスペクション情報がデータベースに記録されます。その記録を見るためには、ブラウザで`/silk/`ディレクトリを表示します。(例: `http://localhost:8000/silk/`)

溜まった記録を削除するには`poetry run python manage.py silk_clear_request_log`を実行します。

## Windowsユーザへの注意

WeasyPrintの出力にGTKのlibcairo-2などが使われますが、WindowsにはGTKは入っていません。

[GTK+ for Windows Runtime Environment Installer: 64-bit](https://github.com/tschoonj/GTK-for-Windows-Runtime-Environment-Installer)のReleasesからgtk3-runtime-xxx-win64.exeをダウンロードしてインストールしてください。
