from typing import Dict, List

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.models import Base
from bases.tests.factories import BaseFactory
from hoyo.models import Hoyo, HoyoSchedule, HoyoSeko
from hoyo.tests.factories import HoyoFactory, HoyoScheduleFactory, HoyoSekoFactory
from seko.tests.factories import KojinFactory, MoshuFactory, SekoFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class HoyoSekoDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff = StaffFactory()
        self.hoyo_seko = HoyoSekoFactory(staff=self.staff)
        self.hoyo_seko_schedule_1 = HoyoScheduleFactory(hoyo_seko=self.hoyo_seko, display_num=2)
        self.hoyo_seko_schedule_2 = HoyoScheduleFactory(hoyo_seko=self.hoyo_seko, display_num=3)
        self.hoyo_seko_schedule_3 = HoyoScheduleFactory(hoyo_seko=self.hoyo_seko, display_num=1)
        MoshuFactory(seko=self.hoyo_seko.seko)
        KojinFactory(seko=self.hoyo_seko.seko, kojin_num=2)
        KojinFactory(seko=self.hoyo_seko.seko, kojin_num=1)

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_seko_detail_succeed(self) -> None:
        """法要施行詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.hoyo_seko.pk)
        self.assertEqual(record['seko']['id'], self.hoyo_seko.seko.pk)
        self.assertEqual(record['hoyo']['id'], self.hoyo_seko.hoyo.pk)
        self.assertEqual(record['hoyo']['name'], self.hoyo_seko.hoyo.name)
        self.assertEqual(record['hoyo']['elapsed_time'], self.hoyo_seko.hoyo.elapsed_time)
        self.assertEqual(record['hoyo_name'], self.hoyo_seko.hoyo_name)
        self.assertEqual(record['hoyo_planned_date'], self.hoyo_seko.hoyo_planned_date.isoformat())
        self.assertEqual(
            record['hoyo_activity_date'], self.hoyo_seko.hoyo_activity_date.isoformat()
        )
        self.assertEqual(record['begin_time'], self.hoyo_seko.begin_time)
        self.assertEqual(record['end_time'], self.hoyo_seko.end_time)
        self.assertEqual(record['hall']['id'], self.hoyo_seko.hall.pk)
        self.assertEqual(record['hall']['base_name'], self.hoyo_seko.hall.base_name)
        self.assertEqual(record['dine_flg'], self.hoyo_seko.dine_flg)
        self.assertEqual(record['seshu_name'], self.hoyo_seko.seshu_name)
        self.assertEqual(
            record['kojin_seshu_relationship'], self.hoyo_seko.kojin_seshu_relationship
        )
        self.assertEqual(record['zip_code'], self.hoyo_seko.zip_code)
        self.assertEqual(record['prefecture'], self.hoyo_seko.prefecture)
        self.assertEqual(record['address_1'], self.hoyo_seko.address_1)
        self.assertEqual(record['address_2'], self.hoyo_seko.address_2)
        self.assertEqual(record['address_3'], self.hoyo_seko.address_3)
        self.assertEqual(record['tel'], self.hoyo_seko.tel)
        self.assertEqual(record['hoyo_sentence'], self.hoyo_seko.hoyo_sentence)
        self.assertEqual(record['shishiki_name'], self.hoyo_seko.shishiki_name)
        self.assertEqual(record['reply_limit_date'], self.hoyo_seko.reply_limit_date.isoformat())
        self.assertEqual(
            record['hoyo_site_end_date'], self.hoyo_seko.hoyo_site_end_date.isoformat()
        )
        self.assertEqual(record['note'], self.hoyo_seko.note)
        self.assertEqual(record['staff']['id'], self.hoyo_seko.staff.pk)
        self.assertEqual(record['staff']['name'], self.hoyo_seko.staff.name)
        self.assertEqual(record['del_flg'], self.hoyo_seko.del_flg)
        self.assertEqual(
            record['created_at'], self.hoyo_seko.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(
            record['updated_at'], self.hoyo_seko.updated_at.astimezone(tz).isoformat()
        )

        for rec_kojin, correct_kojin in zip(
            record['seko']['kojin'], self.hoyo_seko.seko.kojin.order_by('kojin_num')
        ):
            self.assertEqual(rec_kojin['id'], correct_kojin.pk)
            self.assertEqual(rec_kojin['kojin_num'], correct_kojin.kojin_num)
            self.assertEqual(rec_kojin['name'], correct_kojin.name)
            self.assertEqual(rec_kojin['kaimyo'], correct_kojin.kaimyo)
            self.assertEqual(rec_kojin['death_date'], correct_kojin.death_date.isoformat())
            self.assertEqual(rec_kojin['age_kbn'], correct_kojin.age_kbn)
            self.assertEqual(rec_kojin['age'], correct_kojin.age)

        correct_schedules: List[HoyoSchedule] = [
            self.hoyo_seko_schedule_3,
            self.hoyo_seko_schedule_1,
            self.hoyo_seko_schedule_2,
        ]
        for rec_schedule, correct_schedule in zip(record['schedules'], correct_schedules):
            self.assertEqual(rec_schedule['schedule_name'], correct_schedule.schedule_name)
            self.assertEqual(rec_schedule['hall']['id'], correct_schedule.hall.pk)
            self.assertEqual(rec_schedule['hall']['base_name'], correct_schedule.hall.base_name)
            self.assertEqual(rec_schedule['hall_name'], correct_schedule.hall_name)
            self.assertEqual(
                rec_schedule['schedule_date'], correct_schedule.schedule_date.isoformat()
            )
            self.assertEqual(rec_schedule['begin_time'], correct_schedule.begin_time)
            self.assertEqual(rec_schedule['end_time'], correct_schedule.end_time)
            self.assertEqual(rec_schedule['display_num'], correct_schedule.display_num)

    def test_hoyo_seko_detail_failed_with_notfound(self) -> None:
        """法要施行詳細は無効になった法要施行を返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_seko.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_seko_detail_failed_with_disabled_seko(self) -> None:
        """法要施行詳細APIは法要施行の親(施行)が無効化されていて失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_seko.seko.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_seko_detail_succeed_without_auth(self) -> None:
        """法要施行詳細取得APIは認証がなくても成功する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record.get('id'), self.hoyo_seko.pk)


class HoyoSekoUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff = StaffFactory()
        self.hoyo_seko = HoyoSekoFactory(staff=self.staff)
        self.schedule_1 = HoyoScheduleFactory(hoyo_seko=self.hoyo_seko, display_num=2)
        self.schedule_2 = HoyoScheduleFactory(hoyo_seko=self.hoyo_seko, display_num=3)
        self.schedule_3 = HoyoScheduleFactory(hoyo_seko=self.hoyo_seko, display_num=1)
        MoshuFactory(seko=self.hoyo_seko.seko)
        KojinFactory(seko=self.hoyo_seko.seko, kojin_num=2)
        KojinFactory(seko=self.hoyo_seko.seko, kojin_num=1)

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        new_hoyo_seko_data: HoyoSeko = HoyoSekoFactory.build(
            seko=SekoFactory(), hall=BaseFactory(base_type=Base.OrgType.HALL), staff=self.staff
        )

        schedules: List[Dict] = []
        new_schedule_data_1 = self.schedule_1
        new_schedule_data_1.schedule_name = 'updated'
        new_schedule_data_1.hall = BaseFactory(base_type=Base.OrgType.HALL)
        new_schedule_data_1.display_num = 5
        new_schedule_data_2 = HoyoScheduleFactory.build(
            hoyo_seko=new_hoyo_seko_data,
            hall=BaseFactory(base_type=Base.OrgType.HALL),
            display_num=4,
        )
        for schedule_data in [new_schedule_data_1, new_schedule_data_2]:
            schedules.append(
                {
                    'schedule_name': schedule_data.schedule_name,
                    'hall': schedule_data.hall.pk,
                    'hall_name': schedule_data.hall_name,
                    'schedule_date': schedule_data.schedule_date.isoformat(),
                    'begin_time': schedule_data.begin_time,
                    'end_time': schedule_data.end_time,
                    'display_num': schedule_data.display_num,
                }
            )

        return {
            'hoyo_activity_date': new_hoyo_seko_data.hoyo_activity_date.isoformat(),
            'begin_time': new_hoyo_seko_data.begin_time,
            'end_time': new_hoyo_seko_data.end_time,
            'hall': new_hoyo_seko_data.hall.pk,
            'dine_flg': new_hoyo_seko_data.dine_flg,
            'seshu_name': new_hoyo_seko_data.seshu_name,
            'kojin_seshu_relationship': new_hoyo_seko_data.kojin_seshu_relationship,
            'zip_code': new_hoyo_seko_data.zip_code,
            'prefecture': new_hoyo_seko_data.prefecture,
            'address_1': new_hoyo_seko_data.address_1,
            'address_2': new_hoyo_seko_data.address_2,
            'address_3': new_hoyo_seko_data.address_3,
            'tel': new_hoyo_seko_data.tel,
            'hoyo_sentence': new_hoyo_seko_data.hoyo_sentence,
            'shishiki_name': new_hoyo_seko_data.shishiki_name,
            'reply_limit_date': new_hoyo_seko_data.reply_limit_date.isoformat(),
            'hoyo_site_end_date': new_hoyo_seko_data.hoyo_site_end_date.isoformat(),
            'note': new_hoyo_seko_data.note,
            'staff': new_hoyo_seko_data.staff.pk,
            'schedules': schedules,
        }

    def test_hoyo_seko_update_succeed(self) -> None:
        """法要施行を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.hoyo_seko.pk)
        self.assertEqual(record['seko']['id'], self.hoyo_seko.seko.pk)
        self.assertEqual(record['hoyo']['id'], self.hoyo_seko.hoyo.pk)
        self.assertEqual(record['hoyo']['name'], self.hoyo_seko.hoyo.name)
        self.assertEqual(record['hoyo']['elapsed_time'], self.hoyo_seko.hoyo.elapsed_time)
        self.assertEqual(record['hoyo_name'], self.hoyo_seko.hoyo_name)
        self.assertEqual(record['hoyo_planned_date'], self.hoyo_seko.hoyo_planned_date.isoformat())
        self.assertEqual(record['hoyo_activity_date'], params['hoyo_activity_date'])
        self.assertEqual(record['begin_time'], params['begin_time'])
        self.assertEqual(record['end_time'], params['end_time'])
        self.assertEqual(record['hall']['id'], params['hall'])
        self.assertEqual(record['dine_flg'], params['dine_flg'])
        self.assertEqual(record['seshu_name'], params['seshu_name'])
        self.assertEqual(record['kojin_seshu_relationship'], params['kojin_seshu_relationship'])
        self.assertEqual(record['zip_code'], params['zip_code'])
        self.assertEqual(record['prefecture'], params['prefecture'])
        self.assertEqual(record['address_1'], params['address_1'])
        self.assertEqual(record['address_2'], params['address_2'])
        self.assertEqual(record['address_3'], params['address_3'])
        self.assertEqual(record['tel'], params['tel'])
        self.assertEqual(record['hoyo_sentence'], params['hoyo_sentence'])
        self.assertEqual(record['shishiki_name'], params['shishiki_name'])
        self.assertEqual(record['reply_limit_date'], params['reply_limit_date'])
        self.assertEqual(record['hoyo_site_end_date'], params['hoyo_site_end_date'])
        self.assertEqual(record['note'], params['note'])
        self.assertEqual(record['staff']['id'], params['staff'])
        self.assertEqual(record['del_flg'], self.hoyo_seko.del_flg)
        self.assertEqual(
            record['created_at'], self.hoyo_seko.created_at.astimezone(tz).isoformat()
        )
        self.assertGreater(
            record['updated_at'], self.hoyo_seko.updated_at.astimezone(tz).isoformat()
        )

        for rec_kojin, correct_kojin in zip(
            record['seko']['kojin'], self.hoyo_seko.seko.kojin.order_by('kojin_num')
        ):
            self.assertEqual(rec_kojin['id'], correct_kojin.pk)
            self.assertEqual(rec_kojin['kojin_num'], correct_kojin.kojin_num)
            self.assertEqual(rec_kojin['name'], correct_kojin.name)
            self.assertEqual(rec_kojin['kaimyo'], correct_kojin.kaimyo)
            self.assertEqual(rec_kojin['death_date'], correct_kojin.death_date.isoformat())
            self.assertEqual(rec_kojin['age_kbn'], correct_kojin.age_kbn)
            self.assertEqual(rec_kojin['age'], correct_kojin.age)

        for rec_schedule, correct_schedule in zip(
            record['schedules'], reversed(params['schedules'])
        ):
            self.assertEqual(rec_schedule['schedule_name'], correct_schedule['schedule_name'])
            self.assertEqual(rec_schedule['hall']['id'], correct_schedule['hall'])
            self.assertEqual(rec_schedule['hall_name'], correct_schedule['hall_name'])
            self.assertEqual(rec_schedule['schedule_date'], correct_schedule['schedule_date'])
            self.assertEqual(rec_schedule['begin_time'], correct_schedule['begin_time'])
            self.assertEqual(rec_schedule['end_time'], correct_schedule['end_time'])
            self.assertEqual(rec_schedule['display_num'], correct_schedule['display_num'])

    def test_hoyo_seko_update_ignore_some_fields(self) -> None:
        """法要施行更新APIは特定の入力項目を無視する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['seko'] = SekoFactory().pk
        hoyo: Hoyo = HoyoFactory()
        params['hoyo'] = hoyo.pk
        params['hoyo_name'] = hoyo.name
        params['hoyo_planned_date'] = timezone.localdate().isoformat()
        params['del_flg'] = True
        params['created_at'] = timezone.localtime().isoformat()
        response = self.api_client.put(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.hoyo_seko.pk)
        self.assertEqual(record['seko']['id'], self.hoyo_seko.seko.pk)
        self.assertEqual(record['hoyo']['id'], self.hoyo_seko.hoyo.pk)
        self.assertEqual(record['hoyo']['name'], self.hoyo_seko.hoyo.name)
        self.assertEqual(record['hoyo']['elapsed_time'], self.hoyo_seko.hoyo.elapsed_time)
        self.assertEqual(record['hoyo_name'], self.hoyo_seko.hoyo_name)
        self.assertEqual(record['hoyo_planned_date'], self.hoyo_seko.hoyo_planned_date.isoformat())
        self.assertFalse(record['del_flg'])
        self.assertEqual(
            record['created_at'], self.hoyo_seko.created_at.astimezone(tz).isoformat()
        )

    def test_hoyo_seko_update_failed_with_disabled_hoyoseko(self) -> None:
        """法要施行更新APIが無効になった法要施行を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_seko.disable()

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_seko_update_failed_with_disabled_seko(self) -> None:
        """法要施行更新APIが法要施行の親(施行)が無効化されていて失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_seko.seko.disable()

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_seko_update_failed_without_auth(self) -> None:
        """法要施行更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class HoyoSekoDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.staff = StaffFactory()
        self.hoyo_seko = HoyoSekoFactory(staff=self.staff)
        self.hoyo_seko_schedule_1 = HoyoScheduleFactory(hoyo_seko=self.hoyo_seko, display_num=2)
        self.hoyo_seko_schedule_2 = HoyoScheduleFactory(hoyo_seko=self.hoyo_seko, display_num=3)
        self.hoyo_seko_schedule_3 = HoyoScheduleFactory(hoyo_seko=self.hoyo_seko, display_num=1)

        self.api_client = APIClient()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_seko_delete_succeed(self) -> None:
        """法要施行を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 法要施行のdel_flgがTrueになる
        db_hoyo_seko: HoyoSeko = HoyoSeko.objects.get(pk=self.hoyo_seko.pk)
        self.assertTrue(db_hoyo_seko.del_flg)

    def test_hoyo_seko_delete_failed_by_notfound(self) -> None:
        """法要施行削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_hoyo_seko: HoyoSeko = HoyoSekoFactory.build()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': non_saved_hoyo_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_seko_delete_failed_by_already_deleted(self) -> None:
        """法要施行削除APIが論理削除済みの法要施行を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_seko.del_flg = True
        self.hoyo_seko.save()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_seko_delete_failed_without_auth(self) -> None:
        """法要施行削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_seko_detail', kwargs={'pk': self.hoyo_seko.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
