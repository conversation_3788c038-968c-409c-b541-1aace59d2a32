from django_filters import rest_framework as filters
from rest_framework import generics
from rest_framework.permissions import IsAuthenticatedOrReadOnly

from service_reception_terms.models import ServiceReceptionTerm
from service_reception_terms.serializers import ServiceReceptionTermSerializer
from utils.view_mixins import AddContextMixin


class ServiceReceptionTermList(AddContextMixin, generics.ListCreateAPIView):

    queryset = ServiceReceptionTerm.objects.all()
    serializer_class = ServiceReceptionTermSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ['department']
    permission_classes = [IsAuthenticatedOrReadOnly]
