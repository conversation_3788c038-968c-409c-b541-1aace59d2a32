
<div class="container">
  <div class="inner">
    <div class="contents">
      <div class="menu_title">
        <i class="comment dots icon big"></i>
        追悼メッセージ受付一覧
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(companyComboEm, sekoBaseComboEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchMsg()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="form_data.company_id" (selectedItemChange)="companyChange($event, sekoBaseComboEm)"></com-dropdown>
          <label>施行拠点</label>
          <com-dropdown #sekoBaseComboEm [settings]="sekoBaseCombo" [(selectedValue)]="form_data.seko_department"></com-dropdown>
          <label>申込番号</label>
          <div class="ui input small">
            <input type="tel" [(ngModel)]="form_data.entry_id">
          </div>
          <label>申込者名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.entry_name">
          </div>
          <label class="large">申込者電話番号</label>
          <div class="ui input small">
            <input type="tel" [(ngModel)]="form_data.entry_tel">
          </div>
        </div>
        <div class="line">
          <label>葬家名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.soke_name">
          </div>
          <label>故人名</label>
          <div class="ui input">
            <input type="text" [(ngModel)]="form_data.kojin_name">
          </div>
          <label>喪主名</label>
          <div class="ui input small">
            <input type="text" [(ngModel)]="form_data.moshu_name">
          </div>
          <label>公開</label>
          <div class="ui checkbox" (click)="checkClick('is_ok', form_data.is_ng)">
            <input type="checkbox" name="is_ok" [(ngModel)]="form_data.is_ok" [checked]="form_data.is_ok">
            <label>OK</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('is_ng', form_data.is_ok)">
            <input type="checkbox" name="is_ng" [(ngModel)]="form_data.is_ng" [checked]="form_data.is_ng">
            <label>NG</label>
          </div>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="msg_list?.length">
          全{{msg_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?msg_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="entry_id">申込番号</th>
              <th class="entry_name">申込者名</th>
              <th class="entry_tel"><p>申込者</p>電話番号</th>
              <th class="relationship">間柄</th>
              <th class="seko_date">施行日</th>
              <th class="department_name">施行拠点</th>
              <th class="soke_name">葬家名</th>
              <th class="kojin_name">故人名</th>
              <th class="moshu_name">喪主名</th>
              <th class="entry_ts">申込日時</th>
              <th class="release_status">公開状態</th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let msg of msg_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" >
              <td class="center aligned entry_id" title="{{msg.entry_detail.entry.id}}">{{msg.entry_detail.entry.id}}</td>
              <td class="entry_name" title="{{msg.entry_detail.entry.entry_name}}">{{msg.entry_detail.entry.entry_name}}</td>
              <td class="entry_tel" title="{{msg.entry_detail.entry.entry_tel}}">{{msg.entry_detail.entry.entry_tel}}</td>
              <td class="relationship" title="{{msg.relation_ship}}">{{msg.relation_ship}}</td>
              <td class="center aligned seko_date" title="{{msg.entry_detail.entry.seko.seko_date | date: 'yyyy/MM/dd'}}">{{msg.entry_detail.entry.seko.seko_date | date: 'yyyy/MM/dd'}}</td>
              <td class="department_name" title="{{msg.entry_detail.entry.seko.seko_department_name}}">{{msg.entry_detail.entry.seko.seko_department_name}}</td>
              <td class="soke_name" title="{{msg.entry_detail.entry.seko.soke_name}}">{{msg.entry_detail.entry.seko.soke_name}}</td>
              <td class="kojin_name" title="{{msg.entry_detail.entry.seko.kojin[0].name}}">{{msg.entry_detail.entry.seko.kojin[0].name}}</td>
              <td class="moshu_name" title="{{msg.entry_detail.entry.seko.moshu.name}}">{{msg.entry_detail.entry.seko.moshu.name}}</td>
              <td class="center aligned entry_ts" title="{{msg.entry_detail.entry.entry_ts | date: 'MM/dd H:mm'}}">
                {{msg.entry_detail.entry.entry_ts | date: 'MM/dd H:mm'}}</td>
              <td class="center aligned release_status" title="{{Utils.getReleaseStatusName(msg.release_status)}}">
                {{Utils.getReleaseStatusName(msg.release_status)}}
              </td>
            </tr>
            </ng-container>
            <tr *ngIf="!msg_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="8">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>


      <div class="ui modal mini" id="receipt-url">
        <div class="header ui"><i class="linkify icon large"></i>
          領収書URL
        </div>
        <div class="content">
          <div class="input_area">
            <div class="line">
              <div class="ui input fluid">
                <input type="text" autocomplete="off" value="{{receipt_url}}" readonly (focus)="onFocus($event)">
              </div>
            </div>
          </div>
        </div>
      </div>

		</div>
  </div>
</div>
