# Generated by Django 3.1.5 on 2021-01-06 06:02

from pathlib import Path

from django.db import migrations


def is_postgres(schema_editor) -> bool:
    return schema_editor.connection.vendor.startswith('postgres')


def forwards(apps, schema_editor):
    if not is_postgres(schema_editor):
        return

    with open(Path('invoices/migrations/salescalc_v1.sql'), encoding='utf-8') as f:
        ddl: str = f.read()
    schema_editor.execute(ddl.replace('%', '%%'), params=[])


def backwards(apps, schema_editor):
    if not is_postgres(schema_editor):
        return

    schema_editor.execute('DROP PROCEDURE foc_salescalc;', params=[])


class Migration(migrations.Migration):

    dependencies = [
        ('invoices', '0001_initial'),
    ]

    operations = [migrations.RunPython(forwards, backwards, atomic=True)]
