FROM python:3.8-buster

EXPOSE 8000
ARG SECRET_KEY=dummy
ARG POETRY_VERSION=1.1.4

ENV \
  DEBUG=False \
  DJANGO_SETTINGS_MODULE=foc.settings \
  DATABASE_URL=sqlite:///db.sqlite3 \
  ENABLE_SILK=0 \
  ROOT_PATH= \
  WEB_CONCURRENCY= \
  STATIC_ROOT=/usr/src/app/static \
  ALLOWED_HOSTS=*

RUN \
  apt-get update && \
  DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
  apt-utils && \
  DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
  gettext fonts-noto-cjk fonts-kouzan-mouhitsu && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/* && \
  python -m pip install -U pip setuptools \
  pip install "poetry==${POETRY_VERSION}"

WORKDIR /usr/src/app

COPY pyproject.toml poetry.lock ./

RUN \
  poetry export -f requirements.txt > requirements.txt && \
  pip install --no-cache-dir -r requirements.txt

COPY . .

RUN \
  touch .env && \
  python manage.py compilemessages --locale ja && \
  python manage.py collectstatic --no-input

CMD ["gunicorn", "foc.asgi:application", \
"-k", "foc.workers.CustomUvicornWorker", \
"--access-logfile", "-", \
"-b", "0.0.0.0:8000", \
"--max-requests", "500", \
"--max-requests-jitter", "200"]
