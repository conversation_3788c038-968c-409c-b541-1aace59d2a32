from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from hoyo.models import Base, Hoyo
from hoyo.tests.factories import HoyoFactory
from masters.tests.factories import HoyoStyleFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class HoyoDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.hoyo = HoyoFactory(company=base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_detail_succeed(self) -> None:
        """法要詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_detail', kwargs={'pk': self.hoyo.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.hoyo.pk)
        self.assertEqual(record['company'], self.hoyo.company.pk)
        self.assertEqual(record['style']['id'], self.hoyo.style.pk)
        self.assertEqual(record['style']['name'], self.hoyo.style.name)
        self.assertEqual(record['name'], self.hoyo.name)
        self.assertEqual(record['elapsed_time'], self.hoyo.elapsed_time)
        self.assertEqual(record['unit'], self.hoyo.unit)
        self.assertEqual(record['display_num'], self.hoyo.display_num)
        self.assertEqual(record['del_flg'], self.hoyo.del_flg)
        self.assertEqual(record['created_at'], self.hoyo.created_at.astimezone(tz).isoformat())
        self.assertEqual(record['create_user_id'], self.hoyo.create_user_id)
        self.assertEqual(record['updated_at'], self.hoyo.updated_at.astimezone(tz).isoformat())
        self.assertEqual(record['update_user_id'], self.hoyo.update_user_id)

    def test_hoyo_detail_failed_by_notfound(self) -> None:
        """法要詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_hoyo: Hoyo = HoyoFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_detail', kwargs={'pk': non_saved_hoyo.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_detail_ignores_deleted(self) -> None:
        """法要詳細APIは無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_detail', kwargs={'pk': self.hoyo.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_detail_failed_without_auth(self) -> None:
        """法要詳細APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_detail', kwargs={'pk': self.hoyo.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class HoyoUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()
        self.hoyo = HoyoFactory(company=self.base)
        self.new_hoyo_data = HoyoFactory.build(company=self.base, style=HoyoStyleFactory())

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.new_hoyo_data.company.pk,
            'style': self.new_hoyo_data.style.pk,
            'name': self.new_hoyo_data.name,
            'elapsed_time': self.new_hoyo_data.elapsed_time,
            'unit': self.new_hoyo_data.unit,
            'display_num': self.new_hoyo_data.display_num,
        }

    def test_hoyo_update_succeed(self) -> None:
        """法要を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('hoyo:hoyo_detail', kwargs={'pk': self.hoyo.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.hoyo.pk)
        self.assertEqual(record['company'], self.new_hoyo_data.company.pk)
        self.assertEqual(record['style']['id'], self.new_hoyo_data.style.pk)
        self.assertEqual(record['style']['name'], self.new_hoyo_data.style.name)
        self.assertEqual(record['name'], self.new_hoyo_data.name)
        self.assertEqual(record['elapsed_time'], self.new_hoyo_data.elapsed_time)
        self.assertEqual(record['unit'], self.new_hoyo_data.unit)
        self.assertEqual(record['display_num'], self.new_hoyo_data.display_num)
        self.assertEqual(record['del_flg'], self.new_hoyo_data.del_flg)
        self.assertEqual(record['created_at'], self.hoyo.created_at.astimezone(tz).isoformat())
        self.assertEqual(record['create_user_id'], self.hoyo.create_user_id)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_hoyo_update_failed_by_notfound(self) -> None:
        """法要更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_hoyo: Hoyo = HoyoFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_detail', kwargs={'pk': non_saved_hoyo.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_update_ignores_deleted(self) -> None:
        """法要更新APIは無効化された法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('hoyo:hoyo_detail', kwargs={'pk': self.hoyo.pk}), data=params, format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_hoyo_update_failed_without_auth(self) -> None:
        """法要更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('hoyo:hoyo_detail', kwargs={'pk': self.hoyo.company.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class HoyoDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.hoyo: Hoyo = HoyoFactory(company=self.company)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.company)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_delete_succeed(self) -> None:
        """法要を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_detail', kwargs={'pk': self.hoyo.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 法要のdel_flgがTrueになるだけ
        db_hoyo: Hoyo = Hoyo.objects.get(pk=self.hoyo.pk)
        self.assertTrue(db_hoyo.del_flg)
        self.assertEqual(db_hoyo.create_user_id, self.hoyo.create_user_id)
        self.assertEqual(db_hoyo.update_user_id, self.staff.pk)

    def test_hoyo_delete_failed_by_notfound(self) -> None:
        """法要削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_hoyo: Hoyo = HoyoFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_detail', kwargs={'pk': non_saved_hoyo.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_delete_failed_by_already_deleted(self) -> None:
        """法要削除APIが論理削除済みの法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_detail', kwargs={'pk': self.hoyo.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_hoyo_delete_failed_without_auth(self) -> None:
        """法要削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('hoyo:hoyo_detail', kwargs={'pk': self.hoyo.pk}), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
