
.container .inner .contents {
  padding-bottom: 50px;
  .memorial .flexslider {
    margin: auto;
    max-width: 800px;
    margin-bottom: 5px;
    border: 0;
    background-color: #f3f3f3;
    line-height: 1.25;
    ul.slides {
      display: flex;
      >li {
        margin: auto 0;
      }
    }
    img {
      border-radius: 3px;
      max-width: 100%;
      // min-width: 100%;
      margin: auto;
      width: auto;
      height: auto;
      max-height: 500px;
      @media screen and (max-width: 560px) {
        max-height: 300px;
      }
    }
  }
}
:host ::ng-deep .flex-control-nav {
  top: auto;
}
:host ::ng-deep .flex-control-paging li a {
  width:  10px;
  height: 10px;
}
