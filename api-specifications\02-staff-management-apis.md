# スタッフ管理API仕様書

## 概要

FOCシステムのスタッフ管理関連APIの詳細仕様です。スタッフの作成、取得、更新、削除、および階層構造に基づくスタッフ一覧取得機能を提供します。

## 1. ログインユーザー情報取得API

### 1.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /staffs/me/` |
| **機能** | ログイン中のスタッフユーザーの詳細情報を取得 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `StaffMe` |
| **シリアライザー** | `StaffParentBasesSerializer` |

### 1.2 リクエスト仕様

**HTTPメソッド:** `GET`

**ヘッダー:**
```
Authorization: Bearer {access_token}
```

**パラメータ:** なし

### 1.3 レスポンス仕様

**成功時（200 OK）:**
```json
{
  "id": 1,
  "company_code": "COMPANY001",
  "login_id": "staff001",
  "name": "田中太郎",
  "base": {
    "id": 10,
    "base_name": "本社",
    "base_type": 1,
    "company_code": "COMPANY001",
    "parent": {
      "id": 1,
      "base_name": "株式会社サンプル",
      "base_type": 0,
      "company_code": "COMPANY001",
      "parent": null
    }
  },
  "mail_address": "<EMAIL>",
  "retired_flg": false,
  "created_at": "2024-01-01T10:00:00+09:00",
  "updated_at": "2024-01-15T14:30:00+09:00",
  "fdn_code": "FDN001"
}
```

**エラー時（401 Unauthorized）:**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 1.4 使用例

**リクエスト例:**
```bash
curl -X GET http://localhost:8000/staffs/me/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
```

## 2. スタッフ作成API

### 2.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `POST /staffs/` |
| **機能** | 新しいスタッフを作成 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `StaffList` |
| **シリアライザー** | `StaffSerializer` |

### 2.2 リクエスト仕様

**HTTPメソッド:** `POST`

**Content-Type:** `application/json`

**リクエストボディ:**
```json
{
  "company_code": "string",
  "login_id": "string",
  "name": "string",
  "password": "string",
  "base": "integer",
  "mail_address": "string",
  "retired_flg": "boolean",
  "fdn_code": "string"
}
```

**パラメータ詳細:**

| フィールド | 型 | 必須 | 桁数制限 | 説明 | バリデーション |
|---|---|---|---|---|---|
| `company_code` | string | ✓ | 最大50文字 | 会社コード | 存在する会社コードである必要がある |
| `login_id` | string | ✓ | 最大150文字 | ログインID | 会社内で一意である必要がある |
| `name` | string | ✓ | 最大100文字 | スタッフ名 | - |
| `password` | string | ✓ | 最大128文字 | パスワード | ハッシュ化されて保存される |
| `base` | integer | ✓ | - | 所属拠点ID | 存在する拠点IDである必要がある |
| `mail_address` | string | ○ | 最大254文字 | メールアドレス | 有効なメール形式である必要がある |
| `retired_flg` | boolean | ○ | - | 退職フラグ | デフォルト: false |
| `fdn_code` | string | ○ | 最大50文字 | FDNコード | 会社内で一意である必要がある |

### 2.3 バリデーション処理

#### 2.3.1 一意性制約
```python
# company_code + login_id の組み合わせが一意
UniqueTogetherValidator(
    queryset=Staff.objects.filter(del_flg=False).all(),
    fields=['company_code', 'login_id'],
)

# company_code + fdn_code の組み合わせが一意（fdn_codeがnullでない場合）
UniqueTogetherValidator(
    queryset=Staff.objects.filter(del_flg=False, fdn_code__isnull=False).all(),
    fields=['company_code', 'fdn_code'],
)
```

#### 2.3.2 パスワード処理
```python
def create(self, validated_data):
    _password: str = validated_data.pop('password')
    instance: Staff = super().create(validated_data)
    instance.set_password(_password)  # ハッシュ化
    instance.save()
    return instance
```

### 2.4 レスポンス仕様

**成功時（201 Created）:**
```json
{
  "id": 123,
  "company_code": "COMPANY001",
  "login_id": "new_staff",
  "name": "新規スタッフ",
  "base": {
    "id": 10,
    "base_name": "営業部",
    "base_type": 2
  },
  "base_name": "営業部",
  "mail_address": "<EMAIL>",
  "retired_flg": false,
  "create_user_id": 1,
  "created_at": "2024-06-27T10:00:00+09:00",
  "update_user_id": 1,
  "updated_at": "2024-06-27T10:00:00+09:00",
  "fdn_code": "FDN123"
}
```

**エラー時（400 Bad Request）:**
```json
{
  "company_code": ["This field is required."],
  "login_id": ["Staff with this Company code and Login id already exists."],
  "mail_address": ["Enter a valid email address."]
}
```

### 2.5 使用例

**リクエスト例:**
```bash
curl -X POST http://localhost:8000/staffs/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "company_code": "COMPANY001",
    "login_id": "new_staff",
    "name": "新規スタッフ",
    "password": "secure_password123",
    "base": 10,
    "mail_address": "<EMAIL>",
    "retired_flg": false,
    "fdn_code": "FDN123"
  }'
```

## 3. スタッフ詳細・更新・削除API

### 3.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET/PUT/PATCH/DELETE /staffs/{id}/` |
| **機能** | スタッフの詳細取得・更新・削除 |
| **認証** | JWT認証必須 |
| **権限** | IsStaff（スタッフのみ） |
| **実装クラス** | `StaffDetail` |
| **シリアライザー** | `StaffSerializer` |

### 3.2 スタッフ詳細取得

**HTTPメソッド:** `GET`

**パスパラメータ:**

| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `id` | integer | ✓ | スタッフID |

**レスポンス例:**
```json
{
  "id": 123,
  "company_code": "COMPANY001",
  "login_id": "staff123",
  "name": "山田花子",
  "base": {
    "id": 15,
    "base_name": "大阪支店",
    "base_type": 2
  },
  "base_name": "大阪支店",
  "mail_address": "<EMAIL>",
  "retired_flg": false,
  "create_user_id": 1,
  "created_at": "2024-01-01T10:00:00+09:00",
  "update_user_id": 5,
  "updated_at": "2024-06-15T14:30:00+09:00",
  "fdn_code": "FDN456"
}
```

### 3.3 スタッフ更新

**HTTPメソッド:** `PUT` または `PATCH`

**リクエストボディ（PUT - 全項目更新）:**
```json
{
  "company_code": "COMPANY001",
  "login_id": "staff123",
  "name": "山田花子（更新）",
  "password": "new_password123",
  "base": 20,
  "mail_address": "<EMAIL>",
  "retired_flg": false,
  "fdn_code": "FDN456_NEW"
}
```

**リクエストボディ（PATCH - 部分更新）:**
```json
{
  "name": "山田花子（更新）",
  "mail_address": "<EMAIL>"
}
```

**パスワード更新処理:**
```python
def update(self, instance, validated_data):
    update_password = 'password' in validated_data
    _password: str = validated_data.pop('password', None)
    instance = super().update(instance, validated_data)
    if not self.partial or update_password:
        instance.set_password(_password)  # ハッシュ化
        instance.save()
    return instance
```

### 3.4 スタッフ削除（論理削除）

**HTTPメソッド:** `DELETE`

**処理内容:**
- 物理削除ではなく論理削除を実行
- `del_flg`をTrueに設定
- `SoftDestroyMixin`による実装

**レスポンス（204 No Content）:**
```
（レスポンスボディなし）
```

## 4. 拠点配下スタッフ一覧API

### 4.1 基本情報
| 項目 | 値 |
|---|---|
| **エンドポイント** | `GET /staffs/lower/{base_id}/` |
| **機能** | 指定拠点とその配下拠点に所属するスタッフ一覧を取得 |
| **認証** | JWT認証必須 |
| **権限** | IsAuthenticated（認証済みユーザー） |
| **実装クラス** | `StaffListLowerBases` |
| **シリアライザー** | `StaffSerializer` |

### 4.2 リクエスト仕様

**HTTPメソッド:** `GET`

**パスパラメータ:**

| パラメータ | 型 | 必須 | 説明 |
|---|---|---|---|
| `base_id` | integer | ✓ | 基準となる拠点ID |

### 4.3 処理ロジック

```python
def get_queryset(self):
    specified_base: Base = Base.objects.get(Q(pk=self.kwargs['base_id']) & Q(del_flg=False))
    base_ids: QuerySet = (
        specified_base.get_descendants(include_self=True)  # 指定拠点とその配下
        .filter(Q(del_flg=False))
        .values_list('id', flat=True)
    )
    return (
        Staff.objects.select_related('base')
        .filter(Q(base_id__in=base_ids) & Q(del_flg=False))
        .all()
    )
```

### 4.4 レスポンス仕様

**成功時（200 OK）:**
```json
[
  {
    "id": 1,
    "company_code": "COMPANY001",
    "login_id": "manager001",
    "name": "部長 太郎",
    "base": {
      "id": 10,
      "base_name": "営業部",
      "base_type": 2
    },
    "base_name": "営業部",
    "mail_address": "<EMAIL>",
    "retired_flg": false,
    "create_user_id": 1,
    "created_at": "2024-01-01T10:00:00+09:00",
    "update_user_id": 1,
    "updated_at": "2024-01-01T10:00:00+09:00",
    "fdn_code": "FDN001"
  },
  {
    "id": 2,
    "company_code": "COMPANY001",
    "login_id": "staff002",
    "name": "営業 花子",
    "base": {
      "id": 11,
      "base_name": "営業1課",
      "base_type": 3
    },
    "base_name": "営業1課",
    "mail_address": "<EMAIL>",
    "retired_flg": false,
    "create_user_id": 1,
    "created_at": "2024-01-02T10:00:00+09:00",
    "update_user_id": 1,
    "updated_at": "2024-01-02T10:00:00+09:00",
    "fdn_code": "FDN002"
  }
]
```

**エラー時（404 Not Found）:**
```json
{
  "detail": "Not found."
}
```

### 4.5 使用例

**リクエスト例:**
```bash
curl -X GET http://localhost:8000/staffs/lower/10/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
```

## 5. 共通仕様

### 5.1 ミックスイン

#### AddContextMixin
```python
class AddContextMixin:
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['staff'] = self.request.user
        return context
```

#### SoftDestroyMixin
```python
class SoftDestroyMixin(AddContextMixin):
    def perform_destroy(self, instance):
        instance.update_user_id = self.request.user.id
        instance.disable()  # del_flg = True
```

#### AddUserIdMixin
```python
class AddUserIdMixin:
    def create(self, validated_data):
        if 'staff' in self.context:
            validated_data['create_user_id'] = self.context['staff'].id
            validated_data['update_user_id'] = self.context['staff'].id
        return super().create(validated_data)

    def update(self, instance, validated_data):
        if 'staff' in self.context:
            validated_data['update_user_id'] = self.context['staff'].id
        return super().update(instance, validated_data)
```

### 5.2 エラーコード一覧

| ステータスコード | エラーメッセージ | 発生条件 |
|---|---|---|
| 400 | This field is required. | 必須フィールドが未入力 |
| 400 | Staff with this Company code and Login id already exists. | ログインIDが重複 |
| 400 | Staff with this Company code and Fdn code already exists. | FDNコードが重複 |
| 400 | Enter a valid email address. | メールアドレス形式エラー |
| 401 | Authentication credentials were not provided. | 認証情報なし |
| 403 | You do not have permission to perform this action. | 権限不足 |
| 404 | Not found. | 指定されたリソースが存在しない |

### 5.3 セキュリティ考慮事項

- **パスワードハッシュ化**: 平文パスワードは保存されない
- **論理削除**: 物理削除ではなく論理削除でデータ保護
- **権限制御**: スタッフ権限による適切なアクセス制御
- **監査ログ**: 作成者・更新者IDの自動記録

## 6. 補足事項

### 6.1 階層構造処理
スタッフ管理APIでは、拠点の階層構造を利用した検索機能を提供しています：

```python
# 指定拠点とその配下拠点のスタッフを取得
specified_base.get_descendants(include_self=True)
```

### 6.2 パフォーマンス最適化
- **select_related**: 拠点情報の効率的な取得
- **論理削除フィルタ**: `del_flg=False`による有効データのみ取得
- **インデックス**: company_code + login_id、company_code + fdn_codeの複合インデックス
