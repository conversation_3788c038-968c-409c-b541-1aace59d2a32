from django.db import models
from django.utils.translation import gettext_lazy as _

from seko.models import Seko
from staffs.models import Staff


class EventMail(models.Model):
    event_name = models.TextField(_('event name'))
    select_ts = models.DateTimeField(_('selected at'))
    send_ts = models.DateTimeField(_('sent at'), blank=True, null=True)
    content = models.TextField(_('content'))
    staff = models.ForeignKey(Staff, models.PROTECT, verbose_name=_('staff'))
    note = models.TextField(_('note'), blank=True, null=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_event_mail'


class EventMailTarget(models.Model):
    event_mail = models.ForeignKey(
        EventMail, models.CASCADE, verbose_name=_('event mail'), related_name='targets'
    )
    seko = models.Foreign<PERSON>ey(
        Seko, models.PROTECT, verbose_name=_('seko'), related_name='event_mail_targets'
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        db_table = 'tr_event_mail_target'
