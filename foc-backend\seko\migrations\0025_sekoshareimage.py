# Generated by Django 3.1.14 on 2024-09-24 07:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('seko', '0024_auto_20230627_0957'),
    ]

    operations = [
        migrations.CreateModel(
            name='SekoShareImage',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'file_name',
                    models.ImageField(upload_to='seko_share_image', verbose_name='file name'),
                ),
                ('mode', models.IntegerField(verbose_name='mode')),
                ('display_num', models.IntegerField(verbose_name='display order')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='share_images',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_share_image',
            },
        ),
    ]
