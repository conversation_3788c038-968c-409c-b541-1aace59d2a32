from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.models import AfterFollow
from after_follow.tests.factories import AfterFollowFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class AfterFollowDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.after_follow = AfterFollowFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_after_follow_detail_succeed(self) -> None:
        """AF項目詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:after_follow_detail', kwargs={'pk': self.after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.after_follow.pk)
        self.assertEqual(record['af_group']['id'], self.after_follow.af_group.pk)
        self.assertEqual(record['af_group']['name'], self.after_follow.af_group.name)
        self.assertEqual(record['af_group']['display_num'], self.after_follow.af_group.display_num)
        self.assertEqual(record['name'], self.after_follow.name)
        self.assertEqual(record['display_num'], self.after_follow.display_num)
        self.assertEqual(record['del_flg'], self.after_follow.del_flg)
        self.assertEqual(
            record['created_at'], self.after_follow.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.after_follow.create_user_id)
        self.assertEqual(
            record['updated_at'], self.after_follow.updated_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['update_user_id'], self.after_follow.update_user_id)

    def test_after_follow_detail_failed_by_notfound(self) -> None:
        """AF項目詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_after_follow: AfterFollow = AfterFollowFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:after_follow_detail', kwargs={'pk': non_saved_after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_after_follow_detail_ignores_deleted(self) -> None:
        """AF項目詳細APIは無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.after_follow.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:after_follow_detail', kwargs={'pk': self.after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_after_follow_detail_failed_without_auth(self) -> None:
        """AF項目詳細APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:after_follow_detail', kwargs={'pk': self.after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class AfterFollowUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.after_follow = AfterFollowFactory()
        self.new_after_follow_data = AfterFollowFactory.build(af_group=self.after_follow.af_group)

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'af_group': self.new_after_follow_data.af_group.pk,
            'name': self.new_after_follow_data.name,
            'display_num': self.new_after_follow_data.display_num,
        }

    def test_after_follow_update_succeed(self) -> None:
        """AF項目を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('after_follow:after_follow_detail', kwargs={'pk': self.after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.after_follow.pk)
        self.assertEqual(record['af_group']['id'], self.new_after_follow_data.af_group.pk)
        self.assertEqual(record['af_group']['name'], self.new_after_follow_data.af_group.name)
        self.assertEqual(
            record['af_group']['display_num'], self.new_after_follow_data.af_group.display_num
        )
        self.assertEqual(record['name'], self.new_after_follow_data.name)
        self.assertEqual(record['display_num'], self.new_after_follow_data.display_num)
        self.assertEqual(record['del_flg'], self.new_after_follow_data.del_flg)
        self.assertEqual(
            record['created_at'], self.after_follow.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.after_follow.create_user_id)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_after_follow_update_failed_by_notfound(self) -> None:
        """AF項目更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_after_follow: AfterFollow = AfterFollowFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:after_follow_detail', kwargs={'pk': non_saved_after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_after_follow_update_ignores_deleted(self) -> None:
        """AF項目更新APIは無効化された法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.after_follow.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:after_follow_detail', kwargs={'pk': self.after_follow.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_after_follow_update_failed_without_auth(self) -> None:
        """AF項目更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('after_follow:after_follow_detail', kwargs={'pk': self.after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class AfterFollowDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.after_follow: AfterFollow = AfterFollowFactory()

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_after_follow_delete_succeed(self) -> None:
        """AF項目を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:after_follow_detail', kwargs={'pk': self.after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # AF項目のdel_flgがTrueになるだけ
        db_after_follow: AfterFollow = AfterFollow.objects.get(pk=self.after_follow.pk)
        self.assertTrue(db_after_follow.del_flg)
        self.assertEqual(db_after_follow.create_user_id, self.after_follow.create_user_id)
        self.assertEqual(db_after_follow.update_user_id, self.staff.pk)

    def test_after_follow_delete_failed_by_notfound(self) -> None:
        """AF項目削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_after_follow: AfterFollow = AfterFollowFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:after_follow_detail', kwargs={'pk': non_saved_after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_after_follow_delete_failed_by_already_deleted(self) -> None:
        """AF項目削除APIが論理削除済みの法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.after_follow.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:after_follow_detail', kwargs={'pk': self.after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_after_follow_delete_failed_without_auth(self) -> None:
        """AF項目削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:after_follow_detail', kwargs={'pk': self.after_follow.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
