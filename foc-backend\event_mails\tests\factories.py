import factory
import faker
from django.utils import timezone
from factory.django import DjangoModelFactory

from event_mails.models import EventMail, EventMailTarget
from seko.tests.factories import SekoFactory
from staffs.tests.factories import StaffFactory

tz = timezone.get_current_timezone()
fake_provider = faker.Faker('ja_JP')


class EventMailFactory(DjangoModelFactory):
    class Meta:
        model = EventMail

    id = factory.Sequence(lambda n: n)
    event_name = factory.Faker('word', locale='ja_JP')
    select_ts = factory.Faker('past_datetime', start_date='-10d', tzinfo=tz)
    send_ts = factory.Faker('past_datetime', start_date='-10d', tzinfo=tz)
    content = factory.Faker('paragraph', locale='ja_JP')
    staff = factory.SubFactory(StaffFactory)
    note = factory.Faker('paragraph', locale='ja_JP')
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)


class EventMailTargetFactory(DjangoModelFactory):
    class Meta:
        model = EventMailTarget

    id = factory.Sequence(lambda n: n)
    event_mail = factory.SubFactory(EventMailFactory)
    seko = factory.SubFactory(SekoFactory)
    created_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
    updated_at = factory.Faker('past_datetime', start_date='-30d', tzinfo=tz)
