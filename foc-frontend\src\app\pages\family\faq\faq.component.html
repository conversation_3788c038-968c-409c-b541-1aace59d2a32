
<div class="container">
  <div class="inner" *ngIf="!is_loading">
    <div class="contents">
      <h2>よくあるご質問</h2>
      <div class="faq-list">
        <ng-container *ngIf="faq_list?.length">
          <ng-container *ngFor="let faq of faq_list;index as i">
            <div class="faq-data">
              <div class="markQ">Q.</div>
              <div class="question">{{faq.question}}</div>
            </div>
            <div class="faq-data">
              <div class="markA">A.</div>
              <div class="answer">{{faq.answer}}</div>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </div>
</div>

