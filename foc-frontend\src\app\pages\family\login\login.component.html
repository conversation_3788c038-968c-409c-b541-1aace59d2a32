
<div class="container">
  <div class="inner">
    <div class="contents">
      <div class="input-area">
        <div class="line">
          <div class="label required">ご葬家ID</div>
          <div class="input login_id" #loginIdEm [class.error]="isErrorField(loginIdEm)">
            <input type="tel" autocomplete="off" [(ngModel)]="login_id">
          </div>
        </div>
        <div class="line">
          <div class="label required">パスワード</div>
          <div class="input password" #passwordEm [class.error]="isErrorField(passwordEm)">
            <input type="password" autocomplete="off" [(ngModel)]="password">
          </div>
        </div>
      </div>
      <div class="button-area">
        <a class="button grey" (click)="login()">ログイン</a>
      </div>
      <a class="reset" (click)="resetPassComnfirm()">パスワードをお忘れの場合</a>
      <div class="message" *ngIf="message">{{message}}</div>
    </div>
    <div class="ui modal confirm" id="msg-confirm">
      <div class="content">
        パスワードをリセットします。<br>よろしいでしょうか？
      </div>
      <div class="button-area">
        <a class="button" (click)="cancelConfirm()">キャンセル</a>
        <a class="button grey" (click)="resetPass()">確定</a>
      </div>
    </div>
  </div>
</div>
