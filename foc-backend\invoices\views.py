import functools
import itertools
from base64 import b64encode
from io import BytesIO
from typing import Dict

from django.conf import settings
from django.contrib.staticfiles import finders
from django.core.mail import EmailMessage
from django.db import connection
from django.db.models import Prefetch, Q
from django.http.response import FileResponse
from django.template.loader import get_template
from django_filters import rest_framework as filters
from rest_framework import exceptions, generics, status, views
from rest_framework.response import Response
from weasyprint import HTML

from bases.models import Base
from invoices.models import SalesCompany, SalesDetail, SalesSekoDepartment
from invoices.serializers import (
    CancelFixSalesCompanySerializer,
    FixSalesCompanySerializer,
    GatherSumSalesSerializer,
    SalesCompanyRelatedSerializer,
)
from utils.permissions import IsStaff


class GatherSumSales(views.APIView):
    permission_classes = [IsStaff]

    def post(self, request, format=None):
        serializer = GatherSumSalesSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales_yymm: str = serializer.validated_data['sales_yymm']

        with connection.cursor() as cursor:
            cursor.execute('CALL foc_salescalc(%s)', [sales_yymm])

        return Response(data={}, status=status.HTTP_200_OK)


class SalesListFilter(filters.FilterSet):
    sales_yymm = filters.CharFilter(field_name='sales_index__sales_yymm')
    fiscal_year = filters.NumberFilter(field_name='sales_index__fiscal_year')
    company = filters.NumberFilter(field_name='company_id')


class SalesList(generics.ListAPIView):

    queryset = (
        SalesCompany.objects.select_related('sales_index')
        .prefetch_related(
            Prefetch('sales_seko_departments'), Prefetch('sales_seko_departments__sales_details')
        )
        .order_by('sales_index', 'company_id')
    )
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = SalesListFilter
    serializer_class = SalesCompanyRelatedSerializer
    permission_classes = [IsStaff]


class FixSalesCompany(views.APIView):
    permission_classes = [IsStaff]

    def post(self, request, pk, format=None):
        instance: SalesCompany = SalesCompany.objects.filter(Q(pk=pk)).first()
        if not instance:
            raise exceptions.NotFound()
        serializer = FixSalesCompanySerializer(instance=instance, data=request.data)
        serializer.context['staff'] = request.user
        serializer.is_valid(raise_exception=True)

        base = Base.objects.get(pk=instance.company_id)
        address_to_list = list([staff.mail_address for staff in base.staffs.all()])
        address_to_list = list(filter(lambda s: s is not None, address_to_list))
        if len(address_to_list):
            year = instance.sales_index.sales_yymm[0:4]
            month = int(instance.sales_index.sales_yymm[4:6])
            mail_body = get_template('mail/sales_fix.txt').render(
                {
                    'base': base,
                    'year': year,
                    'month': month,
                }
            )
            message = EmailMessage(
                subject='【FOC】ご請求金額確定のお知らせ',
                body=mail_body,
                from_email=settings.EMAIL_FROM,
                to=address_to_list,
                bcc=settings.INVOICE_EMAIL_BCC,
            )
            message.send(fail_silently=False)

        result: SalesCompany = serializer.save()
        read_serializer = SalesCompanyRelatedSerializer(result)
        return Response(read_serializer.data)


class CancelFixSalesCompany(views.APIView):
    permission_classes = [IsStaff]

    def post(self, request, pk, format=None):
        instance: SalesCompany = SalesCompany.objects.filter(Q(pk=pk)).first()
        if not instance:
            raise exceptions.NotFound()
        serializer = CancelFixSalesCompanySerializer(instance=instance, data=request.data)
        serializer.is_valid(raise_exception=True)

        result: SalesCompany = serializer.save()
        read_serializer = SalesCompanyRelatedSerializer(result)
        return Response(read_serializer.data)


class InvoicePDF(generics.RetrieveAPIView):
    permission_classes = [IsStaff]
    queryset = SalesCompany.objects.select_related('sales_index',).prefetch_related(
        Prefetch(
            'sales_seko_departments',
            queryset=SalesSekoDepartment.objects.prefetch_related(
                Prefetch(
                    'sales_details',
                    queryset=SalesDetail.objects.order_by(
                        'seko_id', 'service_id', 'entry_detail_id', 'id'
                    ),
                )
            ).order_by('id'),
        ),
    )

    def retrieve(self, request, *args, **kwargs):
        sales = self.get_object()

        static_images: Dict[str, str] = {'logo': 'pdf/logo.png', 'stamp': 'pdf/stamp.png'}
        image_encoded: Dict[str, str] = {}
        for key, image_name in static_images.items():
            physical_path: str = finders.find(image_name)
            with open(physical_path, 'rb') as image_file:
                encoded: str = b64encode(image_file.read()).decode('ascii')
                image_encoded[key] = f'data:image/png;base64,{encoded}'

        # html_text = get_template('pdf/invoice.html').render(
        #     {
        #         'counter': functools.partial(next, itertools.count(start=1)),
        #         'sales': sales,
        #         'images': image_encoded,
        #     }
        # )

        # css_text = get_template('pdf/invoice.css').render()

        # buffer = BytesIO()
        # HTML(string=html_text, base_url=request.build_absolute_uri()).write_pdf(
        #     buffer,
        #     stylesheets=[CSS(string=css_text, base_url=request.build_absolute_uri())],
        # )
        # buffer.seek(0)

        html_text_top = get_template('pdf/invoice_top.html').render(
            {
                'counter': functools.partial(next, itertools.count(start=1)),
                'sales': sales,
                'images': image_encoded,
            }
        )
        html_text_detail = get_template('pdf/invoice_detail.html').render(
            {'counter': functools.partial(next, itertools.count(start=1)), 'sales': sales}
        )

        pdf_top = HTML(string=html_text_top, base_url=request.build_absolute_uri()).render()
        pdf_detail = HTML(string=html_text_detail, base_url=request.build_absolute_uri()).render()

        all_pages = []
        for doc in pdf_top, pdf_detail:
            for p in doc.pages:
                all_pages.append(p)

        buffer = BytesIO()
        pdf_top.copy(all_pages).write_pdf(buffer)

        buffer.seek(0)

        response = FileResponse(
            buffer,
            as_attachment=True,
            filename=f'請求書-{sales.id}.pdf',
            content_type='application/pdf',
        )
        return response
