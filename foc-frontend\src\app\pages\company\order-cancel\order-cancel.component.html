

<ng-container *ngIf="entry">
  <div class="search_area">
    <div class="line">
      <label>申込番号</label>
      <div>{{entry.id}}</div>
      <label>申込日時</label>
      <div>{{entry.entry_ts | date: 'yyyy/MM/dd H:mm'}}</div>
      <label>メールアドレス</label>
      <div>{{entry.entry_mail_address}}</div>
    </div>
    <div class="line">
      <label>申込者名</label>
      <div>{{entry.entry_name}}</div>
      <label>電話番号</label>
      <div>{{entry.entry_tel}}</div>
      <label>住所</label>
      <div>{{entry.entry_prefecture}} {{entry.entry_address_1}} {{entry.entry_address_2}} {{entry.entry_address_3}}</div>
    </div>
  </div>

  <div class="detail">
    <div class="entry-detail">
      <div class="sub_title">
        申込内容
      </div>
      <div class="table_fixed head" *ngIf="entry.details">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!entry.details?.length">
              <th class="service">サービス</th>
              <th class="item_name">商品</th>
              <th class="item_unit_price">価格</th>
              <th class="quantity">数量</th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let detail of entry.details">
              <tr>
                <td class="service" title="{{detail.item.service.name}}">{{detail.item.service.name}}</td>
                <td class="item_name" title="{{detail.item_name}}">{{detail.item_name}}</td>
                <td class="right aligned item_unit_price" title="{{detail.item_unit_price | number}}">{{detail.item_unit_price | number}}</td>
                <td class="center aligned quantity" title="{{detail.quantity}}">{{detail.quantity}}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
    <div class="henreihin">
      <div class="sub_title">
        返礼品
      </div>
      <div class="table_fixed head" *ngIf="entry.details">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.no-data]="!henrei_count">
              <th class="item_name">商品</th>
              <th class="supplier">発注先</th>
              <th class="order_status">発注状況</th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let detail of entry.details">
              <tr *ngIf="detail.koden && detail.koden.henrei_koden">
                <td class="item_name" title="{{detail.koden.henrei_koden.henreihin_name}}">{{detail.koden.henrei_koden.henreihin_name}}</td>
                <td class="supplier" title="{{detail.koden.henrei_koden.supplier?detail.koden.henrei_koden.supplier.name:''}}">{{detail.koden.henrei_koden.supplier?detail.koden.henrei_koden.supplier.name:''}}</td>
                <td class="order_status" title="{{Utils.getOrderStatusName(detail.koden.henrei_koden.order_status)}}">{{Utils.getOrderStatusName(detail.koden.henrei_koden.order_status)}}</td>
              </tr>
              <tr *ngIf="detail.kumotsu && detail.kumotsu.henrei_kumotsu">
                <td class="item_name" title="{{detail.kumotsu.henrei_kumotsu.henreihin_name}}">{{detail.kumotsu.henrei_kumotsu.henreihin_name}}</td>
                <td class="supplier" title="{{detail.kumotsu.henrei_kumotsu.supplier?detail.kumotsu.henrei_kumotsu.supplier.name:''}}">{{detail.kumotsu.henrei_kumotsu.supplier?detail.kumotsu.henrei_kumotsu.supplier.name:''}}</td>
                <td class="order_status" title="{{Utils.getOrderStatusName(detail.kumotsu.henrei_kumotsu.order_status)}}">{{Utils.getOrderStatusName(detail.kumotsu.henrei_kumotsu.order_status)}}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
<ng-container>
