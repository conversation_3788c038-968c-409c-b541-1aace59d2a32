version: "3.8"
services:
  traefik:
    image: traefik:v2.3
    ports:
      - 80:80
      - 443:443
      - 8080:8080
    configs:
      - source: traefik-config
        target: /etc/traefik/dynamic.yml
        mode: 0440
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-acme:/etc/traefik/acme
      - type: bind
        source: ./config/prd/tls/
        target: /etc/traefik/tls/
    command:
      - "--api.dashboard=true"
      - "--providers.docker.swarmMode=true"
      - "--providers.docker.exposedByDefault=false"
      - "--providers.file.directory=/etc/traefik/"
      - "--providers.file.watch=true"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--entrypoints.dashboard.address=:8080"
      # - "--accesslog=true"
      - "--log.level=INFO"
      - "--certificatesresolvers.focresolver.acme.httpchallenge=true"
      - "--certificatesresolvers.focresolver.acme.httpchallenge.entrypoint=web"
      - "--certificatesresolvers.focresolver.acme.keyType=EC256"
      # - "--certificatesresolvers.focresolver.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory"
      - "--certificatesresolvers.focresolver.acme.email=<EMAIL>"
      - "--certificatesresolvers.focresolver.acme.storage=/etc/traefik/acme/acme.json"
    deploy:
      labels:
        traefik.enable: "true"
        # traefik.http.routers.dashboard.rule: Host(`localhost`) && (PathPrefix(`/api`) || PathPrefix(`/dashboard`))
        traefik.http.routers.dashboard.rule: PathPrefix(`/api`) || PathPrefix(`/dashboard`)
        traefik.http.routers.dashboard.entrypoints: dashboard
        traefik.http.routers.dashboard.service: api@internal
        traefik.http.routers.dashboard.middlewares: auth
        traefik.http.middlewares.auth.basicauth.users: test:$$apr1$$H6uskkkW$$IgXLP6ewTrSuBkTrqE8wj/,test2:$$apr1$$d9hr9HBB$$4HxwgUir3HP4EsggP/QNo0
        # Dummy service for Swarm port detection. The port can be any valid integer value.
        traefik.http.services.dashboard.loadbalancer.server.port: 8080
      placement:
        constraints:
          - node.role == manager
      replicas: 1
      update_config:
        parallelism: 1

  dev-db:
    image: postgres:12
    ports:
      - 4321:5432
    volumes:
      - type: bind
        source: /var/lib/dev-postgresql/data
        target: /var/lib/postgresql/data
    environment:
      POSTGRES_USER: foc
      POSTGRES_PASSWORD: NLyYqTvKeCzZMiWvNyy26ipG
    deploy:
      labels:
        traefik.enable: "false"
      replicas: 1
      update_config:
        parallelism: 1

  dev-backend:
    image: 127.0.0.1:5000/foc-backend
    build:
      context: ./backend/
    environment:
      DATABASE_URL: *****************************************************/foc
      SECRET_KEY: zxdz=i3*_d*8l*d@i46c%&gywv6dv3wwr0rtimu1v-#322s+cl
      DEBUG: 1
      ALLOWED_HOSTS: "*"
      CORS_ORIGIN_ALLOW_ALL: 1
      FRONTEND_HOSTNAME: https://dev.foc.jpn.com/
      JWT_EXPIRATION_MINUTES: 5
      JWT_REFRESH_EXPIRATION_DAYS: 1
      JWT_ISSUER: Focus inc.
      JWT_AUDIENCE: FOC dev team
      JWT_PRIVATE_KEY_FILE: /usr/src/app/jwt-rsa
      ROOT_PATH: /api
      STATIC_ROOT: /usr/src/app/static
      MEDIA_ROOT: /usr/src/app/media
      MEDIA_URL: /media/
      USE_TLS: 1
      EMAIL_VIA_SENDGRID: 1
      SENDGRID_API_KEY: *********************************************************************
      SENDGRID_SANDBOX_MODE_IN_DEBUG: 0
      SENDGRID_TRACK_EMAIL_OPENS: 0
      SENDGRID_TRACK_CLICKS_HTML: 0
      SENDGRID_TRACK_CLICKS_PLAIN: 0
      EMAIL_FROM: <EMAIL>
      ORDER_EMAIL_BCC: <EMAIL>
      WEB_CONCURRENCY: 2
      FORCE_SCRIPT_NAME: /api
      ENABLE_EPSILON_PAYMENT: 1
      EPSILON_REGISTER_PAYMENT_URL: https://beta.epsilon.jp/cgi-bin/order/direct_card_payment.cgi
    configs:
      - source: dev-jwt-key
        target: /usr/src/app/jwt-rsa
        mode: 0440
    volumes:
      - type: bind
        source: /opt/foc/uploads/dev
        target: /usr/src/app/media
    depends_on:
      - dev-db
    deploy:
      labels:
        traefik.enable: "true"
        traefik.http.services.dev-backend-service.loadbalancer.server.port: 8000
        traefik.http.middlewares.deployApi.stripPrefix.prefixes: /api
        traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
        traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
        traefik.http.routers.dev-backend.rule: Host(`dev.foc.jpn.com`) && PathPrefix(`/api`)
        traefik.http.routers.dev-backend.entrypoints: web
        traefik.http.routers.dev-backend.middlewares: https_redirect,deployApi
        traefik.http.routers.dev-backend-tls.rule: Host(`dev.foc.jpn.com`) && PathPrefix(`/api`)
        traefik.http.routers.dev-backend-tls.entrypoints: websecure
        traefik.http.routers.dev-backend-tls.tls.certresolver: focresolver
        traefik.http.routers.dev-backend-tls.middlewares: deployApi
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s

  dev-frontend:
    image: 127.0.0.1:5000/foc-frontend
    build:
      context: ./frontend
    environment:
      DAPHNE_HOST: dev-backend
      DAPHNE_PORT: 8000
      URLS: api|static|_ping
      TIMEOUT: 600
      DEBUG_LOG: "true"
      API_URL: /api
      CARD_TOKEN_URL: https://beta.epsilon.jp/js/token.js
    volumes:
      - type: bind
        source: /opt/foc/uploads/dev
        target: /usr/share/nginx/html/media
        read_only: true
    depends_on:
      - dev-backend
    deploy:
      labels:
        traefik.enable: "true"
        traefik.http.services.dev-frontend-service.loadbalancer.server.port: 80
        traefik.http.middlewares.test-auth.basicauth.users: msi:$$apr1$$Bl.RBMJo$$GDShnvFEncoZU5mMB054d/
        traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
        traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
        traefik.http.routers.dev-frontend.rule: Host(`dev.foc.jpn.com`)
        traefik.http.routers.dev-frontend.entrypoints: web
        traefik.http.routers.dev-frontend.middlewares: https_redirect, test-auth
        traefik.http.routers.dev-frontend-tls.rule: Host(`dev.foc.jpn.com`)
        traefik.http.routers.dev-frontend-tls.entrypoints: websecure
        traefik.http.routers.dev-frontend-tls.tls.certresolver: focresolver
        traefik.http.routers.dev-frontend-tls.middlewares: test-auth
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s

  stg-db:
    image: postgres:12
    ports:
      - 5432:5432
    volumes:
      - type: bind
        source: /var/lib/postgresql/data
        target: /var/lib/postgresql/data
    environment:
      POSTGRES_USER: foc
      POSTGRES_PASSWORD: RsP3HExHVC6caLY2ZdWuncBo
    deploy:
      labels:
        traefik.enable: "false"
      replicas: 1
      update_config:
        parallelism: 1

  stg-backend:
    image: 127.0.0.1:5000/foc-backend
    environment:
      DATABASE_URL: *****************************************************/foc
      SECRET_KEY: l&uc^39mry24&#le^cj0=0oviq8+4&wgl0iyxtx9uy%wvtz4_(
      DEBUG: 1
      ALLOWED_HOSTS: "*"
      CORS_ORIGIN_ALLOW_ALL: 1
      FRONTEND_HOSTNAME: https://stg.foc.jpn.com/
      JWT_EXPIRATION_MINUTES: 5
      JWT_REFRESH_EXPIRATION_DAYS: 1
      JWT_ISSUER: Focus inc.
      JWT_AUDIENCE: FOC dev team
      JWT_PRIVATE_KEY_FILE: /usr/src/app/jwt-rsa
      ROOT_PATH: /api
      STATIC_ROOT: /usr/src/app/static
      MEDIA_ROOT: /usr/src/app/media
      MEDIA_URL: /media/
      USE_TLS: 1
      EMAIL_VIA_SENDGRID: 1
      SENDGRID_API_KEY: *********************************************************************
      SENDGRID_SANDBOX_MODE_IN_DEBUG: 0
      SENDGRID_TRACK_EMAIL_OPENS: 0
      SENDGRID_TRACK_CLICKS_HTML: 0
      SENDGRID_TRACK_CLICKS_PLAIN: 0
      EMAIL_FROM: <EMAIL>
      ORDER_EMAIL_BCC: <EMAIL>
      WEB_CONCURRENCY: 2
      FORCE_SCRIPT_NAME: /api
      ENABLE_EPSILON_PAYMENT: 1
      EPSILON_REGISTER_PAYMENT_URL: https://beta.epsilon.jp/cgi-bin/order/direct_card_payment.cgi
    configs:
      - source: stg-jwt-key
        target: /usr/src/app/jwt-rsa
        mode: 0440
    volumes:
      - type: bind
        source: /opt/foc/uploads/stg
        target: /usr/src/app/media
    depends_on:
      - stg-db
    deploy:
      labels:
        traefik.enable: "true"
        traefik.http.services.stg-backend-service.loadbalancer.server.port: 8000
        traefik.http.middlewares.deployApi.stripPrefix.prefixes: /api
        traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
        traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
        traefik.http.routers.stg-backend.rule: Host(`stg.foc.jpn.com`) && PathPrefix(`/api`)
        traefik.http.routers.stg-backend.entrypoints: web
        traefik.http.routers.stg-backend.middlewares: https_redirect,deployApi
        traefik.http.routers.stg-backend-tls.rule: Host(`stg.foc.jpn.com`) && PathPrefix(`/api`)
        traefik.http.routers.stg-backend-tls.entrypoints: websecure
        traefik.http.routers.stg-backend-tls.tls.certresolver: focresolver
        traefik.http.routers.stg-backend-tls.middlewares: deployApi
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s

  stg-frontend:
    image: 127.0.0.1:5000/foc-frontend
    environment:
      DAPHNE_HOST: stg-backend
      DAPHNE_PORT: 8000
      URLS: api|static|_ping
      TIMEOUT: 600
      DEBUG_LOG: "true"
      API_URL: /api
      CARD_TOKEN_URL: https://beta.epsilon.jp/js/token.js
    volumes:
      - type: bind
        source: /opt/foc/uploads/stg
        target: /usr/share/nginx/html/media
        read_only: true
    depends_on:
      - stg-backend
    deploy:
      labels:
        traefik.enable: "true"
        traefik.http.services.stg-frontend-service.loadbalancer.server.port: 80
        traefik.http.middlewares.test-auth.basicauth.users: msi:$$apr1$$Bl.RBMJo$$GDShnvFEncoZU5mMB054d/
        traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
        traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
        traefik.http.routers.stg-frontend.rule: Host(`stg.foc.jpn.com`)
        traefik.http.routers.stg-frontend.entrypoints: web
        traefik.http.routers.stg-frontend.middlewares: https_redirect, test-auth
        traefik.http.routers.stg-frontend-tls.rule: Host(`stg.foc.jpn.com`)
        traefik.http.routers.stg-frontend-tls.entrypoints: websecure
        traefik.http.routers.stg-frontend-tls.tls.certresolver: focresolver
        traefik.http.routers.stg-frontend-tls.middlewares: test-auth
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s

  prd-db:
    image: postgres:12
    ports:
      - 6543:5432
    volumes:
      - type: bind
        source: /var/lib/prd-postgresql/data
        target: /var/lib/postgresql/data
    environment:
      POSTGRES_USER: foc
      POSTGRES_PASSWORD: LDarcJE3MMooMPQEZKFTsL8R
    deploy:
      labels:
        traefik.enable: "false"
      replicas: 1
      update_config:
        parallelism: 1

  prd-backend:
    image: 127.0.0.1:5000/foc-backend:1.1.0
    environment:
      DATABASE_URL: *****************************************************/foc
      SECRET_KEY: p^j@6!olnn2nqn&iat(@2)fnn=_!8@k7h-(pcct@wp65#80_o)
      DEBUG: 0
      ALLOWED_HOSTS: "*"
      CORS_ORIGIN_ALLOW_ALL: 1
      FRONTEND_HOSTNAME: https://www.foc.jpn.com/
      JWT_EXPIRATION_MINUTES: 5
      JWT_REFRESH_EXPIRATION_DAYS: 1
      JWT_ISSUER: Focus inc.
      JWT_AUDIENCE: FOC dev team
      JWT_PRIVATE_KEY_FILE: /usr/src/app/jwt-rsa
      ROOT_PATH: /api
      STATIC_ROOT: /usr/src/app/static
      MEDIA_ROOT: /usr/src/app/media
      MEDIA_URL: /media/
      USE_TLS: 1
      EMAIL_VIA_SENDGRID: 1
      SENDGRID_API_KEY: *********************************************************************
      SENDGRID_SANDBOX_MODE_IN_DEBUG: 0
      SENDGRID_TRACK_EMAIL_OPENS: 0
      SENDGRID_TRACK_CLICKS_HTML: 0
      SENDGRID_TRACK_CLICKS_PLAIN: 0
      EMAIL_FROM: <EMAIL>
      ORDER_EMAIL_BCC:
      WEB_CONCURRENCY: 2
      FORCE_SCRIPT_NAME: /api
      ENABLE_EPSILON_PAYMENT: 1
      EPSILON_REGISTER_PAYMENT_URL: https://secure.epsilon.jp/cgi-bin/order/direct_card_payment.cgi
    configs:
      - source: prd-jwt-key
        target: /usr/src/app/jwt-rsa
        mode: 0440
    volumes:
      - type: bind
        source: /opt/foc/uploads/prd
        target: /usr/src/app/media
    depends_on:
      - prd-db
    deploy:
      labels:
        traefik.enable: "true"
        traefik.http.services.prd-backend-service.loadbalancer.server.port: 8000
        traefik.http.middlewares.deployApi.stripPrefix.prefixes: /api
        traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
        traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
        traefik.http.routers.prd-backend.rule: (Host(`www.foc.jpn.com`) || Host(`foc.jpn.com`)) && PathPrefix(`/api`)
        traefik.http.routers.prd-backend.entrypoints: web
        traefik.http.routers.prd-backend.middlewares: https_redirect,deployApi
        traefik.http.routers.prd-backend-tls.tls: "true"
        traefik.http.routers.prd-backend-tls.rule: (Host(`www.foc.jpn.com`) || Host(`foc.jpn.com`)) && PathPrefix(`/api`)
        traefik.http.routers.prd-backend-tls.entrypoints: websecure
        traefik.http.routers.prd-backend-tls.middlewares: deployApi
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s

  prd-frontend:
    image: 127.0.0.1:5000/foc-frontend:1.1.0
    environment:
      DAPHNE_HOST: prd-backend
      DAPHNE_PORT: 8000
      URLS: api|static|_ping
      TIMEOUT: 600
    configs:
      - source: prd-nginx-config
        target: /etc/nginx/conf.d/default.conf.template
        mode: 0740
    volumes:
      - type: bind
        source: /opt/foc/uploads/prd
        target: /usr/share/nginx/html/media
        read_only: true
    command: /bin/sh -c "envsubst '$$DAPHNE_HOST$$DAPHNE_PORT$$URLS$$TIMEOUT' < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    depends_on:
      - prd-backend
    deploy:
      labels:
        traefik.enable: "true"
        traefik.http.services.prd-frontend-service.loadbalancer.server.port: 80
        traefik.http.middlewares.https_redirect.redirectscheme.scheme: https
        traefik.http.middlewares.https_redirect.redirectscheme.permanent: "true"
        traefik.http.routers.prd-frontend.rule: Host(`www.foc.jpn.com`) || Host(`foc.jpn.com`)
        traefik.http.routers.prd-frontend.entrypoints: web
        traefik.http.routers.prd-frontend.middlewares: https_redirect
        traefik.http.routers.prd-frontend-tls.tls: "true"
        traefik.http.routers.prd-frontend-tls.rule: Host(`www.foc.jpn.com`) || Host(`foc.jpn.com`)
        traefik.http.routers.prd-frontend-tls.entrypoints: websecure
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s

volumes:
  traefik-acme:

configs:
  traefik-config:
    file: ./config/traefik/dynamic.yml
  dev-jwt-key:
    file: ./config/dev/jwt-rsa
  stg-jwt-key:
    file: ./config/stg/jwt-rsa
  prd-nginx-config:
    file: ./config/prd/nginx.conf
  prd-jwt-key:
    file: ./config/prd/jwt-rsa
