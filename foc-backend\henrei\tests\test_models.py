from django.test import TestCase
from django.utils import timezone

from henrei.models import OrderHenreihin
from henrei.tests.factories import (
    HenreihinKodenFactory,
    HenreihinKumotsuFactory,
    OrderHenreihinFactory,
)
from staffs.models import Staff
from staffs.tests.factories import StaffFactory


class OrderHenreihinModelTest(TestCase):
    def setUp(self) -> None:
        super().setUp()

        self.order_henrei: OrderHenreihin = OrderHenreihinFactory(
            order_status=0, order_ts=None, order_staff=None
        )
        HenreihinKodenFactory(henrei_order=self.order_henrei, order_status=0)
        HenreihinKodenFactory(henrei_order=self.order_henrei, order_status=0)
        HenreihinKumotsuFactory(henrei_order=self.order_henrei, order_status=0)
        HenreihinKumotsuFactory(henrei_order=self.order_henrei, order_status=0)
        HenreihinKumotsuFactory(henrei_order=self.order_henrei, order_status=0)

    def test_set_order_status(self) -> None:
        """返礼品発注を発注済みに更新する際には発注日時なども含め、香典、供花供物も同様に更新する"""
        staff: Staff = StaffFactory()

        self.order_henrei.set_order_status(1, staff)
        self.order_henrei.refresh_from_db()
        self.assertEqual(self.order_henrei.order_status, 1)
        self.assertIsNone(self.order_henrei.order_ts)
        self.assertIsNone(self.order_henrei.order_staff, staff)
        for koden in self.order_henrei.henrei_koden.all():
            self.assertEqual(koden.order_status, 1)
        for kumotsu in self.order_henrei.henrei_kumotsu.all():
            self.assertEqual(kumotsu.order_status, 1)

        # 発注済みにするとorder_ts、order_staffも更新
        self.order_henrei.set_order_status(2, staff)
        self.order_henrei.refresh_from_db()
        self.assertEqual(self.order_henrei.order_status, 2)
        self.assertIsNotNone(self.order_henrei.order_ts)
        self.assertEqual(self.order_henrei.order_staff, staff)
        for koden in self.order_henrei.henrei_koden.all():
            self.assertEqual(koden.order_status, 2)
        for kumotsu in self.order_henrei.henrei_kumotsu.all():
            self.assertEqual(kumotsu.order_status, 2)

        # 発注済みにしてもorder_tsが既に入っている場合は更新しない
        order_ts: timezone.datetime = self.order_henrei.order_ts
        order_staff: Staff = self.order_henrei.order_staff
        self.order_henrei.order_status = 3
        self.order_henrei.save()

        another_staff: Staff = StaffFactory()
        self.order_henrei.set_order_status(2, another_staff)
        self.order_henrei.refresh_from_db()
        self.assertEqual(self.order_henrei.order_status, 2)
        self.assertEqual(self.order_henrei.order_ts, order_ts)
        self.assertEqual(self.order_henrei.order_staff, order_staff)
