from datetime import date
from typing import Dict

from django.utils.translation import gettext_lazy as _
from rest_framework import exceptions, serializers

from invoices.models import SalesCompany, SalesDetail, SalesIndex, SalesSekoDepartment


class GatherSumSalesSerializer(serializers.Serializer):
    sales_yymm = serializers.CharField(min_length=6, max_length=6)

    def validate_sales_yymm(self, value: str) -> str:
        try:
            date(int(value[0:4]), int(value[4:6]), 1)
        except (TypeError, ValueError):
            raise exceptions.ValidationError(_('Cannot parse as format "YYYYMM"'))

        return value


class SalesDetailRelatedSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesDetail
        exclude = ['sales_seko_department']


class SalesSekoDepartmentRelatedSerializer(serializers.ModelSerializer):
    sales_details = SalesDetailRelatedSerializer(many=True)

    class Meta:
        model = SalesSekoDepartment
        exclude = ['sales_company']


class SalesIndexSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesIndex
        fields = '__all__'


class SalesCompanyRelatedSerializer(serializers.ModelSerializer):
    sales_index = SalesIndexSerializer()
    sales_seko_departments = SalesSekoDepartmentRelatedSerializer(many=True)

    class Meta:
        model = SalesCompany
        fields = '__all__'


class FixSalesCompanySerializer(serializers.Serializer):
    def update(self, instance: SalesCompany, validated_data: Dict) -> SalesCompany:
        try:
            instance.fix_invoice(staff=self.context['staff'])
        except ValueError as err:
            raise exceptions.ValidationError({'detail': err})
        return instance


class CancelFixSalesCompanySerializer(serializers.Serializer):
    def update(self, instance: SalesCompany, validated_data: Dict) -> SalesCompany:
        try:
            instance.cancel_fix_invoice()
        except ValueError as err:
            raise exceptions.ValidationError({'detail': err})
        return instance
