from typing import Dict, List

from django.forms.models import model_to_dict
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from hoyo.models import Base, Hoyo
from hoyo.tests.factories import HoyoFactory
from masters.tests.factories import HoyoStyleFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class HoyoListViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.hoyo_3 = HoyoFactory(
            company=BaseFactory(base_type=Base.OrgType.COMPANY), display_num=2
        )
        self.hoyo_1 = HoyoFactory(company=self.base_1, display_num=3)
        self.hoyo_2 = HoyoFactory(company=self.base_1, display_num=1)
        self.hoyo_4 = HoyoFactory(company=self.base_1, display_num=1)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_hoyo_list_succeed(self) -> None:
        """法要一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(reverse('hoyo:hoyo_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 4)
        # 順番確認 (hoyo_2 > hoyo_4 > hoyo_1 > hoyo_3)
        self.assertEqual(records[0]['id'], self.hoyo_2.pk)
        self.assertEqual(records[1]['id'], self.hoyo_4.pk)
        self.assertEqual(records[2]['id'], self.hoyo_1.pk)
        self.assertEqual(records[3]['id'], self.hoyo_3.pk)

    def test_hoyo_list_succeed_by_company(self) -> None:
        """拠点で絞り込みした法要一覧を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company': self.base_1.pk,
        }
        response = self.api_client.get(reverse('hoyo:hoyo_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)
        for record in records:
            self.assertEqual(record['company'], self.base_1.pk)

    def test_hoyo_list_succeed_by_company_2(self) -> None:
        """拠点で絞り込みした法要一覧を返す(項目確認)"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {
            'company': self.hoyo_3.company.pk,
        }
        response = self.api_client.get(reverse('hoyo:hoyo_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 1)
        record = records[0]
        self.assertEqual(record['id'], self.hoyo_3.pk)
        self.assertEqual(record['company'], self.hoyo_3.company.pk)
        self.assertEqual(record['style']['id'], self.hoyo_3.style.pk)
        self.assertEqual(record['style']['name'], self.hoyo_3.style.name)
        self.assertEqual(record['name'], self.hoyo_3.name)
        self.assertEqual(record['elapsed_time'], self.hoyo_3.elapsed_time)
        self.assertEqual(record['unit'], self.hoyo_3.unit)
        self.assertEqual(record['display_num'], self.hoyo_3.display_num)
        self.assertEqual(record['del_flg'], self.hoyo_3.del_flg)
        self.assertEqual(record['created_at'], self.hoyo_3.created_at.astimezone(tz).isoformat())
        self.assertEqual(record['create_user_id'], self.hoyo_3.create_user_id)
        self.assertEqual(record['updated_at'], self.hoyo_3.updated_at.astimezone(tz).isoformat())
        self.assertEqual(record['update_user_id'], self.hoyo_3.update_user_id)

    def test_hoyo_list_ignores_deleted(self) -> None:
        """法要一覧APIは無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.hoyo_1.disable()

        params: Dict = {}
        response = self.api_client.get(reverse('hoyo:hoyo_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        records: List[Dict] = response.json()
        self.assertEqual(len(records), 3)

    def test_hoyo_list_failed_without_auth(self) -> None:
        """法要一覧APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(reverse('hoyo:hoyo_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class HoyoCreateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.hoyo_data: Hoyo = HoyoFactory.build(company=self.base, style=HoyoStyleFactory())

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.base.pk,
            'style': self.hoyo_data.style.pk,
            'name': self.hoyo_data.name,
            'elapsed_time': self.hoyo_data.elapsed_time,
            'unit': self.hoyo_data.unit,
            'display_num': self.hoyo_data.display_num,
        }

    def test_hoyo_create_succeed(self) -> None:
        """法要を追加する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(reverse('hoyo:hoyo_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        record: Dict = response.json()
        self.assertIsNotNone(record['id'])
        self.assertEqual(record['company'], self.base.pk)
        self.assertEqual(record['style']['id'], self.hoyo_data.style.pk)
        self.assertEqual(record['style']['name'], self.hoyo_data.style.name)
        self.assertEqual(record['name'], self.hoyo_data.name)
        self.assertEqual(record['elapsed_time'], self.hoyo_data.elapsed_time)
        self.assertEqual(record['unit'], self.hoyo_data.unit)
        self.assertEqual(record['display_num'], self.hoyo_data.display_num)
        self.assertEqual(record['del_flg'], self.hoyo_data.del_flg)
        self.assertIsNotNone(record['created_at'])
        self.assertEqual(record['create_user_id'], self.staff.pk)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_hoyo_create_failed_without_auth(self) -> None:
        """法要追加APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = model_to_dict(self.hoyo_data)
        response = self.api_client.post(reverse('hoyo:hoyo_list'), data=params, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
