
<div class="container">
  <div class="inner">
    <div class="contents header">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="navigation">
      <div class="item"><span>御香典</span><span>金額入力</span></div>
      <div class="arrow"></div>
      <div class="item current"><span>返礼品</span><span>選択</span></div>
      <div class="arrow"></div>
      <div class="item"><span>カート</span><span>内容確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>注文</span><span>手続き</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込み</span><span>完了</span></div>
    </div>
    <div class="contents">
      <h2>ご香典のお返し</h2>
      <div class="input-area">
        <div class="line">
          <div class="input item" #itemEm [class.error]="isErrorField(itemEm)">
            <a class="button grey" (click)="showItemList()">ご香典のお返しの選択</a>
            <div class="item-box" *ngIf="selected_item">
              <div class="title-box">
                <div class="selecting">選択中</div>
                <div class="name">{{selected_item.name}}</div>
              </div>
              <div class="image-box" [class.no-image]="!selected_item.image_file">
                <img [src]="selected_item.image_file" *ngIf="selected_item.image_file" (error)="imageLoadError(selected_item)">
                <ng-container *ngIf="!selected_item.image_file">
                  <div class="no-image">
                    <i class="image icon huge"></i>
                    <div class="noimage">No image</div>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
        <div class="line">
          <div class="input">
            <input type="checkbox" [(ngModel)]="koden_edit.koden.henreihin_fuyo_flg" (click)="checkChange()">返礼品辞退
          </div>
        </div>
      </div>
    </div>

    <div class="ui modal item" id="item-list">
      <div class="content scrolling">
        <ng-container *ngFor="let item of item_list">
          <div class="item-box" (click)="selectItem(item)">
            <div class="title-box">
              <div class="name">{{item.name}}</div>
            </div>
            <div class="image-box" [class.selected]="item.selected" [class.no-image]="!item.image_file">
              <img [src]="item.image_file" *ngIf="item.image_file" (error)="imageLoadError(item)">
              <ng-container *ngIf="!item.image_file">
                <div class="no-image">
                  <i class="image icon huge"></i>
                  <div class="noimage">No image</div>
                </div>
              </ng-container>

            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <div class="button-area">
      <a class="button" (click)="back()">< 戻る</a>
      <a class="button grey" (click)="saveData()">次へ  ></a>
    </div>
  </div>
</div>

