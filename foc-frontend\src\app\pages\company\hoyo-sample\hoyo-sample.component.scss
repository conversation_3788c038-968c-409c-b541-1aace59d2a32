@import "src/assets/scss/company/setting";
.container {
  .inner {
    .contents {
      .ui.pagination.menu {
        margin-top: 10px;
      }
      .table_fixed.body {
        max-height: calc(100% - 250px);
      }
      tr {
        .display_num {
          width: 80px;
          text-align: center;
        }
        .sentence {
          white-space: pre;
        }
        .operation {
          width: 60px;
        }
      }
    }
    .ui.input textarea {
      height: 150px !important;
    }
    .ui.modal.hoyo-sample .ui.segment{
      cursor: pointer;
      display: flex;
      align-items: center;
      &:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,.3);
        --wekit-box-shadow: 0 5px 20px rgba(0,0,0,.3);
        -webkit-transform: translateY(-1px);
        transform: translateY(-1px);
      }
      pre {
        white-space: pre-wrap;
      }
      .ui.checkbox {
        margin-right: 10px;
      }
      &.selected {
        background-color: $label-border-light2;
      }

    }
  }
}
