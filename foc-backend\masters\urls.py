from django.urls import path

from masters import views

app_name = 'masters'
urlpatterns = [
    path('schedules/', views.ScheduleFullListView.as_view(), name='list_all_schedules'),
    path('seko_styles/', views.SekoStyleFullListView.as_view(), name='list_all_seko_styles'),
    path('zipcode/', views.ZipCodeQueryView.as_view(), name='query_zipcode'),
    path('wareki/', views.WarekiListView.as_view(), name='list_wareki'),
    path(
        'fuho_sample/',
        views.FuhoSampleMasterListView.as_view(),
        name='list_fuho_sample_master',
    ),
    path(
        'chobun_daishi/',
        views.ChobunDaishiMasterListView.as_view(),
        name='list_chobun_daishi_master',
    ),
    path(
        'chobun_sample/',
        views.ChobunSampleListView.as_view(),
        name='list_chobun_sample',
    ),
    path('services/', views.ServiceListView.as_view(), name='list_service'),
    path('taxes/', views.TaxListView.as_view(), name='list_tax'),
    path('relationship/', views.RelationshipListView.as_view(), name='list_relationship'),
    path('henrei_params/', views.HenreihinParameterDetail.as_view(), name='henrei_params'),
    path('koden_params/', views.KodenParameterDetail.as_view(), name='koden_params'),
    path('hoyo_style/', views.HoyoStyleList.as_view(), name='hoyo_style_list'),
    path('hoyo_default/', views.HoyoDefaultMasterList.as_view(), name='hoyo_default_master_list'),
    path('hoyo_sample/', views.HoyoSampleMasterList.as_view(), name='hoyo_sample_master_list'),
    path('soke_menu/', views.SokeMenuListView.as_view(), name='list_soke_menu'),
]
