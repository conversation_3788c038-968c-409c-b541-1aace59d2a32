# Generated by Django 3.1.2 on 2020-11-06 06:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('items', '0003_auto_20201104_1540'),
        ('masters', '0008_relationship'),
        ('seko', '0008_delete_fuhosample'),
    ]

    operations = [
        migrations.CreateModel(
            name='HenreihinKakegami',
            fields=[
                (
                    'seko',
                    models.OneToOneField(
                        db_column='id',
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name='henrei_kakegami',
                        serialize=False,
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
                ('omotegaki', models.TextField(blank=True, null=True, verbose_name='omotegaki')),
                ('mizuhiki', models.TextField(blank=True, null=True, verbose_name='mizu<PERSON>ki')),
                (
                    'package_type_name',
                    models.TextField(blank=True, null=True, verbose_name='package type'),
                ),
                (
                    'okurinushi_name',
                    models.TextField(blank=True, null=True, verbose_name='sender name'),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'db_table': 'tr_henreihin_kakegami',
            },
        ),
        migrations.CreateModel(
            name='SekoService',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('limit_ts', models.DateTimeField(verbose_name='reception ends at')),
                (
                    'henreihin_select_flg',
                    models.BooleanField(verbose_name='selectable as henreihin'),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'hall_service',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='masters.service',
                        verbose_name='hall service',
                    ),
                ),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='services',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_seko_service',
            },
        ),
        migrations.CreateModel(
            name='SekoItem',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                (
                    'item',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='items.item',
                        verbose_name='item',
                    ),
                ),
                (
                    'seko',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='items',
                        to='seko.seko',
                        verbose_name='seko',
                    ),
                ),
            ],
            options={
                'db_table': 'tr_seko_item',
            },
        ),
    ]
