
body {
  font-family: "Noto Serif CJK JP", serif;
  font-size: 12px;
  color: #333;
  margin: 0;
  line-height: 1.6;
}
@page {
  @top-right {
    font-size: 12px;
    content: counter(page) "/" counter(pages);
  }
}
@media print {
  .break-after {
    page-break-after: always;
  }
  .detail .page-header {
    position: fixed;
    right: 5px;
    top: 2px;
    font-size: 10px;
  }
}
@media screen {
  .detail .page-header {
    position: absolute;
    right: 5px;
    top: 2px;
    font-size: 10px;
  }
}
.page-header-space {
  height: 40px;
}
.top {
  font-size: 12px;
}
.top .title {
  text-align: center;
  font-size: 20px;
  margin-top: 10px
}
.top .page-header {
  position: absolute;
  right: 5px;
  top: 2px;
  font-size: 10px;
}
.top .target-company {
  margin-top: 10px;
  border: solid 1px #000000;
  padding: 5px;
  display: inline-block;
  min-width: 270px;
  max-width: 400px;
}
.top .target-company .name {
  font-size: 14px;
  margin-left: 50px;
}
.top .target-company .right {
  text-align: right;
}
.top .sender-company {
  position: absolute;
  right: 25px;
  top: 90px;
}
.top .sender-company .logo{
  width: 40px;
  height: auto;
  margin-right: 15px;
}
.top .sender-company .indent{
  margin-left: 15px;
}
.top .stamp {
  position: absolute;
  right: 0px;
  top: 90px;
  width: 90px;
  height: auto;
}
.top .description {
  margin-top: 70px;
  padding: 5px;
}
.top .total-price {
  position: absolute;
  right: 5px;
  top: 250px;
  font-size: 11px;
}
.top .total-price .header {
  width: 130px;
  border: solid 1px #000000;
  background-color: #eeeeee;
  text-align: center;
  margin-top: 5px;
  padding: 2px 5px;
}
.top .total-price .data {
  width: 130px;
  font-size: 14px;
  border: solid 1px #000000;
  border-top: 0;
  text-align: right;
  padding: 2px 5px;
}
.top .list {
  width: 98%;
  margin: 30px 5px 0;
  border-spacing: 0;
}
.top .list tr td {
  padding: 2px 3px;
}
.top .list tr.header td {
  border-top: solid 1px #000000;
  border-left: dotted 1px #000000;
  border-bottom: solid 1px #000000;
}
.top .list tr.header td:first-child {
  border-left-style: solid;
}
.top .list tr.header td:last-child {
  border-right: solid 1px #000000;
}
.top .list tr.header {
  text-align: center;
  background-color: #eeeeee;
}
.top .list tr.data {
  text-align: center;
}
.top .list .date {
  width: 8%;
}
.top .list .number {
  width: 6%;
}
.top .list .quantity {
  width: 8%;
}
.top .list .quantity1 {
  width: 4%;
}
.top .list .price {
  width: 12%;
}
.top .list .amount {
  width: 20%;
}
.top .list tr.data td {
  border-left: dotted 1px #000000;
  border-bottom: dotted 1px #000000;
}
.top .list tr.data td:first-child {
  border-left-style: solid;
}
.top .list tr.data td:last-child {
  border-right: solid 1px #000000;
}
.top .list tr.data:last-child td {
  border-bottom-style: solid;
}
.top .list tr.data:nth-last-child(2) td {
  border-bottom-style: solid;
}
.top .list tr.data .subject {
  text-align: left;
}
.top .list tr.data .subject span{
  width: 100px;
  display: inline-block;
  text-align: right;
}
.top .list tr.data .indent {
  padding-left: 30px;
}
.top .list tr.data .price {
  text-align: right;
}
.top .list tr.data .amount {
  text-align: right;
}
.top .list tr.data .blank {
  border-left: 0;
  border-bottom: 0;
  text-align: left;
  font-size: 10px;
}
.top .list tr.data .summary {
  border-left-style: solid;
}
.top .bank-title {
  margin-top: 10px;
  padding: 5px;
}
.top .bank {
  margin-left: 20px;
}
.top .bank tr td {
  padding: 2px 3px;
  text-align: left;
}
.top .bank tr .name {
  width: 80px;
}
.top .bank tr .branch {
  width: 100px;
}
.top .bank tr .type {
  width: 40px;
}
.top .bank tr .number {
  width: 70px;
}
.detail {
  position: relative;
}
.detail .list {
  width: 98%;
  margin: 0px 5px 0;
  border-spacing: 0;
}
.detail .list tr td {
  padding: 2px 3px;
}
.detail .list tr.header td {
  border-top: solid 1px #000000;
  border-left: dotted 1px #000000;
  border-bottom: solid 1px #000000;
}
.detail .list tr.header td:first-child {
  border-left-style: solid;
}
.detail .list tr.header td:last-child {
  border-right: solid 1px #000000;
}
.detail .list tr.header {
  text-align: center;
  background-color: #eeeeee;
}
.detail .list tr.data td {
  border-left: dotted 1px #000000;
  border-bottom: dotted 1px #000000;
}
.detail .list tr.data td:first-child {
  border-left-style: solid;
}
.detail .list tr.data td:last-child {
  border-right: solid 1px #000000;
}
.detail .list tfoot tr td {
  border-left: solid 1px #000000;
  border-right: solid 1px #000000;
  border-bottom: solid 1px #000000;
  height: 20px;
}
.detail .list tr.data {
  text-align: left;
}
.detail .list .number {
  width: 10%;
}
.detail .list .quantity {
  width: 6%;
}
.detail .list .entry_name {
  width: 15%;
}
.detail .list .price {
  width: 10%;
}
.detail .list .amount {
  width: 10%;
}
.detail .list .mark {
  width: 15px;
}
.detail .list .center {
  text-align: center;
}
.detail .list .right {
  text-align: right;
}
.list .small-indent {
  padding-left: 15px !important;
}
.list .indent {
  padding-left: 20px !important;
}
