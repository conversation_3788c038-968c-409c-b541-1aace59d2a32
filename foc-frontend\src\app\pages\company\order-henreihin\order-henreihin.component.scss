
@import "src/assets/scss/company/setting";
.contents {
  min-width: 1130px!important;
  .search_area .ui.checkbox {
    padding: 8px 10px 0;
    label {
      cursor: pointer;
    }
  }
  >.table_fixed tr {
    line-height: 1;
    >.check {
      width: 50px;
      padding-left: 15px;
    }
    .seko_id {
      width: 6%;
    }
    .seko_date {
      width: 7%;
    }
    .soke_name {
      width: 7%;
    }
    .moshu_name {
      width: 7%;
    }
    .order_status {
      width: 6%;
    }
    .id {
      width: 6%;
    }
    .supplier {
      width: 10%;
    }
    .order_ts {
      width: 10%;
    }
    .operation {
      width: 60px;
      &.company {
        width: 120px;
      }
      i {
        cursor: pointer;
      }
    }
  }
  .ui.modal {
    .search_area {
      margin-top: 0!important;
      .line {
        &.row4 {
          height: 100px;
          >.ui.input.textarea {
            line-height: 1.5;
            padding: 0;
            max-width: 100%;
            width: 100%;
            height: 96px;
            textarea {
              width: 100%;
              height: 96px;
              resize: none;
            }
          }
        }
        >label {
          min-width: 120px!important;
          &.textarea {
            height: 100%;
          }
        }
        >div {
          padding: 9px;
          background-color: #ffffff;
          line-height: 1;
          font-size: 1em;
          min-width: 150px;
          width: 100%;
        }
      }
    }
    tr {
      .henreihin_price {
        width: 25%;
      }
    }
    .table_fixed.body {
      max-height: 300px!important;
      tr {
        cursor: default;
        &:hover {
          background: $table-row-light;
        }
      }
    }
    &.fax-pdf {
      @page {
        size: A4
      }
      position: fixed !important;
      top: 10px !important;
      height: calc(100vh - 30px);
      .content {
        height: calc(100% - 127px);
        border: 0;
        box-shadow: none;
        margin: 0;
        padding: 5px;
      }
    }
  }
}
