@import "src/assets/scss/customer/setting";
.container .inner .contents {
  margin: 0px auto 50px;
  padding-bottom: 30px;
  h2 {
    margin: 50px auto 40px;
    @media screen and (max-width: 560px) {
      margin: 40px auto 35px;
    }
  }
  &.with-background-image {
    background: url(../../../../assets/img/customer/bg.png) no-repeat #ffffff;
    background-size: 40%;
    background-position: right bottom;
  }
  &.top {
    height: 100%;
    padding: 50px 10px;
    .hoyo_num {
      padding-left: 0;
      padding-right: 0;
      display: flex;
      justify-content: center;
      margin-bottom: 40px;
      div {
        font-size: 1.2rem;
        margin: auto 0;
      }
    }
    .hall-id {
      max-width: 100px;
    }
    .error_msg {
      font-size: 14px;
      color: $text-red;
      text-align: center;
      font-weight: bold;
    }
    @media screen and (max-width: 560px) {
      .hoyo_num div {
        font-size: 1.1rem;
      }
      .hall-id {
        max-width: 70px;
        height: 35px;
      }
      .hoyo-id {
        max-width: 110px;
        height: 35px;
      }
      input {
        font-size: 90%;
      }
      .error_msg {
        font-size: 12px;
      }
    }
    @media screen and (max-width: 380px) {
      .hoyo_num div {
        font-size: 1rem;
      }
      .hall-id {
        max-width: 60px;
        height: 30px;
      }
      .hoyo-id {
        max-width: 100px;
        height: 30px;
      }
      input {
        font-size: 80%;
      }
    }
  }
  td, th {
    vertical-align: top;
    padding: 0;
  }

  th {
    text-align: left;
    font-weight: bold;
  }

  li {
    list-style: none;
  }

  .box01_02 {
    max-width: 90%;
    margin: 0 auto;
    padding-bottom: 30px;
    display: inline-block;
    text-align: left;
    line-height: 35px;
    white-space: pre-wrap;
  }

  /*記*/
  .box02 table {
    margin: 0 auto;
    text-align: left;
    line-height: 35px;
    max-width: calc(100% - 40px);
    @media screen and (max-width: 560px) {
      line-height: 30px;
    }
  }

  .box02 th {
    white-space: nowrap;
  }

  .box02_01 th {
    padding-bottom: 0px;
    white-space: nowrap;
  }

  .box02 tr > td {
    padding-left: 30px;
    @media screen and (max-width: 560px) {
      padding-left: 18px;
    }
    @media screen and (max-width: 380px) {
      padding-left: 10px;
    }
  }

  .box02 td > div {
    margin-bottom: 10px;
    @media screen and (max-width: 560px) {
    margin-bottom: 0px;
    }
  }

  .space_bottom {
    padding-bottom: 25px;
  }
  .schedule-area {
    @media screen and (max-width: 560px) {
      padding-top: 10px;
    }
    .schedule {
      display: flex;
      >div >div {
        margin-left: 20px;
      }
      .schedule_name {
        min-width: 100px;
        @media screen and (max-width: 560px) {
          min-width: 0;
        }
        white-space: nowrap;
      }
      .schedule_date {
        margin-left: 0px;
        display: flex;
        @media screen and (max-width: 560px) {
          display: block;
        }
        .schedule_time {
          min-width: 120px;
          @media screen and (max-width: 560px) {
            min-width: 0;
          }
        }
      }
    }
  }


  .place {
    img {
      max-width: 500px;
      width: calc(100% - 20px);
      padding-right: 20px;
    }
  }

  .map a {
    width: 180px;
    margin-top: 8px;
  }

  .memorial .flexslider {
    margin: auto;
    max-width: 800px;
    margin-bottom: 5px;
    border: 0;
    background-color: #f3f3f3;
    line-height: 1.25;
    ul.slides {
      display: flex;
      >li {
        margin: auto 0;
      }
    }
    img {
      border-radius: 3px;
      max-width: 100%;
      // min-width: 100%;
      margin: auto;
      width: auto;
      height: auto;
      max-height: 500px;
      @media screen and (max-width: 560px) {
        max-height: 300px;
      }
    }
  }

  /*動画配信*/
  .box04 {
    width: 90%;
    margin: 0 auto;
    text-align: left;
    line-height: 35px;
  }

  .box04 > p{
    max-width: 620px;
    margin: 0 auto;
  }

  .box_04 td {
    padding-bottom: 5px;
  }

  .stream table {
    margin: 0 auto;
  }

  .stream table th {
    padding-right: 10px;
  }

  .archive {
    text-align: center;
    padding: 8px 0 20px;
  }

  .archive a {
    width: 165px;
    margin: 0 auto;
  }

  .archive .p_small {
    font-size: 0.8rem;
    margin: 0 auto 15px;
  }

}

:host ::ng-deep .flex-direction-nav a:before {
  font-size: 30px;
  padding-top: 10px;
}
