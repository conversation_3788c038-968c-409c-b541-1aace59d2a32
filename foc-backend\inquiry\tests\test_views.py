from typing import Dict

import faker
from django.core import mail
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

fake_provider = faker.Faker('ja_JP')
jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class InquiryMailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.recipient_address = fake_provider.ascii_safe_email()
        self.sender_name = fake_provider.name()
        self.sender_kana = fake_provider.kana_name()
        self.sender_tel = fake_provider.phone_number()
        self.sender_address = fake_provider.ascii_safe_email()
        self.mail_body = '\n'.join(fake_provider.paragraphs())

        self.api_client = APIClient()

    def test_inquiry_mail_succeed(self) -> None:
        """問い合わせ内容をメールで送信する"""
        params: Dict = {
            'recipient_address': self.recipient_address,
            'sender_name': self.sender_name,
            'sender_kana': self.sender_kana,
            'sender_tel': self.sender_tel,
            'sender_address': self.sender_address,
            'mail_body': self.mail_body,
        }
        response = self.api_client.post(
            reverse('inquiry:inquiry_mail'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(record, {})

        # メールが1件送信されている
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, 'FOCお問合せ')
        self.assertEqual(sent_mail.to, [self.recipient_address])

    def test_inquiry_mail_fail_by_less_params(self) -> None:
        """問い合わせ内容メール送信がパラメータ不足で失敗する"""
        params: Dict = {}
        response = self.api_client.post(
            reverse('inquiry:inquiry_mail'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record['recipient_address'])
        self.assertIsNotNone(record['sender_name'])
        self.assertIsNotNone(record['sender_kana'])
        self.assertIsNotNone(record['sender_address'])
        self.assertIsNotNone(record['mail_body'])
