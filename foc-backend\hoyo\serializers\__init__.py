from drf_extra_fields.relations import PresentablePrimary<PERSON>ey<PERSON><PERSON>tedField
from rest_framework import serializers

from bases.models import Base
from bases.serializers import BaseSerializer
from hoyo.models import Hoyo, HoyoMailTemplate, HoyoSample, HoyoSchedule, HoyoSeko
from masters.models import HoyoStyle
from masters.serializers import HoyoStyleSerializer
from staffs.models import Staff
from staffs.serializers import StaffSerializer
from utils.display_datetime import get_display_time
from utils.serializer_mixins import AddUserIdMixin


class HoyoSerializer(AddUserIdMixin, serializers.ModelSerializer):
    style = PresentablePrimaryKeyRelatedField(
        queryset=HoyoStyle.objects.all(),
        presentation_serializer=HoyoStyleSerializer,
        required=False,
    )
    create_user_id = serializers.IntegerField(required=False)
    update_user_id = serializers.IntegerField(required=False)

    class Meta:
        model = Hoyo
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']


class HoyoSampleSerializer(AddUserIdMixin, serializers.ModelSerializer):
    create_user_id = serializers.IntegerField(required=False)
    update_user_id = serializers.IntegerField(required=False)

    class Meta:
        model = HoyoSample
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']


class HoyoMailTemplateSerializer(AddUserIdMixin, serializers.ModelSerializer):
    create_user_id = serializers.IntegerField(required=False)
    update_user_id = serializers.IntegerField(required=False)

    class Meta:
        model = HoyoMailTemplate
        fields = '__all__'
        read_only_fields = ['del_flg', 'created_at', 'updated_at']


class HoyoScheduleByHoyoSekoSerializer(serializers.ModelSerializer):
    display_begin_time = serializers.SerializerMethodField()
    display_end_time = serializers.SerializerMethodField()
    hall = PresentablePrimaryKeyRelatedField(
        queryset=Base.objects.all(),
        presentation_serializer=BaseSerializer,
        required=False,
        allow_null=True,
    )

    class Meta:
        model = HoyoSchedule
        exclude = ['hoyo_seko']
        read_only_fields = ['created_at', 'updated_at']

    def get_display_begin_time(self, obj: HoyoSeko):
        return get_display_time(obj.begin_time)

    def get_display_end_time(self, obj: HoyoSeko):
        return get_display_time(obj.end_time)


class HoyoSekoSerializerBase(serializers.ModelSerializer):
    display_begin_time = serializers.SerializerMethodField()
    display_end_time = serializers.SerializerMethodField()
    hoyo = PresentablePrimaryKeyRelatedField(
        queryset=Hoyo.objects.all(), presentation_serializer=HoyoSerializer, required=False
    )
    hall = PresentablePrimaryKeyRelatedField(
        queryset=Base.objects.all(),
        presentation_serializer=BaseSerializer,
        required=False,
        allow_null=True,
    )
    staff = PresentablePrimaryKeyRelatedField(
        queryset=Staff.objects.all(), presentation_serializer=StaffSerializer, required=False
    )
    schedules = HoyoScheduleByHoyoSekoSerializer(many=True, required=False)

    class Meta:
        model = HoyoSeko
        read_only_fields = ['del_flg', 'created_at', 'updated_at']

    def get_display_begin_time(self, obj: HoyoSeko):
        return get_display_time(obj.begin_time)

    def get_display_end_time(self, obj: HoyoSeko):
        return get_display_time(obj.end_time)


class HoyoSekoBySekoSerializer(HoyoSekoSerializerBase):
    class Meta(HoyoSekoSerializerBase.Meta):
        exclude = ['seko']
