from base64 import b64encode
from io import Bytes<PERSON>
from typing import Dict, List

from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.test import TestCase
from django.urls import reverse
from faker import Faker
from PIL import Image
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from bases.tests.factories import BaseFactory
from items.models import Base
from items.tests.factories import ItemFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')


def fake_b64encoded_jpeg() -> str:
    img = Image.new('RGB', (100, 100), color=fake.safe_hex_color())
    buffer = BytesIO()
    img.save(buffer, format='jpeg')
    b64encoded_image = b64encode(buffer.getvalue())
    return f'data:image/jpeg;base64,{b64encoded_image.decode("ascii")}'


class ItemImportViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base_1 = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.item_1 = ItemFactory(base=self.base_1)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base_1)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> List[Dict]:
        params: List[Dict] = []
        for idx in range(3):
            params.append(
                {
                    'filename': f"{idx}-{fake.file_name(extension='jpg')}",
                    'file_content': fake_b64encoded_jpeg(),
                }
            )
        return params

    def test_item_import_image_succeed(self) -> None:
        """商品画像をまとめて登録する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: List[Dict] = self.basic_params()

        response = self.api_client.post(
            reverse('items:import_image', kwargs={'base_id': self.base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: List[Dict] = response.json()
        self.assertEqual(record, {})

        # /media/items/<拠点ID>/<ファイル名> にファイルが保存される
        for image_param in params:
            self.assertTrue(
                default_storage.exists(f"items/{self.base_1.pk}/{image_param['filename']}")
            )

    def test_item_import_image_overwrite(self) -> None:
        """商品画像インポートAPIは既存のファイルを上書きする"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: List[Dict] = self.basic_params()
        for item_param in params:
            emptyfile_path = f"items/{self.base_1.pk}/{item_param['filename']}"
            default_storage.save(emptyfile_path, ContentFile(b''))
            self.assertEqual(default_storage.size(emptyfile_path), 0)

        response = self.api_client.post(
            reverse('items:import_image', kwargs={'base_id': self.base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        record: List[Dict] = response.json()
        self.assertEqual(record, {})

        # /media/items/<拠点ID>/<ファイル名> にファイルが保存される
        for image_param in params:
            filepath = f"items/{self.base_1.pk}/{image_param['filename']}"
            self.assertTrue(default_storage.exists(filepath))
            self.assertGreater(default_storage.size(filepath), 0)

    def test_item_import_image_failed_by_less_params(self) -> None:
        """商品画像インポートAPIがパラメータ不足で失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: List[Dict] = self.basic_params()
        params[1].pop('filename')

        response = self.api_client.post(
            reverse('items:import_image', kwargs={'base_id': self.base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        params: List[Dict] = self.basic_params()
        params[2].pop('file_content')

        response = self.api_client.post(
            reverse('items:import_image', kwargs={'base_id': self.base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_item_import_image_failed_without_auth(self) -> None:
        """商品画像インポートAPIがAuthorizationヘッダがなくて失敗する"""
        params: List[Dict] = []
        response = self.api_client.post(
            reverse('items:import_image', kwargs={'base_id': self.base_1.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
