<!DOCTYPE html>
<html>
<title>弔文</title>
<head>
<style type="text/css">
@page {
  @top-right {
    content: counter(page) "/" counter(pages);
  }
}
body {
  font-family: "Noto Serif CJK JP", serif;
  font-size: 12px;
  color: #333;
  margin: 0;
}
.content {
  position: relative;
}
.content table {
  border-collapse: collapse;
  width: 100%;
}
.content td {
  padding: 7px;
  border: 1px solid rgba(0,0,0,0.4);
  vertical-align: top;
}
.content table tr:nth-child(1) {
  background-color:rgba(0,0,0,0.05);
  text-align: center;
}
.content table tr td:nth-child(1) {
  width: 50px;
  text-align: center;
  vertical-align: middle;
}
.content table tr td:nth-child(2) {
  vertical-align: middle;
}
</style>
</head>
<body>
<section class="content" style="clear: both">
  <div>
    <table>
      <tr>
        <td>No</td>
        <td>ご芳名</td>
        <td>摘要</td>
      </tr>
      {% for entry in seko.entries.all %}
      {% for detail in entry.details.all %}
      <tr>
        <td>{{ counter }}</td>
        <td>
          {{ entry.entry_name }}<br>
          {{ entry.entry_name_kana }}
        </td>
        <td>{{ detail.item_name }}</td>
      </tr>
      {% endfor %}
      {% endfor %}
    </table>
  </div>
</section>
</body>
</html>
