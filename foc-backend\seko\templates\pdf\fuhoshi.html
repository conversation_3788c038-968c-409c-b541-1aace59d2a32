{% load fuhoshi_filters %}
{% load static %}
<html>
  <head></head>
  <body>
    <div class="frame">
    <div class="content">
      <h1 class="header">訃        報</h1>
      <div class="kojin_area">
        {% for kojin in seko.kojin.all %}
        <div class="kojin">
          <div class="label">故</div>
          <div class="relation">{{ kojin.moshu_kojin_relationship }}</div>
          <div class="name">{{ kojin.name }}</div>
          <div class="label">儀</div>
          <div class="age_kbn">{{ kojin.age_kbn }}</div>
          <div class="label">{{ kojin.age }}</div>
          <div class="label">歳</div>
        </div>
        {% if seko.seko_style.wareki_use_flg %}
        <div class="death">{{ kojin.death_date|wareki_nen }} 年 {{ kojin.death_date|date:"n 月 j 日" }} 逝去いたしました</div>
        {% else %}
        <div class="death">{{ kojin.death_date|date:"Y 年 n 月 j 日" }} 逝去いたしました</div>
        {% endif %}
        {% endfor %}
      </div>
      <div class="sentence">{{ seko.fuho_sentence }}</div>

      <div class="schedule_area">
        {% for schedule in seko.schedules.all %}
        <div class="schedule">
          <div class="name">{{ schedule.schedule_name }}</div>
          <div  class="date">
            <div class="month">{{ schedule.schedule_date|date:"n月" }}</div>
            <div class="day">{{ schedule.schedule_date|date:"j日" }}</div>
            <div class="week">{{ schedule.schedule_date|date:"（D）" }}</div>
            <div class="time">{{ schedule.begin_time|date:"G時 i分" }}</div>
            {% if schedule.end_time %}
            <div class="nami">～</div>
            <div class="time">{{ schedule.end_time|date:"G時 i分" }}</div>
            {% endif %}
          </div>
        </div>
        {% endfor %}
      </div>
      <div class="hall_area">
        <div class="label">式場</div>
        <div class="hall_info">
        {% for schedule in seko.schedules.all %}
          {% if schedule.schedule.required_flg %}
          <div class="line1">
            <div class="name">{{ schedule.hall_name }}</div>
            <div class="tel">TEL {% if schedule.hall_tel %}{{ schedule.hall_tel }}{% endif %}</div>
          </div>
          <div class="address">〒 {{ schedule.hall_zip_code|slice:"0:3" }}-{{ schedule.hall_zip_code|slice:"3:" }}  {{ schedule.hall_address }}</div>
          {% endif %}
        {% endfor %}
        </div>
      </div>
      <div class="style_area">
        <div class="style">形式           {{ seko.seko_style_name }}</div>
        <div class="moshu">喪主           {{ seko.moshu.name }}</div>
      </div>
      <div class="contact_area">
        <div class="label">お問合せ</div>
        <div class="name">{{ seko.fuho_contact_name }}</div>
        <div class="tel">TEL {{ seko.fuho_contact_tel }}</div>
      </div>
    </div>
    <div class="footer">
      <div class="title">ご葬儀専用Webサイトのご案内</div>
      <div class="smaho_area">
        <div class="label_area">
          <div class="label"><img src="{{ images.smaho }}">スマートフォンの方</div>
          <div class="desc">右のQRコードをカメラで読み込んでください。</div>
        </div>
        <div class="qrcode">
          <img src="{{ qr_image|b64encode_gif }}" alt="URL">
        </div>
      </div>
      <div class="pc_area">
        <div class="label"><img src="{{ images.pc }}">パソコンの方</div>
        <div class="address_area">
          <div class="address">{{ fuho_url }}
            <div class="fuho-num">
              <div class="borderL">ご葬儀番号</div><div class="borderR">{{ seko.seko_company_id }}-{{ seko.id }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </body>
</html>
