
<div class="container">
  <div class="inner">
    <div class="contents header">
      <span class="label">ご葬家名：</span><span class="name">{{seko_info.soke_name}}家</span>
    </div>
    <div class="navigation">
      <div class="item"><span>カート</span><span>内容確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込者</span><span>情報登録</span></div>
      <div class="arrow"></div>
      <div class="item current"><span>申込内容</span><span>確認</span></div>
      <div class="arrow"></div>
      <div class="item"><span>お支払い</span><span>手続き</span></div>
      <div class="arrow"></div>
      <div class="item"><span>申込み</span><span>完了</span></div>
    </div>
    <div class="contents">
      <h2>申込内容確認</h2>
      <div class="data_area">
        <div class="title">お申込み対象ご葬儀</div>
        <table class="normal">
          <tbody>
            <tr>
              <td class="label">故人名</td>
              <td class="data">{{kojinName()}}</td>
            </tr>
            <tr>
              <td class="label">喪主名</td>
              <td class="data">{{seko_info?.moshu?.name}}</td>
            </tr>
            <tr>
              <td class="label">ご葬儀日</td>
              <td class="data">{{seko_info?.seko_date|date:'yyyy年M月d日'}}</td>
            </tr>
            <tr>
              <td class="label">式場</td>
              <td class="data">{{hallName()}}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="data_area">
        <div class="title">お申込み者　<a class="button grey light" (click)="back()">変更</a></div>
        <table class="normal">
          <tbody>
            <tr>
              <td class="label">氏名</td>
              <td class="data">{{entry?.entry_name}}</td>
            </tr>
            <tr>
              <td class="label">住所</td>
              <td class="data">{{zipCode(entry.entry_zip_code)}}</td>
            </tr>
            <tr>
              <td class="label"></td>
              <td class="data">{{entry?.entry_prefecture}}</td>
            </tr>
            <tr>
              <td class="label"></td>
              <td class="data">{{entry?.entry_address_1}}</td>
            </tr>
            <tr>
              <td class="label"></td>
              <td class="data">{{entry?.entry_address_2}}</td>
            </tr>
            <tr *ngIf="entry?.entry_address_3">
              <td class="label"></td>
              <td class="data">{{entry?.entry_address_3}}</td>
            </tr>
            <tr>
              <td class="label">TEL</td>
              <td class="data">{{entry?.entry_tel}}</td>
            </tr>
            <tr>
              <td class="label">メール</td>
              <td class="data">{{entry?.entry_mail_address}}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="data_area detail">
        <div class="title">お申込み内容</div>
        <table>
          <tbody>
            <ng-container *ngFor="let detail of entry.details">
            <tr class="pc">
              <ng-container *ngIf="detail.service.id!==Const.SERVICE_ID_KODEN && detail.service.id!==Const.SERVICE_ID_MESSAGE">
                <td class="name">{{detail.service.name}}<br>
                  <span class="item_name" [class.remark]="detail.keigen_flg">{{detail.item_name}}</span>
                </td>
                <td class="quantity">数量 {{detail.quantity}}</td>
                <td class="price">¥{{detail.item_unit_price * detail.quantity | number}}</td>
              </ng-container>
              <ng-container *ngIf="detail.service.id===Const.SERVICE_ID_KODEN">
                <td class="name" colspan="2">
                  御香典(非課税)<br><span class="system_name">システム利用料</span>
                </td>
                <td class="price">
                  ¥{{detail.item_unit_price * detail.quantity | number}}<br>
                  <span class="system_price">¥{{detail.koden.koden_commission | number}}</span>
                </td>
              </ng-container>
              <ng-container *ngIf="detail.service.id===Const.SERVICE_ID_MESSAGE">
                <td class="name" colspan="2">{{detail.service.name}}</td>
                <td class="price">¥{{detail.item_unit_price * detail.quantity | number}}</td>
              </ng-container>
            </tr>
            <tr class="divided">
              <td class="expired" colspan="4">
              </td>
            </tr>
            </ng-container>
          </tbody>
        </table>
        <table class="summary">
          <tbody>
            <tr *ngIf="sum_tax_8.price">
              <td class="summary_name remark">内税消費税（8%）対象額<br>内税消費税</td>
              <td class="price">¥{{sum_tax_8.price | number}}<br>¥{{sum_tax_8.tax | number}}</td>
            </tr>
            <tr *ngIf="sum_tax_10.price">
              <td class="summary_name">内税消費税（10%）対象額<br>内税消費税</td>
              <td class="price">¥{{sum_tax_10.price | number}}<br>¥{{sum_tax_10.tax | number}}</td>
            </tr>
            <tr *ngIf="sum_tax_0.price">
              <td class="summary_name">非課税対象額</td>
              <td class="price">¥{{sum_tax_0.price | number}}</td>
            </tr>
            <tr class="total">
              <td class="summary_name">合計金額（税込）</td>
              <td class="price">¥{{entry.billing_amount | number}}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="message-area" *ngIf="message">
        {{message}}
      </div>
    </div>
    <div class="button-area">
      <a class="button" (click)="back()">< 戻る</a>
      <a class="button grey" (click)="next()">次へ ></a>
    </div>
  </div>

</div>
