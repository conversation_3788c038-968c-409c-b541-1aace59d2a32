from dataclasses import asdict, dataclass
from typing import Any, ClassVar, Dict, Optional
from urllib.parse import unquote
from xml.etree import ElementTree

import requests
from django.conf import settings
from django.db.models import IntegerChoices
from django.utils.translation import gettext_lazy as _
from rest_framework.exceptions import ValidationError
from sequences import get_next_value

from seko.models import Seko


class ContractType(IntegerChoices):
    BASIC = 1, _('Basic')
    KODEN = 2, _('Koden')

    def contract_code(self, seko: Seko) -> str:
        if not hasattr(seko.seko_company, 'focfee'):
            raise ValueError(
                _(f'The base {seko.seko_company.base_name} does not have FOC fee record')
            )

        if self.value == self.KODEN:
            return seko.seko_company.focfee.gmo_code_koden
        return seko.seko_company.focfee.gmo_code


def left_in_bytes(string: str, limit_in_bytes: int, encoding: str = 'cp932') -> str:
    while len(string.encode(encoding)) > limit_in_bytes:
        string = string[:-1]
    return string


@dataclass
class RequestPayment:
    SEQUENCE_NAME: ClassVar[str] = 'epsilon_request_payment'
    contract_code: str
    token: str
    user_name: str
    user_mail_add: str
    item_price: int
    user_agent: str
    user_id: str = 'user0000'
    item_code: str = 'FOC'
    item_name: str = 'FOCサービス利用'
    st_code: str = '11000-0000-00000'
    mission_code: str = '1'
    process_code: str = '1'
    memo1: str = ''
    memo2: str = ''
    card_st_code: str = '10'
    pay_time: str = ''
    tds_check_code: str = '1'
    tds_flag: str = '22'
    keitai: str = '0'
    kari_flag: str = ''
    security_check: str = '1'
    order_number: Optional[int] = None

    def __post_init__(self) -> None:
        self.order_number = get_next_value(self.SEQUENCE_NAME)

    def as_post_data(self) -> Dict[str, Any]:
        instance_dict: Dict = asdict(self)
        cutoff: Dict[str, int] = {
            'user_name': 64,
            'item_name': 64,
            'memo1': 128,
            'memo2': 128,
        }
        for key, limit in cutoff.items():
            instance_dict[key] = left_in_bytes(instance_dict[key], limit)
        return instance_dict


@dataclass
class ResponsePayment:
    result_code: str
    order_number: int
    pareq: str
    tds2_url: str
    trans_code: str


def register_payment(
    contract_code: str,
    token: str,
    user_name: str,
    user_mail_address: str,
    price: int,
    user_agent: str,
    gmo_test_flg: bool = False,
    secure_chk_flg: bool = False,
) -> ResponsePayment:
    post_data = RequestPayment(
        contract_code=contract_code,
        token=token,
        user_name=user_name,
        user_mail_add=user_mail_address,
        item_price=price,
        user_agent=user_agent,
    ).as_post_data()
    url = (
        settings.EPSILON_REGISTER_PAYMENT_URL_TEST
        if gmo_test_flg
        else settings.EPSILON_REGISTER_PAYMENT_URL
    )
    if not secure_chk_flg:
        post_data.pop('tds_flag')

    response = requests.post(url, post_data)

    xml_text: str = unquote(response.text, encoding='cp932')
    xml_data = ElementTree.fromstring(xml_text)
    result_element = xml_data.find('./result[@result]')
    if result_element is None:
        raise LookupError('Illegal format XML data')

    result_code: Optional[str] = result_element.get('result')
    if result_code == '1':
        return ResponsePayment(
            result_code=xml_data.find('./result[@result]').get('result', ''),
            order_number=post_data['order_number'],
            pareq=None,
            tds2_url=None,
            trans_code=xml_data.find('./result[@trans_code]').get('trans_code', ''),
        )
    elif result_code == '6':
        return ResponsePayment(
            result_code=xml_data.find('./result[@result]').get('result', ''),
            order_number=post_data['order_number'],
            pareq=xml_data.find('./result[@pareq]').get('pareq', ''),
            tds2_url=xml_data.find('./result[@tds2_url]').get('tds2_url', ''),
            trans_code=xml_data.find('./result[@trans_code]').get('trans_code', ''),
        )
    else:
        err_code: str = xml_data.find('./result[@err_code]').get('err_code', '')
        err_detail: str = xml_data.find('./result[@err_detail]').get('err_detail', '')
        raise ValidationError(f'err_code={err_code}, err_detail={err_detail}')


@dataclass
class RequestPayment2:
    contract_code: str
    order_number: int
    tds_pares: str
    tds_check_code: str = '2'

    def as_post_data(self) -> Dict[str, Any]:
        instance_dict: Dict = asdict(self)
        return instance_dict


@dataclass
class ResponsePayment2:
    result_code: str
    trans_code: int
    order_number: int
    err_code: str
    err_detail: str


def register_payment2(
    contract_code: str,
    tds_pares: str,
    order_number: int,
    gmo_test_flg: bool = False,
) -> ResponsePayment2:
    post_data = RequestPayment2(
        contract_code=contract_code,
        tds_pares=tds_pares,
        order_number=order_number,
    ).as_post_data()
    url = (
        settings.EPSILON_REGISTER_PAYMENT_URL_TEST
        if gmo_test_flg
        else settings.EPSILON_REGISTER_PAYMENT_URL
    )

    response = requests.post(url, post_data)

    xml_text: str = unquote(response.text, encoding='cp932')
    xml_data = ElementTree.fromstring(xml_text)
    result_element = xml_data.find('./result[@result]')
    if result_element is None:
        raise LookupError('Illegal format XML data')

    result_code: Optional[str] = result_element.get('result')
    if result_code == '1':
        return ResponsePayment2(
            result_code=xml_data.find('./result[@result]').get('result', ''),
            order_number=post_data['order_number'],
            trans_code=xml_data.find('./result[@trans_code]').get('trans_code', ''),
            err_code=None,
            err_detail=None,
        )
    else:
        return ResponsePayment2(
            result_code=xml_data.find('./result[@result]').get('result', ''),
            order_number=post_data['order_number'],
            trans_code=xml_data.find('./result[@trans_code]').get('trans_code', ''),
            err_code=xml_data.find('./result[@err_code]').get('err_code', ''),
            err_detail=xml_data.find('./result[@err_detail]').get('err_detail', ''),
        )
