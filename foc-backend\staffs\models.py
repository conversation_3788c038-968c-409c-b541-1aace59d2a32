import secrets
from typing import Optional

from django.contrib.auth.base_user import <PERSON>bs<PERSON><PERSON><PERSON><PERSON><PERSON>, BaseUserManager
from django.contrib.auth.models import PermissionsMixin
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from bases.models import Base
from staffs import hash


class StaffManager(BaseUserManager):
    """ユーザの追加コマンドで使用します"""

    def create_superuser(self, base_id: int, login_id: str, password: str) -> 'Staff':
        """管理者アカウントを追加します

        Args:
            base_id (int): 拠点ID
            login_id (str): ログインID
            password (str): パスワード

        Returns:
            Staff: Staffインスタンス
        """
        base = Base.objects.get(pk=base_id)

        user = self.model(
            base=base,
            company_code=base.company_code,
            login_id=login_id,
            create_user_id=0,
            update_user_id=0,
        )
        user.set_password(password)
        user.save(using=self._db)
        return user


class Staff(AbstractBaseUser, PermissionsMixin):
    base = models.ForeignKey(
        Base, on_delete=models.DO_NOTHING, verbose_name=_('base company'), related_name='staffs'
    )
    company_code = models.CharField(_('company code'), max_length=20, blank=True, null=True)
    name = models.TextField(_('name'))
    login_id = models.CharField(_('login id'), max_length=20)
    password = models.TextField(_('password'))
    salt = models.CharField(_('salt'), max_length=128)
    mail_address = models.EmailField(blank=True, null=True)
    retired_flg = models.BooleanField(default=False)
    del_flg = models.BooleanField(default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))
    fdn_code = models.TextField(_('fdn code'), blank=True, null=True)

    objects = StaffManager()

    USERNAME_FIELD = 'login_id'
    REQUIRED_FIELDS = ['base_id']

    class Meta:
        db_table = 'm_staff'
        verbose_name = _('staff')
        verbose_name_plural = _('staffs')

    @property
    def is_active(self) -> bool:
        """アカウントが利用可能かを判定します

        Returns:
            bool: アカウントが利用可能か否か
        """
        return not self.del_flg and not self.retired_flg

    @property
    def is_staff(self) -> bool:
        """Django管理サイトにログインできるアカウントかを判定します

        Returns:
            bool: Django管理サイトにログインできるか否か
        """
        return self.base.base_type == Base.OrgType.ADMIN

    @property
    def is_superuser(self) -> bool:
        """Django管理者権限があるか判定します

        Returns:
            bool: Django管理者権限があるか否か
        """
        return self.base.base_type == Base.OrgType.ADMIN

    def set_password(self, raw_password: Optional[str]) -> None:
        """アカウントのパスワードを更新します

        親クラスと同様、インスタンスのパスワードを更新しますが自動的にcommitは
        行いません

        アカウントのSaltが設定されていなければ先に自動生成し、それを用いて
        パスワードをハッシュ化します

        Args:
            raw_password (str): 設定するパスワード(平文)
        """
        if raw_password is None:
            super().set_password(raw_password)
            return

        if not self.salt:
            self.salt = hash.generate_salt()
        self.password = hash.make_password(raw_password, self.salt)
        self._password = raw_password

    def check_password(self, raw_password: Optional[str]) -> bool:
        """アカウントのパスワードを照合します

        Args:
            raw_password (Optional[str]): パスワード(平文)

        Raises:
            ValueError: アカウントにSaltが設定されていないなどの状態異常

        Returns:
            bool: パスワード照合の成否
        """
        if raw_password is None:
            return super().check_password(raw_password)
        if not self.salt:
            raise ValueError('Salt must be set before checking password')

        return self.password == hash.make_password(raw_password, self.salt)

    @classmethod
    def generate_raw_password(cls) -> str:
        """初期パスワードを生成します

        Returns:
            str: 初期パスワード
        """

        return secrets.token_urlsafe(16)

    def reset_password(self) -> str:
        """パスワードをリセットします

        リセットの場合はSaltもパスワードもシステム側で自動生成します

        Returns:
            str: 自動生成した平文パスワード
        """

        self.salt = hash.generate_salt()
        _raw_password = self.generate_raw_password()
        self.set_password(_raw_password)
        return _raw_password

    def disable(self) -> None:
        """スタッフを即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()

    def update_last_login(self) -> None:
        """ログイン日時を更新します"""
        self.last_login = timezone.localtime()
        self.save(update_fields=['last_login'])
