@import "src/assets/scss/company/setting";
.container {
  .inner {
    .contents {
      .ui.pagination.menu {
        margin-top: 10px;
      }
      .table_fixed.body {
        max-height: calc(100% - 250px);
      }
      tr {
        .base_name {
          width: 15%;
        }
        .banner {
          padding: 2px;
          .image {
            display: flex;
            width: 100%;
            height: 42px;
            >img {
              margin: auto;
              width: auto;
              height: auto;
              max-width: 100%;
              max-height: 42px;
            }
          }
        }
        .url_link {
          &:hover {
            opacity: .5;
          }
        }
        .url {
          width: 20%;
        }
        .link_file {
          width: 15%;
        }
        .begin_date {
          width: 10%;
        }
        .end_date {
          width: 10%;
        }
        .operation {
          width: 60px;
        }
      }
      .ui.modal {
        .input_area >.line >label {
          min-width: 150px;
        }
        .ui.input.full input {
          width: auto;
        }
        .ui.card.image {
          margin: 0 1px;
          width: 100%;
          .image img {
            max-height: 200px;
            max-width: 100%;
            width: auto;
            margin: auto;
          }
        }
        .line.link {
          .desc {
            margin: 8px;
            color: $lable-text-dark;
          }
          .button {
            border-radius: 0!important;
          }
          position: relative;
          >a {
            position: absolute;
            top: 8px;
            left: 170px;
            z-index: 99999;
          }
          >.clear {
            position: absolute;
            top: 2px;
            right: 125px;
            cursor: pointer;
            opacity: .5;
            &:hover {
              opacity: 1;
            }
          }
        }
      }
    }
  }
}
