from typing import Dict

from dateutil.relativedelta import relativedelta
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from masters.tests.factories import WarekiFactory
from orders.models import EntryDetailChobun
from orders.tests.factories import EntryDetailChobunFactory, EntryDetailFactory, EntryFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]


class ChobunPDFViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.entry_detail = EntryDetailFactory(entry=EntryFactory())
        EntryDetailChobunFactory(entry_detail=self.entry_detail)
        WarekiFactory(begin_date=self.entry_detail.entry.entry_ts + relativedelta(years=-1))

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_chobun_pdf_succeed(self) -> None:
        """弔文PDFを返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:chobun_pdf', kwargs={'pk': self.entry_detail.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.filename, f'弔文-{self.entry_detail.pk}.pdf')

        saved_entry_detail_chobun = EntryDetailChobun.objects.get(pk=self.entry_detail.chobun.pk)
        self.assertTrue(saved_entry_detail_chobun.printed_flg)

    def test_chobun_pdf_succeed_without_auth(self) -> None:
        """弔文PDF取得APIはAuthorizationヘッダがなくても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('orders:chobun_pdf', kwargs={'pk': self.entry_detail.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_chobun_pdf_succeed_without_chobundaishi(self) -> None:
        """弔文台紙を選択してない申込詳細IDを指定しても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        self.entry_detail.chobun.daishi.delete()
        response = self.api_client.get(
            reverse('orders:chobun_pdf', kwargs={'pk': self.entry_detail.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_chobun_pdf_succeed_without_chobundaishi_file(self) -> None:
        """無地の弔文台紙を指定しても成功する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        self.entry_detail.chobun.daishi.file_name = None
        self.entry_detail.chobun.daishi.save()
        response = self.api_client.get(
            reverse('orders:chobun_pdf', kwargs={'pk': self.entry_detail.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_chobun_pdf_failed_by_detail_notfound(self) -> None:
        """存在しない申込詳細IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        not_exist_entry_detail = EntryDetailFactory.build()
        response = self.api_client.get(
            reverse('orders:chobun_pdf', kwargs={'pk': not_exist_entry_detail.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_chobun_pdf_failed_by_chobun_notfound(self) -> None:
        """存在しない弔文の申込詳細IDを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        self.entry_detail.chobun.delete()
        response = self.api_client.get(
            reverse('orders:chobun_pdf', kwargs={'pk': self.entry_detail.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
