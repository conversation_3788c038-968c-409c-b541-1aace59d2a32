from typing import Union

from django.conf import settings
from django.core.handlers.wsgi import WSGIRequest
from rest_framework.request import Request


def request_origin(request: Union[Request, WSGIRequest]) -> str:
    """RequestオブジェクトからWebアプリケーションのroot URLを取得します

    Django設定のCORS_ORIGIN_ALLOW_ALLがTrueの場合はリクエストヘッダに
    HTTP_ORIGINが与えられている場合はその値を、それ以外はDjango設定の
    FRONTEND_HOSTNAMEをroot URLとして返します

    Args:
        request (Union[Request, WSGIRequest]): Requestオブジェクト

    Returns:
        str: Webアプリケーションのroot URL
    """
    default_hostname = settings.FRONTEND_HOSTNAME
    origin = request.META.get('HTTP_ORIGIN', None)
    if settings.CORS_ORIGIN_ALLOW_ALL and origin:
        return origin
    return default_hostname or request.build_absolute_uri('/')
