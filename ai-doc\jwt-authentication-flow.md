# FOC JWT認証フローと会社コード取得

## 概要

FOCシステムでのJWT認証処理と会社コード（company_code）の取得方法について詳細に説明します。ログイン後の認証が必要な画面でのアクセストークン検証と、会社コードの取得元を理解できます。

## 1. JWT認証の全体フロー

### 1.1 ログイン時のJWT生成フロー

```
1. フロントエンド: ログイン情報送信
   ↓ POST /staffs/login/ (company_code, login_id, password)
2. バックエンド: 認証処理
   ↓ staffs/auth_backends.py → CombinationModelBackend
3. バックエンド: JWT生成
   ↓ utils/authentication.py → ClassableRefreshToken
4. フロントエンド: JWT保存
   ↓ SessionService → sessionStorage['staff_login_info']
5. フロントエンド: ユーザー情報取得
   ↓ GET /staffs/me/ (JWT認証)
6. フロントエンド: 完全なログイン情報保存
```

### 1.2 認証が必要な画面でのJWT検証フロー

```
1. フロントエンド: ガード実行
   ↓ AuthFGuard.canActivate()
2. フロントエンド: JWT存在確認
   ↓ SessionService.get('staff_login_info')
3. フロントエンド: API呼び出し
   ↓ HttpClientService.getWithToken()
4. フロントエンド: JWT自動更新
   ↓ refreshToken()
5. バックエンド: JWT検証
   ↓ utils/authentication.py → ClassableJWTAuthentication
6. バックエンド: ユーザー情報取得
   ↓ request.user (Staff or Moshu)
```

## 2. JWT認証の詳細実装

### 2.1 バックエンド: JWT認証クラス

```python
# utils/authentication.py
class ClassableJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        try:
            user_id = validated_token[api_settings.USER_ID_CLAIM]  # 'user_id'
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user identification'))

        try:
            user_class: str = validated_token['user_class']  # 'Staff' or 'Moshu'
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user class'))

        try:
            if user_class == 'Moshu':
                # 葬家（Moshu）の場合
                user = Moshu.objects.get(Q(pk=user_id) & Q(seko__del_flg=False))
            else:
                # スタッフ（Staff）の場合
                user = User.objects.get(**{api_settings.USER_ID_FIELD: user_id})
        except (Moshu.DoesNotExist, User.DoesNotExist):
            raise AuthenticationFailed(_('User not found'), code='user_not_found')

        if not user.is_active:
            raise AuthenticationFailed(_('User is inactive'), code='user_inactive')

        return user
```

### 2.2 バックエンド: JWT生成

```python
# utils/authentication.py
class ClassableRefreshToken(RefreshToken):
    @classmethod
    def for_user(cls, user):
        if isinstance(user, Moshu):
            setattr(user, 'id', user.seko.pk)
        token = super().for_user(user)
        token['user_class'] = user._meta.object_name  # 'Staff' or 'Moshu'
        if isinstance(user, Moshu):
            delattr(user, 'id')
        return token
```

**JWTペイロード構造:**
```json
{
  "user_id": 123,
  "user_class": "Staff",
  "exp": 1640995200,
  "iat": 1640991600,
  "jti": "abc123..."
}
```

### 2.3 バックエンド: スタッフ認証

```python
# staffs/auth_backends.py
class CombinationModelBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        company_code: Optional[str] = kwargs.get('company_code')
        if username is None:
            username = kwargs.get(UserModel.USERNAME_FIELD)
        if company_code is None or username is None or password is None:
            return
        try:
            user = UserModel.objects.get(
                Q(company_code=company_code)
                & Q(login_id=username)
                & Q(del_flg=False)
                & Q(retired_flg=False)
            )
        except UserModel.DoesNotExist:
            UserModel().set_password(password)
        else:
            if user.check_password(password) and self.user_can_authenticate(user):
                user.update_last_login()
                return user
```

## 3. 会社コード（company_code）の取得

### 3.1 会社コードの保存場所

#### **スタッフ（Staff）の場合**

```python
# staffs/models.py
class Staff(AbstractBaseUser, PermissionsMixin):
    base = models.ForeignKey(Base, on_delete=models.DO_NOTHING, related_name='staffs')
    company_code = models.CharField(_('company code'), max_length=20, blank=True, null=True)
    name = models.TextField(_('name'))
    login_id = models.CharField(_('login id'), max_length=20)
    # ... 他のフィールド
```

**取得方法:**
```python
# 認証後のAPIビューで
def some_api_view(request):
    staff = request.user  # Staff インスタンス
    company_code = staff.company_code  # 直接取得
```

#### **葬家（Moshu）の場合**

```python
# seko/models.py
class Moshu(AbstractBaseUser):
    seko = models.OneToOneField(Seko, models.DO_NOTHING, related_name='moshu')
    # ... 他のフィールド

class Seko(models.Model):
    base = models.ForeignKey(Base, on_delete=models.DO_NOTHING, related_name='sekos')
    # ... 他のフィールド

# bases/models.py
class Base(MPTTModel):
    company_code = models.CharField(_('company code'), max_length=20, blank=True, null=True)
    # ... 他のフィールド
```

**取得方法:**
```python
# 認証後のAPIビューで
def some_api_view(request):
    moshu = request.user  # Moshu インスタンス
    company_code = moshu.seko.base.company_code  # リレーション経由で取得
```

### 3.2 JWTトークンに会社コードは含まれない

**重要:** FOCシステムでは、JWTトークンに`company_code`は含まれていません。

**JWTトークンの内容:**
- `user_id`: ユーザーID
- `user_class`: ユーザークラス（'Staff' or 'Moshu'）
- `exp`, `iat`, `jti`: JWT標準クレーム

**会社コード取得の流れ:**
```
1. JWT認証 → request.user取得
2. request.userのタイプ判定
3. Staff: user.company_code
   Moshu: user.seko.base.company_code
```

## 4. フロントエンド: JWT認証処理

### 4.1 ログイン処理

```typescript
// pages/company/login/login.component.ts
async login() {
  const loginRequest = new LoginPostRequest();
  loginRequest.company_code = this.companyCode;
  loginRequest.login_id = this.loginId;
  loginRequest.password = this.password;

  this.httpClientService.postLogin(loginRequest)
    .then(async (response) => {
      // JWT保存
      const login_info = { token: response, staff: null };
      this.sessionSvc.save('staff_login_info', login_info);
      
      // スタッフ情報取得（company_code含む）
      await this.httpClientService.getLoginStaff().then(async (staffResponse) => {
        login_info.staff = staffResponse;  // company_code含む
        this.sessionSvc.save('staff_login_info', login_info);
        this.router.navigate([this.previousUrl]);
      });
    });
}
```

### 4.2 認証ガード

```typescript
// guard/authF.guard.ts
export class AuthFGuard implements CanActivate {
  canActivate(): Observable<boolean> | Promise<boolean> | boolean {
    const login_info = this.sessionSvc.get('staff_login_info');
    if (!login_info || !login_info.staff) {
      this.router.navigate(['foc/login/']);
      return false;
    }
    this.httpClientService.refreshToken().catch(err => { });
    return true;
  }
}
```

### 4.3 API呼び出し時のJWT送信

```typescript
// service/http-client.service.ts
private async getWithToken<T>(method: string, params: HttpParams): Promise<T> {
  await this.refreshToken();  // JWT自動更新
  
  const login_info = this.getLoginInfo();
  let str = '';
  if (login_info && login_info.token) {
    str = 'Bearer ' + login_info.token.access;  // JWT設定
  }
  
  this.headers = new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': str 
  });
  
  return this.http.get(this.host + method, { headers: this.headers, params: params })
    .toPromise();
}
```

### 4.4 JWT自動更新

```typescript
// service/http-client.service.ts
public async refreshToken(): Promise<any> {
  let session_name = null;
  let apiUrl = null;
  let login_url = null;
  
  if (window.location.pathname.includes('/foc')) {
    session_name = 'staff_login_info';
    apiUrl = this.host + '/staffs/token/refresh/';
    login_url = 'foc/login';
  } else if (window.location.pathname.includes('/soke')) {
    session_name = 'soke_login_info';
    apiUrl = this.host + '/seko/token/refresh/';
    login_url = 'soke/login';
  }
  
  let login_info = this.sessionSvc.get(session_name);
  if (!login_info || !login_info.token) {
    return Promise.resolve();
  }
  
  const body = JSON.stringify({ refresh: login_info.token.refresh });
  
  return this.http.post<any>(apiUrl, body, { headers: this.headers })
    .toPromise()
    .then((res) => {
      if (res.access) {
        login_info = this.sessionSvc.get(session_name);
        login_info.token.access = res.access;  // 新しいアクセストークン保存
        this.sessionSvc.save(session_name, login_info);
      }
      return res;
    })
    .catch((err) => {
      if (err.status === 401) {
        this.sessionSvc.clearAll();
        this.router.navigate([login_url]);
      }
    });
}
```

## 5. 会社コード取得の具体例

### 5.1 スタッフログイン後の会社コード取得

```typescript
// フロントエンド
const login_info = this.sessionSvc.get('staff_login_info');
const company_code = login_info.staff.company_code;  // セッションから取得
```

```python
# バックエンドAPI
class StaffMe(generics.RetrieveAPIView):
    permission_classes = [IsStaff]
    
    def get_object(self):
        return self.request.user  # Staff インスタンス
    
    def get(self, request):
        staff = request.user
        company_code = staff.company_code  # 直接取得
        return Response({
            'id': staff.id,
            'company_code': company_code,
            'name': staff.name,
            # ...
        })
```

### 5.2 葬家ログイン後の会社コード取得

```python
# バックエンドAPI
class SomeSekoAPI(generics.RetrieveAPIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        if isinstance(request.user, Moshu):
            moshu = request.user
            company_code = moshu.seko.base.company_code  # リレーション経由
        else:
            # Staff の場合
            staff = request.user
            company_code = staff.company_code
            
        return Response({
            'company_code': company_code,
            # ...
        })
```

## 6. セキュリティ考慮事項

### 6.1 JWT設定

```python
# foc/settings.py
SIMPLE_JWT = {
    'AUTH_HEADER_TYPES': ('JWT', 'Bearer'),
    'ALGORITHM': 'RS256',  # RSA署名
    'SIGNING_KEY': privkey,
    'VERIFYING_KEY': pubkey,
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=5),  # 短い有効期限
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
}
```

### 6.2 認証設定

```python
# foc/settings.py
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': ('utils.authentication.ClassableJWTAuthentication',),
}
```

## 7. まとめ

### 7.1 JWT認証フロー
1. **ログイン**: company_code + login_id + password → JWT生成
2. **認証**: JWT → ユーザー情報取得 → request.user設定
3. **会社コード取得**: request.user → company_code取得

### 7.2 会社コード取得元
- **Staff**: `request.user.company_code`（直接）
- **Moshu**: `request.user.seko.base.company_code`（リレーション経由）

### 7.3 重要なポイント
- JWTトークンに会社コードは含まれない
- 会社コードは認証後にユーザーモデルから取得
- フロントエンドではセッションに保存されたユーザー情報から取得
- JWT自動更新により継続的な認証を維持
