

@import "./setting";
app-company-frame, .cdk-overlay-container, .ui.dimmer.modals {
  font-size: 13px;
  font-family: "Hiragino Sans W3", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ ProN W3", "メイリオ", <PERSON><PERSON>, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif, my-font;
  background-color: $background-light1;
  com-dropdown, com-calendar, com-time-input {
    background: #ffffff;
  }

  .container {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: auto;
    background-color: $background-light1;
    .inner {
      position: relative;
      margin-top: 40px;
      height: calc(100% - 40px);
      &.with_footer {
        height: calc(100% - 82px);
      }
      width: 100%;
      background-color: $background-light1;
      color: $lable-text-dark;
      overflow: auto;
      .contents {
        min-width: 970px;
        &.small {
          max-width: 1500px;
        }
        &.mini {
          max-width: 1200px;
        }
        &.tiny {
          max-width: 900px;
        }
        position: relative;
        width: 100%;
        height: 100%;
        margin: 0 auto;
        background-color: $background-light1;
        padding: 10px;
        .menu_title {
          color: $lable-text-dark;
          -webkit-box-sizing: border-box;
          -moz-box-sizing: border-box;
          box-sizing: border-box;
          padding: 5px 0;
          font-size: 1.385em;
          font-weight: 700;
        }

        .top_button_area {
          margin-top: -40px;
          float: right;
          display: flex;
        }
        .search_area {
          margin-top: 10px;
          .line {
            border-top: 1px dotted $label-border-light1;
            border-right: 2px solid $label-border-light1;
            border-left: 2px solid $label-border-light1;
            &:first-child {
              border-top: 2px solid $label-border-light1;
            }
            &:last-child {
              border-bottom: 2px solid $label-border-light1;
            }
            display: flex;
            >label {
              display: block;
              min-width: 95px;
              height: 32px;
              background-color: $background-light1;
              padding: 10px;
              font-size: 1em;
              font-weight: 700;
              color: $lable-text-dark;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              float: left;
              line-height: 1;
              border-right: 3px solid $label-border-light2;
              &.plane {
                min-width: 0;
                border: 0;
                background-color: #ffffff;
                padding: 10px 0;
              }
              &.required {
                border-right: 3px solid $label-border-required;
              }
              &.large {
                min-width: 120px;
              }
              &.divided {
                border-left: solid 1px $label-border-light2;
              }
            }
            .ui.selection.dropdown, >.ui.input, com-dropdown, com-calendar {
              max-width: 200px;
              width: 25%;
              &.small {
                width: 150px;
                min-width: 150px;
              }
              &.mini {
                width: 125px;
                min-width: 125px;
              }
              &.tiny {
                width: 50px;
                min-width: 50px;
              }
              &.full {
                width: 100%;
                max-width: 100%;
              }
            }

            @media screen and (max-width: 1024px) {
              .ui.selection.dropdown, >.ui.input, com-dropdown, com-calendar {
                max-width: 200px;
              }
            }
          }
        }

        .sub_title {
          font-weight: 700;
          font-size: 1.1538em;
          margin-bottom: 10px;
        }
        .input_area .line {
          display: flex;
          margin-bottom: 10px;
          >label {
            display: block;
            min-width: 90px;
            height: 32px;
            background-color: $table-row-dark;
            padding: 10px;
            font-size: 1em;
            font-weight: 700;
            color: $lable-text-dark;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            float: left;
            line-height: 1;
            border-right: 3px solid $label-border-light2;
            // border-bottom: 1px solid $label-border-light2;
            &.plane {
              min-width: 0;
              border-right-width: 0;
            }
            &.data {
              min-width: 0;
              border-right-width: 0;
              background-color: #ffffff;
              font-weight: normal;
            }
            &.large {
              min-width: 120px;
            }
            &.required {
              border-right: 3px solid $label-border-required;
            }
          }
          &:last-child {
            border-bottom-width: 1px;
          }
          >.ui.selection.dropdown {
            max-width: 200px;
            width: 25%;
            // border-bottom: 1px solid $label-border-light2;
            &.tiny {
              width: 80px;
              min-width: 80px;
            }
          }
          com-dropdown {
            max-width: 200px;
            width: 25%;
            // border-bottom: 1px solid $label-border-light2;
            &.large {
              max-width: 300px;
              width: 300px;
              font-size: 1em;
            }
            &.tiny {
              width: 80px;
              min-width: 80px;
            }
            &.divided {
              border-left: solid 1px $label-border-light2;
            }
          }
          >.ui.input {
            max-width: 200px;
            width: 25%;
            &.large {
              max-width: 300px;
              width: 300px;
              font-size: 1em;
            }
            // border-bottom: 1px solid $label-border-light2;
            &.mini {
              width: 125px;
              min-width: 125px;
            }
            &.tiny {
              width: 50px;
              min-width: 50px;
            }
            &.full {
              width: 100%;
              max-width: 100%;
            }
            &.textarea {
              width: 100%;
              max-width: 515px;
              >i.icon {
                cursor: pointer;
                &:hover {
                  opacity: .8;
                }
                &:before {
                  top: 15px;
                }
              }
              textarea {
                resize: none;
                width: 100%;
                padding: 10px 30px 10px 10px;
              }
            }
          }
          com-calendar {
            max-width: 200px;
            width: 25%;
            >.ui.input  {
              width: 100%;
            }
          }
          >button {
            margin-left: 10px;
          }
          &.row2 {
            height: 68px;
            >.ui.input textarea {
              height: 68px;
            }
          }
          &.row3 {
            height: 90px;
            >.ui.input textarea {
              height: 90px;
            }
          }
          &.row4 {
            height: 112px;
            >.ui.input textarea {
              height: 112px;
            }
          }
          &.row5 {
            height: 134px;
            >.ui.input textarea {
              height: 134px;
            }
          }
        }
        .input_area .line:last-child {
          margin-bottom: 0;
        }
        .ui.input.divided {
          border-left: solid 1px $label-border-light2;
          &.required {
            border-left-color: $label-border-required;
          }
        }

        .ui.modal {
          .ui.segment {
            background-color: $label-background-light;
          }
          .messageArea {
            position: absolute;
            z-index: 10;
            left: 5px;
            right: 5px;
            top: 10px;
            display: flex;
            opacity: .9;
            .ui.message {
              width: 100%;
              margin: auto;
              transition: all 0.2s ease-in-out;
              &.hidden{
                display: none!important;
              }
              &.visible{
                display: block!important;
              }
              .segment {
                background-color: inherit;
                padding: 0;
              }
            }
          }
          .input_area .line {
            >.ui.selection.dropdown, >.ui.input, com-dropdown, com-calendar {
              width: 100%;
              &.mini {
                width: 125px;
                min-width: 125px;
              }
              &.tiny {
                width: 50px;
                min-width: 50px;
              }
              &.full {
                width: 100%;
                max-width: 100%;
              }
            }
          }
        }

        .ui.card.image {
          margin: 0 10px;
          >.clear {
            position: absolute;
            padding: 2px;
            top: 0;
            width: 30px;
            opacity: 0;
            right: 0;
            border-radius: 0 !important;
            >i {
              float: right;
              cursor: pointer;
              opacity: .5;
              color: red;
              &:hover {
                opacity: 1;
              }
            }
          }
          &:hover >.clear {
            opacity: 1;
          }
          >.ui.image {
            padding: 1px;
            cursor: pointer;
            border-radius: 5px !important;
            >i {
              margin-top: 20px;
              opacity: .5;
            }
            .noimage {
              opacity: .5;
              font-family: 'arial black';
            }
            .desc {
              font-size: 0.7692em;
              opacity: .5;
              padding: 10px;
            }
          }
          .content {
            padding: 5px;
            cursor: pointer;
            &:hover {
              opacity: .8;
            }
          }
        }
        .disabled_overlay {
          position: absolute;
          width: 100%;
          top: 0;
          bottom: 0;
          left: 0;
          z-index: 10;
        }

        .table_fixed {
          border: solid 2px $label-border-light1;
          overflow-y: scroll;
          table {
            table-layout: fixed;
            border: 0;
            thead {
              border-bottom: 0;
            }
          }
          &.head {
            margin-top: 10px;
            border-bottom: 0;
            tr {
              border-bottom: solid 1px $label-border-light1;
              &.initial {
                border-bottom: 0;
              }
              &.no-data {
                border-bottom: 0;
              }
            }
          }
          &.body {
            max-height: calc(100% - 280px);
            &.no-page-nav {
              max-height: calc(100% - 250px);
            }
            min-height: 0;
            border-top: 0;
          }
          td, th {
            white-space:nowrap;
            overflow:hidden;
            text-overflow:ellipsis;
          }
          tr:not(.no_data) {
            cursor: pointer;
          }
          tr.even_row {
            background: $table-row-dark;
          }
          th {
            min-width: 70px;
            border-bottom: 0;
          }
          td.button {
            i:hover {
              opacity: 0.9;
              transform: translateY(-1px);
            }
          }
        }

        .ui.pagination.menu + .table_fixed.head {
          margin-top: 40px;
        }
        .ui.attached.tab.segment {
          min-height: calc(100% - 100px);
          margin-bottom: 10px;
        }
      }
    }
  }

  .footer_button_area {
    position: absolute;
    width: 100%;
    height: 42px;
    bottom: 0;
    left: 0;
    background-color: $background-dark;
    z-index: 20;
    padding: 0 10px;
    .button_group{
      width: 100%;
      position: relative;
      margin: 3px auto;
      display: flex;
      justify-content: center;
      .btn2 {
        max-height: 35px;
      }
      &.right {
        justify-content: flex-end;
      }
      &.left {
        justify-content: flex-start;
      }
    }
  }
  &.cdk-overlay-container  {
    background-color: transparent;
    //ダイアログ
    .cdk-global-overlay-wrapper {
      padding: 0 5px;
      justify-content: center !important;
    }

    .mat-dialog-container {
      background-color: $background-light1;
      padding: 0 !important;
      overflow: hidden;
    }
    .overlay-dialog .mat-dialog-container{
      overflow: hidden;
      padding: 0 !important;
    }

    .mat-progress-spinner circle,
    .mat-spinner circle {
      stroke: #ffffff;
    }

  }

  input,
  select {
    vertical-align: middle;
  }
  input,
  select,
  textarea {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
  }
  input:focus,
  select:focus,
  textarea:focus {
      outline: none !important;
      border: 0;
  }
  .ui.mini.buttons .button, .ui.mini.buttons .dropdown, .ui.mini.buttons .dropdown .menu>.item, .ui.mini.buttons .or, .ui.ui.ui.ui.mini.button {
    font-size: 1em;
  }
  .ui.label {
    background-color: $label-background-light;
    color: $lable-text-dark;
  }
  .ui.labeled.button>.label {
    border: 1px solid $label-border-dark;
    &:hover {
      background-color: $label-background-light;
    }
  }
  .ui.button, .ui.active.button, .ui.circular.button {
    font-family: inherit;
    background-color: $btn-background-dark;
    color: $btn-text-light;
    white-space: nowrap;
    &.light, &.light:hover {
      border: 1px solid $label-border-dark;
      background-color: $btn-background-light;
      color: $btn-text-dark;
    }
    &:hover{
      background-color: $btn-background-dark;
      color: $btn-text-light;
    }
    &:hover:not(.dropdown) {
      box-shadow: 2px 2px 2px rgba(0,0,0,0.5);
      -webkit-box-shadow: 2px 2px 2px rgba(0,0,0,0.5);
      -webkit-transform: translateY(-1px);
      transform: translateY(-1px);
    }
    &.top-menu:hover{
      opacity: 1;
    }
  }
  .ui.icon.button>.icon {
    opacity: 1;
  }
  .ui.labeled.icon.button {
    padding-left: 2.5em !important;
    >.icon {
      background: 0;
    }
  }
  .ui.active.button:active, .ui.button:active, .ui.button:focus {
    background-color: $btn-background-dark;
    color: $btn-text-light;
    &.light {
      background-color: $btn-background-light;
      color: $btn-text-dark;
    }
  }
  .ui.dropdown .menu>.item, .ui.small.dropdown .menu>.item {
    color: $lable-text-dark;
    font-size: 1em;
    &:hover{
      color: $lable-text-dark;
      background-color: white;
      font-weight: 700;
    }
  }
  .ui.small.buttons .button, .ui.small.buttons .dropdown, .ui.small.buttons .dropdown .menu>.item, .ui.small.buttons .or, .ui.ui.ui.ui.small.button {
    font-size: 1em;
  }
  .ui.dropdown .menu .selected.item, .ui.dropdown.selected {
    color: $lable-text-dark;
    background-color: white;
    &:hover{
      color: $lable-text-dark;
      background-color: white;
      font-weight: 700;
    }
  }
  .ui.dropdown .menu .active.item {
    font-weight: 400;
  }
  .ui.dropdown .menu.static>.item, .ui.small.dropdown .menu.static>.item {
    color: $lable-text-dark;
    font-size: 1em;
    padding: 10px 20px 0 !important;
    min-height: 10px;
    cursor: default;
    &:last-child {
      padding-bottom: 10px !important;
    }
    &:hover{
      background-color: white;
      font-weight: 400;
    }
  }
  .ui.dropdown, .ui.dropdown .menu>.item {
      font-size: inherit;
  }
  .ui.selection.active.dropdown .menu, .ui.selection.active.dropdown:hover .menu {
    border-color: $label-border-light1;
  }
  .ui.selection.active.dropdown .menu .message {
    display: none;
  }
  .ui.selection.dropdown .menu>.item {
    padding: 8px 10px !important;
  }
  .ui.segment {
    background-color: $tab-background;
  }
  .ui.tabular.menu {
    min-height: 30px;
    border-color: $label-border-dark;
    .item {
      min-height: 32px;
      color: $btn-text-dark;
      font-size: 1.07692em;
      border-color: $label-border-dark;
      opacity: .7;
      // border-color: $label-border-light1;
      border-radius: 0 10px 0 0!important;
      // margin-right: 20px;
      border-top-width: 5px;
      padding: 0 30px;
      margin-right: 10px;
      &:last-child {
        margin-right: 0;
      }
      &.active {
        opacity: 1;
        color: $btn-text-dark;
        background-color: $tab-background;
        border-color: $label-border-dark;
        border-top-width: 5px;
        border-radius: 0 10px 0 0!important;
        &:hover {
          cursor: default;
          opacity: 1;
        }
      }
      &:hover {
        opacity: .9;
        cursor: pointer;
        border-color: $label-border-dark;
        color: $btn-text-dark;
      }
    }
  }

  .ui.attached.segment {
    border-color: $label-border-dark;
  }
  com-calendar.error input {
    box-shadow:inset 0 0 0 1px $label-border-required !important;
    -webkit-box-shadow:inset 0 0 0 1px $label-border-required !important;
  }
  com-dropdown.error .ui.selection.dropdown {
    box-shadow:inset 0 0 0 1px $label-border-required;
    -webkit-box-shadow:inset 0 0 0 1px $label-border-required;
  }
  com-time-input.error input {
    box-shadow:inset 0 0 0 1px $label-border-required !important;
    -webkit-box-shadow:inset 0 0 0 1px $label-border-required !important;
  }
  .ui.selection.dropdown {
    min-width: 100px;
    height: 32px;
    min-height: 32px;
    padding: 7px 10px;
    border: 0;
    border-radius: 0;
    font: inherit;
    font-size: 1em;
    &:hover {
      box-shadow:inset 0 0 0 2px $label-border-light1 !important;
      -webkit-box-shadow:inset 0 0 0 2px $label-border-light1 !important;
    }
    input:focus  {
      box-shadow:inset 0 0 0 2px $label-border-dark !important;
      -webkit-box-shadow:inset 0 0 0 2px $label-border-dark !important;
    }
  }
  .ui.input {
    min-width: 100px;
    height: 32px;
    min-height: 32px;
    font-size: 1em;
    input {
      line-height: 1.2 !important;
    }
    input, textarea {
      width: 100%;
      padding: 10px;
      border: 0;
      border-radius: 0;
      font: inherit;
      font-size: 1em;
      &.error {
        box-shadow:inset 0 0 0 1px $label-border-required !important;
        -webkit-box-shadow:inset 0 0 0 1px $label-border-required !important;
      }
      &:hover:not(.readonly) {
        box-shadow:inset 0 0 0 2px $label-border-light1 !important;
        -webkit-box-shadow:inset 0 0 0 2px $label-border-light1 !important;
        // border: 1px solid $label-border-dark;
      }
      &:focus:not(.readonly) {
        box-shadow:inset 0 0 0 2px $label-border-dark !important;
        -webkit-box-shadow:inset 0 0 0 2px $label-border-dark !important;
        // border: 1px solid $label-border-dark;
      }
    }
    i {
      color: $lable-text-dark;
    }
  }
  .ui.action.input {
    .button {
      background-color: $btn-background-dark;
      color: $btn-text-light;
    }
    i {
      color: $lable-text-light;
    }
  }
  .ui.label>.icon {
    margin: 0;
  }
  .ui.icon.input>i.icon {
    opacity: 1;
  }
  .ui.checkbox {
    label {
      font: inherit;
      color: $lable-text-dark;
      font-weight: 700;
      font-size: 1em;
      white-space: nowrap;
      ;
    }
    input:focus~label {
      color: $lable-text-dark!important;
    }
    label:before, input:focus~label:before, input:checked~label:before {
      border-color: $lable-text-dark!important;
    }
  }
  .ui.radio.checkbox {
    padding: 10px;
    input:focus:checked~label:after {
      background-color: $lable-text-dark!important;
    }
    input:checked~label, input:focus~label, input:focus:checked~label {
      color: $lable-text-dark!important;
    }
    input:checked~label, input:focus:checked~label {
      opacity: 1;
    }
  }
  .ui.radio.checkbox input:checked~label:after {
    background-color: $lable-text-dark;
  }
  .ui.checkbox input:checked~label:after {
    color: $lable-text-dark;
  }

  .ui.checkbox label:hover, .ui.checkbox+label:hover {
    color: $lable-text-dark;
    opacity: .8;
  }
  .ui.toggle.checkbox {
    label {
      font: inherit;
      color: $lable-text-dark;
      font-weight: 700;
      font-size: 1em;
      line-height: 1.8;
      opacity: 0.8;
      padding-right: 58px;
      padding-left: 0;
      text-align: right;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      &:before {
        left: initial;
        right: 0;
        width: 45px;
        height: 24px;
      }
      &:after {
        width: 24px;
        height: 24px;
      }
    }
    >input {
      width: 100%;
      height: 100%;
    }
    input~label:after {
      left: initial;
      right: 22px;
    }
    input:checked~label:after {
      left: initial;
      right: 0px;
    }
    input:checked~label:before, input:focus:checked~label:before {
      background-color: $label-border-dark!important;
    }
    input:checked~label, input:focus~label, input:focus:checked~label {
      color: $lable-text-dark!important;
    }
    input:checked~label, input:focus:checked~label {
      opacity: 1;
    }
  }
  .ui.input, .ui.small.input, .ui.mini.input, .ui.tiny.input,
  .ui.segment, .ui.segments,
  .ui.popup {
    font-size: 1em;
  }
  .ui.table {
    border-color: $label-border-light1;
    border-width: 2px;
    border-radius: 5px;
    font-size: 1em;
    background-color: inherit;
    thead {
      border-color: $label-border-light1;
      border-bottom: solid 2px $label-border-light1;
      tr:first-child {
        >th:first-child, >th:last-child  {
          border-radius: 0;
        }
      }
      tr>th {
        vertical-align: middle;
        background: $background-light1;
        color: $lable-text-dark;
        padding: 10px;
        border-color: $label-border-light1;
      }
    }
    tbody {
      tr {
        background: $table-row-light;
        color: $lable-text-dark;
        td {
          vertical-align: middle;
          padding: 10px;
        }
      }
    }
  }
  .ui.celled.table tr td, .ui.celled.table tr th {
    border-color: $label-border-light1;
  }
  .ui.structured.celled.table tr td, .ui.structured.celled.table tr th {
    border-color: $label-border-light1 !important;
    &.operation:not(th) {
      background-color: $table-row-dark;
      text-overflow: unset;
    }
  }
  .ui.structured.celled.table tbody tr:not(:first-child) td {
    border-top-style: dotted;
  }

  .ui.structured.celled.table tbody tr:hover:not(.no_data) {
    background: $table-row-dark;
    // box-shadow:inset 0 0 0 3px $label-border-dark !important;
    // -webkit-box-shadow:inset 0 0 0 3px $label-border-dark !important;

  }
  .ui.calendar .ui.table tr td, .ui.calendar .ui.table tr th {
      padding: .5em;
      white-space: nowrap;
  }
  .ui.calendar {
    .ui.ui.table td.active, .ui.ui.ui.ui.table tr.active {
      background: $label-background-light;
      color: $lable-text-dark;
      box-shadow:inset 0 0 0 2px $label-border-dark !important;
      -webkit-box-shadow:inset 0 0 0 2px $label-border-dark !important;
    }

    .ui.ui.table td.focus, .ui.ui.ui.ui.table tr.focus {
      background: $table-row-dark;
      box-shadow:inset 0 0 0 1px $label-border-light2 !important;
      -webkit-box-shadow:inset 0 0 0 1px $label-border-light2 !important;
    }
    .ui.ui.table td.active.focus, .ui.ui.ui.ui.table tr.active.focus {
      background: $label-background-light;
      color: $lable-text-dark;
      box-shadow:inset 0 0 0 2px $label-border-dark !important;
      -webkit-box-shadow:inset 0 0 0 2px $label-border-dark !important;
    }
    .ui.table.day thead tr {
      &:last-child >th {
        &:last-child {
          color: red;
        }
        &:nth-last-child(2) {
          color: blue;
        }
      }
    }
    .ui.table.day tbody tr {
      &:not(:last-child) >td:not(.disabled) {
        &:last-child {
          color: red;
        }
        &:nth-last-child(2) {
          color: blue;
        }
      }
    }
    .ui.bottom.popup {
      margin: 0;
      &:before {
        content: none;
        // background: $background-light1;
      }
      .ui.table {
        border-width: 1px;
        border-radius: 0;
      }
    }
  }
  .ui.menu {
    font: inherit;
  }
  .ui.pagination.menu {
    min-height: 20px;
    border: none;
    box-shadow: none;
    background: inherit;
    font-size: 1em;
    margin-top: 15px;
    >.item {
      color: $lable-text-dark;
      padding: 0 10px;
      min-width: 0;
      &.current {
        font-weight: bold;
      }
    }
    >.count {
      padding: 0 5px;
    }
  }
  .ui.header, .ui.message .header {
    font: inherit;
    font-weight: 700;
  }
  .ui.modal {
    font: inherit;
  }
  .ui.modal>.header {
      font-family: inherit;
      background: $modal-background;
      padding: 1rem;
      color: $lable-text-dark;
  }
  .ui.modal>.content {
      background: $modal-background;
      color: $lable-text-dark;
  }
  .ui.modal>.actions {
      background: $modal-background;
      color: $lable-text-dark;
  }
  .ui.modal>.scrolling.content {
    max-height: calc(100vh - 10rem);
  }
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type="number"] {
    -moz-appearance:textfield;
  }
}
.ui.dimmer.modals, .ui.inverted.dimmer {
  background-color: rgba(0,0,0,.5);
}

.ui.modal {
  font-size: 1em !important;
  width: 900px !important;
  &.huge {
    width: 1200px !important;
  }
  &.big {
    width: 1024px !important;
  }
  &.small {
    width: 720px !important;
  }
  &.tiny {
    width: 540px !important;
  }
  &.mini {
    width: 430px !important;
  }
}
.cdk-overlay-container {
  z-index: 10000;
}
