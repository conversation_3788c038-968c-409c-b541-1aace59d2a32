import datetime


def get_display_date(date_time: datetime):
    if date_time is None:
        return None
    else:
        local_ts = date_time.astimezone(datetime.timezone(datetime.timedelta(hours=+9)))
        w_list = ['月', '火', '水', '木', '金', '土', '日']
        month = local_ts.strftime('%m').lstrip('0')
        day = local_ts.strftime('%d').lstrip('0')
        return f'{month}/{day} （{w_list[local_ts.weekday()]}）'


def get_display_datetime(date_time: datetime):
    if date_time is None:
        return None
    else:
        local_ts = date_time.astimezone(datetime.timezone(datetime.timedelta(hours=+9)))
        month = local_ts.strftime('%m').lstrip('0')
        day = local_ts.strftime('%d').lstrip('0')
        local_time = local_ts.strftime('%H:%M')
        if local_time[0:1] == '0':
            local_time = local_time[1:5]
        return f'{month}/{day} {local_time}'


def get_display_time(time: datetime.time):
    if time is None:
        return None
    else:
        local_time = time.strftime('%H:%M')
        if local_time[0:1] == '0':
            return local_time[1:5]
        return local_time


def get_display_time1(date_time: datetime):
    if date_time is None:
        return None
    else:
        local_ts = date_time.astimezone(datetime.timezone(datetime.timedelta(hours=+9)))
        return get_display_time(local_ts.time())
