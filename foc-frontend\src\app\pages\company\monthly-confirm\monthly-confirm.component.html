
<div class="container">
  <div class="inner">
    <div class="contents tiny">
      <div class="menu_title">
        <i class="tasks icon big"></i>
        月次確定
      </div>
      <div class="search_area">
        <div class="line">
          <label>対象年月</label>
          <com-calendar [settings]="date_option_config" [readonly]="true" id="sales_month" [(value)]="sales_month" (valueChange)="monthChange($event)"></com-calendar>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="sales_list?.length">
          全{{sales_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?sales_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="company_name">葬儀社名</th>
              <th class="confirm_date">確定日</th>
              <th class="staff_name">担当者</th>
              <th class="operation"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let sales of sales_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count">
              <td class="company_name" title="{{sales.company_name}}">{{sales.company_name}}</td>
              <td class="center aligned confirm_date" title="{{sales.confirm_ts | date: 'yyyy/MM/dd'}}">
                {{sales.confirm_ts | date: 'yyyy/MM/dd'}}</td>
              <td class="staff_name" title="{{sales.confirm_staff_name}}">{{sales.confirm_staff_name}}</td>
              <td class="center aligned button operation">
                <i class="large check circle icon" title="確定" *ngIf="!sales.confirm_ts" (click)="fixSales(sales.id)"></i>
                <i class="large reply circle icon" title="確定取消" *ngIf="sales.confirm_ts" (click)="cancelFixSales(sales.id)"></i>
                <i class="large file pdf icon" title="請求書PDF" (click)="downloadPDF(sales.id)"></i>
              </td>
            </tr>
            </ng-container>
            <tr *ngIf="!sales_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="13">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>

		</div>
  </div>
</div>
