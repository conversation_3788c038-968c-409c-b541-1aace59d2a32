
@import "src/assets/scss/company/setting";

.container .inner {
  .contents >.table_fixed.body {
    max-height: calc(100% - 215px);
  }
}
.contents {
  min-width: 1395px!important;
  .search_area .ui.checkbox {
    padding: 8px 10px 0;
    label {
      cursor: pointer;
    }
  }
  tr {
    line-height: 1;
    td {
      cursor: default;
      >div {
        min-height: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:not(:last-child) {
          min-height: 19px;
          border-bottom: dotted 1px $background-light1;
          margin: 0 -10px 5px;
          padding-left: 10px;
          padding-bottom: 5px;
        }
      }
    }
    .entry_id {
      width: 6%;
    }
    .entry_name {
      width: 8%;
    }
    .entry_tel {
      width: 8%;
    }
    .seko_date {
      width: 10%;
    }
    .soke_name {
      width: 8%;
    }
    .kojin_name {
      width: 8%;
    }
    .koden_price {
      width: 6%;
    }
    .henreihin_price {
      width: 6%;
    }
    .henreihin_selected {
      width: 6%;
    }
    .entry_ts {
      width: 105px;
    }
    .is_cancel {
      width: 100px;
    }
    .operation {
      width: 80px;
      i {
        cursor: pointer;
      }
    }
  }
  #receipt-url.ui.modal {
    .header, .content {
      padding: 10px;
      .input_area .ui.input {
        max-width: 100%;
        >input {
          font-size: 1.2em;
        }
      }
    }
  }
}