# Generated by Django 3.1.7 on 2021-05-07 01:58

from pathlib import Path

from django.db import migrations


def is_postgres(schema_editor) -> bool:
    return schema_editor.connection.vendor.startswith('postgres')


def forwards(apps, schema_editor):
    if not is_postgres(schema_editor):
        return

    with open(Path('invoices/migrations/salescalc_v6.sql'), encoding='utf-8') as f:
        ddl: str = f.read()
    schema_editor.execute(ddl.replace('%', '%%'), params=[])


def backwards(apps, schema_editor):
    if not is_postgres(schema_editor):
        return

    with open(Path('invoices/migrations/salescalc_v5.sql'), encoding='utf-8') as f:
        ddl: str = f.read()
    schema_editor.execute(ddl.replace('%', '%%'), params=[])


class Migration(migrations.Migration):

    dependencies = [
        ('invoices', '0008_auto_20210330_1356'),
    ]

    operations = [migrations.RunPython(forwards, backwards, atomic=True)]
