
<div class="container">
  <div class="inner">
    <div class="contents with-background-image">
      <div class="box">
        <!--img src="assets/img/\family/sano/schedule_page-0001.jpg"-->
        <div class="menu" style="margin-bottom: -{{m_height*22}}px">
          <img #menu src="assets/img/\family/sano/schedule_page-0002.jpg">
          <ng-container *ngFor="let page of pages; index as i">
            <a  class="index {{page.tag}}" routerLink="/soke/soke-manual/02002020" fragment="{{page.tag}}" style="height: {{m_height}}px;top: {{page.menu_p}}px"></a>
          </ng-container>
        </div>
        <div class="detail">
        <ng-container *ngFor="let page of pages; index as i">
          <div class="tag" id="{{page.tag}}" style="top: {{page.tag_p}}px"></div>
        </ng-container>
        <ng-container *ngFor="let id of ids; index as i">
          <img src="assets/img/\family/sano/schedule_page-{{id}}.jpg">
        </ng-container>
        </div>
      </div>
    </div>
    
	</div>
</div>

