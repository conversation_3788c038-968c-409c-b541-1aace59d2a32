<!DOCTYPE html>
{% load humanize %}
{% load orders_filters %}
<html>
<title>弔文</title>
<head>
<style type="text/css">
@page {
  size: B5;
  margin: 0;
  prince-shrink-to-fit:auto;
}
body {
  font-family: "Noto Serif CJK JP", serif;
  font-size: 12px;
  color: #333;
  margin: 0;
}
#content {
  {% if chobun.daishi.file_name %}
  background-image: url('data:image/jpeg;base64,{{ chobun.daishi.file_name.path|base64_encode }}');
  {% endif %}
  background-size: contain;
  background-repeat: no-repeat;
  height: 100%;
  padding: 2.5cm 1.5cm 2.5cm 1cm;
}

.vertical_text {
  display: inline-block;
  float: right;
  width: 1.5ch;
  line-height: 3.2ch;
  text-align: center;
  padding: 0 5px;
  vertical-align: top;
}

.vertical_text.wide {
  line-height: 3.5ch;
}
.vertical_text.narrow {
  line-height: 2.3ch;
}
.vertical_text >div {
  margin: -7px 8px;
}

#atena::after {
  content: '様'
}

</style>
</head>
<body style="position: absolute; width: 100%; height:100%;">
<div id="content">
  <div id="atena" class="vertical_text" style="font-size: 18px">{{ chobun.atena|vertical_text }}</div>
  <div style="width: 28ch; float:left">
    {% autoescape off %}
    <div id="entry_ts" class="vertical_text" style="margin-top:12ch">{{ entry.entry_ts|wareki_day|vertical_text }}</div>
    <div id="address" class="vertical_text" style="margin-top:16ch">{{ chobun.okurinushi_prefecture|vertical_text }}{{ chobun.okurinushi_address_1|vertical_text|address_num_to_kanji }}{{ chobun.okurinushi_address_2|vertical_text|address_num_to_kanji }}</div>
    <div id="address3" class="vertical_text" style="margin-top:20ch">{{ chobun.okurinushi_address_3|vertical_text|address_num_to_kanji }}</div>
    <div id="okurinushi_company" class="vertical_text" style="margin-top:12ch">{{ chobun.okurinushi_company|vertical_text }}</div>
    <div id="okurinushi_title" class="vertical_text" style="margin-top:28ch">{{ chobun.okurinushi_title|vertical_text }}</div>
    {% if chobun.okurinushi_company_kana %}<div id="okurinushi_company_kana" class="vertical_text narrow" style="margin-top:12ch"><div style="margin:0 0 1px 0">︵</div>{{ chobun.okurinushi_company_kana|vertical_text }}<div style="margin:1px 0 0 0">︶</div></div>{% endif %}
    <div id="okurinushi_name" class="vertical_text narrow" style="margin-top:40ch">{{ chobun.okurinushi_name|vertical_text }}<div style="margin:0 0 1px 0">︵</div>{{ chobun.okurinushi_kana|vertical_text }}<div style="margin:1px 0 0 0">︶</div></div>
    <div id="renmei1" class="vertical_text narrow" style="margin-top:40ch">{{ chobun.renmei1|vertical_text }}{% if chobun.renmei_kana1 %}<div style="margin:0 0 1px 0">︵</div>{{ chobun.renmei_kana1|vertical_text }}<div style="margin:1px 0 0 0">︶</div>{% endif %}</div>
    <div id="renmei2" class="vertical_text narrow" style="margin-top:40ch">{{ chobun.renmei2|vertical_text }}{% if chobun.renmei_kana2 %}<div style="margin:0 0 1px 0">︵</div>{{ chobun.renmei_kana2|vertical_text }}<div style="margin:1px 0 0 0">︶</div>{% endif %}</div>
    {% endautoescape %}
  </div>
  <div style="text-align:center">
    {% for honbun in chobun.honbun|format_honbun reversed %}
    <div class="honbun vertical_text wide" style="font-size: 15px; margin-top:8ch; float:none">{% autoescape off %}{{ honbun|vertical_text }}{% endautoescape %}</div>
    {% endfor %}
  </div>
</div>
</body>
</html>
