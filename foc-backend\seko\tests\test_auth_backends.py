from typing import Dict, Optional

from django.test import TestCase
from django.test.client import RequestFactory
from django.urls import reverse

from seko.auth_backends import MoshuAuthBackend
from seko.models import Moshu
from seko.tests.factories import DEFAULT_PASSWORD, MoshuFactory


class MoshuAuthBackendTest(TestCase):
    def setUp(self):
        super().setUp()

        self.moshu: Moshu = MoshuFactory()
        self.auth_backend = MoshuAuthBackend()
        self.request = RequestFactory().post(reverse('seko:moshu_token_obtain_pair'))
        self.basic_params: Dict = {
            'seko_id': self.moshu.pk,
            'password': DEFAULT_PASSWORD,
        }

    def test_authenticate_success(self) -> None:
        """喪主(葬家)認証バックエンドで認証が成功する"""
        self.assertIsNone(self.moshu.last_login)
        authenticated_moshu: Optional[Moshu] = self.auth_backend.authenticate(
            request=self.request, **self.basic_params
        )
        self.assertEqual(authenticated_moshu, self.moshu)

        # 最終ログイン日時は更新されるが更新日時は更新されない
        self.assertNotEqual(authenticated_moshu.last_login, self.moshu.updated_at)
        self.assertEqual(authenticated_moshu.updated_at, self.moshu.updated_at)

    def test_authenticate_failed_with_less_params(self) -> None:
        """喪主(葬家)認証バックエンドがパラメータ不足で失敗する"""
        authenticate_kwargs = self.basic_params.copy()
        authenticate_kwargs.pop('seko_id')
        authenticated_moshu: Optional[Moshu] = self.auth_backend.authenticate(
            request=self.request, **authenticate_kwargs
        )
        self.assertIsNone(authenticated_moshu)

        authenticate_kwargs = self.basic_params.copy()
        authenticate_kwargs.pop('password')
        authenticated_moshu: Optional[Moshu] = self.auth_backend.authenticate(
            request=self.request, **authenticate_kwargs
        )
        self.assertIsNone(authenticated_moshu)

    def test_authenticate_failed_with_disabled_moshu(self) -> None:
        """喪主(葬家)認証バックエンドで喪主が属する施行が無効化されているため失敗する"""
        self.moshu.seko.disable()

        authenticated_moshu: Optional[Moshu] = self.auth_backend.authenticate(
            request=self.request, **self.basic_params
        )
        self.assertIsNone(authenticated_moshu)

    def test_get_user_success(self) -> None:
        """喪主(葬家)認証バックエンドが喪主を特定する"""
        self.assertEqual(self.auth_backend.get_user(self.moshu.pk), self.moshu)

    def test_get_user_failed_with_disabled_moshu(self) -> None:
        """喪主(葬家)認証バックエンドが無効化された施行の喪主を特定しようとして失敗する"""
        self.moshu.seko.disable()
        self.assertIsNone(self.auth_backend.get_user(self.moshu.pk))
