

body {
  font-size: 14px;
}
.contents{
  background-color: #ffffff;
  width: 90%;
  max-width: 1000px;
  margin: 0px auto 50px;
  border-radius: 10px;
  font-size: 1rem;
  padding: 25px 20px 80px;
  color: #000000;
  @media screen and (max-width: 560px) {
    padding: 25px 20px 80px;
    margin-top: 10px;
  }
  h1 {
    font-size: 1.3em;
    letter-spacing: 0.1em;
    font-weight: normal;
    line-height: 36px;
    text-align: center;
    text-indent: 0.1em;
    margin-bottom: 30px;
    @media screen and (max-width: 560px) {
      font-size: 1.2em;
      margin-bottom: 25px;
      line-height: initial;
    }
  }
  .siteWrapper {
    table {
      margin: auto;
      font-style: normal;
      font-weight: 200;
      text-align: justify;
      background-color: #ffffff;
      border-top: solid 1px #000000;;
      border-right: solid 1px #000000;
      font-size: 1em;
      th,td {
        padding: 10px;
        border-bottom: solid 1px #000000;
        border-left: solid 1px #000000;
        vertical-align: middle;
        line-height: 1.5;
      }
      th {
        background-color: #c9c9c9;
        font-weight: bold;
        font-size: 0.9em;
      }
      td {
        font-size: 0.8em;
      }
      @media screen and (max-width: 560px) {
          th {
              width: 30%;
              font-size: 0.8em;
          }
          td {
              font-size: 0.7em;
              padding: 8px;
          }
      }
    }
  }
}
