
<div class="container">
  <div class="inner with_footer">
    <div class="contents">
      <div class="menu_title">
        <i class="gifts icon big"></i>
        返礼品発注一覧
      </div>
      <div class="top_button_area">
        <button class="ui labeled icon button mini light" (click)="clearForm(sekoBaseComboEm, orderTsFromEm, orderTsToEm)">
          <i class="delete icon"></i>クリア
        </button>
        <button class="ui labeled icon button mini" (click)="searchOrderHenrei()">
          <i class="search icon"></i>検索
        </button>
      </div>
      <div class="search_area">
        <div class="line">
          <label>葬儀社</label>
          <com-dropdown #companyComboEm [settings]="companyCombo" [(selectedValue)]="form_data.company_id" (selectedItemChange)="companyChange($event, sekoBaseComboEm)"></com-dropdown>
          <label>施行拠点</label>
          <com-dropdown #sekoBaseComboEm [settings]="sekoBaseCombo" [(selectedValue)]="form_data.seko_department"></com-dropdown>
          <label>発注日</label>
          <com-calendar #orderTsFromEm [settings]="calendarOptionDate" id="order_ts_from" [(value)]="form_data.order_ts_from"></com-calendar>
          <label class="plane">～</label>
          <com-calendar #orderTsToEm [settings]="calendarOptionDate" id="order_ts_to" [(value)]="form_data.order_ts_to"></com-calendar>
        </div>
        <div class="line">
          <label>発注先</label>
          <com-dropdown #supplierComboEm [settings]="supplierCombo" [(selectedValue)]="form_data.supplier_id"></com-dropdown>
          <label>発注番号</label>
          <div class="ui input">
            <input type="tel" [(ngModel)]="form_data.order_henrei_id">
          </div>
          <label>発注状況</label>
          <div class="ui checkbox" (click)="checkClick('unorder', form_data.ordered, form_data.confirmed)">
            <input type="checkbox" name="unorder" [(ngModel)]="form_data.unorder" [checked]="form_data.unorder">
            <label>未発注</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('ordered', form_data.unorder, form_data.confirmed)">
            <input type="checkbox" name="ordered" [(ngModel)]="form_data.ordered" [checked]="form_data.ordered">
            <label>発注済</label>
          </div>
          <div class="ui checkbox" (click)="checkClick('confirmed', form_data.unorder, form_data.ordered)">
            <input type="checkbox" name="confirmed" [(ngModel)]="form_data.confirmed" [checked]="form_data.confirmed">
            <label>確認済</label>
          </div>
        </div>
      </div>
      <div class="ui left floated left pagination menu" *ngIf="pagination.pages.length > 1">
        <div class="count" *ngIf="order_henrei_list?.length">
          全{{order_henrei_list?.length}}件中
          {{(pagination.current-1)*page_per_count+1}}件
          ～
          {{pagination.current===pagination.pages.length?order_henrei_list?.length:pagination.current*page_per_count}}件表示
        </div>
        <a class="icon item" [class.disabled]="pagination.current===1" (click)="pageTo(pagination.current-1)">
          <i class="left chevron icon"></i>
        </a>
        <ng-container *ngFor="let page of pagination.pages">
          <a class="item" [class.current]="page===pagination.current" (click)="pageTo(page)">{{page}}</a>
        </ng-container>
        <a class="icon item" [class.disabled]="pagination.current===pagination.pages.length" (click)="pageTo(pagination.current+1)">
          <i class="right chevron icon"></i>
        </a>
      </div>
      <div class="table_fixed head">
        <table class="ui celled structured unstackable table">
          <thead>
            <tr class="center aligned" [class.initial]="!searched">
              <th class="check">
                <div class="ui checkbox all_check" [class.disabled]="!order_henrei_list?.length">
                  <input type="checkbox" [(ngModel)]="all_check" (change)="checkAllItem()">
                  <label></label>
                </div>
              </th>
              <th class="seko_id">施行番号</th>
              <th class="seko_date">施行日</th>
              <th class="department_name">施行拠点</th>
              <th class="hall_name">式場名</th>
              <th class="soke_name">葬家名</th>
              <th class="moshu_name">喪主名</th>
              <th class="order_status">発注状況</th>
              <th class="id">発注番号</th>
              <th class="order_ts">発注日時</th>
              <th class="supplier">発注先</th>
              <th class="operation" [class.company]="login_company.base_type !== Const.BASE_TYPE_ADMIN"></th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="table_fixed body">
        <table class="ui celled structured unstackable table">
          <tbody>
            <ng-container *ngFor="let order_henrei of order_henrei_list; index as i">
            <tr *ngIf="i >= (pagination.current-1)*page_per_count && i < pagination.current*page_per_count" (click)="checkItem($event, order_henrei)">
              <td class="center aligned check">
                <div class="ui checkbox">
                  <input type="checkbox" [(ngModel)]="order_henrei.selected">
                  <label></label>
                </div>
              </td>
              <td class="center aligned seko_id" title="{{order_henrei.seko}}">{{order_henrei.seko}}</td>
              <td class="center aligned seko_date" title="{{order_henrei.seko_date | date: 'yyyy/MM/dd'}}">
                {{order_henrei.seko_date | date: 'yyyy/MM/dd'}}</td>
              <td class="department_name" title="{{order_henrei.seko_department_name}}">{{order_henrei.seko_department_name}}</td>
              <td class="hall_name" title="{{order_henrei.hall_name}}">{{order_henrei.hall_name}}</td>
              <td class="soke_name" title="{{order_henrei.soke_name}}">{{order_henrei.soke_name}}</td>
              <td class="moshu_name" title="{{order_henrei.moshu_name}}">{{order_henrei.moshu_name}}</td>
              <td class="center aligned order_status" title="{{Utils.getOrderStatusName(order_henrei.order_status)}}">
                {{Utils.getOrderStatusName(order_henrei.order_status)}}
              </td>
              <td class="center aligned id" title="{{order_henrei.id}}">{{order_henrei.id}}</td>
              <td class="center aligned order_ts" title="{{order_henrei.order_ts | date: 'MM/dd H:mm'}}">
                {{order_henrei.order_ts | date: 'MM/dd H:mm'}}
              </td>
              <td class="supplier" title="{{order_henrei.supplier_name}}">{{order_henrei.supplier_name}}</td>

              <td class="center aligned button operation" [class.company]="login_company.base_type !== Const.BASE_TYPE_ADMIN">
                <i class="large money check icon" title="詳細" (click)="showDetail(order_henrei)"></i>
                <i class="large file pdf icon" title="発注PDF" *ngIf="login_company.base_type !== Const.BASE_TYPE_ADMIN" (click)="downloadPDF(order_henrei.id)"></i>
                <i class="large fax icon" title="発注書送信" *ngIf="canOrder(order_henrei)" (click)="showSendFax(order_henrei)"></i>
              </td>
            </tr>
            </ng-container>
            <tr *ngIf="!order_henrei_list?.length && searched" class="no_data">
              <td class="center aligned" colspan="11">対象データがありません。</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="ui modal mini detail" id="detail">
        <div class="header ui"><i class="money check icon large"></i>発注詳細</div>
        <div class="content" *ngIf="selected_order_henrei">
          <div class="search_area">
            <div class="line">
              <label>発注状況</label>
              <div>{{Utils.getOrderStatusName(selected_order_henrei.order_status)}}</div>
            </div>
            <div class="line">
              <label>発注日時</label>
              <div>{{selected_order_henrei.order_ts | date: 'yyyy/MM/dd H:mm'}}</div>
            </div>
            <div class="line">
              <label>発注先</label>
              <div>{{selected_order_henrei.supplier_name}}</div>
            </div>
            <div class="line row4">
              <label class="textarea">発注備考</label>
              <div class="ui input textarea">
                  <textarea rows="4" [(ngModel)]="selected_order_henrei.order_note" [readonly]="!canOrder(selected_order_henrei)"></textarea>
              </div>
            </div>
          </div>
          <div class="table_fixed head">
            <table class="ui celled structured unstackable table">
              <thead>
                <tr class="center aligned" [class.no-data]="!selected_order_henrei?.henrei_koden?.length && !selected_order_henrei?.henrei_kumotsu?.length">
                  <th class="henreihin_name">商品名</th>
                  <th class="henreihin_price">金額</th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="table_fixed body">
            <table class="ui celled structured unstackable table">
              <tbody>
                <ng-container *ngIf="selected_order_henrei?.henrei_koden?.length">
                  <tr *ngFor="let henrei of selected_order_henrei.henrei_koden">
                    <td class="henreihin_name" title="{{henrei.henreihin_name}}">{{henrei.henreihin_name}}</td>
                    <td class="right aligned henreihin_price" title="{{henrei.henreihin_price | number}}">{{henrei.henreihin_price | number}}</td>
                  </tr>
                </ng-container>
                <ng-container *ngIf="selected_order_henrei?.henrei_kumotsu?.length">
                  <tr *ngFor="let henrei of selected_order_henrei.henrei_kumotsu">
                    <td class="henreihin_name" title="{{henrei.henreihin_name}}">{{henrei.henreihin_name}}</td>
                    <td class="right aligned henreihin_price" title="{{henrei.henreihin_price | number}}">{{henrei.henreihin_price | number}}</td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="saveDetail()" [class.disabled]="!canOrder(selected_order_henrei)">
            <i class="save icon"></i>保存
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeDialog()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>

      <div class="ui modal fax-pdf" id="send-fax">
        <div class="header ui"><i class="fax icon large"></i>
          発注書送信
        </div>
        <div class="content ui segment" [class.loading]="fax_pdf_loading">
          <ng2-pdfjs-viewer #pdfViewerEm [fullScreen]="false" [openFile]="false" [print]="true" [find]="false" [viewBookmark]="false" downloadFileName="返礼品発注書" zoom="page-height">
          </ng2-pdfjs-viewer>
        </div>
        <div class="actions">
          <button class="ui labeled icon button mini" (click)="faxPDF()">
            <i class="fax icon"></i>発注書送信
          </button>
          <button class="ui labeled icon button mini light cancel" (click)="closeDialog()">
            <i class="delete icon"></i>閉じる
          </button>
        </div>
      </div>
		</div>
  </div>
</div>

<div class="footer_button_area">
  <div class="button_group">
    <button class="ui labeled icon button mini light" [class.disabled]="buttonDisabled(Const.ORDER_STATUS_ID_UNORDER)" (click)="saveOrderStatus(Const.ORDER_STATUS_ID_ORDERED)">
      <i class="check icon"></i>発注済
    </button>
    <button class="ui labeled icon button mini light" [class.disabled]="buttonDisabled(Const.ORDER_STATUS_ID_ORDERED)" (click)="saveOrderStatus(Const.ORDER_STATUS_ID_CONFIRMED)">
      <i class="double check icon"></i>発注確認
    </button>
    <button class="ui labeled icon button mini light" [class.disabled]="buttonDisabled(Const.ORDER_STATUS_ID_CONFIRMED)" (click)="saveOrderStatus(Const.ORDER_STATUS_ID_ORDERED)">
      <i class="times icon"></i>確認取消
    </button>
    <button class="ui labeled icon button mini light" routerLink="/foc/henreihin">
      <i class="list icon"></i>受付一覧
    </button>
  </div>
</div>
