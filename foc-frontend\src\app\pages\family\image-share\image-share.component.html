<div class="container">
  <div class="inner">
    <div class="contents">
      <h2>葬家様アップ―ロード画像</h2>
      <div class="button-area">
        <a class="button pink" (click)="selectFile()">新規アップロード</a>
      </div>
      <div class="disclaimer">
        ※画像は最大{{imageCountMax}}枚まで（1ファイル上限{{imageFileSizeMax}}MB）共有可能です。
      </div>
      <ng-container *ngIf="imageItems?.length">
        <div class="item-box">
          <ng-container *ngFor="let image of imageItems; index as i">
          <div class="image-frame">
            <div class="image-box"  (click)="showPopImage(image)">
              <img [src]="image.image_file?image.image_file:image.file_name">
            </div>
            <div class="clear">
              <a class="button grey" (click)="deleteImage(i)">削除</a>
            </div>
          </div>
          </ng-container>
        </div>
      </ng-container>
      <div class="button-area save">
        <a class="button pink" (click)="saveShareImage()">保存</a>
      </div>
    </div>
    <div class="contents">
      <h2>葬儀社アップ―ロード画像</h2>
      <ng-container *ngIf="companyImageItems?.length">
        <div class="item-box">
          <ng-container *ngFor="let image of companyImageItems; index as i">
          <div class="image-frame">
            <div class="image-box" (click)="showPopImage(image)">
              <img [src]="image.image_file?image.image_file:image.file_name">
            </div>
            <div class="clear">
              <a class="button grey" (click)="downloadImage(image.image_file?image.image_file:image.file_name)">ダウンロード</a>
            </div>
          </div>   
          </ng-container>
        </div>   
      </ng-container>
      <ng-container *ngIf="!companyImageItems?.length">
        <div class="no-data">共有された画像がございません。</div>
      </ng-container>
    </div>
    <div class="ui modal pop" id="pop-image">
      <div class="pop-content scrolling">
        <div class="image-box">
          <img [src]="pop_image_file" >
        </div>
      </div>
    </div>
    <div class="ui modal confirm" id="message-popup">
      <div class="content red">
        {{message}}
      </div>
      <div class="button-area">
        <a class="button grey" (click)="closePopup()">閉じる</a>
      </div>
    </div>
    <input type="file" #imageFileUpload id="imageFileUpload" name="imageFileUpload" style="display:none;" />
    <div class="ui modal confirm" id="message-popup">
      <div class="content red">
        {{message}}
      </div>
      <div class="button-area">
        <a class="button grey" (click)="closePopup()">閉じる</a>
      </div>
    </div>
  </div>
</div>

