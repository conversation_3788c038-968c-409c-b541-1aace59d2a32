@import "src/assets/scss/customer/setting";
app-not-found {
  font-size: 14px;
}
.main-header {
  position: fixed;
  width: 100%;
  height: 100px;
  top: 0;
  z-index: 99;
  @media screen and (max-width: 560px) {
    height: 80px;
  }
  background-color: $background-light;
  box-shadow: 0px 5px 5px -5px rgba(0,0,0,0.5);
  --wekit-box-shadow: 0px 5px 5px -5px rgba(0,0,0,0.5);
  .logo {
    position: relative;
    width: 90%;
    max-width: 1000px;
    height: 100px;
    margin: 0 auto;
    background-image: url(../../../../assets/img/customer/logo_cocoro.png);
    background-repeat: no-repeat;
    background-size: 240px auto;
    text-indent: -9999px;
    cursor: pointer;
    &:focus {
      outline: 0;
    }
    &:hover {
      opacity: .8;
    }
    @media screen and (max-width: 560px) {
      height: 80px;
      background-size: 200px auto;
    }
  }
}
.contents {
  height: 100%;
  display: flex;
  >.ui.card {
    .header {
      font-family: inherit;
      font-size: 18px;
      &.huge {
        font-size: 24px;
      }
    }
    .meta {
      font-family: inherit;
      font-size: 14px;

    }
    margin: auto;
    max-width: 800px;
    min-width: 300px;
    width: 50%;
  }
}
footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #413f36;
  color: #ffffff;
  padding: 10px 0;
  font-size: 14px;
  line-height: 1;
  @media screen and (max-width: 560px) {
    font-size: 12px;
  }
  >div {
    width: 90%;
    margin: 0 auto;
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    li {
      list-style: disc;
      margin: 10px 0px 10px 40px;
    }
  }
}
