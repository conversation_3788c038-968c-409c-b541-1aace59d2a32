{% load orders_filters %}
<style type="text/css">
@media print {
  .break-after {
    page-break-after: always;
  }
}
body {
  font-family: "Noto Serif CJK JP", serif;
  font-size: 12px;
  color: #333;
  margin: 0;
}
.logo {
  {% if seko.seko_company.company_logo_file %}
  background-image: url('data:image/jpeg;base64,{{ seko.seko_company.company_logo_file.path|base64_encode }}');
  {% endif %}
  height: 60px;
  width: 100px;
  background-position-x: center;
  background-position-y: center;
  background-repeat: no-repeat;
  background-size: contain;
}
.title {
  text-align: center;
  margin-top: 200px;
}
.company {
  margin-top: 200px;
  width: 50%;
  float: right;
}

.title div, .company div {
  display:inline-block;
  vertical-align: middle;
}
.justify-text {
  text-align: justify;
  line-height: 2ch;
  height: 2ch;
}
.justify-text::after {
  content: "";
  display: inline-block;
  width: 100%;
}
#title2:empty {
  display: none;
}
</style>
</head>
<body>
<section class="top break-after">
  <div>
    <div class="title">
      <div style="width:132px; margin-right:1ch;">
        <div class="justify-text" style="width:100%; font-size:4ch;">{{ title1 }}</div>
        <div id="title2" class="justify-text" style="width:100%; font-size:4ch;">{{ title2 }}</div>
      </div>
      <div class="justify-text" style="width:400px; font-size:8ch;">拝 受 御 芳 名</div>
    </div>
    <div class="kojin" style="text-align:center; font-size:3ch; margin-top:300px">故　{{ seko.kojin.get.name }}　様</div>
    <div class="company">
      <div class="logo"></div>
      <div style="font-size:3ch;">{{ seko.seko_company.base_name }}</div>
    </div>
  </div>
</section>
</body>
</html>
