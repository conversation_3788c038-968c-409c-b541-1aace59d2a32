from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from after_follow.models import AfContactWay
from after_follow.tests.factories import AfContactWayFactory
from bases.models import Base
from bases.tests.factories import BaseFactory
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake = Faker(locale='ja_JP')
tz = timezone.get_current_timezone()


class AfContactWayDetailViewTest(TestCase):
    def setUp(self):
        super().setUp()

        base = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.af_company_way = AfContactWayFactory(company=base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_af_company_way_detail_succeed(self) -> None:
        """AF連絡方法詳細を返す"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_detail', kwargs={'pk': self.af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.af_company_way.pk)
        self.assertEqual(record['company'], self.af_company_way.company.pk)
        self.assertEqual(record['contact_way'], self.af_company_way.contact_way)
        self.assertEqual(record['display_num'], self.af_company_way.display_num)
        self.assertEqual(record['del_flg'], self.af_company_way.del_flg)
        self.assertEqual(
            record['created_at'], self.af_company_way.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.af_company_way.create_user_id)
        self.assertEqual(
            record['updated_at'], self.af_company_way.updated_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['update_user_id'], self.af_company_way.update_user_id)

    def test_af_company_way_detail_failed_by_notfound(self) -> None:
        """AF連絡方法詳細APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_af_company_way: AfContactWay = AfContactWayFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_detail', kwargs={'pk': non_saved_af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_af_company_way_detail_ignores_deleted(self) -> None:
        """AF連絡方法詳細APIは無効化されたものは返さない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.af_company_way.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_detail', kwargs={'pk': self.af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_af_company_way_detail_failed_without_auth(self) -> None:
        """AF連絡方法詳細APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_detail', kwargs={'pk': self.af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class AfContactWayUpdateViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.base = BaseFactory()
        self.af_company_way = AfContactWayFactory(company=self.base)
        self.new_af_company_way_data = AfContactWayFactory.build(company=self.base)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.base)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'company': self.new_af_company_way_data.company.pk,
            'contact_way': self.new_af_company_way_data.contact_way,
            'display_num': self.new_af_company_way_data.display_num,
        }

    def test_af_company_way_update_succeed(self) -> None:
        """AF連絡方法を更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse('after_follow:contact_way_detail', kwargs={'pk': self.af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        record: Dict = response.json()
        self.assertEqual(record['id'], self.af_company_way.pk)
        self.assertEqual(record['company'], self.new_af_company_way_data.company.pk)
        self.assertEqual(record['contact_way'], self.new_af_company_way_data.contact_way)
        self.assertEqual(record['display_num'], self.new_af_company_way_data.display_num)
        self.assertEqual(record['del_flg'], self.new_af_company_way_data.del_flg)
        self.assertEqual(
            record['created_at'], self.af_company_way.created_at.astimezone(tz).isoformat()
        )
        self.assertEqual(record['create_user_id'], self.af_company_way.create_user_id)
        self.assertIsNotNone(record['updated_at'])
        self.assertEqual(record['update_user_id'], self.staff.pk)

    def test_af_company_way_update_failed_by_notfound(self) -> None:
        """AF連絡方法更新APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_af_company_way: AfContactWay = AfContactWayFactory.build()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_detail', kwargs={'pk': non_saved_af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_af_company_way_update_ignores_deleted(self) -> None:
        """AF連絡方法更新APIは無効化された法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.af_company_way.disable()

        params: Dict = {}
        response = self.api_client.get(
            reverse('after_follow:contact_way_detail', kwargs={'pk': self.af_company_way.pk}),
            data=params,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))

    def test_af_company_way_update_failed_without_auth(self) -> None:
        """AF連絡方法更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.put(
            reverse(
                'after_follow:contact_way_detail', kwargs={'pk': self.af_company_way.company.pk}
            ),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))


class AfContactWayDeleteViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.company = BaseFactory(base_type=Base.OrgType.COMPANY)
        self.af_company_way: AfContactWay = AfContactWayFactory(company=self.company)

        self.api_client = APIClient()
        self.staff = StaffFactory(base=self.company)
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def test_af_company_way_delete_succeed(self) -> None:
        """AF連絡方法を論理削除する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:contact_way_detail', kwargs={'pk': self.af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # AF連絡方法のdel_flgがTrueになるだけ
        db_af_contact_way: AfContactWay = AfContactWay.objects.get(pk=self.af_company_way.pk)
        self.assertTrue(db_af_contact_way.del_flg)
        self.assertEqual(db_af_contact_way.create_user_id, self.af_company_way.create_user_id)
        self.assertEqual(db_af_contact_way.update_user_id, self.staff.pk)

    def test_af_company_way_delete_failed_by_notfound(self) -> None:
        """AF連絡方法削除APIが存在しないPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_af_company_way: AfContactWay = AfContactWayFactory.build()
        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:contact_way_detail', kwargs={'pk': non_saved_af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_af_company_way_delete_failed_by_already_deleted(self) -> None:
        """AF連絡方法削除APIが論理削除済みの法要を指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        self.af_company_way.disable()

        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:contact_way_detail', kwargs={'pk': self.af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_af_company_way_delete_failed_without_auth(self) -> None:
        """AF連絡方法削除APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = {}
        response = self.api_client.delete(
            reverse('after_follow:contact_way_detail', kwargs={'pk': self.af_company_way.pk}),
            data=params,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
