# Generated by Django 3.1.2 on 2020-10-22 07:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bases', '0004_focfee_tokusho'),
    ]

    operations = [
        migrations.AlterField(
            model_name='focfee',
            name='chobun_fee_unit',
            field=models.IntegerField(
                choices=[(None, '(Unknown)'), (1, 'Percentage'), (2, 'Fixed Amount')],
                verbose_name='chobun fee unit',
            ),
        ),
        migrations.AlterField(
            model_name='focfee',
            name='henreihin_fee_unit',
            field=models.IntegerField(
                choices=[(None, '(Unknown)'), (1, 'Percentage'), (2, 'Fixed Amount')],
                verbose_name='henreihin fee unit',
            ),
        ),
        migrations.AlterField(
            model_name='focfee',
            name='koden_fee_unit',
            field=models.IntegerField(
                choices=[(None, '(Unknown)'), (1, 'Percentage'), (2, 'Fixed Amount')],
                verbose_name='koden fee unit',
            ),
        ),
        migrations.AlterField(
            model_name='focfee',
            name='kumotsu_fee_unit',
            field=models.IntegerField(
                choices=[(None, '(Unknown)'), (1, 'Percentage'), (2, 'Fixed Amount')],
                verbose_name='kumotsu fee unit',
            ),
        ),
    ]
