<div class="container">
  <ng-container *ngIf="!is_loading && !err_msg">
    <app-sokepolicy></app-sokepolicy>
    <div class="approve">
      <div class="ui checkbox">
        <input type="checkbox" [(ngModel)]="approve_flg">
        <label>ご利用規約に同意する</label>
      </div>
    </div>
  <div class="button">
    <button class="ui button teal" (click)="approveSeko()" [disabled]="!approve_flg">
      同意する
    </button>
  </div>
  </ng-container>
  <div class="contents error" *ngIf="err_msg">
    <div class="error_msg">{{err_msg}}</div>
  </div>
</div>
