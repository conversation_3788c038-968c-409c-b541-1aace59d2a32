from typing import Dict

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.settings import api_settings

from orders.models import EntryDetailKumotsu
from orders.tests.factories import EntryDetailKumotsuFactory
from staffs.models import Staff
from staffs.tests.factories import StaffFactory
from utils.authentication import ClassableRefreshToken as RefreshToken

tz = timezone.get_current_timezone()
jwt_auth_prefix = api_settings.AUTH_HEADER_TYPES[0]
fake_provider = Faker(locale='ja_JP')


class KumotsuUpdateStatusViewTest(TestCase):
    def setUp(self):
        super().setUp()

        self.detail_kumotsu_1 = EntryDetailKumotsuFactory(
            order_status=1, order_ts=None, order_staff=None
        )
        self.detail_kumotsu_2 = EntryDetailKumotsuFactory(
            order_status=1, order_ts=None, order_staff=None
        )
        self.detail_kumotsu_3 = EntryDetailKumotsuFactory(
            order_status=1, order_ts=None, order_staff=None
        )

        self.api_client = APIClient()
        self.staff = StaffFactory()
        refresh = RefreshToken.for_user(self.staff)
        self.refresh_token = str(refresh)
        self.access_token = str(refresh.access_token)

    def basic_params(self) -> Dict:
        return {
            'ids': [elem.pk for elem in [self.detail_kumotsu_1, self.detail_kumotsu_2]],
            'order_status': 3,
        }

    def test_kumotsu_status_update_succeed(self) -> None:
        """供花供物明細(複数)の発注ステータスを2以外に更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('orders:kumotsu_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(len(record), 2)
        for record_kumotsu, self_kumotsu in zip(
            record, [self.detail_kumotsu_1, self.detail_kumotsu_2]
        ):
            self.assertEqual(record_kumotsu['order_status'], 3)
            self.assertEqual(record_kumotsu['okurinushi_company'], self_kumotsu.okurinushi_company)
            self.assertEqual(record_kumotsu['entry_detail']['id'], self_kumotsu.entry_detail.pk)
            self.assertEqual(record_kumotsu['supplier']['id'], self_kumotsu.supplier.pk)

        for kumotsu in [self.detail_kumotsu_1, self.detail_kumotsu_2, self.detail_kumotsu_3]:
            kumotsu.refresh_from_db()
        self.assertEqual(self.detail_kumotsu_1.order_status, 3)
        self.assertEqual(self.detail_kumotsu_2.order_status, 3)
        self.assertEqual(self.detail_kumotsu_3.order_status, 1)

    def test_kumotsu_status_update_succeed_2(self) -> None:
        """供花供物明細(複数)の発注ステータスを2に更新する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        params: Dict = self.basic_params()
        params['order_status'] = 2
        response = self.api_client.post(
            reverse('orders:kumotsu_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(len(record), 2)
        for record_kumotsu, self_kumotsu in zip(
            record, [self.detail_kumotsu_1, self.detail_kumotsu_2]
        ):
            self.assertEqual(record_kumotsu['order_status'], 2)
            self.assertIsNotNone(record_kumotsu['order_ts'])
            self.assertEqual(record_kumotsu['order_staff'], self.staff.pk)
            self.assertEqual(record_kumotsu['okurinushi_company'], self_kumotsu.okurinushi_company)
            self.assertEqual(record_kumotsu['entry_detail']['id'], self_kumotsu.entry_detail.pk)
            self.assertEqual(record_kumotsu['supplier']['id'], self_kumotsu.supplier.pk)

        for kumotsu in [self.detail_kumotsu_1, self.detail_kumotsu_2, self.detail_kumotsu_3]:
            kumotsu.refresh_from_db()
        self.assertEqual(self.detail_kumotsu_1.order_status, 2)
        self.assertEqual(self.detail_kumotsu_2.order_status, 2)
        self.assertEqual(self.detail_kumotsu_3.order_status, 1)

    def test_kumotsu_status_update_succeed_back_to_2(self) -> None:
        """供花供物明細(複数)の発注ステータスを2に更新する際、既にorder_tsが入っている場合は更新しない"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        another_staff: Staff = StaffFactory()
        for detail_kumotsu in [self.detail_kumotsu_1, self.detail_kumotsu_2]:
            detail_kumotsu.order_status = 3
            detail_kumotsu.order_ts = timezone.localtime()
            detail_kumotsu.order_staff = another_staff
            detail_kumotsu.save()

        params: Dict = self.basic_params()
        params['order_status'] = 2
        response = self.api_client.post(
            reverse('orders:kumotsu_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        record: Dict = response.json()
        self.assertEqual(len(record), 2)
        for record_kumotsu, self_kumotsu in zip(
            record, [self.detail_kumotsu_1, self.detail_kumotsu_2]
        ):
            self.assertEqual(record_kumotsu['order_status'], 2)
            self.assertEqual(
                record_kumotsu['order_ts'], self_kumotsu.order_ts.astimezone(tz).isoformat()
            )
            self.assertEqual(record_kumotsu['order_staff'], another_staff.pk)
            self.assertEqual(record_kumotsu['okurinushi_company'], self_kumotsu.okurinushi_company)
            self.assertEqual(record_kumotsu['entry_detail']['id'], self_kumotsu.entry_detail.pk)
            self.assertEqual(record_kumotsu['supplier']['id'], self_kumotsu.supplier.pk)

        for kumotsu in [self.detail_kumotsu_1, self.detail_kumotsu_2, self.detail_kumotsu_3]:
            kumotsu.refresh_from_db()
        self.assertEqual(self.detail_kumotsu_1.order_status, 2)
        self.assertEqual(self.detail_kumotsu_2.order_status, 2)
        self.assertEqual(self.detail_kumotsu_3.order_status, 1)

    def test_kumotsu_status_update_failed_by_illegal_pk(self) -> None:
        """供花供物明細発注ステータス更新APIが不正なPKを指定して失敗する"""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'{jwt_auth_prefix} {self.access_token}')

        non_saved_kumotsu: EntryDetailKumotsu = EntryDetailKumotsuFactory.build()
        params: Dict = self.basic_params()
        params['ids'].append(non_saved_kumotsu.pk)

        response = self.api_client.post(
            reverse('orders:kumotsu_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('ids'))

    def test_kumotsu_status_update_failed_without_auth(self) -> None:
        """供花供物明細発注ステータス更新APIがAuthorizationヘッダがなくて失敗する"""
        params: Dict = self.basic_params()
        response = self.api_client.post(
            reverse('orders:kumotsu_update_status'), data=params, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        record: Dict = response.json()
        self.assertIsNotNone(record.get('detail'))
