# Generated by Django 3.1.5 on 2021-02-17 07:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bases', '0013_smsaccount'),
    ]

    operations = [
        migrations.CreateModel(
            name='Advertise',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'banner_file',
                    models.ImageField(
                        db_column='file_name', upload_to='adv_banner', verbose_name='banner file'
                    ),
                ),
                ('url', models.TextField(blank=True, null=True, verbose_name='URL')),
                (
                    'link_file',
                    models.ImageField(
                        blank=True,
                        db_column='link_file_name',
                        null=True,
                        upload_to='adv_link',
                        verbose_name='link file',
                    ),
                ),
                ('begin_date', models.DateField(verbose_name='begin on')),
                ('end_date', models.DateField(verbose_name='end on')),
                ('del_flg', models.Bo<PERSON>anField(default=False, verbose_name='deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('create_user_id', models.IntegerField(verbose_name='created by')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('update_user_id', models.IntegerField(verbose_name='updated by')),
                (
                    'company',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='advertises',
                        to='bases.base',
                        verbose_name='company',
                    ),
                ),
            ],
            options={
                'db_table': 'm_advertise',
            },
        ),
    ]
