from typing import Dict

from django.db import models
from django.db.models import Case, Q, QuerySet, Sum, When
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _
from mptt.models import MPTTModel, TreeForeignKey

from masters.models import Service, SokeMenu


class Base(MPTTModel):
    class OrgType(models.IntegerChoices):
        ADMIN = 0, _('Administrator')
        COMPANY = 1, _('Company')
        DEPARTMENT = 2, _('Department')
        HALL = 3, _('Hall')
        OTHER = 9, _('Other hall')
        __empty__ = _('(Unknown)')

    base_type = models.IntegerField(_('base type'), choices=OrgType.choices)
    parent = TreeForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_column='parent_base_id',
        verbose_name=_('parent base company'),
        related_name='children',
    )
    company_code = models.CharField(_('company code'), max_length=20, blank=True, null=True)
    base_name = models.TextField(
        _('base name'),
    )
    zip_code = models.CharField(_('zipcode'), max_length=7)
    prefecture = models.TextField(
        _('prefecture'),
    )
    address_1 = models.TextField(
        _('address1'),
    )
    address_2 = models.TextField(_('address2'), blank=True, null=True)
    address_3 = models.TextField(_('address3'), blank=True, null=True)
    tel = models.CharField(_('tel no'), max_length=15)
    fax = models.CharField(_('fax no'), max_length=15)
    company_logo_file = models.ImageField(
        _('company logo file'), upload_to='company_logo', blank=True, null=True
    )
    company_map_file = models.ImageField(
        _('company map file'), upload_to='company_map', blank=True, null=True
    )
    calc_type = models.IntegerField(blank=True, null=True)
    display_num = models.IntegerField(_('display order'), default=1)
    del_flg = models.BooleanField(_('deleted'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    create_user_id = models.IntegerField(_('created by'))
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    update_user_id = models.IntegerField(_('updated by'))
    chobun_daishi_masters = models.ManyToManyField(
        'masters.ChobunDaishiMaster',
        through='chobun_daishi.ChobunDaishi',
        verbose_name='chobun daishi master',
        related_name='bases',
    )
    services = models.ManyToManyField(
        'masters.Service',
        through='bases.BaseService',
        verbose_name='services',
        related_name='bases',
    )
    soke_menus = models.ManyToManyField(
        'masters.SokeMenu',
        through='bases.BaseSokeMenu',
        verbose_name='soke menu',
        related_name='bases',
    )
    fdn_code = models.TextField(_('fdn code'), blank=True, null=True)
    order_mail_address = models.TextField(_('order mail address'), blank=True, null=True)

    class Meta:
        db_table = 'm_base'

    class MPTTMeta:
        order_insertion_by = ['display_num', 'base_name']

    def active_children(self) -> QuerySet:
        return self.children.filter(Q(del_flg=False)).order_by('display_num', 'base_name')

    def disable(self) -> None:
        """拠点を即時無効化(論理削除)します"""
        self.del_flg = True
        self.save()

    def get_unprinted_chobun_count(self) -> int:
        qs = Base.objects.filter(
            pk=self.pk,
            seko__del_flg=False,
            seko__entries__cancel_ts=None,
            seko__entries__details__chobun__printed_flg=False,
        )

        return qs.count()

    def get_kumotsu_count(self) -> Dict[str, int]:
        qs = Base.objects.filter(
            pk=self.pk,
            seko__del_flg=False,
            seko__entries__cancel_ts=None,
        )
        result = qs.aggregate(
            unordered=Coalesce(
                Sum(
                    Case(
                        When(seko__entries__details__kumotsu__order_status=1, then=1),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
            unconfirmed=Coalesce(
                Sum(
                    Case(
                        When(seko__entries__details__kumotsu__order_status=2, then=1),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
        )

        return result

    def get_henrei_kumotsu_count(self) -> Dict[str, int]:
        qs = Base.objects.filter(
            pk=self.pk,
            seko__del_flg=False,
            seko__entries__cancel_ts=None,
            seko__entries__details__kumotsu__henrei_kumotsu__select_status=2,
        )
        result = qs.aggregate(
            unordered=Coalesce(
                Sum(
                    Case(
                        When(
                            seko__entries__details__kumotsu__henrei_kumotsu__order_status__in=[  # noqa: E501
                                0,
                                1,
                            ],
                            then=1,
                        ),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
            unconfirmed=Coalesce(
                Sum(
                    Case(
                        When(
                            seko__entries__details__kumotsu__henrei_kumotsu__order_status=2,  # noqa: E501
                            then=1,
                        ),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
        )

        return result

    def get_henrei_koden_count(self) -> Dict[str, int]:
        qs = Base.objects.filter(
            pk=self.pk,
            seko__del_flg=False,
            seko__entries__cancel_ts=None,
            seko__entries__details__koden__henrei_koden__select_status=2,
        )
        result = qs.aggregate(
            unordered=Coalesce(
                Sum(
                    Case(
                        When(
                            seko__entries__details__koden__henrei_koden__order_status__in=[  # noqa: E501
                                0,
                                1,
                            ],
                            then=1,
                        ),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
            unconfirmed=Coalesce(
                Sum(
                    Case(
                        When(
                            seko__entries__details__koden__henrei_koden__order_status=2,
                            then=1,
                        ),
                        default=0,
                        output_field=models.IntegerField(),
                    )
                ),
                0,
            ),
        )

        return result

    def seko_inquiries(self) -> Dict[str, int]:
        from after_follow.models import AfWish
        from seko.models import SekoInquiry

        unanswerd_inquiries = SekoInquiry.objects.filter(
            (Q(answers__isnull=True) | Q(answers__del_flg=True))
            & Q(seko__del_flg=False)
            & Q(seko__seko_department=self)
        )
        unanswerd_af_wishes = AfWish.objects.filter(
            Q(answered_flg=False)
            & Q(seko_af__seko__del_flg=False)
            & Q(seko_af__seko__seko_department=self)
        )
        return {
            'unanswerd_inquiry': unanswerd_inquiries.count(),
            'unanswerd_af': unanswerd_af_wishes.count(),
        }


class Tokusho(models.Model):
    company = models.OneToOneField(
        Base, models.CASCADE, primary_key=True, verbose_name=_('company'), related_name='tokusho'
    )
    responsible_name = models.TextField(_('responsible name'))
    mail_address = models.EmailField(_('mail address'))
    from_name = models.TextField(_('from name'), unique=True)
    url = models.TextField(_('url'))
    kumotsu_offer_timing = models.TextField(_('kumotsu offer timing'), blank=True, null=True)
    koden_offer_timing = models.TextField(_('koden offer timing'), blank=True, null=True)
    henrei_offer_timing = models.TextField(_('henrei offer timing'), blank=True, null=True)
    business_no = models.TextField(_('business number'), blank=True, null=True)

    class Meta:
        db_table = 'm_tokusho'


class FocFee(models.Model):
    class UnitType(models.IntegerChoices):
        PERCENTAGE = 1, _('Percentage')
        FIXED = 2, _('Fixed Amount')
        __empty__ = _('(Unknown)')

    company = models.OneToOneField(
        Base, models.CASCADE, primary_key=True, verbose_name=_('company'), related_name='focfee'
    )
    monthly_fee = models.IntegerField(_('monthly fee'))
    chobun_fee = models.IntegerField(_('chobun fee'))
    chobun_fee_unit = models.IntegerField(_('chobun fee unit'), choices=UnitType.choices)
    kumotsu_fee = models.IntegerField(_('kumotsu fee'))
    kumotsu_fee_unit = models.IntegerField(_('kumotsu fee unit'), choices=UnitType.choices)
    koden_fee = models.IntegerField(_('koden fee'))
    koden_fee_unit = models.IntegerField(_('koden fee unit'), choices=UnitType.choices)
    henreihin_fee = models.IntegerField(_('henreihin fee'))
    henreihin_fee_unit = models.IntegerField(_('henreihin fee unit'), choices=UnitType.choices)
    gmo_code = models.CharField(max_length=8, blank=True, null=True)
    gmo_code_koden = models.CharField(max_length=8, blank=True, null=True)
    gmo_test_flg = models.BooleanField(default=False, blank=True, null=True)
    secure_chk_flg = models.BooleanField(default=False, blank=True, null=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True, blank=True, null=True)
    create_user_id = models.IntegerField(_('created by'), blank=True, null=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True, blank=True, null=True)
    update_user_id = models.IntegerField(_('created by'), blank=True, null=True)

    class Meta:
        db_table = 'm_foc_fee'


class BaseService(models.Model):
    base = models.ForeignKey(Base, models.DO_NOTHING, verbose_name=_('base'))
    service = models.ForeignKey(Service, models.DO_NOTHING, verbose_name=_('service'))

    class Meta:
        db_table = 'm_base_service'


class SmsAccount(models.Model):
    company = models.OneToOneField(
        Base,
        models.CASCADE,
        primary_key=True,
        verbose_name=_('company'),
        related_name='sms_account',
    )
    token = models.TextField(_('token'))
    client_id = models.TextField(_('client id'))
    sms_code = models.CharField(_('SMS code'), max_length=5, blank=True, null=True)

    class Meta:
        db_table = 'm_sms_account'


class BaseSokeMenu(models.Model):
    base = models.ForeignKey(Base, models.DO_NOTHING, verbose_name=_('base'))
    soke_menu = models.ForeignKey(SokeMenu, models.DO_NOTHING, verbose_name=_('soke_menu'))

    class Meta:
        db_table = 'm_base_soke_menu'
