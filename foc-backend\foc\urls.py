"""foc URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path

urlpatterns = [
    path('masters/', include('masters.urls')),
    path('bases/', include('bases.urls')),
    path('staffs/', include('staffs.urls')),
    path('seko/', include('seko.urls')),
    path('suppliers/', include('suppliers.urls')),
    path('items/', include('items.urls')),
    path('fuho_samples/', include('fuho_samples.urls')),
    path('chobun_daishi/', include('chobun_daishi.urls')),
    path('service_reception_terms/', include('service_reception_terms.urls')),
    path('orders/', include('orders.urls')),
    path('henrei/', include('henrei.urls')),
    path('inquiry/', include('inquiry.urls')),
    path('invoices/', include('invoices.urls')),
    path('hoyo/', include('hoyo.urls')),
    path('after_follow/', include('after_follow.urls')),
    path('event_mails/', include('event_mails.urls')),
    path('faqs/', include('faqs.urls')),
    path('advertises/', include('advertises.urls')),
    path('admin/', admin.site.urls),
]
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
