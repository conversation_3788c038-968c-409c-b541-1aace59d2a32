
<div class="container">
  <div class="inner">
    <div class="contents" *ngIf="send_complete">
      <div class="message">{{message}}</div>
    </div>
    <div class="contents" *ngIf="!send_complete">
      <h2>お問合せ</h2>
      <div class="pre-wrap">メールアドレスが間違っていると返信できない場合がございます。送信前に再度ご確認ください。
以下の項目にご記入のうえ、「入力内容確認」ボタンを押下してください。</div>
      <div class="input-area big">
        <div class="line">
          <div class="label required">お名前</div>
          <div class="input sender_name" #nameEm [class.error]="isErrorField(nameEm)">
            <input type="text" [(ngModel)]="inquiry.sender_name">
          </div>
        </div>
        <div class="line">
          <div class="label required">フリガナ</div>
          <div class="input sender_kana" #nameKanaEm [class.error]="isErrorField(nameKanaEm)">
            <input type="text" [(ngModel)]="inquiry.sender_kana">
          </div>
        </div>
        <div class="line">
          <div class="label">お電話番号</div>
          <div class="input sender_tel" #telEm [class.error]="isErrorField(telEm)">
            <input type="tel" autocomplete="off" maxlength="15" [(ngModel)]="inquiry.sender_tel">
          </div>
        </div>
        <div class="line">
          <div class="label required">メールアドレス</div>
          <div class="input sender_address" #mailAddressEm [class.error]="isErrorField(mailAddressEm)">
            <input type="email" [(ngModel)]="inquiry.sender_address">
          </div>
        </div>
        <div class="line">
          <div class="label required">お問合せ内容</div>
          <div class="input mail_body" #bodyEm [class.error]="isErrorField(bodyEm)">
            <textarea rows="10" [(ngModel)]="inquiry.mail_body"></textarea>
          </div>
        </div>
        <div class="line">
          <div class="label required huge">個人情報のお取扱いについて</div>
          <div class="input approve_flg" #approveFlgEm [class.error]="isErrorField(approveFlgEm)">
            <div class="description">個人情報の取扱いにきましては、<br>
            「<a target="_blank" routerLink="/privacypolicy">プライバシーポリシー</a>」をご参照ください。</div>
            <input type="checkbox" name="approve" [value]="1" [(ngModel)]="approve_flg"> 個人情報の取扱いに同意する
          </div>
        </div>
      </div>
    </div>
    <div class="button-area" *ngIf="!send_complete">
      <a class="button grey" (click)="sendConfirm()" [class.disabled]="!approve_flg">送信</a>
    </div>
    <div class="ui modal confirm" id="send-confirm">
      <div class="content">
        問合せ内容を送信します。<br>よろしいでしょうか？
      </div>
      <div class="button-area">
        <a class="button" (click)="cancelConfirm()">キャンセル</a>
        <a class="button grey" (click)="sendData()">送信</a>
      </div>
    </div>
  </div>
</div>
